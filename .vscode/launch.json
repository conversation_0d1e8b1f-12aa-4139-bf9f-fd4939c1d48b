{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "purpose": ["debug-test"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "justMyCode": true, "subProcess": true, "env": {"PYTHONPATH": "${workspaceFolder}/job", "GEVENT_SUPPORT": "True", "PYTEST_ADDOPTS": "--no-cov"}, "python": "${command:python.interpreterPath}"}]}