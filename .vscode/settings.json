{
  // SQL Formatter 設定: SQLキーワードを大文字に変換
  "sqltools.format.uppercase": true,
  // YAMLでカスタムタグを認識させる設定（AWS CloudFormation用のタグ例）
  "yaml.customTags": [
    "!Ref", // CloudFormationのリファレンスタグ
    "!Sub scalar", // 文字列置換タグ
    "!Sub sequence", // 配列での文字列置換
    "!Join sequence", // 配列の結合
    "!FindInMap sequence", // マッピングの取得
    "!GetAtt scalar", // 属性の取得
    "!GetAtt sequence", // 複数属性の取得
    "!Base64 mapping", // Base64エンコード
    "!GetAZs", // 利用可能なアベイラビリティゾーンの取得
    "!Select scalar", // 配列からの選択（単一値）
    "!Select sequence", // 配列からの選択（複数値）
    "!Split sequence", // 文字列の分割
    "!ImportValue", // エクスポートされた値のインポート
    "!Condition", // 条件
    "!Equals sequence", // 条件式の等価性チェック
    "!And", // 条件式のAND
    "!If", // 条件式のIF
    "!Not", // 条件式のNOT（単一値）
    "!Not sequence", // 条件式のNOT（配列）
    "!Or" // 条件式のOR
  ],
  // Python Linter（コードチェック）の設定
  "flake8.args": [
    "--max-line-length=120",
    "--extend-ignore='E203,E266,E501,W503,F403,F401'"
  ],
  "python.linting.flake8Args": [
    "--format=%(path)s:%(row)d:%(col)d: %(code)s %(text)s"
  ],
  // Python Formatterの設定
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
  },
  // エディタ設定: 保存時に自動フォーマット
  "editor.formatOnSave": true,
  // Javaコードのフォーマット設定（Google Styleに準拠）
  "java.format.settings.url": "https://raw.githubusercontent.com/google/google-java-format/master/eclipse-java-google-style.xml",
  "java.format.settings.profile": "GoogleStyle",
  // Java拡張機能との連携
  "java.format.enabled": true,
  "java.codeGeneration.hashCodeEquals.useInstanceof": true,
  "java.completion.enabled": true,
  "java.format.comments.enabled": true,
  "java.format.onType.enabled": true,
  "java.saveActions.organizeImports": true,
  "git.enableSmartCommit": true,
  "git.autofetch": true,
  "git.confirmSync": false,
  "java.configuration.updateBuildConfiguration": "automatic",
  "java.compile.nullAnalysis.mode": "automatic",
  "files.exclude": {
    "**/.classpath": true,
    "**/.project": true,
    "**/.settings": true,
    "**/.factorypath": true
  },
  "gitlens.proxy": {
    "url": "http://tkyproxy-std.intra.tis.co.jp:8080",
    "strictSSL": false,
    "noProxy": "127.0.0.1,localhost,s2105c10703-t1"
  },
  "python.defaultInterpreterPath": "job/.venv/bin/python",
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "python.envFile": "${workspaceFolder}/.vscode/.env",
  "terminal.integrated.env.linux": {
    "PYTHONPATH": "${workspaceFolder}/job"
  },
  "files.associations": {
    ".coveragerc": "ini",
    "log_message.config": "ini"
  },
  "python.analysis.extraPaths": [
    "./job"
  ],
  "pythonTestExplorer.testFramework": "pytest",
  "flake8.path": [
    "flake8"
  ],
  "terminal.integrated.scrollback": 100000,
  "python.testing.autoTestDiscoverOnSaveEnabled": true,
  "debugpy.justMyCode": true,
  "python.testing.pytestArgs": [
    "job/test",
    "--rootdir",
    "job"
  ],
  "xml.fileAssociations": [
    {
      "pattern": "**/*{プロモーション,Promotion}*.xml",
      "systemId": "https://salesforcecommercecloud.github.io/b2c-dev-doc/docs/current/xsd/promotion.xsd"
    },
    {
      "pattern": "**/*{顧客グループ,CustomerGroups}*.xml",
      "systemId": "https://salesforcecommercecloud.github.io/b2c-dev-doc/docs/current/xsd/customergroup.xsd"
    },
    {
      "pattern": "**/*{価格表,Pricebook}*.xml",
      "systemId": "https://salesforcecommercecloud.github.io/b2c-dev-doc/docs/current/xsd/pricebook.xsd"
    },
    {
      "pattern": "**/*{Inventory}*.xml",
      "systemId": "https://salesforcecommercecloud.github.io/b2c-dev-doc/docs/current/xsd/inventory.xsd"
    }
  ]
}
