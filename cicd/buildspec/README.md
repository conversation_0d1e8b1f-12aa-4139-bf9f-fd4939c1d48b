# ビルド仕様（Buildspec）ディレクトリ

このディレクトリには、AWS CodeBuildで使用するビルド仕様ファイル（buildspec.yml）が含まれています。

## ファイル構成

- `deploy-buildspec.yml`: デプロイ用のビルド仕様ファイル

## 設計方針

現在の`deploy-buildspec.yml`は多くの処理ステップを含んでおり、今後の保守性向上のために以下の方針でリファクタリングを予定しています。

### 現状の課題

- ファイルサイズが大きい（約600ステップ）
- 複数の機能が単一ファイルに混在している
- 変更や機能追加が難しい
- テストが困難

### リファクタリング方針

処理を機能単位で分割し、シェルスクリプトとして外出しすることで、保守性と再利用性を向上させます。

#### ディレクトリ構成

```
cicd/
├── scripts/
│   ├── auth/
│   │   ├── github-auth.sh        # GitHub認証関連の処理
│   │   └── generate_jwt.sh       # JWT生成処理
│   ├── pr/
│   │   ├── get-merged-prs.sh     # マージ済みPR取得処理
│   │   └── comment-pr.sh         # PRへのコメント追加処理
│   ├── detect/
│   │   ├── detect-changes.sh     # 変更検出処理
│   │   ├── detect-glue.sh        # Glue変更検出処理
│   │   └── detect-cfn.sh         # CloudFormation変更検出処理
│   ├── deploy/
│   │   ├── deploy-glue.sh        # Glueデプロイ処理
│   │   ├── deploy-cfn.sh         # CloudFormationデプロイ処理
│   │   └── update-parameter-store.sh # Parameter Store更新処理
│   ├── codebuild/                # CodeBuild用スクリプト
│   │   ├── common.sh             # CodeBuild共通処理（環境変数設定など）
│   │   ├── pre_build.sh          # pre_buildフェーズの処理
│   │   ├── build.sh              # buildフェーズの処理
│   │   └── post_build.sh         # post_buildフェーズの処理
│   ├── utils/
│   │   ├── logging.sh            # ログ出力関連の共通処理
│   │   └── error-handling.sh     # エラーハンドリング共通処理
│   └── deploy.sh                 # CodeBuildプロジェクト起動スクリプト
└── buildspec/
    └── deploy-buildspec.yml      # シンプル化されたbuildspec
```

#### 実装アプローチ

1. **段階的な移行**:
   - 一度にすべてを移行するのではなく、機能ごとに段階的に移行
   - 各段階でテストを行い、問題がないことを確認

2. **スクリプト間の連携**:
   - 環境変数を使用してスクリプト間でデータを受け渡し
   - 戻り値を適切に処理して、エラーハンドリングを確実に行う

3. **シンプルなbuildspec.yml**:
   - buildspec.ymlはスクリプトの呼び出しと基本的な環境設定のみに留める
   - 複雑なロジックはすべてスクリプトに移動

#### メリット

1. **保守性の向上**:
   - 機能ごとに分割されたスクリプトは理解しやすく、修正も容易
   - 新機能の追加も既存機能に影響を与えにくい

2. **テスト容易性**:
   - 個別のスクリプトは単体でテスト可能
   - 問題の切り分けが容易になる

3. **再利用性**:
   - 共通処理を別のワークフローでも再利用可能

4. **ドキュメント性**:
   - ディレクトリ構造自体がシステムの構成を表現
   - 各スクリプトの役割が明確になる

## スクリプト間の呼び出し関係

スクリプト間の呼び出し関係を以下のMermaid図で示します。この図は、各スクリプトがどのスクリプトを呼び出すか、および共通ユーティリティの使用状況を表しています。

```mermaid
graph TD
    A[deploy-buildspec.yml] --> B[pre_build.sh]
    A --> C[build.sh]
    A --> D[post_build.sh]
    
    B --> E[common.sh]
    C --> E
    D --> E
    
    B --> F[github-auth.sh]
    F --> G[generate_jwt.sh]
    
    C --> H[get-merged-prs.sh]
    C --> I[detect-changes.sh]
    C --> J[detect-glue.sh]
    C --> K[detect-cfn.sh]
    C --> L[deploy-glue.sh]
    C --> M[deploy-cfn.sh]
    
    D --> N[update-parameter-store.sh]
    D --> O[comment-pr.sh]
    
    %% 共通ユーティリティの依存関係
    E -.-> P[logging.sh]
    E -.-> Q[error-handling.sh]
    F -.-> P
    F -.-> Q
    H -.-> P
    H -.-> Q
    I -.-> P
    I -.-> Q
    J -.-> P
    J -.-> Q
    K -.-> P
    K -.-> Q
    L -.-> P
    L -.-> Q
    M -.-> P
    M -.-> Q
    N -.-> P
    N -.-> Q
    O -.-> P
    O -.-> Q
    Q -.-> P
    
    %% スタイル設定
    classDef buildspec fill:#f9f,stroke:#333,stroke-width:2px;
    classDef codebuild fill:#f99,stroke:#333,stroke-width:2px;
    classDef script fill:#bbf,stroke:#333,stroke-width:1px;
    classDef util fill:#bfb,stroke:#333,stroke-width:1px;
    
    class A buildspec;
    class B,C,D,E codebuild;
    class F,G,H,I,J,K,L,M,N,O script;
    class P,Q util;
```

### データフロー

スクリプト間のデータフローは主に以下の方法で行われます：

1. **環境変数**:
   - `deploy-buildspec.yml`から各スクリプトへの入力パラメータとして使用
   - 例: `ENVIRONMENT`, `GITHUB_OWNER`, `GITHUB_REPO`など

2. **コマンドライン引数**:
   - 各スクリプトへの直接的な入力として使用

3. **標準出力（stdout）**:
   - スクリプトからの戻り値として使用

4. **終了コード**:
   - エラー状態の伝達に使用

5. **JSON形式の戻り値**:
   - 複数の値を返す場合に使用

### エラーハンドリングフロー

エラーハンドリングは以下の方法で行われます：

1. **共通エラーハンドリング関数**:
   - `error-handling.sh`の`handle_error`関数を使用

2. **リトライロジック**:
   - `error-handling.sh`の`run_with_retry`関数を使用

3. **終了コードのチェック**:
   - 各スクリプトの実行後に`$?`をチェック

4. **エラートラップ**:
   - `setup_error_trap`関数を使用してエラー発生時の処理を設定

## 実装状況

以下の機能が外部スクリプトに分離されています：

1. ✅ GitHub認証部分の外出し (`cicd/scripts/auth/github-auth.sh`)
2. ✅ PR取得・コメント処理の外出し (`cicd/scripts/pr/get-merged-prs.sh`, `cicd/scripts/pr/comment-pr.sh`)
3. ✅ 変更検出処理の外出し (`cicd/scripts/detect/detect-changes.sh`, `cicd/scripts/detect/detect-glue.sh`, `cicd/scripts/detect/detect-cfn.sh`)
4. ✅ デプロイ処理の外出し (`cicd/scripts/deploy/deploy-glue.sh`, `cicd/scripts/deploy/deploy-cfn.sh`)
5. ✅ Parameter Store更新処理の外出し (`cicd/scripts/deploy/update-parameter-store.sh`)
6. ✅ 共通ユーティリティの整備 (`cicd/scripts/utils/logging.sh`, `cicd/scripts/utils/error-handling.sh`)
7. ✅ CodeBuildフェーズ処理の外出し (`cicd/scripts/codebuild/pre_build.sh`, `cicd/scripts/codebuild/build.sh`, `cicd/scripts/codebuild/post_build.sh`)
8. ✅ CodeBuild共通処理の整備 (`cicd/scripts/codebuild/common.sh`)

すべての計画されたリファクタリングが完了しています。

## CodeBuild用スクリプトの概要

### common.sh

CodeBuild用の共通処理を提供するスクリプトです。主な機能は以下の通りです：

- 共通ユーティリティ（logging.sh, error-handling.sh）の読み込み
- 共通環境変数の設定
- 共通関数の定義（check_required_env_vars, print_env_vars, is_dry_run, log_dry_run）
- スクリプト実行関数（run_script, run_script_get_output）

### pre_build.sh

pre_buildフェーズの処理を行うスクリプトです。主な機能は以下の通りです：

- 共通処理の読み込み
- 必須環境変数のチェック
- GitHub認証
- トークンのテスト
- ブランチのチェックアウト

### build.sh

buildフェーズの処理を行うスクリプトです。主な機能は以下の通りです：

- 共通処理の読み込み
- 最後にデプロイされたPR情報の取得
- マージされたPRの取得
- 変更の検出（Glue, CloudFormation）
- デプロイ処理の実行

### post_build.sh

post_buildフェーズの処理を行うスクリプトです。主な機能は以下の通りです：

- 共通処理の読み込み
- Parameter Storeの更新
- PRへのコメント追加
- 一時ファイルのクリーンアップ
