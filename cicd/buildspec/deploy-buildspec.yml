version: 0.2

env:
  variables:
    GITHUB_OWNER: "TIS-DSDev"
    GITHUB_REPO: "tis-dlpf-app"
    ENVIRONMENT: "dev" # Default environment, can be overridden
    FROM_PR: "0" # Default: from the beginning
    TO_PR: "0" # Default: to the latest
    DRY_RUN: "false" # Default: actual deployment
    DEBUG_MODE: "false" # Default: disable detailed logging (reduce log volume)
    SNS_NOTIFICATION_ENABLED: "true" # Default: enable SNS notifications (only for dev environment)
    SNS_TOPIC_ARN: "" # Will be set by CloudFormation

phases:
  install:
    runtime-versions:
      python: 3.9
    commands:
      - echo "Setting up cache directories..."
      - mkdir -p /tmp/cache
      - mkdir -p /root/.cache/pip
      - mkdir -p /var/cache/apt/archives
      - export PIP_CACHE_DIR=/root/.cache/pip
      - export APT_CACHE_DIR=/var/cache/apt/archives
      - echo "Installing dependencies..."
      - apt-get update -y
      - apt-get install -y jq tar gzip openssl curl docker.io python3-pip zip unzip
      - echo "Installing Python build tools..."
      - pip3 install --upgrade pip
      - pip3 install build wheel setuptools
      - echo "Installing GitHub CLI..."
      - curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
      - chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg
      - echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null
      - apt-get update -y
      - apt-get install -y gh
      - echo "Starting Docker service..."
      - nohup dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &> /tmp/dockerd.log &
      - echo "Waiting for Docker daemon to start..."
      - |
        for i in {1..30}; do
          if docker info >/dev/null 2>&1; then
            echo "Docker daemon started successfully after $i seconds"
            break
          fi
          if [ $i -eq 30 ]; then
            echo "Docker daemon failed to start within 30 seconds"
            cat /tmp/dockerd.log
            exit 1
          fi
          echo "Waiting for Docker daemon... ($i/30)"
          sleep 1
        done

  pre_build:
    commands:
      - echo "Running pre-build phase..."
      - chmod +x $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/pre_build.sh
      - echo "===== Running pre_build.sh with DEBUG_MODE=$DEBUG_MODE ====="
      - $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/pre_build.sh

  build:
    commands:
      - echo "Running build phase..."
      - chmod +x $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh
      - echo "===== Running build.sh with DEBUG_MODE=$DEBUG_MODE ====="
      - $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh

  post_build:
    commands:
      - echo "Running post-build phase..."
      - chmod +x $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/post_build.sh
      - echo "===== Running post_build.sh with DEBUG_MODE=$DEBUG_MODE ====="
      - $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/post_build.sh

cache:
  paths:
    - "/root/.cache/pip/**/*" # Python pip package cache
    - "/var/cache/apt/archives/**/*" # APT package cache
    - "/tmp/cache/**/*" # Custom cache directory
