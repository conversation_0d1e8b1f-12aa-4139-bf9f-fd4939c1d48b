AWSTemplateFormatVersion: "2010-09-09"
Description: "SNS notification topics for DLPF application"

Parameters:
  Environment:
    Type: String
    Description: "Environment (dev, stg, prd)"
    AllowedValues:
      - dev
      - stg
      - prd
    Default: dev
  NotificationEmail:
    Type: String
    Description: "Email address for error notifications (only used in dev environment)"
    Default: ""

Conditions:
  IsDevelopment: !Equals [!Ref Environment, "dev"]

Resources:
  # SNS Topic for error notifications (only for dev environment)
  ErrorNotificationTopic:
    Type: AWS::SNS::Topic
    Condition: IsDevelopment
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TopicName: !Sub "cdp-${Environment}-dlpf-deploy-error-notification"
      DisplayName: !Sub "DLPF ${Environment} Deployment Error Notifications"

  # SNS Subscription for email notifications (only for dev environment)
  ErrorNotificationSubscription:
    Type: AWS::SNS::Subscription
    Condition: IsDevelopment
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TopicArn: !Ref ErrorNotificationTopic
      Protocol: email
      Endpoint: !Ref NotificationEmail

Outputs:
  ErrorNotificationTopicArn:
    Condition: IsDevelopment
    Description: "The ARN of the SNS topic for error notifications"
    Value: !Ref ErrorNotificationTopic
    Export:
      Name: !Sub "dlpf-${Environment}-error-notification-topic-arn"
