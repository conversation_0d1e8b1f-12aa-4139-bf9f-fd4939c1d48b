AWSTemplateFormatVersion: "2010-09-09"
Description: "Parameter Store resources for DLPF application"

Parameters:
  Environment:
    Type: String
    Description: "Environment (dev, stg, prd)"
    AllowedValues:
      - dev
      - stg
      - prd
    Default: dev
  NotificationEmail:
    Type: String
    Description: "Email address for error notifications (only used in dev environment)"
    Default: ""

Conditions:
  IsDevelopment: !Equals [!Ref Environment, "dev"]

Resources:
  DeployBaselineTimeParameter:
    Type: AWS::SSM::Parameter
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      Name: !Sub "/dlpf/${Environment}/deploy-baseline-time"
      Type: String
      Value: '{"deploy_baseline_time": "1970-01-01T00:00:00Z"}'
      Description: "Deploy baseline time information - DO NOT MODIFY DIRECTLY"

  # SSM Parameter for notification email list (only for dev environment)
  NotificationEmailParameter:
    Type: AWS::SSM::Parameter
    Condition: IsDevelopment
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      Name: !Sub "/dlpf/${Environment}/notification-email"
      Type: String
      Value: !Ref NotificationEmail
      Description: !Sub "Email address for error notifications in ${Environment} environment - DO NOT MODIFY DIRECTLY"

Outputs:
  DeployBaselineTimeParameterName:
    Description: "Name of the deploy baseline time parameter"
    Value: !Ref DeployBaselineTimeParameter
    Export:
      Name: !Sub "dlpf-${Environment}-deploy-baseline-time-parameter"

  NotificationEmailParameterName:
    Condition: IsDevelopment
    Description: "Name of the notification email parameter"
    Value: !Ref NotificationEmailParameter
    Export:
      Name: !Sub "dlpf-${Environment}-notification-email-parameter"
