# AWS インフラリソース一覧

本ドキュメントは、デプロイシステムで使用するAWSリソースの一覧です。

## 1. 開発環境リソース

| リソース名 | リソースタイプ | 目的 | 設定値 |
|----------|--------------|------|-------|
| cdp-dev-dlpf-deploy | AWS::CodeBuild::Project | デプロイ処理を実行するCodeBuildプロジェクト | - コンピュートタイプ: BUILD_GENERAL1_MEDIUM<br>- イメージ: aws/codebuild/standard:6.0<br>- ソースバージョン: develop<br>- VPC設定: あり |
| cdp-dev-dlpf-deploy-role | AWS::IAM::Role | CodeBuildプロジェクトが使用するIAMロール | - 信頼関係: codebuild.amazonaws.com<br>- 権限: CloudFormationデプロイ（包括的）、全業務用AWSサービス、Glueジョブ更新、S3アクセス、SecretsManagerアクセス、SSMパラメータアクセス、SNS通知<br>- 2025-05-27更新: 10種類の業務用AWSサービス権限を追加 |
| /dlpf/dev/last-deployed-pr | AWS::SSM::Parameter | 前回デプロイしたPR情報を保存するParameter Store | - タイプ: String<br>- 値: JSON形式（PR番号とマージ日時） |
| /dlpf/dev/notification-email | AWS::SSM::Parameter | エラー通知用メールアドレスを保存するParameter Store | - タイプ: String<br>- 値: メールアドレス |
| cdp-dev-dlpf-deploy-error-notification | AWS::SNS::Topic | デプロイエラー通知用SNSトピック | - 表示名: DLPF dev Deployment Error Notifications<br>- プロトコル: email |
| github-app-credentials-dev | AWS::SecretsManager::Secret | GitHub App認証情報を保存するSecrets Manager | - タイプ: 文字列<br>- 値: JSON形式（app_id, installation_id, private_key） |

## 2. 検証環境リソース

| リソース名 | リソースタイプ | 目的 | 設定値 |
|----------|--------------|------|-------|
| cdp-stg-dlpf-deploy | AWS::CodeBuild::Project | デプロイ処理を実行するCodeBuildプロジェクト | - コンピュートタイプ: BUILD_GENERAL1_MEDIUM<br>- イメージ: aws/codebuild/standard:6.0<br>- ソースバージョン: release<br>- VPC設定: あり |
| cdp-stg-dlpf-deploy-role | AWS::IAM::Role | CodeBuildプロジェクトが使用するIAMロール | - 信頼関係: codebuild.amazonaws.com<br>- 権限: CloudFormationデプロイ（包括的）、全業務用AWSサービス、Glueジョブ更新、S3アクセス、SecretsManagerアクセス、SSMパラメータアクセス<br>- 2025-05-27更新: 10種類の業務用AWSサービス権限を追加 |
| /dlpf/stg/last-deployed-pr | AWS::SSM::Parameter | 前回デプロイしたPR情報を保存するParameter Store | - タイプ: String<br>- 値: JSON形式（PR番号とマージ日時） |
| github-app-credentials-stg | AWS::SecretsManager::Secret | GitHub App認証情報を保存するSecrets Manager | - タイプ: 文字列<br>- 値: JSON形式（app_id, installation_id, private_key） |

## 3. CloudFormationスタック

| スタック名 | 目的 | テンプレートファイル | パラメータファイル |
|-----------|------|-------------------|-----------------|
| cdp-dev-dlpf-deploy | 開発環境のデプロイシステムを作成 | cicd/cloudformation/templates/codebuild/cicd-codebuild-deploy.yml | cicd/cloudformation/environments/dev/parameters/codebuild/default.json |
| cdp-stg-dlpf-deploy | 検証環境のデプロイシステムを作成 | cicd/cloudformation/templates/codebuild/cicd-codebuild-deploy.yml | cicd/cloudformation/environments/stg/parameters/codebuild/default.json |

## 4. AWS CodeConnections リソース

| リソース名 | 目的 | ARN |
|-----------|------|-----|
| GitHub接続（開発環境） | GitHubリポジトリへの接続 | arn:aws:codeconnections:ap-northeast-1:886436956581:connection/2ddaaacd-fafd-447a-8f84-046a8b3ca5f6 |
| GitHub接続（検証環境） | GitHubリポジトリへの接続 | [要確認] 現在はダミー値（arn:aws:codeconnections:ap-northeast-1:869935081854:connection/2ddaaacd-fafd-447a-8f84-046a8b3ca5f6）が設定されています |

## 5. VPC設定

CodeBuildプロジェクトは、GitHub App認証のための固定IPアドレスを使用するために、VPC内で実行されます。

### 5.1 開発環境

| 設定項目 | 値 |
|---------|-----|
| VPC ID | vpc-0cfcb9b50e15ba692 |
| サブネット | subnet-00d13a611d7bdb00a,subnet-06d7cae16fde6f45c |
| セキュリティグループ | sg-00fee2146f098b0d9 |

### 5.2 検証環境

| 設定項目 | 値 |
|---------|-----|
| VPC ID | [要確認] 現在はダミー値（vpc-0cfcb9b50e15ba692）が設定されています |
| サブネット | [要確認] 現在はダミー値（subnet-00d13a611d7bdb00a,subnet-06d7cae16fde6f45c）が設定されています |
| セキュリティグループ | [要確認] 現在はダミー値（sg-00fee2146f098b0d9）が設定されています |

## 6. 削除済みリソース

以下のリソースは、新しいデプロイシステムへの移行に伴い削除されました：

| リソース名 | リソースタイプ | 目的 | 削除日 |
|----------|--------------|------|-------|
| dlpf-codebuild-runner | CloudFormationスタック | GitHub Actions用のself-hostedランナーを実行するCodeBuildプロジェクト | 2025-05-20 |
| cdp-dev-dlpf-github-codebuild-runner | AWS::CodeBuild::Project | GitHub Actions用のself-hostedランナー | 2025-05-20 |
| cdp-dev-dlpf-github-codebuild-runner-role | AWS::IAM::Role | GitHub Actions用のself-hostedランナーが使用するIAMロール | 2025-05-20 |
| dlpf-dev-codebuild-runner | CloudFormationスタック | GitHub Actions用のself-hostedランナーを実行するCodeBuildプロジェクト | 2025-05-20 |
| dlpf-app-github-actions-setup | CloudFormationスタック | GitHub Actions用のIAMロールとポリシーを設定 | 2025-05-20 |
| github-actions-dlpf-app-dev-role | AWS::IAM::Role | GitHub Actionsが使用するIAMロール | 2025-05-20 |
| github-actions-dlpf-app-dev-deploy-policy | AWS::IAM::ManagedPolicy | GitHub Actionsが使用するIAMポリシー | 2025-05-20 |

## 7. 環境別のリソース命名規則

AWSリソースの命名規則は以下の通りです：

| 環境 | プレフィックス | 例 |
|------|--------------|-----|
| 開発環境 | cdp-dev-dlpf- | cdp-dev-dlpf-deploy |
| 検証環境 | cdp-stg-dlpf- | cdp-stg-dlpf-deploy |
| 本番環境 | cdp-prd-dlpf- | cdp-prd-dlpf-deploy |

## 8. 注意事項

1. **本番環境**:
   - 本番環境（prd）は手動リリースのみを使用し、自動デプロイメカニズムは使用しません

2. **通知メールアドレス**:
   - 現在、通知メールアドレスは空の値（""）が設定されています
   - 保守チームのメールリストが決定次第、パラメータファイルを更新する必要があります
   - 誤送信防止のため、決定するまでは空のままにしておきます

3. **エラー通知機能**:
   - エラー通知機能は開発環境のみで有効です
   - 検証環境と本番環境ではエラー通知機能は無効です
   - エラー通知はSNSトピックを使用し、登録されたメールアドレスに送信されます

4. **CodeBuildコンピュートタイプ**:
   - Docker操作に十分なメモリを確保するため、BUILD_GENERAL1_MEDIUM（7GB RAM）を使用しています

## 8.1 Parameter Store（時刻ベース管理）

### 8.1.1 パラメータ一覧

| パラメータ名 | 説明 | 初期値 | 環境 |
|-------------|------|--------|------|
| `/dlpf/dev/deploy-baseline-time` | 開発環境のデプロイ基準時刻情報 | `{"deploy_baseline_time": "1970-01-01T00:00:00Z"}` | dev |
| `/dlpf/stg/deploy-baseline-time` | 検証環境のデプロイ基準時刻情報 | `{"deploy_baseline_time": "1970-01-01T00:00:00Z"}` | stg |
| `/dlpf/prd/deploy-baseline-time` | 本番環境のデプロイ基準時刻情報 | `{"deploy_baseline_time": "1970-01-01T00:00:00Z"}` | prd |
| `/dlpf/dev/notification-email` | 開発環境のエラー通知メールアドレス | CloudFormationパラメータから設定 | dev |

### 8.1.2 パラメータ構造の変更

**従来（PR番号ベース）**:
```json
{
  "number": 123,
  "merged_at": "2023-06-01T12:34:56Z"
}
```

**新方式（時刻ベース）**:
```json
{
  "deploy_baseline_time": "2025-05-27T10:30:00Z"
}
```

### 8.1.3 時刻ベース管理の利点

1. **PR追越し問題の解決**: PR番号の大小関係に依存しない
2. **直感的運用**: 「X時刻以降の変更をデプロイ」という明確な概念
3. **柔軟な運用**: 手動での時刻調整が可能

## 9. CodeBuild権限の詳細（2025-05-27更新）

### 9.1 権限更新の背景

2025-05-27にAccessDeniedエラーが発生し、業務用CloudFormationテンプレートのデプロイに必要な権限を包括的に追加しました。

### 9.2 追加された権限

#### 9.2.1 CloudFormation権限拡張

```
- cloudformation:GetTemplateSummary      # 主要エラー原因
- cloudformation:DescribeStackResources
- cloudformation:DescribeStackEvents
- cloudformation:GetTemplate
```

#### 9.2.2 業務用AWSサービス権限

| サービス | 追加権限 | 対象リソース | 対応テンプレート |
|---------|----------|-------------|-----------------|
| **IAM** | ロール管理の包括的権限 | `dlpf-*` パターン | 全テンプレート |
| **EventBridge** | ルール・ターゲット管理 | `rule/*` | `event-bridge/` |
| **Lambda** | 関数管理・権限設定 | `function:*` | `lambda/` |
| **Step Functions** | ステートマシン管理 | `stateMachine:*` | `step-functions/` |
| **Cognito** | ユーザープール管理 | `userpool/*` | `cognito/` |
| **Secrets Manager** | シークレット管理 | `dlpf*` パターン | `secrets-manager/` |
| **EC2** | セキュリティグループ管理 | 全リソース | `security-group/` |
| **Systems Manager** | パラメータストア拡張 | `dlpf/*` パターン | `system-manager/` |
| **Glue** | ジョブ・コネクター管理 | `job/*`, `connection/*` | `glue-job/`, `glue-connector/` |

### 9.3 対象業務用リソース

```
cloudformation/templates/
├── cognito/           # Amazon Cognito
├── event-bridge/      # Amazon EventBridge
├── glue-connector/    # AWS Glue Connector
├── glue-job/          # AWS Glue Job
├── lambda/            # AWS Lambda
├── secrets-manager/   # AWS Secrets Manager
├── security-group/    # EC2 Security Group
├── step-functions/    # AWS Step Functions
└── system-manager/    # AWS Systems Manager
```

### 9.4 権限修正の効果

- **AccessDeniedエラーの解決**: `cloudformation:GetTemplateSummary` 権限不足を解決
- **包括的対応**: 全業務用AWSサービスのCloudFormationデプロイが可能
- **運用安定化**: 権限不足による自動デプロイ失敗を防止
