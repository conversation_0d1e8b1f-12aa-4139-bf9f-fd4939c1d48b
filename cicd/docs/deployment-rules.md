# デプロイ実施ルール仕様書

## 概要

本書は、PRの変更内容に基づいて適切なデプロイを実行するためのルールを定義します。
デプロイシステムは、PRのマージ履歴を分析し、変更されたファイルに応じて適切なデプロイ処理を実行します。

## 1. Glueジョブ関連のデプロイルール

Glueジョブの変更は、その種類に応じて異なる処理が必要です。`job/build_and_deploy.sh`スクリプトでは、以下の3つの異なるデプロイ処理があります：

1. WHLファイルのアップロード（ソースコード変更時）
2. deps.zipファイルのアップロード（依存関係変更時）
3. Glueジョブメインスクリプト（glue_job_*.py）のアップロード（ジョブスクリプト変更時）

各デプロイ処理は個別にユーザー確認を求めるため、変更の種類に応じて適切な自動応答を設定する必要があります。

### 1.1 依存関係の変更時

- **変更対象**: `job/pyproject.toml`
- **デプロイ処理**: deps.zipファイルのビルドとアップロード
- **確認ポイント**: `job/dist/deps.zip`ファイルがS3にデプロイされたことを確認
- **自動応答**: deps.zipのアップロードに対して`yes`
- **実装方法**:
  ```bash
  if echo "$CHANGED_FILES" | grep -q "job/pyproject.toml"; then
    echo "依存関係の変更を検出しました。"
    HAS_GLUE_DEPENDENCY_CHANGES=true
  fi
  ```

### 1.2 ソースコード・設定ファイルの変更時

- **変更対象**: `job/source/**/*.{py,yaml,yml,sql,config}`（glue_job_*.pyを除く）
- **デプロイ処理**: WHLファイルのビルドとアップロード
- **確認ポイント**: `job/dist/glue_job-0.1.0-py3-none-any.whl`ファイルがS3にデプロイされたことを確認
- **自動応答**: WHLファイルのアップロードに対して`yes`
- **実装方法**:
  ```bash
  if echo "$CHANGED_FILES" | grep -q -E "job/source/.*\.(py|yaml|yml|sql|config)$" | grep -v -E "job/source/.*/glue_job_.*\.py$"; then
    echo "ソースコードまたは設定ファイルの変更を検出しました。"
    HAS_GLUE_SOURCE_CHANGES=true
  fi
  ```

### 1.3 特定のGlueジョブスクリプトの変更時

- **変更対象**: `job/source/**/glue_job_*.py`
- **デプロイ処理**: Glueジョブメインスクリプトのアップロード
- **確認ポイント**: 変更された`glue_job_*.py`ファイルがS3にデプロイされたことを確認
- **自動応答**: Glueジョブメインスクリプトのアップロードに対して`yes`
- **実装方法**:
  ```bash
  if echo "$CHANGED_FILES" | grep -q -E "job/source/.*/glue_job_.*\.py$"; then
    echo "Glueジョブスクリプトの変更を検出しました。"
    HAS_GLUE_JOB_SCRIPT_CHANGES=true
  fi
  ```

### 1.4 複合的な変更時の処理

変更の種類に応じて、適切な自動応答を設定する必要があります：

```bash
# デプロイ処理の実行
if [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] || [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ] || [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  echo "Glueジョブのデプロイを実行します"
  if [ "$DRY_RUN" = "true" ]; then
    echo "【DRY-RUN】Glueジョブのデプロイをスキップします"
    echo "【DRY-RUN】実行されるコマンド: cd job && chmod +x build_and_deploy_${ENVIRONMENT}.sh"
    if [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ]; then
      echo "【DRY-RUN】deps.zipをビルドしてアップロードします"
    fi
    if [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ]; then
      echo "【DRY-RUN】WHLファイルをビルドしてアップロードします"
    fi
    if [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
      echo "【DRY-RUN】Glueジョブメインスクリプトをアップロードします"
    fi
  else
    cd job
    chmod +x build_and_deploy_${ENVIRONMENT}.sh

    # 各種類の変更に応じて異なる自動応答を設定
    if [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] && [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
      # すべての変更がある場合、すべてにyesで応答
      yes | ./build_and_deploy_${ENVIRONMENT}.sh
    elif [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] && [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ]; then
      # 依存関係とソースコードの変更がある場合
      (echo "y"; echo "y"; echo "n") | ./build_and_deploy_${ENVIRONMENT}.sh
    elif [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
      # 依存関係とジョブスクリプトの変更がある場合
      (echo "n"; echo "y"; echo "y") | ./build_and_deploy_${ENVIRONMENT}.sh
    elif [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
      # ソースコードとジョブスクリプトの変更がある場合
      (echo "y"; echo "n"; echo "y") | ./build_and_deploy_${ENVIRONMENT}.sh
    elif [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ]; then
      # 依存関係の変更のみ
      (echo "n"; echo "y"; echo "n") | ./build_and_deploy_${ENVIRONMENT}.sh
    elif [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ]; then
      # ソースコードの変更のみ
      (echo "y"; echo "n"; echo "n") | ./build_and_deploy_${ENVIRONMENT}.sh
    elif [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
      # ジョブスクリプトの変更のみ
      (echo "n"; echo "n"; echo "y") | ./build_and_deploy_${ENVIRONMENT}.sh
    fi

    cd ..
  fi
else
  echo "No Glue changes detected, skipping Glue deployment."
fi
```

この実装により、変更の種類に応じて適切なデプロイ処理が実行され、不要なファイルのアップロードを避けることができます。

## 2. CloudFormation関連のデプロイルール

### 2.1 テンプレートファイルの変更時

- **変更対象**: `cloudformation/templates/**/*.yaml`
- **除外対象**: `cloudformation/templates/{api-gateway,secrets-manager}/**/*.yaml`
- **デプロイ処理**: 変更されたテンプレートに対して`./scripts/cfn_deploy.sh`を実行
- **実装方法**:
  ```bash
  # 除外対象のディレクトリパターン
  EXCLUDED_DIRS="api-gateway|secrets-manager"

  # CloudFormationテンプレートの変更を検出
  if echo "$CHANGED_FILES" | grep -q -E "cloudformation/templates/.*\.ya?ml$" | grep -v -E "cloudformation/templates/($EXCLUDED_DIRS)/"; then
    echo "CloudFormationテンプレートの変更を検出しました。"
    HAS_CFN_CHANGES=true

    # 変更されたテンプレートを特定
    CHANGED_TEMPLATES=$(echo "$CHANGED_FILES" | grep -E "cloudformation/templates/.*\.ya?ml$" | grep -v -E "cloudformation/templates/($EXCLUDED_DIRS)/")

    for template in $CHANGED_TEMPLATES; do
      resource_type=$(echo $template | sed -E 's|cloudformation/templates/([^/]+)/.*|\1|')
      template_name=$(basename $template .yaml)
      template_name=${template_name%.yml}  # .yml拡張子にも対応

      echo "テンプレートをデプロイします: $template (Type: $resource_type, Name: $template_name)"
      ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name
    done
  fi
  ```

### 2.2 開発環境パラメータファイルの変更時

- **変更対象**: `cloudformation/environments/${ENVIRONMENT}/parameters/*.json`
- **除外対象**: `api-gateway`, `secrets-manager`関連のテンプレート
- **デプロイ処理**: すべてのテンプレートに対して`./scripts/cfn_deploy.sh`を実行
- **実装方法**:
  ```bash
  # パラメータファイルの変更を検出
  if echo "$CHANGED_FILES" | grep -q -E "cloudformation/environments/${ENVIRONMENT}/parameters/[^/]+\.json$"; then
    echo "環境パラメータファイルの変更を検出しました。すべてのCloudFormationテンプレートをデプロイします。"
    HAS_CFN_CHANGES=true

    # 除外対象のディレクトリパターン
    EXCLUDED_DIRS="api-gateway|secrets-manager"

    # すべてのテンプレートをデプロイ
    for template_dir in cloudformation/templates/*; do
      if [ -d "$template_dir" ]; then
        resource_type=$(basename "$template_dir")

        # 除外対象のディレクトリをスキップ
        if echo "$resource_type" | grep -q -E "^($EXCLUDED_DIRS)$"; then
          echo "除外対象のディレクトリをスキップします: $resource_type"
          continue
        fi

        for template_file in "$template_dir"/*.yml "$template_dir"/*.yaml; do
          if [ -f "$template_file" ]; then
            template_name=$(basename "$template_file" .yml)
            template_name=${template_name%.yaml}  # .yaml拡張子にも対応

            echo "テンプレートをデプロイします: $template_file (Type: $resource_type, Name: $template_name)"
            ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name
          fi
        done
      fi
    done
  fi
  ```

### 2.3 開発環境パラメータファイルの変更時（サブディレクトリ）

- **変更対象**: `cloudformation/environments/${ENVIRONMENT}/parameters/**/*.json`
- **除外対象**: `api-gateway`, `secrets-manager`関連のテンプレート
- **デプロイ処理**: 該当するテンプレートに対して`./scripts/cfn_deploy.sh`を実行
- **実装方法**:
  ```bash
  # サブディレクトリのパラメータファイルの変更を検出
  if echo "$CHANGED_FILES" | grep -q -E "cloudformation/environments/${ENVIRONMENT}/parameters/.+/[^/]+\.json$"; then
    echo "サブディレクトリのパラメータファイルの変更を検出しました。該当するCloudFormationテンプレートをデプロイします。"
    HAS_CFN_CHANGES=true

    # 除外対象のディレクトリパターン
    EXCLUDED_DIRS="api-gateway|secrets-manager"

    # 変更されたパラメータファイルを特定
    CHANGED_PARAMS=$(echo "$CHANGED_FILES" | grep -E "cloudformation/environments/${ENVIRONMENT}/parameters/.+/[^/]+\.json$")

    for param_file in $CHANGED_PARAMS; do
      # パラメータファイルのディレクトリ名を取得（リソースタイプに対応）
      resource_type=$(echo $param_file | sed -E "s|cloudformation/environments/${ENVIRONMENT}/parameters/([^/]+)/.*|\1|")

      # 除外対象のディレクトリをスキップ
      if echo "$resource_type" | grep -q -E "^($EXCLUDED_DIRS)$"; then
        echo "除外対象のディレクトリをスキップします: $resource_type"
        continue
      fi

      # パラメータファイル名からテンプレート名を取得
      param_name=$(basename $param_file .json)

      # 対応するテンプレートファイルを検索
      template_file="cloudformation/templates/$resource_type/$param_name.yaml"
      if [ ! -f "$template_file" ]; then
        template_file="cloudformation/templates/$resource_type/$param_name.yml"
      fi

      if [ -f "$template_file" ]; then
        echo "テンプレートをデプロイします: $template_file (Type: $resource_type, Name: $param_name)"
        ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $param_name
      else
        echo "警告: パラメータファイル $param_file に対応するテンプレートが見つかりません"
      fi
    done
  fi
  ```

## 3. Lambda関数関連のデプロイルール

### 3.1 Lambda関数の変更時

- **変更対象**:
  - CloudFormationテンプレート: `cloudformation/templates/lambda/*.yaml`
  - Lambda関数のソースコード: `lambda/**/lambda_function.py`
- **デプロイ処理**:
  1. CloudFormationテンプレートのデプロイ（`./scripts/cfn_deploy.sh`を使用）
  2. Lambda関数のソースコードのZIP化と更新（`aws lambda update-function-code`を使用）

### 3.2 Lambda関数の自動デプロイ実装

- **変更検出スクリプト**: `cicd/scripts/detect/detect-lambda.sh`
- **検出ロジック**:
  1. CloudFormationテンプレートの変更: `cloudformation/templates/lambda/*.yaml`
  2. Lambda関数のソースコード変更: `lambda/**/lambda_function.py`
- **関数名抽出**:
  - テンプレートファイル名から関数名を抽出（例: `DLPF_RETURN_PARALLEL_NUM_ARRAY.yaml` → `DLPF_RETURN_PARALLEL_NUM_ARRAY`）
  - ソースコードパスから関数名を抽出（例: `lambda/DLPF_RETURN_PARALLEL_NUM_ARRAY/lambda_function.py` → `DLPF_RETURN_PARALLEL_NUM_ARRAY`）
- **デプロイ実行**:
  - 変更された関数ごとに `cloudformation/scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n lambda ${FUNCTION_NAME}` を実行
  - CloudFormationテンプレートのデプロイとLambda関数のコード更新を自動実行

## 4. 実装上の注意点

1. **変更検出の精度**:
   - `git diff --name-only`コマンドを使用して、PRの変更ファイルを正確に検出する
   - 正規表現パターンを使用して、対象ファイルを正確にマッチングする

2. **エラーハンドリング**:
   - デプロイ処理中のエラーを適切に検出し、ログに記録する
   - エラーが発生した場合でも、可能な限り処理を継続する

3. **DRY-RUNモード**:
   - DRY-RUNモードでは、実際のデプロイ処理を実行せず、実行されるコマンドをログに出力する
   - 本番環境への適用前に、DRY-RUNモードでデプロイ処理を検証する

4. **ログ出力**:
   - デプロイ処理の各ステップで、詳細なログを出力する
   - 変更検出、デプロイ対象の特定、デプロイ処理の実行結果を明確にログに記録する

## 4. 実装例

以下は、buildspec.ymlファイル内での実装例です：

```yaml
# PRの変更内容を分析し、デプロイ対象を特定
- |
  # 変更ファイルの取得
  CHANGED_FILES=$(git diff --name-only $BASE_SHA $MERGE_COMMIT_SHA || echo "")

  # Glue変更の検出
  if echo "$CHANGED_FILES" | grep -q -E "job/pyproject.toml|job/source/.*\.(py|yaml|yml|sql|config)$"; then
    HAS_GLUE_CHANGES=true
    echo "Glue関連の変更を検出しました"
  fi

  # CloudFormation変更の検出（除外対象を考慮）
  EXCLUDED_DIRS="api-gateway|secrets-manager"
  if echo "$CHANGED_FILES" | grep -q -E "cloudformation/" | grep -v -E "cloudformation/templates/($EXCLUDED_DIRS)/"; then
    HAS_CFN_CHANGES=true
    echo "CloudFormation関連の変更を検出しました"
  fi

  # デプロイ処理の実行
  if [ "$HAS_GLUE_CHANGES" = "true" ]; then
    echo "Glueジョブのデプロイを実行します"
    if [ "$DRY_RUN" = "true" ]; then
      echo "【DRY-RUN】Glueジョブのデプロイをスキップします"
      echo "【DRY-RUN】実行されるコマンド: cd job && chmod +x build_and_deploy_${ENVIRONMENT}.sh && ./build_and_deploy_${ENVIRONMENT}.sh"
    else
      cd job
      chmod +x build_and_deploy_${ENVIRONMENT}.sh
      ./build_and_deploy_${ENVIRONMENT}.sh
      cd ..
    fi
  fi

  if [ "$HAS_CFN_CHANGES" = "true" ]; then
    echo "CloudFormationテンプレートのデプロイを実行します"
    # （CloudFormationデプロイの詳細な実装）
  fi
```

## 5. 今後の拡張

1. **デプロイ対象の最適化**:
   - 変更内容に基づいて、最小限のデプロイを実行するロジックの改善
   - 依存関係を考慮したデプロイ順序の最適化

2. **並列デプロイ**:
   - 独立したリソースの並列デプロイによるデプロイ時間の短縮
   - 依存関係を考慮した並列デプロイの実装

3. **デプロイ履歴の管理**:
   - デプロイ履歴をDynamoDBなどに保存し、デプロイ状況を可視化
   - デプロイの成功/失敗、所要時間などの統計情報の収集

## 変更履歴

| 日付 | バージョン | 変更者 | 変更内容 |
|------|-----------|-------|---------|
| 2025-05-14 | 0.1 | TIS黄 | 初版作成 |
| 2025-05-14 | 0.2 | TIS黄 | Glueジョブ変更の種類に応じた処理の詳細を追加（HAS_GLUE_DEPENDENCY_CHANGES、HAS_GLUE_SOURCE_CHANGES、HAS_GLUE_JOB_SCRIPT_CHANGES） |
| 2025-05-16 | 0.3 | TIS黄 | Cognitoを除外対象から削除（自動デプロイ対象に含める） |
| 2025-05-23 | 0.4 | TIS黄 | Lambda関数のデプロイルールを追加 |
| 2025-05-26 | 0.5 | TIS黄 | Lambda関数の自動デプロイ実装を追加 |
