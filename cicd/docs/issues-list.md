# 自動デプロイシステム 課題一覧

## 📋 概要
自動デプロイシステムの運用において特定された課題と対応方針検討状況を管理します。

## 🚨 未解決課題

### 課題 #001: Deploy済PRのラベル管理
**優先度**: 高
**発見日**: 2025-05-27
**カテゴリ**: 運用管理・可視化

#### 問題の詳細
- Deploy済のPRに対して、Deploy完了を示すラベルが自動付与されていない
- 現在はコメント追加を想定していたが、ラベルの方が視認性が高い
- Deploy状況の把握が困難

#### 現在の仕様
- PRのDeploy状況はSSM Parameter Storeの`last-deployed-pr`で管理
- PRへの自動フィードバック機能は未実装

#### 影響範囲
- Deploy済PRの識別困難
- 運用チームの作業効率低下
- Deploy状況の可視化不足

#### 対応方針検討事項
1. **ラベル名称**: `deployed` (環境統一、ベースブランチで環境判別)
2. **付与タイミング**: Deploy完了時の自動付与
3. **権限設定**: CodeBuildからGitHub APIでのラベル操作権限
4. **実装方式**: GitHub CLI vs GitHub API直接呼び出し

#### 関連ファイル
- `cicd/scripts/codebuild/post_build.sh` (Deploy完了処理)
- `cicd/scripts/pr/get-merged-prs.sh` (PR情報取得)

---

### 課題 #002: PR追越し問題（Deploy漏れリスク）
**優先度**: 最高
**発見日**: 2025-05-27
**カテゴリ**: データ整合性・Deploy漏れ

#### 問題の詳細
- 大きい番号のPRが先にマージされた場合、小さい番号のPRがDeploy対象から除外される
- SSMに格納されるのは「Deploy済の最大PR番号」のため、それより小さい番号は次回Deploy対象にならない

#### 具体例
```
シナリオ:
1. PR #1100, #1101, #1102 が作成
2. PR #1102 が先にマージ → Deploy実行 → SSMに1102を格納
3. PR #1100, #1101 が後でマージ
4. 次回Deploy時: FROM_PR=1102 のため、#1100, #1101 はDeploy対象外
```

#### 現在の仕様
- SSM Parameter: `/dlpf/dev/last-deployed-pr` に最大PR番号を格納
- Deploy対象: `FROM_PR < PR番号 <= TO_PR` の範囲

#### 影響範囲
- **重大**: Deploy漏れによるデータ不整合
- **重大**: 業務影響の可能性
- **重大**: 手動Deploy作業の増加

#### 対応方針検討事項
**推奨方式: GitHubラベル活用方式**
1. **Deploy済ラベル自動付与**
   - ラベル名: `deployed` (環境統一)
   - 色: 緑色で視認性向上
   - 付与タイミング: Deploy完了時の自動付与
   - 環境判別: PRのベースブランチで自然に判別可能

2. **GitHub CLI連携**
   - Deploy対象PR取得: `gh pr list --state merged --label "!deployed" --base develop`
   - 配列管理不要でシンプルな実装
   - 人間による手動ラベル付与も可能（緊急時対応）

3. **実装方式**
   - CodeBuildからGitHub API経由でラベル操作
   - 権限設定: GitHub App or Personal Token
   - エラーハンドリング: ラベル付与失敗時の対応

**従来方式（参考）**
- SSM配列管理方式は複雑性が高いため非推奨

#### 関連ファイル
- `cicd/scripts/pr/get-merged-prs.sh` (PR取得・判定ロジック)
- `cicd/scripts/codebuild/post_build.sh` (Deploy完了処理)
- `cicd/scripts/codebuild/common.sh` (SSM操作)

---

### 課題 #003: 削除ファイルの複雑パターン対応
**優先度**: 中
**発見日**: 2025-05-27
**カテゴリ**: ファイル変更検出・Deploy精度

#### 問題の詳細
- PR間でのファイル削除・追加の複雑なパターンに対する適切な処理が未定義
- 現在の変更検出ロジックでは、削除ファイルの扱いが不十分

#### 具体例
```
複雑パターン例:
- PR #1100: fileA.py を削除
- PR #1101: fileA.py を新規追加（内容は異なる）
- PR #1102: fileA.py を修正

現在の検出ロジックでの問題:
- 削除→追加の連続処理での整合性
- 同一ファイル名での削除・追加判定
- Deploy順序による影響の考慮不足
```

#### 現在の仕様
- ファイル変更検出: `git diff --name-only` ベース
- 削除ファイル: `git diff --name-only --diff-filter=D`
- 追加ファイル: `git diff --name-only --diff-filter=A`

#### 影響範囲
- Deploy対象ファイルの誤判定
- 削除されたファイルの不適切なDeploy試行
- ファイル状態の不整合

#### 対応方針検討事項
1. **ファイル状態管理**
   - PR単位でのファイル状態追跡
   - 削除→追加パターンの特別処理

2. **検出ロジック改善**
   - `git diff --name-status` による詳細状態取得
   - ファイル操作種別（A/M/D/R）の適切な処理

3. **Deploy順序考慮**
   - PR順序とファイル操作の依存関係分析
   - 競合状態の検出と警告

4. **テストケース整備**
   - 複雑パターンのテストシナリオ作成
   - 自動テストでの検証強化

#### 関連ファイル
- `cicd/scripts/detect/detect-*.sh` (各種変更検出スクリプト)
- `cicd/scripts/pr/get-merged-prs.sh` (PR変更ファイル取得)
- `cicd/ut_test/test_*.sh` (テストスクリプト群)

---

### 課題 #004: 各リソースのDeploy失敗時メール警告通知機能
**優先度**: 最高
**発見日**: 2025-05-28
**カテゴリ**: 運用監視・エラー対応

#### 問題の詳細
- 業務リソースのDeploy失敗時に、担当者への自動メール通知機能が未実装
- Deploy失敗の早期発見・対応が困難
- 例: スタック 'dlpf-EB-ST005-FF01-001' のデプロイ失敗: 終了コード 254

#### 現在の仕様
- Deploy失敗時の通知機能なし
- 手動でのログ確認が必要

#### 影響範囲
- **重大**: Deploy失敗の発見遅延
- **重大**: 業務影響の拡大リスク
- **重大**: 運用負荷の増加

#### 対応方針検討事項
1. **通知システム**: インフラチーム提供のLambdaを活用
2. **通知対象**: 各担当者への個別通知
3. **通知内容**: 原因解析・対応指示を含む
4. **メーリングリスト**: 開発環境エラー通知用（申請中）

#### 担当者
- 二宮さん（設計・実装）

---

### 課題 #005: GitHub App一元化の可能性調査
**優先度**: 低
**発見日**: 2025-05-28
**カテゴリ**: アーキテクチャ・認証統合

#### 問題の詳細
- 現在GitHub AppとCodeConnectionsの両方を使用
- 認証方式の複雑性・管理コストの増加

#### 現在の仕様
- GitHub App: PR情報取得・ラベル操作
- CodeConnections: リポジトリクローン

#### 影響範囲
- 認証管理の複雑性
- 設定・運用コストの増加

#### 対応方針検討事項
1. **CodeConnectionsのみでの運用可能性**
2. **GitHub API機能の代替手段**
3. **移行時の影響範囲評価**

#### 担当者
- インフラチーム（調査・回答）

---

### 課題 #006: 通知用メーリングリスト申請状況
**優先度**: 中
**発見日**: 2025-05-28
**カテゴリ**: 運用環境・通知設定

#### 問題の詳細
- 開発環境のエラー・警告通知用メーリングリストが未設定
- 通知機能実装の前提条件が未整備

#### 現在の仕様
- メーリングリスト申請中
- 通知先アドレス未確定

#### 影響範囲
- エラー通知機能の実装遅延
- 運用開始の遅延

#### 対応方針検討事項
1. **申請進捗の確認**
2. **暫定的な通知先の検討**
3. **通知機能の段階的実装**

---

## 📊 課題管理ステータス

| 課題ID | タイトル | 優先度 | ステータス | 担当 | 期限 |
|--------|----------|--------|------------|------|------|
| #001 | Deploy済PRラベル管理 | 高 | 分析中 | - | - |
| #002 | PR追越し問題 | 最高 | 方針決定 | - | - |
| #003 | 削除ファイル複雑パターン | 中 | 分析中 | - | - |
| #004 | Deploy失敗時メール警告通知 | 最高 | 新規 | 二宮さん | - |
| #005 | GitHub App一元化調査 | 低 | 新規 | インフラチーム | - |
| #006 | 通知用メーリングリスト | 中 | 申請中 | - | - |

## 🔄 課題解決フロー

1. **課題特定** → 本リストに追加
2. **影響分析** → 優先度・影響範囲の評価
3. **対応方針検討** → 技術的解決策の検討
4. **方針決定** → 実装方式の確定
5. **ToDoList移行** → 具体的タスクとして`ToDoList.md`に移動
6. **実装・テスト** → 開発作業実施
7. **課題クローズ** → 解決済みセクションに移動

## ✅ 解決済み課題

### 課題 #000: CodeBuild権限不足
**解決日**: 2025-05-27
**対応内容**: 全業務用AWSサービスの包括的権限追加により、AccessDeniedエラーを解決

---

## 📝 更新履歴
- 2025-05-27: 初版作成、課題#001, #002を追加
- 2025-05-28: CICD定例会議対応、課題#004, #005, #006を追加
