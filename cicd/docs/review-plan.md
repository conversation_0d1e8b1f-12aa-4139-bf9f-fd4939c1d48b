# CICDレビュー手順プラン

以下フローでレビューを実施します。

```mermaid
flowchart TB
    A[ドキュメント確認] --> B[設計整合性チェック]
    B --> C[実装確認]
    C --> D[環境設定検証]
    D --> E[セキュリティ・権限]
    E --> F[エラーハンドリング]
    F --> G[パフォーマンス・コスト]
    G --> H[ドキュメントギャップ]
    H --> I[指摘事項まとめ]
```

## 1. ドキュメント確認
- [`deployment-system.md`](cicd/docs/deployment-system.md)
- [`operation-guide.md`](cicd/docs/operation-guide.md)
- [`ToDoList.md`](cicd/docs/ToDoList.md)

## 2. 設計整合性チェック
- 機能要件、異常系、パラメータ管理が設計書と合致しているか確認

## 3. 実装確認
- [`cicd-codebuild-deploy.yml`](cicd/cloudformation/templates/codebuild/cicd-codebuild-deploy.yml)
- [`deploy-buildspec.yml`](cicd/buildspec/deploy-buildspec.yml)
- [`deploy.sh`](cicd/scripts/deploy.sh)
- [`cfn-deploy.sh`](cicd/scripts/cfn-deploy.sh)

## 4. 環境設定検証
- dev 環境: [`default.json`](cicd/cloudformation/environments/dev/parameters/default.json)
- stg 環境: [`default.json`](cicd/cloudformation/environments/stg/parameters/default.json)

## 5. セキュリティ・権限
- GitHub App 認証情報／Secrets Manager 設定
- CodeBuild IAM ロール最小権限

## 6. エラーハンドリング
- デプロイ失敗時の再開手順
- ログ出力と通知設定
- Timeout・リトライ設定

## 7. パフォーマンス・コスト
- キャッシュ設定（CodeBuild キャッシュ）
- 並列実行・同時実行数
- ランナー稼働状況によるコスト最適化

## 8. ドキュメントギャップ
- 実装に対する説明不足や更新漏れ

## 9. 指摘事項まとめ
- 上記各観点で検出した不整合・懸念点を一覧化
