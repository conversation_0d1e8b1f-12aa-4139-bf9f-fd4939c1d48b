# デプロイシステム結合テスト

このディレクトリには、デプロイシステムの結合テストに関するドキュメントとスクリプトが含まれています。

## 1. 概要

結合テストでは、デプロイシステムの各コンポーネントが連携して正しく動作することを確認します。特に以下の点を検証します：

1. GitHub APIとの連携（PRマージ履歴の取得）
2. Parameter Storeとの連携（デプロイ状態の管理）
3. デプロイ処理の実行（CloudFormation、Glueジョブなど）
4. エラーハンドリングと通知機能

## 2. テストケース

### 2.1 PR指定の検証（代替案）

#### 目的
最新のPR番号をパラメータストアに登録する方法での動作確認を行います。

#### 前提条件
- GitHub CLIがインストールされていること
- AWS CLIがインストールされていること
- 適切なGitHubとAWSの認証情報が設定されていること

#### テスト手順
1. `test_pr_parameter_store.sh` スクリプトを実行
2. スクリプトは以下の処理を行います：
   - GitHub CLIを使用して最新のマージ済みPR番号を取得
   - 取得したPR番号をParameter Storeに登録
   - CodeBuildプロジェクトを起動してデプロイを実行
   - ビルドログを監視して処理状況を確認

#### 期待結果
- 最新のPR番号がParameter Storeに正しく登録されること
- CodeBuildプロジェクトが正常に実行されること
- デプロイ処理が正常に完了すること
- ビルドログに処理されたPR番号が表示されること

#### 検証方法
- AWS Management Consoleでパラメータストアの値を確認
- CodeBuildのビルドログで処理状況を確認
- S3バケットにデプロイされたファイルを確認

### 2.2 エラーハンドリングの検証

#### 目的
エラー発生時の処理と通知機能を検証します。

#### 前提条件
- GitHub CLIがインストールされていること
- AWS CLIがインストールされていること
- 適切なGitHubとAWSの認証情報が設定されていること

#### テスト手順
1. `test_error_handling.sh` スクリプトを実行
2. スクリプトは以下の処理を行います：
   - 意図的にエラーを発生させる条件を設定（存在しないPR番号など）
   - CodeBuildプロジェクトを起動
   - ビルドログを監視してエラー処理を確認

#### 期待結果
- エラーが適切に検出されること
- エラーメッセージがログに記録されること
- CodeBuildプロジェクトがエラー終了すること
- （実装後）エラー通知メールが送信されること

#### 検証方法
- CodeBuildのビルドログでエラーメッセージを確認
- CloudWatch Logsでエラーログを確認
- （実装後）通知メールの受信を確認

## 3. テスト環境

### 3.1 開発環境

- AWS アカウントID: 886436956581
- リージョン: ap-northeast-1
- CodeBuildプロジェクト: cdp-dev-dlpf-deploy
- Parameter Store: /dlpf/dev/last-deployed-pr

### 3.2 検証環境

- AWS アカウントID: 869935081854
- リージョン: ap-northeast-1
- CodeBuildプロジェクト: cdp-stg-dlpf-deploy
- Parameter Store: /dlpf/stg/last-deployed-pr

## 4. テスト実行方法

各テストスクリプトは以下のように実行します：

```bash
# 開発環境でのテスト実行
./test_pr_parameter_store.sh dev

# 検証環境でのテスト実行
./test_pr_parameter_store.sh stg
```

## 5. テスト結果の確認方法

### 5.1 Parameter Storeの確認

```bash
# 開発環境のパラメータ確認
aws ssm get-parameter --name "/dlpf/dev/last-deployed-pr" --query "Parameter.Value" --output text

# 検証環境のパラメータ確認
aws ssm get-parameter --name "/dlpf/stg/last-deployed-pr" --query "Parameter.Value" --output text
```

### 5.2 CodeBuildログの確認

AWS Management ConsoleのCodeBuildプロジェクトページでビルドログを確認します。
または、AWS CLIを使用して以下のように確認します：

```bash
# ビルドIDを指定してログを取得
aws codebuild batch-get-builds --ids <build-id> --query "builds[0].phases"
```

### 5.3 S3バケットの確認

```bash
# デプロイされたファイルの確認
aws s3 ls s3://cdp-dev-dlpf-<bucket-name>/ --recursive
```

## 6. トラブルシューティング

### 6.1 GitHub CLI認証エラー

GitHub CLIの認証情報が正しく設定されていない場合は、以下のコマンドで認証を行います：

```bash
gh auth login
```

### 6.2 AWS CLI認証エラー

AWS CLIの認証情報が正しく設定されていない場合は、以下のコマンドで認証を行います：

```bash
aws configure
```

### 6.3 CodeBuildプロジェクト起動エラー

CodeBuildプロジェクトの起動に失敗した場合は、IAM権限を確認してください。
