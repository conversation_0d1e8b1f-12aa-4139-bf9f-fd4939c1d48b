#!/bin/bash
#
# エラーハンドリングの検証テストスクリプト
# エラー発生時の処理と通知機能を検証します
#
# 使用方法:
#   ./test_error_handling.sh <環境> [オプション]
#
# 引数:
#   <環境>    - デプロイ環境（dev または stg）
#
# オプション:
#   -d, --dry-run  - 実際のデプロイを行わず、実行される操作をログとして出力
#   -v, --verbose  - 詳細なログを出力
#

set -e

# 色付きログ出力用の設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ログ出力関数
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}[DEBUG]${NC} $1"
  fi
}

# エラーハンドリング関数
handle_error() {
  local exit_code=$1
  local message=$2
  log_error "$message"
  exit $exit_code
}

# 終了時の処理
cleanup() {
  if [ $? -ne 0 ]; then
    log_error "テストが異常終了しました。"
  fi
}

trap cleanup EXIT

# オプションの解析
DRY_RUN=false
VERBOSE=false
POSITIONAL_ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -v|--verbose)
      VERBOSE=true
      shift
      ;;
    *)
      POSITIONAL_ARGS+=("$1")
      shift
      ;;
  esac
done

# 位置引数の復元
set -- "${POSITIONAL_ARGS[@]}"

# 引数の検証
if [ $# -lt 1 ]; then
  handle_error 1 "環境を指定してください（dev または stg）\n使用方法: $0 <環境> [オプション]"
fi

ENVIRONMENT=$1

# 環境の検証
if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "stg" ]; then
  handle_error 1 "環境は dev または stg を指定してください"
fi

# 環境に応じた設定
if [ "$ENVIRONMENT" = "dev" ]; then
  ACCOUNT_ID="************"
  BRANCH="develop"
else
  ACCOUNT_ID="************"
  BRANCH="release"
fi

# CodeBuildプロジェクト名
PROJECT_NAME="cdp-${ENVIRONMENT}-dlpf-deploy"

# パラメータストア名
PARAMETER_NAME="/dlpf/${ENVIRONMENT}/last-deployed-pr"

# 前提条件のチェック
log_info "前提条件をチェックしています..."

# AWS CLI のチェック
if ! command -v aws &> /dev/null; then
  handle_error 1 "AWS CLI が見つかりません。インストールしてください。"
fi

# AWS 認証のチェック
if ! aws sts get-caller-identity &> /dev/null; then
  log_warn "AWS CLI の認証が必要です。以下のコマンドで認証してください："
  log_warn "  aws configure"
  handle_error 1 "AWS 認証が設定されていません。"
fi

log_info "前提条件のチェックが完了しました。"

# 意図的にエラーを発生させるための設定
log_info "エラーテスト用の設定を行っています..."

# 存在しないPR番号を指定
INVALID_PR_NUMBER=999999

log_info "存在しないPR番号を指定: ${INVALID_PR_NUMBER}"

# CodeBuildプロジェクトを起動
log_info "CodeBuildプロジェクトを起動します..."

# 環境変数のオーバーライド
ENV_VARS="[{\"name\":\"ENVIRONMENT\",\"value\":\"${ENVIRONMENT}\",\"type\":\"PLAINTEXT\"}"
ENV_VARS="${ENV_VARS},{\"name\":\"FROM_PR\",\"value\":\"${INVALID_PR_NUMBER}\",\"type\":\"PLAINTEXT\"}"
ENV_VARS="${ENV_VARS},{\"name\":\"TO_PR\",\"value\":\"${INVALID_PR_NUMBER}\",\"type\":\"PLAINTEXT\"}"

# DRY-RUNモードの設定
if [ "$DRY_RUN" = true ]; then
  ENV_VARS="${ENV_VARS},{\"name\":\"DRY_RUN\",\"value\":\"true\",\"type\":\"PLAINTEXT\"}"
  log_info "【DRY-RUNモード】実際のデプロイは行われません"
else
  ENV_VARS="${ENV_VARS},{\"name\":\"DRY_RUN\",\"value\":\"false\",\"type\":\"PLAINTEXT\"}"
fi

# デバッグモードの設定
if [ "$VERBOSE" = true ]; then
  ENV_VARS="${ENV_VARS},{\"name\":\"DEBUG_MODE\",\"value\":\"true\",\"type\":\"PLAINTEXT\"}"
  log_info "【デバッグモード】詳細なログが出力されます"
else
  ENV_VARS="${ENV_VARS},{\"name\":\"DEBUG_MODE\",\"value\":\"false\",\"type\":\"PLAINTEXT\"}"
fi

ENV_VARS="${ENV_VARS}]"

# CodeBuildプロジェクトの起動
if [ "$DRY_RUN" = true ]; then
  log_info "【DRY-RUN】CodeBuildプロジェクトを起動します: ${PROJECT_NAME}"
  log_info "【DRY-RUN】環境変数: ${ENV_VARS}"
  BUILD_ID="dry-run-build-id"
else
  log_info "CodeBuildプロジェクトを起動しています..."
  BUILD_ID=$(aws codebuild start-build \
    --project-name ${PROJECT_NAME} \
    --environment-variables-override "${ENV_VARS}" \
    --query "build.id" \
    --output text) || handle_error 2 "CodeBuildプロジェクトの起動に失敗しました"
  
  log_info "CodeBuildプロジェクトを起動しました。ビルドID: ${BUILD_ID}"
fi

# ビルドの状態を監視
if [ "$DRY_RUN" = true ]; then
  log_info "【DRY-RUN】ビルドの監視をシミュレートします"
  sleep 2
  log_info "【DRY-RUN】ビルド状態: IN_PROGRESS"
  sleep 2
  log_info "【DRY-RUN】ビルド状態: FAILED"
  log_info "【DRY-RUN】エラーメッセージ: 指定されたPR番号 ${INVALID_PR_NUMBER} が見つかりません"
else
  log_info "ビルドの状態を監視しています..."
  
  EXPECTED_FAILURE=false
  
  while true; do
    BUILD_STATUS=$(aws codebuild batch-get-builds \
      --ids ${BUILD_ID} \
      --query "builds[0].buildStatus" \
      --output text) || handle_error 3 "ビルド状態の取得に失敗しました"
    
    log_info "ビルド状態: ${BUILD_STATUS}"
    
    if [ "$BUILD_STATUS" = "SUCCEEDED" ]; then
      log_warn "ビルドが正常に完了しました。エラーが発生しませんでした。"
      log_warn "エラーハンドリングのテストは失敗しました。"
      break
    elif [ "$BUILD_STATUS" = "FAILED" ] || [ "$BUILD_STATUS" = "FAULT" ] || [ "$BUILD_STATUS" = "STOPPED" ] || [ "$BUILD_STATUS" = "TIMED_OUT" ]; then
      log_info "ビルドが失敗しました。これは期待された動作です。"
      EXPECTED_FAILURE=true
      break
    fi
    
    log_info "ビルドの完了を待機しています..."
    sleep 30
  done
  
  # ビルドログの取得
  if [ "$EXPECTED_FAILURE" = true ]; then
    log_info "ビルドログを確認しています..."
    
    # CloudWatch Logsからログを取得する方法もありますが、
    # ここではAWSコンソールでの確認を促すメッセージを表示します
    log_info "ビルドログはAWSコンソールで確認できます:"
    log_info "https://console.aws.amazon.com/codesuite/codebuild/projects/${PROJECT_NAME}/build/${BUILD_ID}/log"
    
    log_info "エラーハンドリングのテストは成功しました。"
  fi
fi

# テスト結果の出力
log_info "テスト結果:"
log_info "- 環境: ${ENVIRONMENT}"
log_info "- 無効なPR番号: ${INVALID_PR_NUMBER}"
log_info "- CodeBuildプロジェクト: ${PROJECT_NAME}"
log_info "- ビルドID: ${BUILD_ID}"

if [ "$DRY_RUN" = false ]; then
  log_info "ビルドの詳細はAWSコンソールで確認できます:"
  log_info "https://console.aws.amazon.com/codesuite/codebuild/projects/${PROJECT_NAME}/build/${BUILD_ID}/log"
  
  if [ "$EXPECTED_FAILURE" = true ]; then
    log_info "エラーハンドリングのテストは成功しました。"
  else
    log_warn "エラーハンドリングのテストは失敗しました。"
  fi
fi

log_info "テストが完了しました。"
exit 0
