#!/bin/bash
#
# PR指定の検証（代替案）テストスクリプト
# 最新のPR番号をパラメータストアに登録する方法での動作確認を行います
#
# 使用方法:
#   ./test_pr_parameter_store.sh <環境> [オプション]
#
# 引数:
#   <環境>    - デプロイ環境（dev または stg）
#
# オプション:
#   -d, --dry-run  - 実際のデプロイを行わず、実行される操作をログとして出力
#   -v, --verbose  - 詳細なログを出力
#

set -e

# 色付きログ出力用の設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ログ出力関数
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
  if [ "$VERBOSE" = true ]; then
    echo -e "${BLUE}[DEBUG]${NC} $1"
  fi
}

# エラーハンドリング関数
handle_error() {
  local exit_code=$1
  local message=$2
  log_error "$message"
  exit $exit_code
}

# 終了時の処理
cleanup() {
  if [ $? -ne 0 ]; then
    log_error "テストが異常終了しました。"
  fi
}

trap cleanup EXIT

# オプションの解析
DRY_RUN=false
VERBOSE=false
POSITIONAL_ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    -v|--verbose)
      VERBOSE=true
      shift
      ;;
    *)
      POSITIONAL_ARGS+=("$1")
      shift
      ;;
  esac
done

# 位置引数の復元
set -- "${POSITIONAL_ARGS[@]}"

# 引数の検証
if [ $# -lt 1 ]; then
  handle_error 1 "環境を指定してください（dev または stg）\n使用方法: $0 <環境> [オプション]"
fi

ENVIRONMENT=$1

# 環境の検証
if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "stg" ]; then
  handle_error 1 "環境は dev または stg を指定してください"
fi

# 環境に応じた設定
if [ "$ENVIRONMENT" = "dev" ]; then
  ACCOUNT_ID="************"
  BRANCH="develop"
else
  ACCOUNT_ID="************"
  BRANCH="release"
fi

# CodeBuildプロジェクト名
PROJECT_NAME="cdp-${ENVIRONMENT}-dlpf-deploy"

# パラメータストア名
PARAMETER_NAME="/dlpf/${ENVIRONMENT}/deploy-baseline-time"

# GitHub リポジトリ情報
GITHUB_OWNER="TIS-DSDev"
GITHUB_REPO="tis-dlpf-app"

# 前提条件のチェック
log_info "前提条件をチェックしています..."

# GitHub CLI のチェック
if ! command -v gh &> /dev/null; then
  handle_error 1 "GitHub CLI (gh) が見つかりません。インストールしてください。"
fi

# AWS CLI のチェック
if ! command -v aws &> /dev/null; then
  handle_error 1 "AWS CLI が見つかりません。インストールしてください。"
fi

# GitHub 認証のチェック
if ! gh auth status &> /dev/null; then
  log_warn "GitHub CLI の認証が必要です。以下のコマンドで認証してください："
  log_warn "  gh auth login"
  handle_error 1 "GitHub 認証が設定されていません。"
fi

# AWS 認証のチェック
if ! aws sts get-caller-identity &> /dev/null; then
  log_warn "AWS CLI の認証が必要です。以下のコマンドで認証してください："
  log_warn "  aws configure"
  handle_error 1 "AWS 認証が設定されていません。"
fi

log_info "前提条件のチェックが完了しました。"

# 最新のマージ済みPR番号を取得
log_info "最新のマージ済みPR番号を取得しています..."

# GitHub APIを使用して最新のマージ済みPRを取得
LATEST_PR=$(gh pr list --repo "${GITHUB_OWNER}/${GITHUB_REPO}" --state merged --base ${BRANCH} --limit 1 --json number,mergedAt,title --jq '.[0]')

if [ -z "$LATEST_PR" ]; then
  handle_error 1 "マージ済みのPRが見つかりません。"
fi

PR_NUMBER=$(echo $LATEST_PR | jq -r '.number')
MERGED_AT=$(echo $LATEST_PR | jq -r '.mergedAt')
PR_TITLE=$(echo $LATEST_PR | jq -r '.title')

log_info "最新のマージ済みPR: #${PR_NUMBER} (${PR_TITLE})"
log_info "マージ日時: ${MERGED_AT}"

# 現在のパラメータストアの値を取得
log_info "現在のパラメータストアの値を取得しています..."

CURRENT_PARAM=$(aws ssm get-parameter --name "${PARAMETER_NAME}" --query "Parameter.Value" --output text 2>/dev/null || echo "{}")
CURRENT_BASELINE_TIME=$(echo $CURRENT_PARAM | jq -r '.deploy_baseline_time // "1970-01-01T00:00:00Z"')

log_info "現在のパラメータストアの値: ${CURRENT_BASELINE_TIME}"

# 新しいパラメータ値を作成（現在時刻をデプロイ基準時刻として設定）
CURRENT_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
NEW_PARAM="{\"deploy_baseline_time\": \"${CURRENT_TIME}\"}"

log_info "新しいパラメータ値: ${NEW_PARAM}"

# パラメータストアを更新
if [ "$DRY_RUN" = true ]; then
  log_info "【DRY-RUN】パラメータストアを更新します: ${PARAMETER_NAME} = ${NEW_PARAM}"
else
  log_info "パラメータストアを更新しています..."
  aws ssm put-parameter --name "${PARAMETER_NAME}" --value "${NEW_PARAM}" --type "String" --overwrite

  # 更新後の値を確認
  UPDATED_PARAM=$(aws ssm get-parameter --name "${PARAMETER_NAME}" --query "Parameter.Value" --output text)
  UPDATED_BASELINE_TIME=$(echo $UPDATED_PARAM | jq -r '.deploy_baseline_time // "1970-01-01T00:00:00Z"')

  if [ "$UPDATED_BASELINE_TIME" != "$CURRENT_TIME" ]; then
    handle_error 1 "パラメータストアの更新に失敗しました。"
  fi

  log_info "パラメータストアを更新しました: ${UPDATED_BASELINE_TIME}"
fi

# CodeBuildプロジェクトを起動
log_info "CodeBuildプロジェクトを起動します..."

# 環境変数のオーバーライド
ENV_VARS="[{\"name\":\"ENVIRONMENT\",\"value\":\"${ENVIRONMENT}\",\"type\":\"PLAINTEXT\"}"
ENV_VARS="${ENV_VARS},{\"name\":\"deploy_baseline_time\",\"value\":\"${CURRENT_TIME}\",\"type\":\"PLAINTEXT\"}"

# DRY-RUNモードの設定
if [ "$DRY_RUN" = true ]; then
  ENV_VARS="${ENV_VARS},{\"name\":\"DRY_RUN\",\"value\":\"true\",\"type\":\"PLAINTEXT\"}"
  log_info "【DRY-RUNモード】実際のデプロイは行われません"
else
  ENV_VARS="${ENV_VARS},{\"name\":\"DRY_RUN\",\"value\":\"false\",\"type\":\"PLAINTEXT\"}"
fi

# デバッグモードの設定
if [ "$VERBOSE" = true ]; then
  ENV_VARS="${ENV_VARS},{\"name\":\"DEBUG_MODE\",\"value\":\"true\",\"type\":\"PLAINTEXT\"}"
  log_info "【デバッグモード】詳細なログが出力されます"
else
  ENV_VARS="${ENV_VARS},{\"name\":\"DEBUG_MODE\",\"value\":\"false\",\"type\":\"PLAINTEXT\"}"
fi

ENV_VARS="${ENV_VARS}]"

# CodeBuildプロジェクトの起動
if [ "$DRY_RUN" = true ]; then
  log_info "【DRY-RUN】CodeBuildプロジェクトを起動します: ${PROJECT_NAME}"
  log_info "【DRY-RUN】環境変数: ${ENV_VARS}"
  BUILD_ID="dry-run-build-id"
else
  log_info "CodeBuildプロジェクトを起動しています..."
  BUILD_ID=$(aws codebuild start-build \
    --project-name ${PROJECT_NAME} \
    --environment-variables-override "${ENV_VARS}" \
    --query "build.id" \
    --output text) || handle_error 2 "CodeBuildプロジェクトの起動に失敗しました"

  log_info "CodeBuildプロジェクトを起動しました。ビルドID: ${BUILD_ID}"
fi

# ビルドの状態を監視
if [ "$DRY_RUN" = true ]; then
  log_info "【DRY-RUN】ビルドの監視をシミュレートします"
  sleep 2
  log_info "【DRY-RUN】ビルド状態: IN_PROGRESS"
  sleep 2
  log_info "【DRY-RUN】ビルド状態: SUCCEEDED"
else
  log_info "ビルドの状態を監視しています..."

  while true; do
    BUILD_STATUS=$(aws codebuild batch-get-builds \
      --ids ${BUILD_ID} \
      --query "builds[0].buildStatus" \
      --output text) || handle_error 3 "ビルド状態の取得に失敗しました"

    log_info "ビルド状態: ${BUILD_STATUS}"

    if [ "$BUILD_STATUS" = "SUCCEEDED" ]; then
      log_info "ビルドが正常に完了しました。"
      break
    elif [ "$BUILD_STATUS" = "FAILED" ] || [ "$BUILD_STATUS" = "FAULT" ] || [ "$BUILD_STATUS" = "STOPPED" ] || [ "$BUILD_STATUS" = "TIMED_OUT" ]; then
      handle_error 4 "ビルドが失敗しました。詳細はAWSコンソールで確認してください。\nhttps://console.aws.amazon.com/codesuite/codebuild/projects/${PROJECT_NAME}/build/${BUILD_ID}/log"
    fi

    log_info "ビルドの完了を待機しています..."
    sleep 30
  done
fi

# テスト結果の出力
log_info "テスト結果:"
log_info "- 環境: ${ENVIRONMENT}"
log_info "- デプロイ基準時刻: ${CURRENT_TIME}"
log_info "- パラメータストア: ${PARAMETER_NAME}"
log_info "- CodeBuildプロジェクト: ${PROJECT_NAME}"
log_info "- ビルドID: ${BUILD_ID}"

if [ "$DRY_RUN" = false ]; then
  log_info "ビルドの詳細はAWSコンソールで確認できます:"
  log_info "https://console.aws.amazon.com/codesuite/codebuild/projects/${PROJECT_NAME}/build/${BUILD_ID}/log"
fi

log_info "テストが正常に完了しました。"
exit 0
