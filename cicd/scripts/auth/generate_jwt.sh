#!/bin/bash
set -e

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# パラメータを取得
APP_ID="$1"
PRIVATE_KEY="$2"
NOW="$3"
EXP="$4"

# 秘密鍵ファイルを作成し、ヘッダーを追加
echo "-----BEGIN RSA PRIVATE KEY-----" > private-key.pem

# 空白文字を改行コードに置き換えて処理
# 1. ヘッダーとフッターを削除
# 2. 空白文字を改行に変換
# 3. 空行を削除
echo "$PRIVATE_KEY" | \
  sed 's/-----BEGIN RSA PRIVATE KEY-----//g' | \
  sed 's/-----END RSA PRIVATE KEY-----//g' | \
  sed 's/ /\n/g' | \
  grep -v '^$' >> private-key.pem

# デバッグ出力
log_debug "Private key content after processing (line count): $(wc -l < private-key.pem) lines"

# 秘密鍵ファイルにフッターを追加
echo "-----END RSA PRIVATE KEY-----" >> private-key.pem

chmod 600 private-key.pem

# デバッグ出力：秘密鍵ファイルの情報
log_debug "Private key file exists: $(ls -la private-key.pem)"
log_debug "Private key file total size: $(wc -c < private-key.pem) bytes"
log_debug "Private key file structure check:"
log_debug "  - Header exists: $(grep -c '^-----BEGIN RSA PRIVATE KEY-----$' private-key.pem) (should be 1)"
log_debug "  - Footer exists: $(grep -c '^-----END RSA PRIVATE KEY-----$' private-key.pem) (should be 1)"
log_debug "Private key file content (first 100 chars):"
log_debug "$(head -c 100 private-key.pem)"

# JWTヘッダーとペイロードを作成
HEADER='{"alg":"RS256","typ":"JWT"}'
PAYLOAD="{\"iat\":$NOW,\"exp\":$EXP,\"iss\":\"$APP_ID\"}"

# デバッグ出力
log_debug "JWT Header: $HEADER"
log_debug "JWT Payload: $PAYLOAD"
log_debug "APP_ID: $APP_ID, NOW: $NOW, EXP: $EXP"

# ヘッダーとペイロードをBase64エンコード
HEADER_B64=$(echo -n "$HEADER" | base64 | tr -d '=' | tr '+/' '-_' | tr -d '\n')
PAYLOAD_B64=$(echo -n "$PAYLOAD" | base64 | tr -d '=' | tr '+/' '-_' | tr -d '\n')

# 署名対象の入力を作成
SIGNATURE_INPUT="$HEADER_B64.$PAYLOAD_B64"

# 署名対象の入力を一時ファイルに保存
echo -n "$SIGNATURE_INPUT" > signature_input.txt

# 署名対象のデバッグ出力
log_debug "Header Base64: $HEADER_B64"
log_debug "Payload Base64: $PAYLOAD_B64"
log_debug "Signature input: $SIGNATURE_INPUT"
log_debug "Signature input file exists: $(ls -la signature_input.txt)"

# 秘密鍵で署名を生成
log_debug "Signing with openssl..."
SIGNATURE=$(openssl dgst -sha256 -sign private-key.pem signature_input.txt | openssl base64 | tr -d '=' | tr '+/' '-_' | tr -d '\n')
log_debug "Signature length: ${#SIGNATURE}"

# JWTトークンを作成
JWT="$SIGNATURE_INPUT.$SIGNATURE"

# 一時ファイルを削除
rm -f private-key.pem signature_input.txt

echo "$JWT"
