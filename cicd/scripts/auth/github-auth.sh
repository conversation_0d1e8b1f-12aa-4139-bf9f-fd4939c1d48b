#!/bin/bash
set -e

# GitHub App認証を行い、インストールアクセストークンを取得するスクリプト
# 使用方法: ./github-auth.sh <シークレット名>
# 例: ./github-auth.sh github-app-credentials-dev

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
if [ -z "$1" ]; then
  log_error "Secret name is required"
  exit 1
fi

SECRET_NAME="$1"
log_debug "Fetching GitHub App credentials from Secrets Manager: ${SECRET_NAME}"

# シークレットを取得
SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "${SECRET_NAME}" --query SecretString --output text)
if [ $? -ne 0 ] || [ -z "$SECRET_JSON" ]; then
  log_error "Failed to retrieve secret ${SECRET_NAME}"
  exit 1
fi

# 値を抽出
APP_ID=$(echo "$SECRET_JSON" | jq -r '.app_id')
INSTALLATION_ID=$(echo "$SECRET_JSON" | jq -r '.installation_id')
PRIVATE_KEY=$(echo "$SECRET_JSON" | jq -r '.private_key')

# JWT生成
export TZ=UTC
NOW=$(date +%s)
EXP=$((NOW + 540))
JWT=$($(dirname "$0")/generate_jwt.sh "$APP_ID" "$PRIVATE_KEY" "$NOW" "$EXP")

# トークン取得
MAX_RETRIES=3
RETRY_COUNT=0
INSTALLATION_TOKEN=""

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ -z "$INSTALLATION_TOKEN" -o "$INSTALLATION_TOKEN" = "null" ]; do
  if [ $RETRY_COUNT -gt 0 ]; then
    echo "Retry attempt $RETRY_COUNT of $MAX_RETRIES..."
    sleep 2
  fi

  log_debug "Requesting installation token (attempt $((RETRY_COUNT+1)))"

  # APIリクエストを実行し、レスポンスを保存
  API_RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Authorization: Bearer ${JWT}" \
    -H "Accept: application/vnd.github+json" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    -H "Connection: close" \
    "https://api.github.com/app/installations/${INSTALLATION_ID}/access_tokens")

  # HTTPステータスコードを抽出
  HTTP_STATUS=$(echo "$API_RESPONSE" | tail -n1)
  # JSONレスポンスを抽出（最後の行を除く）
  INSTALLATION_TOKEN_RESPONSE=$(echo "$API_RESPONSE" | sed '$d')

  log_debug "HTTP Status: $HTTP_STATUS"

  if [ "$HTTP_STATUS" -ne 201 ]; then
    log_debug "API Error: $INSTALLATION_TOKEN_RESPONSE"
    INSTALLATION_TOKEN_RESPONSE='{"error":"Failed to get token", "status":"'$HTTP_STATUS'"}'
  fi

  INSTALLATION_TOKEN=$(echo "$INSTALLATION_TOKEN_RESPONSE" | jq -r '.token')
  log_debug "Token obtained: ${INSTALLATION_TOKEN:0:4}...${INSTALLATION_TOKEN: -4}"
  RETRY_COUNT=$((RETRY_COUNT + 1))
done

if [ -z "$INSTALLATION_TOKEN" ] || [ "$INSTALLATION_TOKEN" = "null" ]; then
  log_error "Failed to get Installation Access Token after $MAX_RETRIES attempts."
  exit 1
fi

# 結果を出力
echo "$INSTALLATION_TOKEN"
