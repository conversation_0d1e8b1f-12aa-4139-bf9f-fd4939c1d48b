#!/bin/bash
set -e

# 共通処理の読み込み
source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh

# 前のフェーズからの環境変数を読み込む
if [ -f "$CODEBUILD_SRC_DIR/env_vars.sh" ]; then
  source "$CODEBUILD_SRC_DIR/env_vars.sh"
else
  log_warn "env_vars.sh not found. Pre-build phase may have failed."
fi

# 前のフェーズからの環境変数を確認
if [ -z "$INSTALLATION_TOKEN" ]; then
  log_error "INSTALLATION_TOKEN is not set. Pre-build phase may have failed."
  exit 1
fi

# GitHub認証設定（gh CLI用）
export GITHUB_TOKEN="$INSTALLATION_TOKEN"
log_info "GITHUB_TOKEN configured for gh CLI (length: ${#GITHUB_TOKEN})"

# 最後にデプロイされた基準時刻の取得
DEPLOY_BASELINE_TIME_PARAM="/dlpf/${ENVIRONMENT}/deploy-baseline-time"
log_info "Getting deploy baseline time from Parameter Store: ${DEPLOY_BASELINE_TIME_PARAM}"
LAST_DEPLOYED=$(aws ssm get-parameter --name "${DEPLOY_BASELINE_TIME_PARAM}" --query "Parameter.Value" --output text 2>/dev/null)

# Parameter Storeから値を取得できたか確認
if [ $? -ne 0 ] || [ -z "$LAST_DEPLOYED" ]; then
  log_error "Parameter Storeから値を取得できませんでした: ${DEPLOY_BASELINE_TIME_PARAM}"
  exit 1
fi

# JSONから必要な値を抽出
deploy_baseline_time=$(echo $LAST_DEPLOYED | jq -r '.deploy_baseline_time // "1970-01-01T00:00:00Z"')

log_info "Deploy baseline time: ${deploy_baseline_time}"

# 初回デプロイ（1970-01-01T00:00:00Z）の場合は、すべてのデプロイ対象をTRUEにする
if [ "${deploy_baseline_time}" = "1970-01-01T00:00:00Z" ]; then
  log_info "Initial deployment detected (baseline time: 1970-01-01T00:00:00Z)"
  log_info "Optimizing: Skipping PR retrieval and setting all deployment targets to TRUE"

  # 最新のPRを取得して、LAST_PROCESSED_PRに設定する
  log_info "Getting latest PR for tracking purposes..."
  LATEST_PR=$(curl -s -H "Authorization: Bearer ${INSTALLATION_TOKEN}" \
    -H "Accept: application/vnd.github+json" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    "https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPO}/pulls?state=closed&sort=updated&direction=desc&base=${BASE_BRANCH}&per_page=1")

  LATEST_PR_NUMBER=$(echo $LATEST_PR | jq -r '.[0].number // 0')
  LATEST_PR_MERGED_AT=$(echo $LATEST_PR | jq -r '.[0].merged_at // "1970-01-01T00:00:00Z"')

  if [ "$LATEST_PR_NUMBER" -eq "0" ]; then
    log_warn "Could not get latest PR. Using default values."
    LATEST_PR_NUMBER=1
    LATEST_PR_MERGED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
  fi

  log_info "Latest PR: #${LATEST_PR_NUMBER} (merged at ${LATEST_PR_MERGED_AT})"

  # 最新のPRをMERGED_PRSに設定
  MERGED_PRS="[{\"number\": ${LATEST_PR_NUMBER}, \"merged_at\": \"${LATEST_PR_MERGED_AT}\"}]"
  PR_COUNT=1

  # すべてのデプロイ対象をTRUEに設定
  HAS_GLUE_DEPENDENCY_CHANGES=true
  HAS_GLUE_SOURCE_CHANGES=true
  HAS_GLUE_JOB_SCRIPT_CHANGES=true
  HAS_CFN_CHANGES=true
  HAS_LAMBDA_CHANGES=true

  log_info "All deployment targets set to TRUE for initial deployment"
  log_info "PR to track: #${LATEST_PR_NUMBER}"

  # 最後に処理したPRを設定
  LAST_PROCESSED_PR="{\"number\": ${LATEST_PR_NUMBER}, \"merged_at\": \"${LATEST_PR_MERGED_AT}\"}"
else
  # 通常のデプロイ処理（初回デプロイ以外の場合）
  log_info "Getting merged PRs since last deployment..."
  log_info "GITHUB_OWNER: ${GITHUB_OWNER}"
  log_info "GITHUB_REPO: ${GITHUB_REPO}"
  log_info "BASE_BRANCH: ${BASE_BRANCH}"
  log_info "deploy_baseline_time: ${deploy_baseline_time}"

  # スクリプトの存在確認
  if [ ! -f "$CODEBUILD_SRC_DIR/cicd/scripts/pr/get-merged-prs.sh" ]; then
    log_error "Script not found: $CODEBUILD_SRC_DIR/cicd/scripts/pr/get-merged-prs.sh"
    exit 1
  fi

  # 直接スクリプトを実行して結果を取得
  chmod +x "$CODEBUILD_SRC_DIR/cicd/scripts/pr/get-merged-prs.sh"
  # deploy_baseline_timeが有効な日時形式であることを確認
  if ! date -d "$deploy_baseline_time" >/dev/null 2>&1; then
    log_error "deploy_baseline_time ($deploy_baseline_time) が有効な日時形式ではありません"
    log_error "Parameter Storeの値が不正か、前処理で正しく変換されていません"
    log_error "管理者に連絡してください"
    exit 1
  fi
  # gh CLI方式でシンプルに実行
  MERGED_PRS=$(run_script_get_output "$CODEBUILD_SRC_DIR/cicd/scripts/pr/get-merged-prs.sh" "${deploy_baseline_time}")

  # JSONの検証
  if ! echo "$MERGED_PRS" | jq . > /dev/null 2>&1; then
    log_error "Invalid JSON output from get-merged-prs.sh"
    log_error "JSON validation failed. Check script output for details."
    exit 1
  fi

  # Check if we have PRs to process
  PR_COUNT=$(echo $MERGED_PRS | jq '. | length' 2>/dev/null || echo "0")

  # PR_COUNTが数値でない場合のエラーハンドリング
  if ! [[ "$PR_COUNT" =~ ^[0-9]+$ ]]; then
    log_error "Invalid PR_COUNT: $PR_COUNT"
    PR_COUNT=0
  fi

  if [ "$PR_COUNT" -eq "0" ]; then
    log_info "No new merged PRs to deploy."
    echo "export PR_COUNT=\"0\"" > $CODEBUILD_SRC_DIR/build_vars.sh
    exit 0
  fi

  log_info "Found ${PR_COUNT} PRs to process."
  # PR番号の取得も安全に実行
  PR_NUMBERS=$(echo $MERGED_PRS | jq -r '[.[].number] | join(", ")' 2>/dev/null || echo "Unable to extract PR numbers")
  log_info "PR numbers: $PR_NUMBERS"

  # Initialize variables to track changes
  HAS_GLUE_DEPENDENCY_CHANGES=false
  HAS_GLUE_SOURCE_CHANGES=false
  HAS_GLUE_JOB_SCRIPT_CHANGES=false
  HAS_CFN_CHANGES=false
  HAS_LAMBDA_CHANGES=false
  CHANGED_LAMBDA_FUNCTIONS=""
  LAST_PROCESSED_PR="{}"

  # Process each PR to analyze changes
  # 一時ファイルを使用してPRを1つずつ処理（JSON解析エラーを防ぐため）
  TEMP_PR_FILE=$(mktemp)
  echo "$MERGED_PRS" | jq -c '.[]' > "$TEMP_PR_FILE" 2>/dev/null

  while IFS= read -r PR; do
    if [ -z "$PR" ]; then
      continue
    fi

    # JSON解析エラーを防ぐため、各フィールドにエラーハンドリングを追加
    PR_NUMBER=$(echo "$PR" | jq -r '.number // empty' 2>/dev/null || echo "")
    PR_TITLE=$(echo "$PR" | jq -r '.title // empty' 2>/dev/null || echo "")
    PR_MERGED_AT=$(echo "$PR" | jq -r '.merged_at // empty' 2>/dev/null || echo "")
    PR_HEAD_SHA=$(echo "$PR" | jq -r '.head_sha // empty' 2>/dev/null || echo "")
    MERGE_COMMIT_SHA=$(echo "$PR" | jq -r '.merge_commit_sha // empty' 2>/dev/null || echo "")

    # BASE_SHAはGitHub APIから取得（修正版get-merged-prs.shから正しい値を取得）
    BASE_SHA=$(echo "$PR" | jq -r '.base_sha // empty' 2>/dev/null || echo "")

    # 必須フィールドのチェック（厳格モード: エラー終了）
    if [ -z "$PR_NUMBER" ] || [ -z "$PR_HEAD_SHA" ] || [ -z "$BASE_SHA" ]; then
      log_error "Critical PR data missing - cannot proceed with deployment"
      log_error "PR_NUMBER=$PR_NUMBER, PR_HEAD_SHA=$PR_HEAD_SHA, BASE_SHA=$BASE_SHA"
      log_error "Raw PR JSON: $PR"
      log_error "This indicates a critical bug in PR data retrieval system"
      exit 1
    fi

    # BASE_SHA="develop"の場合もエラー終了（厳格モード）
    if [ "$BASE_SHA" = "develop" ]; then
      log_error "Invalid BASE_SHA='develop' detected for PR #$PR_NUMBER"
      log_error "This indicates a critical bug in PR data retrieval - BASE_SHA should be a commit SHA, not a branch name"
      log_error "Deployment cannot proceed with inaccurate change detection"
      exit 1
    fi

    log_info "Processing PR #${PR_NUMBER}: ${PR_TITLE} (merged at ${PR_MERGED_AT})"

    # 変更ファイル検出にはBASE_SHAとMERGE_COMMIT_SHAを使用
    # MERGE_COMMIT_SHAが空の場合はPR_HEAD_SHAを使用
    if [ -z "$MERGE_COMMIT_SHA" ]; then
      MERGE_COMMIT_SHA="$PR_HEAD_SHA"
    fi

    # Debug log only essential information
    if [ "${DEBUG_MODE:-false}" = "true" ]; then
      log_debug "PR #${PR_NUMBER}: head_sha=${PR_HEAD_SHA}, merge_commit_sha=${MERGE_COMMIT_SHA}, base_sha=${BASE_SHA}"
    fi

    # Get changed files
    CHANGED_FILES=$(run_script_get_output "$CODEBUILD_SRC_DIR/cicd/scripts/detect/detect-changes.sh" "$BASE_SHA" "$MERGE_COMMIT_SHA")

    log_info "Changed files in PR #${PR_NUMBER}:"
    log_info "$CHANGED_FILES"

    # Detect Glue changes
    # 変更ファイルが空の場合は空文字列ではなく適切なデフォルト値を渡す
    if [ -z "$CHANGED_FILES" ]; then
      GLUE_CHANGES='{"HAS_GLUE_DEPENDENCY_CHANGES":false,"HAS_GLUE_SOURCE_CHANGES":false,"HAS_GLUE_JOB_SCRIPT_CHANGES":false}'
      log_info "No changed files for PR #${PR_NUMBER}, skipping Glue change detection"
    else
      GLUE_CHANGES=$(run_script_get_output "$CODEBUILD_SRC_DIR/cicd/scripts/detect/detect-glue.sh" "$CHANGED_FILES")
    fi

    # Extract Glue change flags
    HAS_GLUE_DEPENDENCY_CHANGES_PR=$(echo $GLUE_CHANGES | jq -r '.HAS_GLUE_DEPENDENCY_CHANGES')
    HAS_GLUE_SOURCE_CHANGES_PR=$(echo $GLUE_CHANGES | jq -r '.HAS_GLUE_SOURCE_CHANGES')
    HAS_GLUE_JOB_SCRIPT_CHANGES_PR=$(echo $GLUE_CHANGES | jq -r '.HAS_GLUE_JOB_SCRIPT_CHANGES')

    log_debug "PR #${PR_NUMBER} Glue changes: DEPENDENCY=${HAS_GLUE_DEPENDENCY_CHANGES_PR}, SOURCE=${HAS_GLUE_SOURCE_CHANGES_PR}, JOB_SCRIPT=${HAS_GLUE_JOB_SCRIPT_CHANGES_PR}"

    # Update global flags
    if [ "$HAS_GLUE_DEPENDENCY_CHANGES_PR" = "true" ]; then
      HAS_GLUE_DEPENDENCY_CHANGES=true
      log_info "Detected Glue dependency changes in PR #${PR_NUMBER}"
    fi

    if [ "$HAS_GLUE_SOURCE_CHANGES_PR" = "true" ]; then
      HAS_GLUE_SOURCE_CHANGES=true
      log_info "Detected Glue source code changes in PR #${PR_NUMBER}"
    fi

    if [ "$HAS_GLUE_JOB_SCRIPT_CHANGES_PR" = "true" ]; then
      HAS_GLUE_JOB_SCRIPT_CHANGES=true
      log_info "Detected Glue job script changes in PR #${PR_NUMBER}"
    fi

    # Detect CloudFormation changes
    if [ -z "$CHANGED_FILES" ]; then
      CFN_CHANGES='{"HAS_CFN_CHANGES":false}'
      log_info "No changed files for PR #${PR_NUMBER}, skipping CloudFormation change detection"
    else
      CFN_CHANGES=$(run_script_get_output "$CODEBUILD_SRC_DIR/cicd/scripts/detect/detect-cfn.sh" "$CHANGED_FILES")
    fi

    # Extract CloudFormation change flag
    HAS_CFN_CHANGES_PR=$(echo $CFN_CHANGES | jq -r '.HAS_CFN_CHANGES')

    log_debug "PR #${PR_NUMBER} CloudFormation changes: ${HAS_CFN_CHANGES_PR}"

    # Update global flag
    if [ "$HAS_CFN_CHANGES_PR" = "true" ]; then
      HAS_CFN_CHANGES=true
      log_info "Detected CloudFormation changes in PR #${PR_NUMBER}"
    fi

    # Detect Lambda changes
    if [ -z "$CHANGED_FILES" ]; then
      LAMBDA_CHANGES='{"HAS_LAMBDA_CHANGES":false,"CHANGED_LAMBDA_FUNCTIONS":""}'
      log_info "No changed files for PR #${PR_NUMBER}, skipping Lambda change detection"
    else
      LAMBDA_CHANGES=$(run_script_get_output "$CODEBUILD_SRC_DIR/cicd/scripts/detect/detect-lambda.sh" "$CHANGED_FILES")
    fi

    # Extract Lambda change flags
    HAS_LAMBDA_CHANGES_PR=$(echo $LAMBDA_CHANGES | jq -r '.HAS_LAMBDA_CHANGES')
    CHANGED_LAMBDA_FUNCTIONS_PR=$(echo $LAMBDA_CHANGES | jq -r '.CHANGED_LAMBDA_FUNCTIONS')

    log_debug "PR #${PR_NUMBER} Lambda changes: ${HAS_LAMBDA_CHANGES_PR}, Functions: ${CHANGED_LAMBDA_FUNCTIONS_PR}"

    # Update global flags
    if [ "$HAS_LAMBDA_CHANGES_PR" = "true" ]; then
      HAS_LAMBDA_CHANGES=true
      # Merge changed Lambda functions (remove duplicates)
      CHANGED_LAMBDA_FUNCTIONS="${CHANGED_LAMBDA_FUNCTIONS} ${CHANGED_LAMBDA_FUNCTIONS_PR}"
      log_info "Detected Lambda function changes in PR #${PR_NUMBER}: ${CHANGED_LAMBDA_FUNCTIONS_PR}"
    fi

    # Update last processed PR
    LAST_PROCESSED_PR="{\"number\": ${PR_NUMBER}, \"merged_at\": \"${PR_MERGED_AT}\"}"
  done < "$TEMP_PR_FILE"

  # 一時ファイルをクリーンアップ
  rm -f "$TEMP_PR_FILE"
fi

# Continue with deployment using the same repository
log_info "Starting deployment for environment: ${ENVIRONMENT}"

# デプロイ前の最終サマリー
log_info "=== Deployment Summary ==="
log_info "Processed PRs: ${PR_COUNT}"
log_info "Glue Changes: DEPENDENCY=${HAS_GLUE_DEPENDENCY_CHANGES}, SOURCE=${HAS_GLUE_SOURCE_CHANGES}, JOB_SCRIPT=${HAS_GLUE_JOB_SCRIPT_CHANGES}"
log_info "CloudFormation Changes: ${HAS_CFN_CHANGES}"
log_info "Lambda Changes: ${HAS_LAMBDA_CHANGES}"
if [ -n "$CHANGED_LAMBDA_FUNCTIONS" ]; then
  log_info "Changed Lambda Functions: ${CHANGED_LAMBDA_FUNCTIONS}"
fi
log_info "=========================="

# Deploy Glue jobs if needed
if [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] || [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ] || [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  log_info "Deploying Glue jobs..."
  run_script "$CODEBUILD_SRC_DIR/cicd/scripts/deploy/deploy-glue.sh" "${ENVIRONMENT}" "${DRY_RUN}" "${HAS_GLUE_DEPENDENCY_CHANGES}" "${HAS_GLUE_SOURCE_CHANGES}" "${HAS_GLUE_JOB_SCRIPT_CHANGES}"
else
  log_info "No Glue changes detected, skipping Glue deployment."
fi

# Deploy CloudFormation if needed
if [ "$HAS_CFN_CHANGES" = "true" ]; then
  log_info "Deploying CloudFormation templates..."
  run_script "$CODEBUILD_SRC_DIR/cicd/scripts/deploy/deploy-cfn.sh" "${ENVIRONMENT}" "${DRY_RUN}" "${HAS_CFN_CHANGES}"
else
  log_info "No CloudFormation changes detected, skipping CloudFormation deployment."
fi

# Deploy Lambda functions if needed
if [ "$HAS_LAMBDA_CHANGES" = "true" ]; then
  log_info "Deploying Lambda functions..."

  # Remove duplicates from CHANGED_LAMBDA_FUNCTIONS
  if [ -n "$CHANGED_LAMBDA_FUNCTIONS" ]; then
    CHANGED_LAMBDA_FUNCTIONS=$(echo "$CHANGED_LAMBDA_FUNCTIONS" | tr ' ' '\n' | sort -u | tr '\n' ' ' | sed 's/[[:space:]]*$//')
    log_info "Lambda functions to deploy: $CHANGED_LAMBDA_FUNCTIONS"

    # Deploy each changed Lambda function
    for function_name in $CHANGED_LAMBDA_FUNCTIONS; do
      if [ -n "$function_name" ]; then
        log_info "Deploying Lambda function: $function_name"
        if [ "$DRY_RUN" = "true" ]; then
          log_info "【DRY-RUN】Lambda function deployment: $function_name"
          log_info "【DRY-RUN】Command: cd cloudformation && ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n lambda $function_name"
        else
          cd cloudformation
          run_script "./scripts/cfn_deploy.sh" "-e" "${ENVIRONMENT}" "-y" "-n" "lambda" "$function_name"
          cd ..
        fi
      fi
    done
  fi
else
  log_info "No Lambda function changes detected, skipping Lambda deployment."
fi

# 環境変数をファイルに書き出す（post_buildフェーズで使用するため）
echo "export LAST_PROCESSED_PR='${LAST_PROCESSED_PR}'" > $CODEBUILD_SRC_DIR/build_vars.sh
echo "export PR_COUNT=\"${PR_COUNT}\"" >> $CODEBUILD_SRC_DIR/build_vars.sh
echo "export LAST_PR_NUMBER=\"${LAST_PR_NUMBER}\"" >> $CODEBUILD_SRC_DIR/build_vars.sh
echo "export LAST_PR_MERGED_AT=\"${LAST_MERGED_AT}\"" >> $CODEBUILD_SRC_DIR/build_vars.sh

log_info "Build phase completed successfully"
