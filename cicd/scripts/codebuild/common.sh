#!/bin/bash
set -e

# デバッグモードを有効化（詳細なログ出力）
if [ "${DEBUG_MODE:-false}" = "true" ]; then
  set -x
fi

# 共通ユーティリティの読み込み
source $CODEBUILD_SRC_DIR/cicd/scripts/utils/logging.sh
source $CODEBUILD_SRC_DIR/cicd/scripts/utils/error-handling.sh

# エラートラップの設定
setup_error_trap

# 共通環境変数の設定
export BASE_BRANCH="develop"  # デフォルトのベースブランチ
export DEBUG_MODE="${DEBUG_MODE:-false}"  # デバッグモードのデフォルト値

# 共通関数の定義
function check_required_env_vars() {
  local required_vars=("GITHUB_OWNER" "GITHUB_REPO" "ENVIRONMENT" "FROM_PR" "TO_PR" "DRY_RUN")

  for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
      log_error "Required environment variable $var is not set"
      exit 1
    fi
  done

  log_info "All required environment variables are set"
}

# 環境変数の出力（デバッグ用）
function print_env_vars() {
  log_info "Environment variables:"
  log_info "- GITHUB_OWNER: $GITHUB_OWNER"
  log_info "- GITHUB_REPO: $GITHUB_REPO"
  log_info "- ENVIRONMENT: $ENVIRONMENT"
  log_info "- FROM_PR: $FROM_PR"
  log_info "- TO_PR: $TO_PR"
  log_info "- DRY_RUN: $DRY_RUN"
  log_info "- CODEBUILD_SRC_DIR: $CODEBUILD_SRC_DIR"
  log_info "- BASE_BRANCH: $BASE_BRANCH"
}

# DRY-RUNモードのチェック
function is_dry_run() {
  if [ "$DRY_RUN" = "true" ]; then
    return 0  # true
  else
    return 1  # false
  fi
}

# DRY-RUNモードのログ出力
function log_dry_run() {
  local message="$1"
  log_info "【DRY-RUN】$message"
}

# スクリプトの実行
function run_script() {
  local script_path="$1"
  shift
  local script_args="$@"

  if [ ! -f "$script_path" ]; then
    log_error "Script not found: $script_path"
    exit 1
  fi

  chmod +x "$script_path"
  log_info "Running script: $script_path $script_args"

  # スクリプトの内容を表示（デバッグ用）
  log_debug "===== Script content: $script_path ====="
  cat "$script_path" | sed 's/^/    /' | while read line; do log_debug "    $line"; done
  log_debug "===== End of script content ====="

  # スクリプト実行時にデバッグモードを継承
  if [ "${DEBUG_MODE:-false}" = "true" ]; then
    (set -x; "$script_path" $script_args)
  else
    "$script_path" $script_args
  fi
  local exit_code=$?

  if [ $exit_code -ne 0 ]; then
    log_error "Script execution failed: $script_path (exit code: $exit_code)"
    exit $exit_code
  fi

  log_info "Script execution completed: $script_path"
}

# スクリプトの実行結果を取得
function run_script_get_output() {
  local script_path="$1"
  shift
  local script_args="$@"

  if [ ! -f "$script_path" ]; then
    log_error "Script not found: $script_path"
    exit 1
  fi

  chmod +x "$script_path"
  log_info "Running script and capturing output: $script_path $script_args"

  # スクリプトの内容を表示（デバッグ用）
  log_debug "===== Script content: $script_path ====="
  cat "$script_path" | sed 's/^/    /' | while read line; do log_debug "    $line"; done
  log_debug "===== End of script content ====="

  local output
  # スクリプト実行時にデバッグモードを継承
  if [ "${DEBUG_MODE:-false}" = "true" ]; then
    # デバッグモードでは標準出力と標準エラー出力を分離
    local temp_stdout=$(mktemp)
    local temp_stderr=$(mktemp)
    (set -x; "$script_path" $script_args > "$temp_stdout" 2> "$temp_stderr")
    local exit_code=$?
    # 標準エラー出力をログに出力
    if [ -s "$temp_stderr" ]; then
      log_debug "Script stderr output:"
      while read line; do log_debug "$line"; done < "$temp_stderr"
    fi
    # 標準出力を変数に格納
    output=$(cat "$temp_stdout")
    rm -f "$temp_stdout" "$temp_stderr"
  else
    output=$("$script_path" $script_args)
    local exit_code=$?
  fi

  if [ $exit_code -ne 0 ]; then
    log_error "Script execution failed: $script_path (exit code: $exit_code)"
    log_error "Script failed. Check logs for details."
    exit $exit_code
  fi

  log_info "Script execution completed: $script_path"
  # デバッグ出力は大量になる可能性があるため、サイズのみ記録
  if [ "${DEBUG_MODE:-false}" = "true" ]; then
    local output_size=$(echo "$output" | wc -c)
    log_debug "Script output size: ${output_size} bytes"
  fi
  echo "$output"
}

# 初期化処理
log_info "Loading common utilities from $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh"
