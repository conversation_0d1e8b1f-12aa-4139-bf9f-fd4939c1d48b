#!/bin/bash
set -e

# 共通処理の読み込み
source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh

# 現在のディレクトリとファイル一覧を表示（デバッグ用）
log_info "Current directory: $(pwd)"
log_info "Listing utility files:"
ls -la $CODEBUILD_SRC_DIR/cicd/scripts/utils/

# 必須環境変数のチェック
check_required_env_vars

# 環境変数の出力（デバッグ用）
print_env_vars

# GitHub認証
log_info "Authenticating with GitHub..."
# github-auth.sh スクリプトを実行し、最後の行のみを取得（トークン）
chmod +x "$CODEBUILD_SRC_DIR/cicd/scripts/auth/github-auth.sh"

# シークレット名が設定されているか確認
if [ -z "${GITHUB_APP_SECRET_NAME}" ]; then
  log_error "GITHUB_APP_SECRET_NAME 環境変数が設定されていません"
  exit 1
fi

if [ "${DEBUG_MODE:-false}" = "true" ]; then
  # デバッグモードでは標準エラー出力をそのまま表示
  AUTH_OUTPUT=$("$CODEBUILD_SRC_DIR/cicd/scripts/auth/github-auth.sh" "${GITHUB_APP_SECRET_NAME}" 2>&1)
  AUTH_EXIT_CODE=$?
  log_debug "Auth script output (first 100 chars): ${AUTH_OUTPUT:0:100}..."
else
  # 通常モードでは標準エラー出力を捨てる
  AUTH_OUTPUT=$("$CODEBUILD_SRC_DIR/cicd/scripts/auth/github-auth.sh" "${GITHUB_APP_SECRET_NAME}" 2>/dev/null)
  AUTH_EXIT_CODE=$?
fi

# 認証スクリプトの終了コードを確認
if [ $AUTH_EXIT_CODE -ne 0 ]; then
  log_error "GitHub認証に失敗しました (終了コード: $AUTH_EXIT_CODE)"
  log_error "認証スクリプトの出力: ${AUTH_OUTPUT}"
  exit 1
fi

# 最後の行をトークンとして取得
INSTALLATION_TOKEN=$(echo "$AUTH_OUTPUT" | tail -n 1)

# トークンが空でないことを確認
if [ -z "$INSTALLATION_TOKEN" ]; then
  log_error "GitHub認証トークンが空です"
  log_error "認証スクリプトの出力: ${AUTH_OUTPUT}"
  exit 1
fi

log_debug "Token preview: ${INSTALLATION_TOKEN:0:4}...${INSTALLATION_TOKEN: -4}"

# トークンのテスト
log_info "Testing token with a simple API call..."
REPO_TEST=$(curl -s --fail \
  -H "Authorization: Bearer ${INSTALLATION_TOKEN}" \
  -H "Accept: application/vnd.github+json" \
  -H "X-GitHub-Api-Version: 2022-11-28" \
  "https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPO}" || echo '{"error":"Failed to access repository"}')

REPO_NAME=$(echo "$REPO_TEST" | jq -r '.full_name')
if [ -z "$REPO_NAME" ] || [ "$REPO_NAME" = "null" ]; then
  log_warn "Could not verify token access to repository. Response: $REPO_TEST"
  log_info "Continuing anyway..."
else
  log_info "Successfully verified token access to repository: $REPO_NAME"
fi

# ブランチのチェックアウト
log_info "Environment: ${ENVIRONMENT}"
log_info "Base branch: ${BASE_BRANCH} (固定ブランチを使用中)"

log_info "Checking out branch ${BASE_BRANCH}"
git checkout ${BASE_BRANCH} || log_warn "Failed to checkout ${BASE_BRANCH}, continuing with default branch"

# トークンを環境変数としてエクスポート（他のスクリプトで使用するため）
# CodeBuildでは環境変数は自動的に引き継がれないため、ファイルに書き出す
cat > $CODEBUILD_SRC_DIR/env_vars.sh << EOF
export INSTALLATION_TOKEN="${INSTALLATION_TOKEN}"
export GITHUB_OWNER="${GITHUB_OWNER}"
export GITHUB_REPO="${GITHUB_REPO}"
export ENVIRONMENT="${ENVIRONMENT}"
export DEBUG_MODE="${DEBUG_MODE}"
EOF

log_info "INSTALLATION_TOKEN length: ${#INSTALLATION_TOKEN}"

log_info "Pre-build phase completed successfully"
