#!/bin/bash

# デプロイスクリプト（時刻ベース版）
# 使用方法:
#   ./deploy.sh <環境> [deploy_baseline_time] [オプション]
#
# 引数:
#   <環境>                  - デプロイ環境（dev, stg, prd）
#   [deploy_baseline_time]  - この時刻以降にマージされたPRを処理、省略時はParameter Storeから取得
#
# オプション:
#   -d, --dry-run  - 実際のデプロイを行わず、実行される操作をログとして出力
#
# 例:
#   ./deploy.sh dev                           # 開発環境に最後のデプロイ以降のすべてのPRをデプロイ
#   ./deploy.sh dev "2025-05-27T10:00:00Z"    # 開発環境に指定時刻以降のPRをデプロイ
#   ./deploy.sh stg "2025-05-20T00:00:00Z"    # 検証環境に指定時刻以降のPRをデプロイ
#   ./deploy.sh dev --dry-run                 # 開発環境に最後のデプロイ以降のすべてのPRをDRY-RUNモードでテスト
#   ./deploy.sh dev "2025-05-27T10:00:00Z" -d # 開発環境に指定時刻以降のPRをDRY-RUNモードでテスト

set -e

# エラーハンドリング関数
handle_error() {
  local exit_code=$1
  local message=$2
  echo "エラー: $message"
  exit $exit_code
}

# 終了時の処理
cleanup() {
  if [ $? -ne 0 ]; then
    echo "デプロイが異常終了しました。"
  fi
}

trap cleanup EXIT

# オプションの解析
DRY_RUN=false
POSITIONAL_ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
    -d|--dry-run)
      DRY_RUN=true
      shift
      ;;
    *)
      POSITIONAL_ARGS+=("$1")
      shift
      ;;
  esac
done

# 位置引数の復元
set -- "${POSITIONAL_ARGS[@]}"

# 引数の検証
if [ $# -lt 1 ]; then
  handle_error 1 "環境を指定してください（dev, stg, prd）\n使用方法: $0 <環境> [deploy_baseline_time] [オプション]"
fi

ENVIRONMENT=$1
deploy_baseline_time_arg=${2:-"/dlpf/${ENVIRONMENT}/deploy-baseline-time"}

# 環境の検証
if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "stg" ] && [ "$ENVIRONMENT" != "prd" ]; then
  handle_error 1 "環境は dev, stg, prd のいずれかを指定してください"
fi

# deploy_baseline_time_argが日時形式の場合は検証
if [[ "$deploy_baseline_time_arg" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}Z$ ]]; then
  if ! date -d "$deploy_baseline_time_arg" >/dev/null 2>&1; then
    handle_error 1 "deploy_baseline_time ($deploy_baseline_time_arg) が有効な日時形式ではありません"
  fi
fi

# CodeBuildプロジェクト名
PROJECT_NAME="cdp-${ENVIRONMENT}-dlpf-deploy"

# 環境変数のオーバーライド
ENV_VARS="[{\"name\":\"ENVIRONMENT\",\"value\":\"${ENVIRONMENT}\",\"type\":\"PLAINTEXT\"}"

# deploy_baseline_timeが日時形式の場合
if [[ "$deploy_baseline_time_arg" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}Z$ ]]; then
  ENV_VARS="${ENV_VARS},{\"name\":\"deploy_baseline_time\",\"value\":\"${deploy_baseline_time_arg}\",\"type\":\"PLAINTEXT\"}"
else
  # deploy_baseline_time_argがパラメータストアのパスの場合
  # パラメータストアから値を取得
  echo "パラメータストアから最後にデプロイした基準時刻を取得: ${deploy_baseline_time_arg}"
  LAST_DEPLOYED=$(aws ssm get-parameter --name "${deploy_baseline_time_arg}" --query "Parameter.Value" --output text 2>/dev/null || echo "{}")

  # JSONから時刻を抽出
  deploy_baseline_time=$(echo $LAST_DEPLOYED | jq -r '.deploy_baseline_time // "1970-01-01T00:00:00Z"')
  echo "最後にデプロイした基準時刻: ${deploy_baseline_time}"

  # 時刻として渡す
  ENV_VARS="${ENV_VARS},{\"name\":\"deploy_baseline_time\",\"value\":\"${deploy_baseline_time}\",\"type\":\"PLAINTEXT\"}"
fi

# DRY_RUNモードの設定
if [ "$DRY_RUN" = true ]; then
  ENV_VARS="${ENV_VARS},{\"name\":\"DRY_RUN\",\"value\":\"true\",\"type\":\"PLAINTEXT\"}"
  echo "【DRY-RUNモード】実際のデプロイは行われません"
else
  ENV_VARS="${ENV_VARS},{\"name\":\"DRY_RUN\",\"value\":\"false\",\"type\":\"PLAINTEXT\"}"
fi

ENV_VARS="${ENV_VARS}]"

# デプロイの実行
echo "デプロイを開始します..."
echo "環境: ${ENVIRONMENT}"
echo "deploy_baseline_time: ${deploy_baseline_time_arg}"
echo "DRY_RUN: ${DRY_RUN}"

# CodeBuildプロジェクトの起動
echo "CodeBuildプロジェクトを起動します..."
BUILD_ID=$(aws codebuild start-build \
  --project-name ${PROJECT_NAME} \
  --environment-variables-override "${ENV_VARS}" \
  --query "build.id" \
  --output text) || handle_error 2 "CodeBuildプロジェクトの起動に失敗しました"

echo "デプロイが開始されました。ビルドID: ${BUILD_ID}"
echo "AWSコンソールでデプロイの進行状況を確認できます:"
echo "https://console.aws.amazon.com/codesuite/codebuild/projects/${PROJECT_NAME}/build/${BUILD_ID}/log"

# DRY-RUNモードの場合でもビルドの監視を行う
if [ "$DRY_RUN" = true ]; then
  echo "【DRY-RUN】ビルドの監視を開始します（実際のデプロイは行われません）"
fi

# ビルドの状態を監視
echo "ビルドの状態を監視しています..."
while true; do
  BUILD_STATUS=$(aws codebuild batch-get-builds \
    --ids ${BUILD_ID} \
    --query "builds[0].buildStatus" \
    --output text) || handle_error 3 "ビルド状態の取得に失敗しました"

  echo "ビルド状態: ${BUILD_STATUS}"

  if [ "$BUILD_STATUS" = "SUCCEEDED" ]; then
    echo "デプロイが正常に完了しました。"
    exit 0
  elif [ "$BUILD_STATUS" = "FAILED" ] || [ "$BUILD_STATUS" = "FAULT" ] || [ "$BUILD_STATUS" = "STOPPED" ] || [ "$BUILD_STATUS" = "TIMED_OUT" ]; then
    handle_error 4 "デプロイが失敗しました。詳細はAWSコンソールで確認してください。\nhttps://console.aws.amazon.com/codesuite/codebuild/projects/${PROJECT_NAME}/build/${BUILD_ID}/log"
  fi

  sleep 30
done
