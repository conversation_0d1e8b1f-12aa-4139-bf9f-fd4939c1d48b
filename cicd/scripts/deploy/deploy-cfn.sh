#!/bin/bash
set -e

# CloudFormationテンプレートをデプロイするスクリプト
# 使用方法: ./deploy-cfn.sh <環境> <DRY_RUNフラグ> <変更フラグ>
# 例: ./deploy-cfn.sh "dev" "false" "true"

# 引数の検証
if [ $# -lt 3 ]; then
  echo "Error: Not enough arguments"
  echo "Usage: $0 <environment> <dry_run> <has_cfn_changes>"
  exit 1
fi

ENVIRONMENT="$1"
DRY_RUN="$2"
HAS_CFN_CHANGES="$3"

echo "Deploying CloudFormation templates for environment: ${ENVIRONMENT}"
echo "DRY_RUN: ${DRY_RUN}"
echo "HAS_CFN_CHANGES: ${HAS_CFN_CHANGES}"

# 変更がない場合はスキップ
if [ "$HAS_CFN_CHANGES" != "true" ]; then
  echo "No CloudFormation changes detected, skipping CloudFormation deployment."
  exit 0
fi

# DRY-RUNモードの場合
if [ "$DRY_RUN" = "true" ]; then
  echo "【DRY-RUN】CloudFormationテンプレートのデプロイをスキップします"
  echo "【DRY-RUN】以下のテンプレートがデプロイされます："
  for template_dir in cloudformation/templates/*; do
    if [ -d "$template_dir" ]; then
      resource_type=$(basename "$template_dir")
      
      # 除外対象のディレクトリをスキップ
      if [[ "$resource_type" == "api-gateway" || "$resource_type" == "secrets-manager" ]]; then
        echo "【DRY-RUN】除外対象ディレクトリのためスキップ: $resource_type"
        continue
      fi
      
      for template_file in "$template_dir"/*.yml "$template_dir"/*.yaml; do
        if [ -f "$template_file" ]; then
          template_name=$(basename "$template_file" .yml)
          template_name=${template_name%.yaml}  # Also handle .yaml extension
          echo "【DRY-RUN】テンプレート: $template_file (Type: $resource_type, Name: $template_name)"
          echo "【DRY-RUN】実行されるコマンド: ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name"
        fi
      done
    fi
  done
  exit 0
fi

# 実際のデプロイ処理
chmod +x cloudformation/scripts/cfn_deploy.sh
cd cloudformation

# Deploy all templates
for template_dir in templates/*; do
  if [ -d "$template_dir" ]; then
    resource_type=$(basename "$template_dir")
    
    # 除外対象のディレクトリをスキップ
    if [[ "$resource_type" == "api-gateway" || "$resource_type" == "secrets-manager" ]]; then
      echo "除外対象ディレクトリのためスキップ: $resource_type"
      continue
    fi
    
    for template_file in "$template_dir"/*.yml "$template_dir"/*.yaml; do
      if [ -f "$template_file" ]; then
        template_name=$(basename "$template_file" .yml)
        template_name=${template_name%.yaml}  # Also handle .yaml extension
        
        echo "Deploying template: $template_file (Type: $resource_type, Name: $template_name)"
        ./scripts/cfn_deploy.sh -e ${ENVIRONMENT} -y -n $resource_type $template_name
      fi
    done
  fi
done

cd ..

echo "CloudFormation deployment completed."
exit 0
