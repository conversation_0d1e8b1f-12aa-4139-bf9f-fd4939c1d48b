#!/bin/bash
set -e

# Glueジョブをデプロイするスクリプト
# 使用方法: ./deploy-glue.sh <環境> <DRY_RUNフラグ> <依存関係変更フラグ> <ソース変更フラグ> <ジョブスクリプト変更フラグ>
# 例: ./deploy-glue.sh "dev" "false" "true" "true" "false"

# 引数の検証
if [ $# -lt 5 ]; then
  echo "Error: Not enough arguments"
  echo "Usage: $0 <environment> <dry_run> <has_dependency_changes> <has_source_changes> <has_job_script_changes>"
  exit 1
fi

ENVIRONMENT="$1"
DRY_RUN="$2"
HAS_GLUE_DEPENDENCY_CHANGES="$3"
HAS_GLUE_SOURCE_CHANGES="$4"
HAS_GLUE_JOB_SCRIPT_CHANGES="$5"

echo "Deploying Glue jobs for environment: ${ENVIRONMENT}"
echo "DRY_RUN: ${DRY_RUN}"
echo "HAS_GLUE_DEPENDENCY_CHANGES: ${HAS_GLUE_DEPENDENCY_CHANGES}"
echo "HAS_GLUE_SOURCE_CHANGES: ${HAS_GLUE_SOURCE_CHANGES}"
echo "HAS_GLUE_JOB_SCRIPT_CHANGES: ${HAS_GLUE_JOB_SCRIPT_CHANGES}"

# DRY-RUNモードの場合は、build_and_deploy.shに--dry-runオプションを渡す
DRY_RUN_OPTION=""
if [ "$DRY_RUN" = "true" ]; then
  echo "【DRY-RUN】Glueジョブのドライランモードでデプロイします"
  DRY_RUN_OPTION="--dry-run"
fi

# 実際のデプロイ処理
cd job
chmod +x build_and_deploy_${ENVIRONMENT}.sh

# 非対話モードで実行
echo "Deploying Glue components with non-interactive mode..."

# 依存関係の変更があるかどうかに基づいて--skip-deps-buildオプションを設定
SKIP_DEPS_BUILD_OPTION=""
if [ "$HAS_GLUE_DEPENDENCY_CHANGES" != "true" ]; then
  echo "No dependency changes detected, skipping deps build..."
  SKIP_DEPS_BUILD_OPTION="--skip-deps-build"
fi

# 変更フラグに基づいてデプロイオプションを設定
if [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] && [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  # すべての変更がある場合
  echo "Deploying all Glue components (dependencies, source, job scripts)..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps true --deploy-source true --deploy-scripts true $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
elif [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] && [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ]; then
  # 依存関係とソースコードの変更がある場合
  echo "Deploying Glue dependencies and source code..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps true --deploy-source true --deploy-scripts false $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
elif [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  # 依存関係とジョブスクリプトの変更がある場合
  echo "Deploying Glue dependencies and job scripts..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps true --deploy-source false --deploy-scripts true $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
elif [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  # ソースコードとジョブスクリプトの変更がある場合
  echo "Deploying Glue source code and job scripts..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps false --deploy-source true --deploy-scripts true $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
elif [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "true" ]; then
  # 依存関係の変更のみ
  echo "Deploying Glue dependencies only..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps true --deploy-source false --deploy-scripts false $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
elif [ "$HAS_GLUE_SOURCE_CHANGES" = "true" ]; then
  # ソースコードの変更のみ
  echo "Deploying Glue source code only..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps false --deploy-source true --deploy-scripts false $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
elif [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "true" ]; then
  # ジョブスクリプトの変更のみ
  echo "Deploying Glue job scripts only..."
  ./build_and_deploy_${ENVIRONMENT}.sh --non-interactive --deploy-deps false --deploy-source false --deploy-scripts true $DRY_RUN_OPTION $SKIP_DEPS_BUILD_OPTION
else
  echo "No Glue changes to deploy."
fi

cd ..

echo "Glue deployment completed."
exit 0
