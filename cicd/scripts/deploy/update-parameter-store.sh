#!/bin/bash
set -e

# Parameter Storeを更新するスクリプト（時刻ベース版）
# 使用方法: ./update-parameter-store.sh <環境> <DRY_RUNフラグ>
# 例: ./update-parameter-store.sh "dev" "false"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
validate_args $# 2 "$0 <environment> <dry_run>"

ENVIRONMENT="$1"
DRY_RUN="$2"

# Parameter Store名の設定
DEPLOY_BASELINE_TIME_PARAM="/dlpf/${ENVIRONMENT}/deploy-baseline-time"
DEPLOYMENT_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

log_info "Updating Parameter Store for environment: ${ENVIRONMENT}"
log_info "Deploy baseline time: ${DEPLOYMENT_TIME}"

# DRY-RUNモードの場合
if [ "$DRY_RUN" = "true" ]; then
  log_dry_run "Parameter Storeの更新をスキップします"
  BASELINE_JSON="{\"deploy_baseline_time\": \"${DEPLOYMENT_TIME}\"}"
  log_dry_run "実行されるコマンド: aws ssm put-parameter --name \"${DEPLOY_BASELINE_TIME_PARAM}\" --value '${BASELINE_JSON}' --type String --overwrite"
  exit 0
fi

# デプロイ基準時刻を更新（JSON形式）
log_info "Updating deploy baseline time parameter: ${DEPLOY_BASELINE_TIME_PARAM}"
BASELINE_JSON="{\"deploy_baseline_time\": \"${DEPLOYMENT_TIME}\"}"
log_info "Parameter value: ${BASELINE_JSON}"
run_with_retry "aws ssm put-parameter --name \"${DEPLOY_BASELINE_TIME_PARAM}\" --value '${BASELINE_JSON}' --type String --overwrite" 3 2 "Failed to update deploy baseline time parameter"

log_info "Parameter Store update completed successfully"
echo "{\"DEPLOYMENT_TIME\":\"${DEPLOYMENT_TIME}\"}"
