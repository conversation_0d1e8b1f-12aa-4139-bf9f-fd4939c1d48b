#!/bin/bash
set -e

# CloudFormation変更を検出するスクリプト
# 使用方法: ./detect-cfn.sh <変更ファイルリスト>
# 例: ./detect-cfn.sh "$(cat changed_files.txt)"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
validate_args $# 1 "$0 <changed_files>"

CHANGED_FILES="$1"

# 初期化
HAS_CFN_CHANGES=false
EXCLUDED_ONLY=true

log_info "Detecting CloudFormation changes..."

# CloudFormation変更の検出
CFN_FILES=$(echo "$CHANGED_FILES" | grep -E 'cloudformation/' || true)
if [ -n "$CFN_FILES" ]; then
  log_debug "Found CloudFormation directory changes, checking for excluded directories..."
  log_debug "CloudFormation files: $CFN_FILES"

  # 除外対象以外の変更があるかチェック
  NON_EXCLUDED_FILES=""
  for file in $CFN_FILES; do
    if [[ $file == cloudformation/* ]] && ! [[ $file =~ cloudformation/templates/(api-gateway|secrets-manager)/ ]]; then
      NON_EXCLUDED_FILES="${NON_EXCLUDED_FILES} ${file}"
      log_debug "Found non-excluded CloudFormation change: $file"
    else
      log_debug "Found excluded CloudFormation change: $file"
    fi
  done

  if [ -n "$NON_EXCLUDED_FILES" ]; then
    HAS_CFN_CHANGES=true
    EXCLUDED_ONLY=false
    log_info "Detected CloudFormation changes"
    log_debug "Non-excluded CloudFormation files: $NON_EXCLUDED_FILES"
  else
    log_info "Detected CloudFormation changes only in excluded directories (api-gateway, secrets-manager) - skipping deployment"
  fi
else
  log_info "No CloudFormation changes detected"
fi

# 結果をJSON形式で出力
echo "{\"HAS_CFN_CHANGES\":${HAS_CFN_CHANGES}}"
