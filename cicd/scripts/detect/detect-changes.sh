#!/bin/bash
set -e

# PRの変更ファイルを取得するスクリプト
# 使用方法: ./detect-changes.sh <ベースSHA> <マージコミットSHA>
# 例: ./detect-changes.sh "abc123" "def456"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
validate_args $# 2 "$0 <base_sha> <merge_commit_sha>"

BASE_SHA="$1"
MERGE_COMMIT_SHA="$2"

log_info "Detecting changes between $BASE_SHA and $MERGE_COMMIT_SHA..."

# gitコマンドで変更ファイルを取得
CHANGED_FILES=$(git diff --name-only $BASE_SHA $MERGE_COMMIT_SHA 2>/dev/null || echo "")

# エラーチェック
if [ $? -ne 0 ]; then
  log_error "Failed to get changed files using git diff"
  exit 1
fi

# 変更ファイルの数を確認
FILE_COUNT=$(echo "$CHANGED_FILES" | grep -v "^$" | wc -l)
if [ $FILE_COUNT -eq 0 ]; then
  log_warn "No changed files found between $BASE_SHA and $MERGE_COMMIT_SHA"
  echo ""
  exit 0
fi

log_info "Found $FILE_COUNT changed files"

# 結果を出力
echo "$CHANGED_FILES"
