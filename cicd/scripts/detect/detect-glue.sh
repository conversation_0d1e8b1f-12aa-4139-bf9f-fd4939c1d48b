#!/bin/bash
set -e

# Glue変更を検出するスクリプト
# 使用方法: ./detect-glue.sh <変更ファイルリスト>
# 例: ./detect-glue.sh "$(cat changed_files.txt)"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
validate_args $# 1 "$0 <changed_files>"

CHANGED_FILES="$1"

# 初期化
HAS_GLUE_DEPENDENCY_CHANGES=false
HAS_GLUE_SOURCE_CHANGES=false
HAS_GLUE_JOB_SCRIPT_CHANGES=false

log_info "Detecting Glue changes..."

# 1. Check for dependency changes (pyproject.toml)
if echo "$CHANGED_FILES" | grep -q "job/pyproject.toml"; then
  HAS_GLUE_DEPENDENCY_CHANGES=true
  log_info "Detected Glue dependency changes"
fi

# 2. Check for source code changes (excluding glue_job_*.py)
GLUE_SOURCE_FILES=$(echo "$CHANGED_FILES" | grep -E "job/source/.*\.(py|yaml|yml|sql|config)$" | grep -v -E "job/source/.*/glue_job_.*\.py$" || true)
if [ -n "$GLUE_SOURCE_FILES" ]; then
  HAS_GLUE_SOURCE_CHANGES=true
  log_info "Detected Glue source code changes"
  log_debug "Changed Glue source files: $GLUE_SOURCE_FILES"
fi

# 3. Check for SQL files specifically (important for data processing)
SQL_FILES=$(echo "$CHANGED_FILES" | grep -E "job/source/.*\.sql$" || true)
if [ -n "$SQL_FILES" ]; then
  HAS_GLUE_SOURCE_CHANGES=true
  log_info "Detected SQL file changes (Glue source)"
  log_debug "Changed SQL files: $SQL_FILES"
fi

# 3. Check for job script changes (glue_job_*.py)
if echo "$CHANGED_FILES" | grep -q -E "job/source/.*/glue_job_.*\.py$"; then
  HAS_GLUE_JOB_SCRIPT_CHANGES=true
  log_info "Detected Glue job script changes"
fi

# 変更がない場合
if [ "$HAS_GLUE_DEPENDENCY_CHANGES" = "false" ] && [ "$HAS_GLUE_SOURCE_CHANGES" = "false" ] && [ "$HAS_GLUE_JOB_SCRIPT_CHANGES" = "false" ]; then
  log_info "No Glue changes detected"
fi

# 結果をJSON形式で出力
echo "{\"HAS_GLUE_DEPENDENCY_CHANGES\":${HAS_GLUE_DEPENDENCY_CHANGES},\"HAS_GLUE_SOURCE_CHANGES\":${HAS_GLUE_SOURCE_CHANGES},\"HAS_GLUE_JOB_SCRIPT_CHANGES\":${HAS_GLUE_JOB_SCRIPT_CHANGES}}"
