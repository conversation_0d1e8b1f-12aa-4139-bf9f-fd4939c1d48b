#!/bin/bash
set -e

# Lambda関数変更を検出するスクリプト
# 使用方法: ./detect-lambda.sh <変更ファイルリスト>
# 例: ./detect-lambda.sh "$(cat changed_files.txt)"

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"

# 引数の検証
validate_args $# 1 "$0 <changed_files>"

CHANGED_FILES="$1"

# 初期化
HAS_LAMBDA_CHANGES=false
CHANGED_LAMBDA_FUNCTIONS=""

log_info "Detecting Lambda function changes..."

# 1. CloudFormationテンプレートの変更を検出
LAMBDA_TEMPLATE_CHANGES=$(echo "$CHANGED_FILES" | grep -E "cloudformation/templates/lambda/.*\.ya?ml$" || true)
if [ -n "$LAMBDA_TEMPLATE_CHANGES" ]; then
  HAS_LAMBDA_CHANGES=true
  log_info "Detected Lambda CloudFormation template changes"
  log_debug "Changed Lambda templates: $LAMBDA_TEMPLATE_CHANGES"

  # 変更されたテンプレートから関数名を抽出
  for template_file in $LAMBDA_TEMPLATE_CHANGES; do
    function_name=$(basename "$template_file" .yaml)
    function_name=${function_name%.yml}  # .yml拡張子にも対応
    CHANGED_LAMBDA_FUNCTIONS="${CHANGED_LAMBDA_FUNCTIONS} ${function_name}"
    log_debug "Lambda template changed: $template_file -> function: $function_name"
  done
fi

# 2. Lambda関数のソースコードの変更を検出
LAMBDA_SOURCE_CHANGES=$(echo "$CHANGED_FILES" | grep -E "lambda/.*/lambda_function\.py$" || true)
if [ -n "$LAMBDA_SOURCE_CHANGES" ]; then
  HAS_LAMBDA_CHANGES=true
  log_info "Detected Lambda source code changes"
  log_debug "Changed Lambda sources: $LAMBDA_SOURCE_CHANGES"

  # 変更されたソースコードから関数名を抽出
  for source_file in $LAMBDA_SOURCE_CHANGES; do
    # lambda/FUNCTION_NAME/lambda_function.py から FUNCTION_NAME を抽出
    function_name=$(echo "$source_file" | sed -E 's|lambda/([^/]+)/lambda_function\.py|\1|')
    CHANGED_LAMBDA_FUNCTIONS="${CHANGED_LAMBDA_FUNCTIONS} ${function_name}"
    log_debug "Lambda source changed: $source_file -> function: $function_name"
  done
fi

# 重複を除去
if [ -n "$CHANGED_LAMBDA_FUNCTIONS" ]; then
  CHANGED_LAMBDA_FUNCTIONS=$(echo "$CHANGED_LAMBDA_FUNCTIONS" | tr ' ' '\n' | sort -u | tr '\n' ' ' | sed 's/[[:space:]]*$//')
  log_info "Changed Lambda functions: $CHANGED_LAMBDA_FUNCTIONS"
fi

# 変更がない場合
if [ "$HAS_LAMBDA_CHANGES" = "false" ]; then
  log_info "No Lambda function changes detected"
fi

# 結果をJSON形式で出力
echo "{\"HAS_LAMBDA_CHANGES\":${HAS_LAMBDA_CHANGES}, \"CHANGED_LAMBDA_FUNCTIONS\":\"${CHANGED_LAMBDA_FUNCTIONS}\"}"
