#!/bin/bash

# CloudFormationスタック状態調査スクリプト
# Deploy失敗エラー調査用

set -e

# ログ設定
source "$(dirname "$0")/../utils/logging.sh"

# 環境設定
ENVIRONMENT=${1:-dev}
REGION="ap-northeast-1"

log_info "CloudFormationスタック状態調査開始 - 環境: $ENVIRONMENT"

# DLPFプロジェクト関連スタックのプレフィックス
STACK_PREFIXES=(
    "dlpf-"
    "cdp-${ENVIRONMENT}-dlpf-"
)

# 問題のあるスタック状態
PROBLEMATIC_STATES=(
    "ROLLBACK_COMPLETE"
    "ROLLBACK_FAILED"
    "CREATE_FAILED"
    "UPDATE_ROLLBACK_COMPLETE"
    "UPDATE_ROLLBACK_FAILED"
)

log_info "対象スタックプレフィックス: ${STACK_PREFIXES[*]}"
log_info "問題状態: ${PROBLEMATIC_STATES[*]}"

# 結果格納
PROBLEMATIC_STACKS=()
HEALTHY_STACKS=()

# 全スタック取得・確認
log_info "スタック一覧取得中..."

for prefix in "${STACK_PREFIXES[@]}"; do
    log_info "プレフィックス '$prefix' のスタックを確認中..."

    # スタック一覧取得
    STACKS=$(aws cloudformation list-stacks \
        --region "$REGION" \
        --query "StackSummaries[?starts_with(StackName, \`$prefix\`) && StackStatus != \`DELETE_COMPLETE\`].[StackName,StackStatus]" \
        --output text 2>/dev/null || true)

    if [ -z "$STACKS" ]; then
        log_info "プレフィックス '$prefix' のスタックは見つかりませんでした"
        continue
    fi

    # 各スタックの状態確認
    while IFS=$'\t' read -r stack_name stack_status; do
        if [ -z "$stack_name" ]; then
            continue
        fi

        log_info "スタック: $stack_name - 状態: $stack_status"

        # 問題のある状態かチェック
        is_problematic=false
        for problematic_state in "${PROBLEMATIC_STATES[@]}"; do
            if [ "$stack_status" = "$problematic_state" ]; then
                is_problematic=true
                break
            fi
        done

        if [ "$is_problematic" = true ]; then
            PROBLEMATIC_STACKS+=("$stack_name:$stack_status")
            log_warn "🚨 問題のあるスタック: $stack_name ($stack_status)"
        else
            HEALTHY_STACKS+=("$stack_name:$stack_status")
            log_info "✅ 正常なスタック: $stack_name ($stack_status)"
        fi
    done <<< "$STACKS"
done

# 結果サマリー
echo ""
log_info "=========================================="
log_info "CloudFormationスタック状態調査結果"
log_info "=========================================="

echo ""
log_info "🚨 問題のあるスタック (${#PROBLEMATIC_STACKS[@]}件):"
if [ ${#PROBLEMATIC_STACKS[@]} -eq 0 ]; then
    log_info "  なし"
else
    for stack_info in "${PROBLEMATIC_STACKS[@]}"; do
        stack_name="${stack_info%:*}"
        stack_status="${stack_info#*:}"
        log_warn "  - $stack_name ($stack_status)"
    done
fi

echo ""
log_info "✅ 正常なスタック (${#HEALTHY_STACKS[@]}件):"
if [ ${#HEALTHY_STACKS[@]} -eq 0 ]; then
    log_info "  なし"
else
    for stack_info in "${HEALTHY_STACKS[@]}"; do
        stack_name="${stack_info%:*}"
        stack_status="${stack_info#*:}"
        log_info "  - $stack_name ($stack_status)"
    done
fi

# 対処方法の提案
if [ ${#PROBLEMATIC_STACKS[@]} -gt 0 ]; then
    echo ""
    log_info "🔧 対処方法:"
    log_info "1. 各問題スタックの詳細確認:"
    for stack_info in "${PROBLEMATIC_STACKS[@]}"; do
        stack_name="${stack_info%:*}"
        log_info "   aws cloudformation describe-stack-events --stack-name $stack_name --region $REGION"
    done

    echo ""
    log_info "2. スタック削除（データ影響なしの場合）:"
    for stack_info in "${PROBLEMATIC_STACKS[@]}"; do
        stack_name="${stack_info%:*}"
        log_info "   aws cloudformation delete-stack --stack-name $stack_name --region $REGION"
    done

    echo ""
    log_info "3. 削除後の再デプロイ:"
    log_info "   ./cicd/scripts/deploy.sh $ENVIRONMENT"
fi

log_info "調査完了"
