# Deploy失敗エラー調査 - 対応サマリー

**調査日**: 2025年5月28日
**対象**: 2025年5月27日 CodeBuild Deploy失敗
**調査者**: TIS黄

---

## 📊 **エラーパターン別対応表**

| パターン | 対象件数 | 原因 | 対応方法 | 優先度 | 担当者 | 実行可能性 |
|---------|---------|------|---------|--------|--------|-----------|
| **パターン1** | 10件 | ROLLBACK_COMPLETE状態 | 削除→再作成 | 🔴 高 | TIS黄 | ✅ 即座実行可能 |
| **パターン2** | 9件 | CloudFormation構造不正 | Dummyファイル削除 | 🟢 低 | 王さん | ⏳ 外部依存 |
| **パターン3** | 1件 | UPDATE_ROLLBACK_COMPLETE状態 | 削除→再作成 | 🔴 高 | TIS黄 | ✅ 即座実行可能 |
| **パターン4** | 1件 | VpcIdパラメータ不足 | パラメータ追加 | 🟡 中 | TIS黄 | ✅ 即座実行可能 |

**合計**: 21件のエラー

---

## 🔧 **スタック復旧状況表**

### **✅ 復旧完了 (4件)**

| スタック名 | 復旧日時 | 対応方法 | ステータス |
|-----------|---------|---------|-----------|
| `dlpf-cognito` | 2025-05-28 | 削除→再作成 | ✅ 完了 |
| `dlpf-parameter-store` | 2025-05-28 | 削除→再作成 | ✅ 完了 |
| `dlpf-security-group` | 2025-05-28 | 削除→再作成 | ✅ 完了 |
| `dlpf-job-api-to-file` | 2025-05-28 | 削除→再作成 | ✅ 完了 |

### **🔧 システム改修対象 - Glue Job関連 (9件)**

| スタック名 | 現在状態 | 問題 | 対応方法 | 実行予定 |
|-----------|---------|------|---------|---------|
| `dlpf-job-bulk-api-register` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-convert-character-encoding` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-convert-format` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-db-to-file` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-get-file` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-internal-db-clear` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-internal-db-import` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-send-file` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |
| `dlpf-job-file-compress` | 削除完了 | 王さんの既存Job名重複 | 別名パラメータ対応 | システム改修後 |

### **⏳ 外部依存 - パターン2 (9件) - Dummyファイル**

| スタック名 | 現在状態 | 対応方法 | 実行予定 | 備考 |
|-----------|---------|---------|---------|------|
| `dlpf-EB-AC001-FD01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-AC002-FD01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-AC003-DF01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-AC004-DF01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-PR001-FF01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-SH001-DD01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-SH002-FD01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-ST005-FF01-001` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |
| `dlpf-EB-ST005-FF01-002` | Deploy失敗 | Dummyファイル削除 | 王さん都合 | 業務影響なし |

### **✅ パラメータ修正完了 - パターン4 (1件)**

| スタック名 | 復旧日時 | 対応方法 | ステータス |
|-----------|---------|---------|-----------|
| `dlpf-security-group` | 2025-05-28 | VpcIdパラメータ追加→削除→再作成 | ✅ 完了 |

---

## 📋 **アクションアイテム表**

### **✅ 完了済み (今日中)**

| タスク | 詳細 | 担当者 | 期限 | ステータス |
|-------|------|--------|------|-----------|
| VpcIdパラメータ追加 | `environments/dev/parameters/default.json`にVpcId追加 | TIS黄 | 今日中 | ✅ 完了 |
| 失敗スタック削除 | ROLLBACK_COMPLETEスタック9件の削除 | TIS黄 | 今日中 | ✅ 完了 |
| 業務影響スタック復旧 | 4件の重要スタック復旧完了 | TIS黄 | 今日中 | ✅ 完了 |
| 復旧進捗管理 | 各スタックの復旧状況を追跡・記録 | TIS黄 | 今日中 | ✅ 完了 |

### **🔴 新規追加 (今日中)**

| タスク | 詳細 | 担当者 | 期限 | ステータス |
|-------|------|--------|------|-----------|
| 王さんの既存Glue Job名調査 | 実際のJob名とCloudFormationスタック名の特定 | TIS黄 | 今日中 | 🔄 実行中 |
| 別名パラメータ対応設計 | cfn_deploy.shの別名パラメータ機能設計 | TIS黄 | 今日中 | ⏳ 予定 |

### **🟡 短期実行 (今週中)**

| タスク | 詳細 | 担当者 | 期限 | ステータス |
|-------|------|--------|------|-----------|
| 別名パラメータ対応実装 | cfn_deploy.shの別名パラメータ機能実装 | TIS黄 | 今週中 | ⏳ 予定 |
| Glue Job再デプロイ | 正しい名前での9件Glue Job再デプロイ | TIS黄 | 今週中 | ⏳ 予定 |
| Event Bridge Dummyファイル削除 | 王さんに9件のDummyテンプレート削除を依頼 | TIS黄 | 王さん都合 | ⏳ 予定 |
| Deploy失敗メール通知実装 | 各担当者への自動通知機能 | 二宮さん | 今週中 | ⏳ 予定 |
| スタック状態監視強化 | 定期的な状態確認の仕組み構築 | TIS黄 | 今週中 | ⏳ 予定 |

### **🟢 中期実行 (今月中)**

| タスク | 詳細 | 担当者 | 期限 | ステータス |
|-------|------|--------|------|-----------|
| テンプレート事前検証 | Deploy前の構造チェック機能 | TIS黄 | 今月中 | ⏳ 予定 |
| リソース名重複チェック | 自動重複検出機能の実装 | TIS黄 | 今月中 | ⏳ 予定 |

---

## 🎯 **確立された復旧手順**

### **削除→再作成手順 (パターン1・3対応)**

```bash
# 1. スタック削除
aws cloudformation delete-stack --stack-name [STACK_NAME] --region ap-northeast-1

# 2. 削除完了待機
aws cloudformation wait stack-delete-complete --stack-name [STACK_NAME] --region ap-northeast-1

# 3. 手動Deploy実行
cd cloudformation
./scripts/cfn_deploy.sh -e dev -n -y [TYPE] [NAME]
```

### **VpcIdパラメータ追加手順 (パターン4対応)**

```bash
# environments/dev/parameters/default.json に追加
{
  "Parameters": {
    "AccountId": "************",
    "Environment": "dev",
    "VpcId": "[開発環境VPC_ID]"
  }
}
```

---

## 📞 **連絡・エスカレーション**

### **王さんへの依頼事項**
- **対象**: Event Bridge関連9件のDummyテンプレート
- **状況**: 全件がDummyファイルで業務使用なし（王さん確認済み）
- **依頼内容**: 時間がある時にDummyファイルの削除対応

### **二宮さんとの連携事項**
- **Deploy失敗メール通知機能**: 設計・実装の進捗確認
- **定時自動実行**: 開発環境での仕組み構築状況

---

## 📈 **進捗状況**

- **✅ 復旧完了**: 4/21件 (19.0%) - 業務影響のある重要スタック
- **🔧 システム改修対象**: 9/21件 (42.9%) - Glue Job関連（別名パラメータ対応後）
- **⏳ 外部依存**: 9/21件 (42.9%) - Event Bridge Dummyファイル（業務影響なし）

**業務影響のある復旧**: 4/4件 (100%) 完了 🎉
**技術的改修**: 9件のGlue Job（システム改修後に対応）
**外部依存**: 9件のDummyファイル（王さん都合で削除）

---

**最終更新**: 2025年5月28日
**次回更新予定**: 復旧作業完了後
