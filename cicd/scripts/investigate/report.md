# Deploy失敗エラー調査報告書

**調査日時**: 2025年5月28日
**調査者**: TIS黄
**対象**: CodeBuild Deploy失敗（2025年5月27日発生）

---

## 📋 **概要**

2025年5月27日のCodeBuild Deploy実行時に複数のCloudFormationスタックが失敗状態となった問題について、根本原因の特定と対処を実施した。

### **影響範囲**
- **問題スタック数**: 16件
- **正常スタック数**: 133件
- **主な影響**: 業務用Parameter Store、Glue Job関連リソース

---

## 🔍 **調査結果**

### **1. 初期仮説の検証**

#### **仮説1: CodeBuild vs 手動Deployの環境差異**
- **検証方法**: 手動Deploy実行
- **結果**: 手動Deployでも同じエラー（ROLLBACK_COMPLETE状態）
- **結論**: 環境差異ではなく、スタック状態の問題

#### **仮説2: 実行順序・タイミング問題**
- **検証方法**: 個別スタックでの手動Deploy
- **結果**: 個別実行でも同じエラー
- **結論**: 並列実行の問題ではない

### **2. 根本原因の特定**

#### **調査手順**
```bash
# スタック状態確認
aws cloudformation describe-stacks --stack-name dlpf-parameter-store

# 失敗イベント詳細確認
aws cloudformation describe-stack-events --stack-name dlpf-parameter-store \
  --query 'StackEvents[?ResourceStatus==`CREATE_FAILED`]'
```

#### **発見された事実**
- **失敗日時**: 2025-05-27 09:52:47 UTC
- **スタック状態**: ROLLBACK_COMPLETE
- **エラー内容**: Parameter Store名の重複

#### **具体的エラーメッセージ**
```
/glue/job/environment-config already exists in stack
arn:aws:cloudformation:ap-northeast-1:886436956581:stack/ou-parameter-store/cda10510-e770-11ef-b1da-0a09ca169bb3
```

### **3. 根本原因**

#### **問題の詳細**
- **競合リソース**: `/glue/job/environment-config` Parameter Store
- **既存スタック**: `ou-parameter-store` (2025-02-10作成)
- **新規スタック**: `dlpf-parameter-store` (2025-05-27作成試行)

#### **原因分析**
1. **規約違反**: 王さんが手動で `ou-parameter-store` スタックを作成
2. **命名規則違反**: `ou-` プレフィックスは非標準
3. **リソース重複**: 同じParameter Store名を異なるスタックで使用
4. **連鎖失敗**: 依存関係により他スタックも失敗

---

## 🔧 **対処方法**

### **実施した対処**

#### **Step 1: 規約違反スタックの削除**
```bash
# 王さんの規約違反スタック削除
aws cloudformation delete-stack --stack-name ou-parameter-store --region ap-northeast-1
aws cloudformation wait stack-delete-complete --stack-name ou-parameter-store --region ap-northeast-1
```

#### **Step 2: 失敗スタックの削除**
```bash
# 失敗状態スタック削除
aws cloudformation delete-stack --stack-name dlpf-parameter-store --region ap-northeast-1
aws cloudformation wait stack-delete-complete --stack-name dlpf-parameter-store --region ap-northeast-1
```

#### **Step 3: 正規手順での再作成**
```bash
# 手動Deploy手順書に従った再作成
cd cloudformation
./scripts/cfn_deploy.sh -e dev -n -y system-manager dlpf_parameter_store
```

### **対処結果**

#### **✅ 成功事例**
1. **dlpf-cognito**: 削除→再作成成功
2. **dlpf-parameter-store**: 削除→再作成成功

#### **確立された復旧手順**
```bash
# 1. スタック削除
aws cloudformation delete-stack --stack-name [STACK_NAME] --region ap-northeast-1

# 2. 削除完了待機
aws cloudformation wait stack-delete-complete --stack-name [STACK_NAME] --region ap-northeast-1

# 3. 手動Deploy実行
./scripts/cfn_deploy.sh -e dev -n -y [TYPE] [NAME]
```

---

## 📊 **影響分析**

### **復旧済みスタック (2/16)**
- ✅ `dlpf-cognito` (CREATE_COMPLETE)
- ✅ `dlpf-parameter-store` (CREATE_COMPLETE)

### **残存問題スタック (14/16)**

#### **Glue Job関連 (10件)**
- `dlpf-job-send-file`
- `dlpf-job-internal-db-import`
- `dlpf-job-internal-db-clear`
- `dlpf-job-get-file`
- `dlpf-job-db-to-file`
- `dlpf-job-convert-format`
- `dlpf-job-convert-character-encoding`
- `dlpf-job-bulk-api-register`
- `dlpf-job-api-to-file`
- `dlpf-job-file-compress`

#### **個別業務ジョブ (4件)**
- `dlpf-JN-PR011-DF01-001`
- `dlpf-JN-PR003-DF01-001`
- `dlpf-JN-ST002-DF01-001`
- `dlpf-JN-AC001-FD01-001-test`

---

## 🔍 **詳細エラーパターン分析**

### **昨日のCodeBuildログ分析結果**

#### **パターン1: ROLLBACK_COMPLETE状態エラー（終了コード254）**
- **エラーメッセージ**: `Stack is in ROLLBACK_COMPLETE state and can not be updated`
- **原因**: 過去のDeploy失敗により、スタックが更新不可状態
- **対象スタック数**: 10件
- **対処方法**: 削除→再作成

**対象スタック一覧:**
```
dlpf-cognito
dlpf-job-api-to-file
dlpf-job-bulk-api-register
dlpf-job-convert-character-encoding
dlpf-job-convert-format
dlpf-job-db-to-file
dlpf-job-get-file
dlpf-job-internal-db-clear
dlpf-job-internal-db-import
dlpf-job-send-file
```

#### **パターン2: 空テンプレートエラー（終了コード254）**
- **エラーメッセージ**: `Template format error: At least one Resources member must be defined`
- **原因**: CloudFormationテンプレート構造が不正（`Resources`セクション欠如）
- **対象スタック数**: 9件（Event Bridge関連）
- **詳細調査結果**: Event Bridgeテンプレートは空ではないが、CloudFormation必須セクションが欠如
- **対処方法**: 王さんに連絡してテンプレート構造修正を依頼

**対象スタック一覧:**
```
dlpf-EB-AC001-FD01-001
dlpf-EB-AC002-FD01-001
dlpf-EB-AC003-DF01-001
dlpf-EB-AC004-DF01-001
dlpf-EB-PR001-FF01-001
dlpf-EB-SH001-DD01-001
dlpf-EB-SH002-FD01-001
dlpf-EB-ST005-FF01-001
dlpf-EB-ST005-FF01-002
```

#### **パターン3: 実際のデプロイ失敗（終了コード255）**
- **エラーメッセージ**: `Failed to create/update the stack`
- **原因**: Deploy処理中の実際のエラー
- **対象スタック数**: 1件
- **詳細調査結果**: スタック状態は`UPDATE_ROLLBACK_COMPLETE`（2025-04-01作成）
- **対処方法**: パターン1と同様の削除→再作成手順で対処可能

**対象スタック一覧:**
```
dlpf-job-file-compress
```

#### **パターン4: パラメータ不足エラー（終了コード254）**
- **エラーメッセージ**: `Parameters: [VpcId] must have values`
- **原因**: 必須パラメータの値が未設定
- **対象スタック数**: 1件
- **詳細調査結果**:
  - テンプレートでVpcIdパラメータが定義済み
  - `environments/dev/parameters/default.json`にVpcIdが未設定
  - security-group専用のパラメータファイルが存在しない
- **対処方法**: default.jsonにVpcIdパラメータを追加、または専用パラメータファイル作成

**対象スタック一覧:**
```
dlpf-security-group
```

---

## 🎯 **今後の対策**

### **即座実施すべき対策**

#### **1. エラーパターン別の復旧計画**

**パターン1対応（ROLLBACK_COMPLETE状態）- 優先度：高**
- 確立された削除→再作成手順で復旧
- 対象：Glue Job関連10件
- 実行順序：業務影響度順

**パターン2対応（CloudFormation構造不正）- 優先度：中**
- 王さんに連絡してEvent Bridgeテンプレート構造修正を依頼
- 必須セクション（AWSTemplateFormatVersion、Resources）の追加
- 対象：Event Bridge関連9件

**パターン3対応（UPDATE_ROLLBACK_COMPLETE状態）- 優先度：高**
- `dlpf-job-file-compress` をパターン1と同様の手順で復旧
- 削除→再作成による確実な復旧

**パターン4対応（VpcIdパラメータ不足）- 優先度：中**
- `environments/dev/parameters/default.json` にVpcIdパラメータ追加
- 開発環境VPCの特定と設定値確認

#### **2. 規約遵守の徹底**
- 手動スタック作成の禁止
- 命名規則の厳格化
- レビュープロセスの強化

### **中長期対策**

#### **1. 予防策**
- CloudFormation テンプレートの事前検証
- リソース名重複チェックの自動化
- Deploy前の依存関係確認

#### **2. 監視・通知**
- スタック状態の定期監視
- 失敗時の即座通知（メール通知機能）
- 異常状態の早期検出

#### **3. 運用改善**
- Deploy手順書の更新
- 緊急時対応手順の整備
- 定期的な状態確認の実施

---

## 📝 **教訓**

### **技術的教訓**
1. **リソース名の一意性**: CloudFormation間でのリソース名重複は致命的
2. **状態管理の重要性**: ROLLBACK_COMPLETE状態は更新不可
3. **手動操作のリスク**: 規約違反の手動作成は後の障害原因となる

### **運用的教訓**
1. **規約遵守**: 命名規則・手順の厳格な遵守が必要
2. **早期発見**: 問題の早期発見・対処が被害拡大を防ぐ
3. **手順書の価値**: 確立された手順書の重要性

---

## 🚀 **次のアクション**

### **即座実行 (今日中)**
- [ ] **パターン1**: Glue Job関連10スタックの削除→再作成
- [ ] **パターン3**: dlpf-job-file-compress の削除→再作成（パターン1と同様）
- [ ] **パターン4**: default.jsonにVpcIdパラメータ追加
- [ ] 復旧状況の進捗管理

### **短期実行 (今週中)**
- [ ] **パターン2**: 王さんへEvent Bridgeテンプレート修正依頼
- [ ] Deploy失敗時メール通知機能の実装
- [ ] スタック状態監視の強化
- [ ] 手順書の更新

### **中期実行 (今月中)**
- [ ] テンプレート事前検証機能の実装
- [ ] リソース名重複チェック機能の実装
- [ ] 定期的な状態確認の自動化
- [ ] 緊急時対応手順の整備

---

**報告者**: TIS黄
**報告日**: 2025年5月28日
**ステータス**: 根本原因特定完了、復旧手順確立、順次復旧実行中
