#!/bin/bash
set -e

# PRにコメントを追加するスクリプト
# 使用方法: ./comment-pr.sh <インストールトークン> <GitHubオーナー> <GitHubリポジトリ> <PR番号> <コメント内容>
# 例: ./comment-pr.sh "ghs_xxx" "TIS-DSDev" "tis-dlpf-app" 10 "デプロイが完了しました"

# 引数の検証
if [ $# -lt 5 ]; then
  echo "Error: Not enough arguments"
  echo "Usage: $0 <installation_token> <github_owner> <github_repo> <pr_number> <comment_body>"
  exit 1
fi

INSTALLATION_TOKEN="$1"
GITHUB_OWNER="$2"
GITHUB_REPO="$3"
PR_NUMBER="$4"
COMMENT_BODY="$5"

echo "Adding comment to PR #${PR_NUMBER}..."

MAX_RETRIES=3
RETRY_COUNT=0
COMMENT_SUCCESS=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$COMMENT_SUCCESS" != "true" ]; do
  if [ $RETRY_COUNT -gt 0 ]; then
    echo "Retry attempt $RETRY_COUNT of $MAX_RETRIES to add comment to PR #${PR_NUMBER}..."
    sleep 2
  fi

  COMMENT_RESPONSE=$(curl -s --fail -X POST \
    -H "Authorization: Bearer ${INSTALLATION_TOKEN}" \
    -H "Accept: application/vnd.github+json" \
    -H "X-GitHub-Api-Version: 2022-11-28" \
    -H "Content-Type: application/json" \
    "https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPO}/issues/${PR_NUMBER}/comments" \
    -d "{\"body\":\"${COMMENT_BODY}\"}" || echo '{"error":"Failed to add comment"}')

  if [ "$(echo $COMMENT_RESPONSE | jq -r '.id // ""')" != "" ]; then
    COMMENT_SUCCESS=true
    echo "Successfully added comment to PR #${PR_NUMBER}"
  else
    echo "Failed to add comment to PR #${PR_NUMBER}"
    echo "Response: $COMMENT_RESPONSE"
  fi

  RETRY_COUNT=$((RETRY_COUNT + 1))
done

if [ "$COMMENT_SUCCESS" != "true" ]; then
  echo "WARNING: Failed to add comment to PR #${PR_NUMBER} after $MAX_RETRIES attempts."
  exit 1
fi

# 成功した場合は0を返す
exit 0
