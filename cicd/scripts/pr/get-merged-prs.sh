#!/bin/bash
set -e

# マージ済みPRを取得するスクリプト（時刻ベース版）
# 使用方法: ./get-merged-prs.sh <デプロイ基準時刻>
# 例: ./get-merged-prs.sh "2025-05-27T10:00:00Z"
# 注意: GITHUB_TOKENが環境変数として設定されている必要があります

# スクリプトディレクトリの取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 共通ユーティリティの読み込み
source "$SCRIPT_DIR/../utils/logging.sh"
source "$SCRIPT_DIR/../utils/error-handling.sh"





# 引数の検証
if [ $# -lt 1 ]; then
  log_error "Not enough arguments"
  log_error "Usage: $0 <deploy_baseline_time>"
  echo "[]"
  exit 1
fi

# 環境変数の確認
if [ -z "$GITHUB_TOKEN" ]; then
  log_error "GITHUB_TOKEN environment variable is not set"
  echo "[]"
  exit 1
fi

# GitHub リポジトリ情報のデフォルト値設定
GITHUB_OWNER="${GITHUB_OWNER:-TIS-DSDev}"
GITHUB_REPO="${GITHUB_REPO:-tis-dlpf-app}"

deploy_baseline_time="$1"

log_debug "GITHUB_TOKEN length: ${#GITHUB_TOKEN}"
log_debug "deploy_baseline_time: $deploy_baseline_time"

# deploy_baseline_timeが有効な日時形式であることを確認
if ! date -d "$deploy_baseline_time" >/dev/null 2>&1; then
  log_error "deploy_baseline_time ($deploy_baseline_time) が有効な日時形式ではありません"
  echo "[]"
  exit 1
fi

# 初回デプロイ（1970-01-01T00:00:00Z）の場合はエラーを返す
if [ "$deploy_baseline_time" = "1970-01-01T00:00:00Z" ]; then
  log_error "初回デプロイ（1970-01-01T00:00:00Z）は上位レベルで処理されるべきパターンです"
  log_error "Initial deployment should be handled at the higher level"
  echo "[]"
  exit 1
fi

log_info "Getting merged PRs since ${deploy_baseline_time}..."

# gh CLIを使用してPRを取得
log_debug "Using gh CLI to fetch merged PRs"

# 基本的なgh CLIコマンドを構築
REPO_NAME="${GITHUB_OWNER}/${GITHUB_REPO}"
GH_COMMAND="gh pr list --repo \"$REPO_NAME\" --state closed --limit 100"

# 必要なフィールドを指定（mergeCommitからbase_shaを取得）
GH_FIELDS="number,title,mergedAt,headRefOid,mergeCommit,baseRefName"
GH_COMMAND="$GH_COMMAND --json \"$GH_FIELDS\""

log_debug "gh CLI command: $GH_COMMAND"

# gh CLIでPRを取得
log_info "Fetching merged PRs with gh CLI..."

# gh CLIコマンドを実行（エラーハンドリング強化）
log_debug "Executing: $GH_COMMAND"

# 一時ファイルを使用してgh CLIの出力を安全に取得
TEMP_OUTPUT=$(mktemp)
if eval "$GH_COMMAND" > "$TEMP_OUTPUT" 2>/dev/null; then
  ALL_PRS=$(cat "$TEMP_OUTPUT")
  rm -f "$TEMP_OUTPUT"
else
  log_error "gh CLI command failed"
  rm -f "$TEMP_OUTPUT"
  echo "[]"
  exit 0
fi

# JSON形式の検証
if ! echo "$ALL_PRS" | jq . >/dev/null 2>&1; then
  log_error "Invalid JSON output from gh CLI"
  log_error "Output: $ALL_PRS"
  echo "[]"
  exit 0
fi

if [ "$ALL_PRS" = "[]" ] || [ -z "$ALL_PRS" ]; then
  log_info "No PRs found"
  echo "[]"
  exit 0
fi

log_debug "Retrieved $(echo "$ALL_PRS" | jq '. | length' 2>/dev/null || echo '0') PRs from gh CLI"

# PRをフィルタリング（時刻ベース）
log_debug "Filtering PRs: mergedAt != null AND mergedAt >= ${deploy_baseline_time}"

# jqフィルタを構築（時刻ベース）
JQ_FILTER='.[] | select(.mergedAt != null and .mergedAt >= "'${deploy_baseline_time}'")'
log_debug "jq filter: ${JQ_FILTER}"

# フィルタリングを実行し、build.shで必要な形式に変換
# mergeCommitの親コミットからbase_shaを取得
log_debug "Converting PRs to required format with proper base_sha..."

# 一時的にフィルタリングされたPRを取得
TEMP_FILTERED_PRS=$(echo "$ALL_PRS" | jq -c "[${JQ_FILTER}]" 2>/dev/null || echo '[]')

# 各PRのmergeCommitから正確なbase_shaを取得
FILTERED_PRS="[]"
# 一時ファイルを使用してPRを1つずつ処理
TEMP_PR_FILE=$(mktemp)
echo "$TEMP_FILTERED_PRS" | jq -c '.[]' > "$TEMP_PR_FILE" 2>/dev/null

while IFS= read -r pr; do
  if [ -z "$pr" ]; then
    continue
  fi

  pr_number=$(echo "$pr" | jq -r '.number' 2>/dev/null)
  merge_commit_sha=$(echo "$pr" | jq -r '.mergeCommit.oid' 2>/dev/null)
  base_ref_name=$(echo "$pr" | jq -r '.baseRefName' 2>/dev/null)

  # merge commitの親コミット（最初の親がbase_sha）を取得
  if [ -n "$merge_commit_sha" ] && [ "$merge_commit_sha" != "null" ]; then
    base_sha=$(git rev-parse "${merge_commit_sha}^1" 2>/dev/null || echo "")
    if [ -z "$base_sha" ]; then
      log_warn "Could not get base_sha from merge commit for PR #$pr_number, using current branch HEAD"
      base_sha=$(git rev-parse "origin/$base_ref_name" 2>/dev/null || echo "$base_ref_name")
    fi
  else
    log_warn "No merge commit found for PR #$pr_number, using current branch HEAD"
    base_sha=$(git rev-parse "origin/$base_ref_name" 2>/dev/null || echo "$base_ref_name")
  fi

  # base_shaが空の場合はスキップ
  if [ -z "$base_sha" ] || [ "$base_sha" = "null" ]; then
    log_error "Could not determine base_sha for PR #$pr_number"
    continue
  fi

  # PRオブジェクトを構築
  pr_json=$(echo "$pr" | jq -c --arg base_sha "$base_sha" '{
    number: .number,
    title: .title,
    merged_at: .mergedAt,
    head_sha: .headRefOid,
    merge_commit_sha: .mergeCommit.oid,
    base_sha: $base_sha
  }')

  # 配列に追加
  FILTERED_PRS=$(echo "$FILTERED_PRS" | jq -c ". + [$pr_json]")
  log_debug "Added PR #$pr_number with base_sha: $base_sha (from merge commit parent)"
done < "$TEMP_PR_FILE"

# 一時ファイルをクリーンアップ
rm -f "$TEMP_PR_FILE"

MERGED_PRS="$FILTERED_PRS"

# 結果の確認
PR_COUNT=$(echo "$MERGED_PRS" | jq '. | length' 2>/dev/null || echo '0')
log_debug "Filtered PR count: $PR_COUNT"

if [ "$PR_COUNT" -eq "0" ]; then
  log_info "No new merged PRs to deploy."
  echo "[]"
  exit 0
fi

# PRをマージ日時でソート
MERGED_PRS=$(echo "$MERGED_PRS" | jq 'sort_by(.merged_at)' 2>/dev/null || echo "$MERGED_PRS")

log_info "Found ${PR_COUNT} PRs to process."
log_info "PR numbers: $(echo "$MERGED_PRS" | jq -r '[.[].number] | join(", ")' 2>/dev/null || echo 'Unable to extract PR numbers')"

# 結果を出力
echo "$MERGED_PRS"
