#!/bin/bash

# エラーハンドリング共通処理
# 使用方法: source ./error-handling.sh

# ログ関数とSNS通知関数を読み込み
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/logging.sh"
source "$SCRIPT_DIR/sns-notification.sh"

# エラーハンドリング関数
# 引数:
#   $1: 終了コード
#   $2: メッセージ
#   $3: 追加情報（オプション）
handle_error() {
  local exit_code=$1
  local message=$2
  local additional_info="${3:-}"

  log_error "$message"

  # SNS通知が有効な場合はエラー通知を送信
  if is_sns_notification_enabled; then
    log_info "エラー通知を送信します..."
    send_error_notification "$message" "$additional_info"
  else
    log_debug "SNS通知は無効です。エラー通知は送信されません。"
  fi

  exit $exit_code
}

# コマンド実行関数（エラーハンドリング付き）
# 引数:
#   $1: コマンド
#   $2: エラーメッセージ
#   $3: 終了コード（デフォルト: 1）
#   $4: 追加情報（オプション）
run_command() {
  local command="$1"
  local error_message="$2"
  local exit_code="${3:-1}"
  local additional_info="${4:-}"

  log_debug "実行コマンド: $command"

  # コマンド実行
  local output
  output=$(eval "$command" 2>&1)
  local result=$?

  # エラーチェック
  if [ $result -ne 0 ]; then
    # 追加情報が指定されていない場合は、コマンド出力を追加情報として使用
    if [ -z "$additional_info" ]; then
      additional_info="コマンド出力:\n$output"
    else
      additional_info="$additional_info\n\nコマンド出力:\n$output"
    fi

    handle_error $exit_code "$error_message (終了コード: $result)" "$additional_info"
  fi

  return $result
}

# リトライ付きコマンド実行関数
# 引数:
#   $1: コマンド
#   $2: 最大リトライ回数
#   $3: リトライ間隔（秒）
#   $4: エラーメッセージ
#   $5: 終了コード（デフォルト: 1）
#   $6: 追加情報（オプション）
run_with_retry() {
  local command="$1"
  local max_retries="$2"
  local retry_interval="$3"
  local error_message="$4"
  local exit_code="${5:-1}"
  local additional_info="${6:-}"

  local retry_count=0
  local success=false
  local all_outputs=""

  while [ $retry_count -lt $max_retries ] && [ "$success" != "true" ]; do
    if [ $retry_count -gt 0 ]; then
      log_info "リトライ $retry_count/$max_retries (間隔: ${retry_interval}秒)..."
      sleep $retry_interval
    fi

    log_debug "実行コマンド: $command"

    # コマンド実行
    local output
    output=$(eval "$command" 2>&1)
    local result=$?

    # 成功判定
    if [ $result -eq 0 ]; then
      success=true
    else
      log_warn "コマンド実行失敗 (終了コード: $result)"
      log_debug "コマンド出力: $output"
      all_outputs="${all_outputs}試行 $((retry_count + 1)):\n$output\n\n"
    fi

    retry_count=$((retry_count + 1))
  done

  # 最終的に失敗した場合
  if [ "$success" != "true" ]; then
    # 追加情報が指定されていない場合は、すべての試行の出力を追加情報として使用
    if [ -z "$additional_info" ]; then
      additional_info="すべての試行の出力:\n$all_outputs"
    else
      additional_info="$additional_info\n\nすべての試行の出力:\n$all_outputs"
    fi

    handle_error $exit_code "$error_message (最大リトライ回数に到達: $max_retries)" "$additional_info"
  fi

  return 0
}

# 引数検証関数
# 引数:
#   $1: 引数の数
#   $2: 必要な引数の数
#   $3: 使用方法メッセージ
validate_args() {
  local arg_count=$1
  local required_count=$2
  local usage_message=$3

  if [ $arg_count -lt $required_count ]; then
    log_error "引数が不足しています (必要: $required_count, 実際: $arg_count)"
    echo "使用方法: $usage_message"
    exit 1
  fi
}

# トラップ設定関数
setup_error_trap() {
  # エラー発生時の処理
  trap 'last_command=$current_command; current_command=$BASH_COMMAND;
    error_code=$?;
    error_message="コマンド \"${last_command}\" が失敗しました (行: ${LINENO}, 終了コード: $?)";
    log_error "$error_message";

    # SNS通知が有効な場合はエラー通知を送信
    if is_sns_notification_enabled; then
      log_info "エラー通知を送信します...";
      send_error_notification "$error_message" "スクリプト: $0";
    fi;
  ' ERR

  # スクリプト終了時の処理
  trap 'log_debug "スクリプト終了 (終了コード: $?)"' EXIT
}

# 使用例
# validate_args $# 2 "$0 <arg1> <arg2>"
# run_command "ls -la" "ディレクトリ一覧の取得に失敗しました"
# run_with_retry "curl -s http://example.com" 3 2 "APIリクエストに失敗しました"
