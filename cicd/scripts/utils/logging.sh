#!/bin/bash

# ログ出力関連の共通処理
# 使用方法: source ./logging.sh

# ログレベル
LOG_LEVEL_DEBUG=0
LOG_LEVEL_INFO=1
LOG_LEVEL_WARN=2
LOG_LEVEL_ERROR=3

# デフォルトのログレベル（環境変数で設定可能）
# DEBUG_MODEが有効な場合はデバッグレベル、それ以外は情報レベル
if [ "${DEBUG_MODE:-false}" = "true" ]; then
  CURRENT_LOG_LEVEL=$LOG_LEVEL_DEBUG
else
  CURRENT_LOG_LEVEL=${LOG_LEVEL:-$LOG_LEVEL_INFO}
fi

# ログ出力関数
# 引数:
#   $1: ログレベル
#   $2: メッセージ
log() {
  local level=$1
  local message=$2
  local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
  local level_str=""

  case $level in
    $LOG_LEVEL_DEBUG)
      level_str="DEBUG"
      ;;
    $LOG_LEVEL_INFO)
      level_str="INFO"
      ;;
    $LOG_LEVEL_WARN)
      level_str="WARN"
      ;;
    $LOG_LEVEL_ERROR)
      level_str="ERROR"
      ;;
    *)
      level_str="UNKNOWN"
      ;;
  esac

  # 現在のログレベル以上の場合のみ出力（標準エラーに出力）
  if [ $level -ge $CURRENT_LOG_LEVEL ]; then
    echo "[$timestamp] [$level_str] $message" >&2
  fi
}

# デバッグログ
# 引数:
#   $1: メッセージ
log_debug() {
  log $LOG_LEVEL_DEBUG "$1"
}

# 情報ログ
# 引数:
#   $1: メッセージ
log_info() {
  log $LOG_LEVEL_INFO "$1"
}

# 警告ログ
# 引数:
#   $1: メッセージ
log_warn() {
  log $LOG_LEVEL_WARN "$1"
}

# エラーログ
# 引数:
#   $1: メッセージ
log_error() {
  log $LOG_LEVEL_ERROR "$1"
}

# DRY-RUNモード用のログ
# 引数:
#   $1: メッセージ
log_dry_run() {
  log $LOG_LEVEL_INFO "【DRY-RUN】$1"
}

# 使用例
# log_debug "デバッグメッセージ"
# log_info "情報メッセージ"
# log_warn "警告メッセージ"
# log_error "エラーメッセージ"
# log_dry_run "DRY-RUNモードでの実行コマンド"
