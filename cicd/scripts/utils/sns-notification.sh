#!/bin/bash

# SNS通知関連の共通処理
# 使用方法: source ./sns-notification.sh

# ログ関数を読み込み
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/logging.sh"

# SNS通知が有効かどうかを判定する関数
# 戻り値:
#   0: 有効
#   1: 無効
is_sns_notification_enabled() {
  # 環境変数SNS_NOTIFICATION_ENABLEDが設定されていて、値がtrueの場合のみ有効
  if [ "${SNS_NOTIFICATION_ENABLED:-false}" = "true" ]; then
    return 0  # true
  else
    return 1  # false
  fi
}

# SNSトピックARNが設定されているかを確認する関数
# 戻り値:
#   0: 設定されている
#   1: 設定されていない
is_sns_topic_arn_set() {
  if [ -n "${SNS_TOPIC_ARN}" ]; then
    return 0  # true
  else
    return 1  # false
  fi
}

# SNSトピックにメッセージを送信する関数
# 引数:
#   $1: 件名
#   $2: メッセージ本文
send_sns_notification() {
  local subject="$1"
  local message="$2"
  
  # SNS通知が無効な場合は何もしない
  if ! is_sns_notification_enabled; then
    log_debug "SNS通知は無効です。メッセージは送信されません。"
    return 0
  fi
  
  # SNSトピックARNが設定されていない場合はエラー
  if ! is_sns_topic_arn_set; then
    log_error "SNS_TOPIC_ARNが設定されていません。SNS通知を送信できません。"
    return 1
  fi
  
  log_info "SNSトピック「${SNS_TOPIC_ARN}」にメッセージを送信します..."
  
  # DRY-RUNモードの場合は実際には送信しない
  if [ "${DRY_RUN:-false}" = "true" ]; then
    log_dry_run "SNSメッセージの送信をスキップします"
    log_dry_run "件名: $subject"
    log_dry_run "本文: $message"
    return 0
  fi
  
  # AWS CLIを使用してSNSトピックにメッセージを送信
  local result
  result=$(aws sns publish \
    --topic-arn "${SNS_TOPIC_ARN}" \
    --subject "${subject}" \
    --message "${message}" 2>&1)
  local exit_code=$?
  
  if [ $exit_code -ne 0 ]; then
    log_error "SNSメッセージの送信に失敗しました: $result"
    return 1
  fi
  
  log_info "SNSメッセージを送信しました: $result"
  return 0
}

# エラー通知を送信する関数
# 引数:
#   $1: エラーメッセージ
#   $2: 追加情報（オプション）
send_error_notification() {
  local error_message="$1"
  local additional_info="${2:-}"
  local environment="${ENVIRONMENT:-unknown}"
  local project_name="${CODEBUILD_PROJECT_NAME:-unknown}"
  local build_id="${CODEBUILD_BUILD_ID:-unknown}"
  local build_number="${CODEBUILD_BUILD_NUMBER:-unknown}"
  local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
  
  # 件名の作成
  local subject="[ERROR] ${environment} 環境のデプロイに失敗しました"
  
  # メッセージ本文の作成
  local message="デプロイエラー通知

環境: ${environment}
プロジェクト: ${project_name}
ビルドID: ${build_id}
ビルド番号: ${build_number}
タイムスタンプ: ${timestamp}

エラーメッセージ:
${error_message}
"

  # 追加情報がある場合は追加
  if [ -n "$additional_info" ]; then
    message="${message}

追加情報:
${additional_info}
"
  fi
  
  # CodeBuildコンソールへのリンクを追加
  local region="${AWS_REGION:-us-east-1}"
  local console_url="https://${region}.console.aws.amazon.com/codesuite/codebuild/projects/${project_name}/build/${build_id}?region=${region}"
  
  message="${message}

CodeBuildコンソールURL:
${console_url}
"
  
  # SNS通知を送信
  send_sns_notification "$subject" "$message"
  return $?
}

# 使用例
# if is_sns_notification_enabled; then
#   send_error_notification "デプロイに失敗しました" "追加のデバッグ情報"
# fi
