#!/bin/bash

# SNS通知機能のテスト用スクリプト
# 使用方法: ./test-sns-notification.sh [トピックARN]

# 共通処理の読み込み
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
UTILS_DIR="$SCRIPT_DIR/../scripts/utils"
source "$UTILS_DIR/logging.sh"
source "$UTILS_DIR/sns-notification.sh"
source "$UTILS_DIR/error-handling.sh"

# エラートラップの設定
setup_error_trap

# 引数の検証
if [ $# -gt 0 ]; then
  export SNS_TOPIC_ARN="$1"
  log_info "指定されたSNSトピックARN: $SNS_TOPIC_ARN"
else
  log_info "SNSトピックARNが指定されていません。環境変数から読み込みます。"
  if [ -z "$SNS_TOPIC_ARN" ]; then
    log_error "SNS_TOPIC_ARNが設定されていません。引数として指定するか、環境変数を設定してください。"
    echo "使用方法: $0 [トピックARN]"
    exit 1
  fi
fi

# SNS通知を有効化
export SNS_NOTIFICATION_ENABLED="true"

# テスト通知の送信
log_info "テスト通知を送信します..."
send_sns_notification "テスト通知" "これはSNS通知機能のテストメッセージです。\n\nタイムスタンプ: $(date -u +"%Y-%m-%dT%H:%M:%SZ")"

# エラー通知のテスト
log_info "エラー通知をテストします..."
send_error_notification "テストエラーメッセージ" "これはエラー通知機能のテストです。\n実際のエラーではありません。"

log_info "テスト完了"

# エラーハンドリング関数のテスト（コメントアウトされています）
# 以下のコードのコメントを外すと、実際にエラーが発生し、エラー通知が送信されます
# log_info "エラーハンドリング関数をテストします..."
# run_command "ls /nonexistent_directory" "存在しないディレクトリの一覧取得に失敗しました"

exit 0
