#!/bin/bash
set -e

# 空の変更ファイルリストに対するbuild.shの処理をテストするスクリプト

# テスト用のディレクトリに移動
cd $(dirname $0)
cd ../..

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER=TIS-DSDev
export GITHUB_REPO=tis-dlpf-app
export BASE_BRANCH=develop
export ENVIRONMENT=dev
export DRY_RUN=true
export CODEBUILD_SRC_DIR=$(pwd)

echo "Testing build.sh with empty changed files..."

# 一時ファイルの準備
TEMP_FILE1=/tmp/test_build_empty_changes_$(date +%s)_1.log

# テスト用のスクリプトを作成
echo "#!/bin/bash" > /tmp/test_empty_changes.sh
echo "set -e" >> /tmp/test_empty_changes.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_empty_changes.sh

# テスト対象の関数を抽出
cat << 'EOF' >> /tmp/test_empty_changes.sh

# テスト用の変数設定
PR_NUMBER=1234
CHANGED_FILES=""  # 空の変更ファイルリスト

echo "Testing with empty CHANGED_FILES..."
echo "PR_NUMBER: $PR_NUMBER"
echo "CHANGED_FILES: '$CHANGED_FILES'"

# Glue変更検出のテスト
echo "=== Testing Glue change detection ==="
if [ -z "$CHANGED_FILES" ]; then
  GLUE_CHANGES='{"HAS_GLUE_DEPENDENCY_CHANGES":false,"HAS_GLUE_SOURCE_CHANGES":false,"HAS_GLUE_JOB_SCRIPT_CHANGES":false}'
  echo "No changed files for PR #${PR_NUMBER}, skipping Glue change detection"
else
  echo "Would call detect-glue.sh with: $CHANGED_FILES"
fi

echo "GLUE_CHANGES: $GLUE_CHANGES"

# JSON解析のテスト
HAS_GLUE_DEPENDENCY_CHANGES_PR=$(echo $GLUE_CHANGES | jq -r '.HAS_GLUE_DEPENDENCY_CHANGES')
HAS_GLUE_SOURCE_CHANGES_PR=$(echo $GLUE_CHANGES | jq -r '.HAS_GLUE_SOURCE_CHANGES')
HAS_GLUE_JOB_SCRIPT_CHANGES_PR=$(echo $GLUE_CHANGES | jq -r '.HAS_GLUE_JOB_SCRIPT_CHANGES')

echo "Parsed Glue flags:"
echo "  HAS_GLUE_DEPENDENCY_CHANGES_PR: $HAS_GLUE_DEPENDENCY_CHANGES_PR"
echo "  HAS_GLUE_SOURCE_CHANGES_PR: $HAS_GLUE_SOURCE_CHANGES_PR"
echo "  HAS_GLUE_JOB_SCRIPT_CHANGES_PR: $HAS_GLUE_JOB_SCRIPT_CHANGES_PR"

# CloudFormation変更検出のテスト
echo "=== Testing CloudFormation change detection ==="
if [ -z "$CHANGED_FILES" ]; then
  CFN_CHANGES='{"HAS_CFN_CHANGES":false}'
  echo "No changed files for PR #${PR_NUMBER}, skipping CloudFormation change detection"
else
  echo "Would call detect-cfn.sh with: $CHANGED_FILES"
fi

echo "CFN_CHANGES: $CFN_CHANGES"

# JSON解析のテスト
HAS_CFN_CHANGES_PR=$(echo $CFN_CHANGES | jq -r '.HAS_CFN_CHANGES')
echo "Parsed CloudFormation flag:"
echo "  HAS_CFN_CHANGES_PR: $HAS_CFN_CHANGES_PR"

# Lambda変更検出のテスト
echo "=== Testing Lambda change detection ==="
if [ -z "$CHANGED_FILES" ]; then
  LAMBDA_CHANGES='{"HAS_LAMBDA_CHANGES":false,"CHANGED_LAMBDA_FUNCTIONS":""}'
  echo "No changed files for PR #${PR_NUMBER}, skipping Lambda change detection"
else
  echo "Would call detect-lambda.sh with: $CHANGED_FILES"
fi

echo "LAMBDA_CHANGES: $LAMBDA_CHANGES"

# JSON解析のテスト
HAS_LAMBDA_CHANGES_PR=$(echo $LAMBDA_CHANGES | jq -r '.HAS_LAMBDA_CHANGES')
CHANGED_LAMBDA_FUNCTIONS_PR=$(echo $LAMBDA_CHANGES | jq -r '.CHANGED_LAMBDA_FUNCTIONS')
echo "Parsed Lambda flags:"
echo "  HAS_LAMBDA_CHANGES_PR: $HAS_LAMBDA_CHANGES_PR"
echo "  CHANGED_LAMBDA_FUNCTIONS_PR: $CHANGED_LAMBDA_FUNCTIONS_PR"

echo "=== Test completed successfully ==="

EOF

# 実行権限の付与
chmod +x /tmp/test_empty_changes.sh

# スクリプトの実行
echo "Executing test script..."
/tmp/test_empty_changes.sh | tee $TEMP_FILE1

# 結果の確認
echo ""
echo "=== Test Results Summary ==="
echo "Expected all flags to be 'false' for empty change list:"

# 結果の検証
if grep -q "HAS_GLUE_DEPENDENCY_CHANGES_PR: false" $TEMP_FILE1 && \
   grep -q "HAS_GLUE_SOURCE_CHANGES_PR: false" $TEMP_FILE1 && \
   grep -q "HAS_GLUE_JOB_SCRIPT_CHANGES_PR: false" $TEMP_FILE1 && \
   grep -q "HAS_CFN_CHANGES_PR: false" $TEMP_FILE1 && \
   grep -q "HAS_LAMBDA_CHANGES_PR: false" $TEMP_FILE1; then
  echo "✅ All tests PASSED - Empty change list handled correctly"
  EXIT_CODE=0
else
  echo "❌ Some tests FAILED - Check the output above"
  EXIT_CODE=1
fi

# 一時ファイルの削除
rm -f $TEMP_FILE1 /tmp/test_empty_changes.sh
echo "一時ファイルを削除しました: $TEMP_FILE1, /tmp/test_empty_changes.sh"

echo "Empty changes test completed!"
exit $EXIT_CODE
