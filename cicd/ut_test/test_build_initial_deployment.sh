#!/bin/bash
set -e

# テスト用のディレクトリに移動
cd $(dirname $0)
cd ../..

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER=TIS-DSDev
export GITHUB_REPO=tis-dlpf-app
export BASE_BRANCH=develop
export ENVIRONMENT=dev
export DRY_RUN=true
export CODEBUILD_SRC_DIR=$(pwd)

# 一時ファイルの準備
TEMP_FILE1=/tmp/test_build_initial_deployment_$(date +%s)_1.log

echo "Fetching GitHub App token..."
export DEBUG_MODE=true
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials-dev | tail -n 1)

if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Failed to get installation token"
  exit 1
fi

echo "Token obtained successfully. Length: ${#INSTALLATION_TOKEN}"
echo "Token preview: ${INSTALLATION_TOKEN:0:10}..."

echo "Testing build.sh script with initial deployment (1970-01-01T00:00:00Z)..."

# 環境変数の設定
export deploy_baseline_time="1970-01-01T00:00:00Z"
export INSTALLATION_TOKEN=$INSTALLATION_TOKEN

# build.shの実行（一部のみ）
echo "#!/bin/bash" > /tmp/test_build.sh
echo "set -e" >> /tmp/test_build.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_build.sh
echo "deploy_baseline_time=\"1970-01-01T00:00:00Z\"" >> /tmp/test_build.sh

# 初回デプロイの処理部分を抽出
sed -n '/# 初回デプロイ（1970-01-01T00:00:00Z）の場合は、すべてのデプロイ対象をTRUEにする/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 結果の出力
echo "echo \"HAS_GLUE_DEPENDENCY_CHANGES: \$HAS_GLUE_DEPENDENCY_CHANGES\"" >> /tmp/test_build.sh
echo "echo \"HAS_GLUE_SOURCE_CHANGES: \$HAS_GLUE_SOURCE_CHANGES\"" >> /tmp/test_build.sh
echo "echo \"HAS_GLUE_JOB_SCRIPT_CHANGES: \$HAS_GLUE_JOB_SCRIPT_CHANGES\"" >> /tmp/test_build.sh
echo "echo \"HAS_CFN_CHANGES: \$HAS_CFN_CHANGES\"" >> /tmp/test_build.sh
echo "echo \"LAST_PROCESSED_PR: \$LAST_PROCESSED_PR\"" >> /tmp/test_build.sh
echo "echo \"PR_COUNT: \$PR_COUNT\"" >> /tmp/test_build.sh
echo "echo \"MERGED_PRS: \$MERGED_PRS\"" >> /tmp/test_build.sh

# 実行権限の付与
chmod +x /tmp/test_build.sh

# スクリプトの実行
/tmp/test_build.sh | tee $TEMP_FILE1

# 結果の確認
echo "Test results:"
grep -E "HAS_GLUE_DEPENDENCY_CHANGES|HAS_GLUE_SOURCE_CHANGES|HAS_GLUE_JOB_SCRIPT_CHANGES|HAS_CFN_CHANGES|LAST_PROCESSED_PR|PR_COUNT|MERGED_PRS" $TEMP_FILE1

# 一時ファイルの削除
rm -f $TEMP_FILE1 /tmp/test_build.sh
echo "一時ファイルを削除しました: $TEMP_FILE1, /tmp/test_build.sh"

echo "Tests completed!"
