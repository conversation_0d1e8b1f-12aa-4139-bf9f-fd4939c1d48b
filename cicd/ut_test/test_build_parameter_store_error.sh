#!/bin/bash
set -e

# テスト用のディレクトリに移動
cd $(dirname $0)
cd ../..

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER=TIS-DSDev
export GITHUB_REPO=tis-dlpf-app
export BASE_BRANCH=develop
export ENVIRONMENT=dev
export DRY_RUN=true
export CODEBUILD_SRC_DIR=$(pwd)

# 一時ファイルの準備
TEMP_FILE1=/tmp/test_build_parameter_store_error_$(date +%s)_1.log
TEMP_FILE2=/tmp/test_build_parameter_store_error_$(date +%s)_2.log
TEMP_FILE3=/tmp/test_build_parameter_store_error_$(date +%s)_3.log

echo "Fetching GitHub App token..."
export DEBUG_MODE=true
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials-dev | tail -n 1)

if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Failed to get installation token"
  exit 1
fi

echo "Token obtained successfully. Length: ${#INSTALLATION_TOKEN}"
echo "Token preview: ${INSTALLATION_TOKEN:0:10}..."

echo "Testing build.sh script with invalid Parameter Store values (time-based)..."

# テスト1: Parameter Storeから値を取得できない場合
echo "Test 1: Parameter Store not found"

# 環境変数の設定
export deploy_baseline_time="/test/nonexistent/parameter/store/path"
export INSTALLATION_TOKEN=$INSTALLATION_TOKEN

# build.shの実行（一部のみ）
echo "#!/bin/bash" > /tmp/test_build.sh
echo "set -e" >> /tmp/test_build.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_build.sh

# Parameter Store関数をモック
echo "function aws() {" >> /tmp/test_build.sh
echo "  if [[ \"\$1 \$2\" == \"ssm get-parameter\" ]]; then" >> /tmp/test_build.sh
echo "    return 1" >> /tmp/test_build.sh
echo "  else" >> /tmp/test_build.sh
echo "    command aws \"\$@\"" >> /tmp/test_build.sh
echo "  fi" >> /tmp/test_build.sh
echo "}" >> /tmp/test_build.sh

# デプロイ基準時刻取得部分を抽出
sed -n '/# 最後にデプロイされた基準時刻の取得/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 実行権限の付与
chmod +x /tmp/test_build.sh

# スクリプトの実行（エラーが発生することを期待）
if /tmp/test_build.sh > $TEMP_FILE1 2>&1; then
  echo "Test 1 failed: Expected script to fail but it succeeded"
  exit 1
else
  echo "Test 1 passed: Script failed as expected"
  grep -E "Parameter Storeから値を取得できませんでした" $TEMP_FILE1 && echo "Error message found"
fi

# テスト2: Parameter Storeの値が有効なJSONでない場合
echo "Test 2: Invalid JSON in Parameter Store"

# 環境変数の設定
export FROM_PR="/test/parameter/store/path"
export TO_PR=0
export INSTALLATION_TOKEN=$INSTALLATION_TOKEN

# build.shの実行（一部のみ）
echo "#!/bin/bash" > /tmp/test_build.sh
echo "set -e" >> /tmp/test_build.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_build.sh

# Parameter Store関数をモック
echo "function aws() {" >> /tmp/test_build.sh
echo "  if [[ \"\$1 \$2\" == \"ssm get-parameter\" ]]; then" >> /tmp/test_build.sh
echo "    echo 'invalid json'" >> /tmp/test_build.sh
echo "    return 0" >> /tmp/test_build.sh
echo "  else" >> /tmp/test_build.sh
echo "    command aws \"\$@\"" >> /tmp/test_build.sh
echo "  fi" >> /tmp/test_build.sh
echo "}" >> /tmp/test_build.sh

# PR情報取得部分を抽出
sed -n '/# 最後にデプロイされたPR情報の取得/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 実行権限の付与
chmod +x /tmp/test_build.sh

# スクリプトの実行（エラーが発生することを期待）
if /tmp/test_build.sh > $TEMP_FILE2 2>&1; then
  echo "Test 2 failed: Expected script to fail but it succeeded"
  exit 1
else
  echo "Test 2 passed: Script failed as expected"
  grep -E "Parameter Storeの値が有効なJSON形式ではありません" $TEMP_FILE2 && echo "Error message found"
fi

# テスト3: JSONに'number'フィールドがない場合
echo "Test 3: Missing 'number' field in JSON"

# 環境変数の設定
export FROM_PR="/test/parameter/store/path"
export TO_PR=0
export INSTALLATION_TOKEN=$INSTALLATION_TOKEN

# build.shの実行（一部のみ）
echo "#!/bin/bash" > /tmp/test_build.sh
echo "set -e" >> /tmp/test_build.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_build.sh

# Parameter Store関数をモック
echo "function aws() {" >> /tmp/test_build.sh
echo "  if [[ \"\$1 \$2\" == \"ssm get-parameter\" ]]; then" >> /tmp/test_build.sh
echo "    echo '{\"merged_at\": \"2025-05-23T19:00:00Z\"}'" >> /tmp/test_build.sh
echo "    return 0" >> /tmp/test_build.sh
echo "  else" >> /tmp/test_build.sh
echo "    command aws \"\$@\"" >> /tmp/test_build.sh
echo "  fi" >> /tmp/test_build.sh
echo "}" >> /tmp/test_build.sh

# PR情報取得部分を抽出
sed -n '/# 最後にデプロイされたPR情報の取得/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 実行権限の付与
chmod +x /tmp/test_build.sh

# スクリプトの実行（エラーが発生することを期待）
if /tmp/test_build.sh > $TEMP_FILE3 2>&1; then
  echo "Test 3 failed: Expected script to fail but it succeeded"
  exit 1
else
  echo "Test 3 passed: Script failed as expected"
  grep -E "Parameter Storeの値に有効な'number'フィールドがありません" $TEMP_FILE3 && echo "Error message found"
fi

# 一時ファイルの削除
rm -f $TEMP_FILE1 $TEMP_FILE2 $TEMP_FILE3 /tmp/test_build.sh
echo "一時ファイルを削除しました: $TEMP_FILE1, $TEMP_FILE2, $TEMP_FILE3, /tmp/test_build.sh"

echo "All tests completed successfully!"
