#!/bin/bash
# Lambda関数デプロイ機能のユニットテスト

# 色付きログ出力用の設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;36m'
NC='\033[0m' # No Color

# テスト結果カウンター
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# ログ出力関数
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
  TESTS_PASSED=$((TESTS_PASSED + 1))
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
  TESTS_FAILED=$((TESTS_FAILED + 1))
}

log_warning() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

# テスト関数
run_test() {
  local test_name="$1"
  local test_command="$2"
  local expected_result="$3"
  local test_description="$4"
  
  TESTS_TOTAL=$((TESTS_TOTAL + 1))
  
  echo ""
  echo "========================================================"
  echo "テスト #$TESTS_TOTAL: $test_name"
  echo "説明: $test_description"
  echo "コマンド: $test_command"
  echo "期待結果: $expected_result"
  echo "========================================================"
  
  # コマンド実行
  eval "$test_command" > /tmp/test_output.log 2>&1
  local exit_code=$?
  
  # 結果確認
  if [ $exit_code -eq 0 ]; then
    if grep -q "$expected_result" /tmp/test_output.log; then
      log_success "テスト成功: 期待通りの結果が得られました"
      cat /tmp/test_output.log | grep -E "$expected_result" --color=always
    else
      log_error "テスト失敗: 期待する結果が含まれていません"
      echo "実際の出力:"
      cat /tmp/test_output.log
    fi
  else
    log_error "テスト失敗: コマンドがエラーで終了しました (終了コード: $exit_code)"
    echo "エラー出力:"
    cat /tmp/test_output.log
  fi
}

# テスト実行前の準備
log_info "Lambda関数デプロイ機能のユニットテストを開始します"

# プロジェクトルートディレクトリを取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

# テスト1: Lambda関数デプロイスクリプトの存在確認
run_test "Lambda関数デプロイスクリプトの存在確認" \
  "ls -l ${PROJECT_ROOT}/cloudformation/scripts/lambda/deploy_lambda.sh" \
  "deploy_lambda.sh" \
  "Lambda関数デプロイスクリプトが存在することを確認します"

# テスト2: Lambda関数デプロイスクリプトの実行権限確認
run_test "Lambda関数デプロイスクリプトの実行権限確認" \
  "ls -l ${PROJECT_ROOT}/cloudformation/scripts/lambda/deploy_lambda.sh" \
  "x" \
  "Lambda関数デプロイスクリプトに実行権限があることを確認します"

# テスト3: Lambda関数デプロイスクリプトのドライラン実行
run_test "Lambda関数デプロイスクリプトのドライラン実行" \
  "${PROJECT_ROOT}/cloudformation/scripts/lambda/deploy_lambda.sh dev DLPF_RETURN_PARALLEL_NUM_ARRAY true" \
  "【ドライラン】Lambda関数のコードを更新します" \
  "Lambda関数デプロイスクリプトがドライランモードで正常に動作することを確認します"

# テスト4: CloudFormationデプロイスクリプトのLambda関数デプロイ機能（ドライラン）
run_test "CloudFormationデプロイスクリプトのLambda関数デプロイ機能（ドライラン）" \
  "cd ${PROJECT_ROOT}/cloudformation && ./scripts/cfn_deploy.sh -e dev -d -n lambda DLPF_RETURN_PARALLEL_NUM_ARRAY" \
  "ドライラン.*実際のデプロイは行いません" \
  "CloudFormationデプロイスクリプトがLambda関数をドライランモードで正常に処理することを確認します"

# テスト結果の表示
echo ""
echo "========================================================"
echo "テスト結果サマリー"
echo "========================================================"
echo "実行したテスト数: $TESTS_TOTAL"
echo -e "${GREEN}成功したテスト数: $TESTS_PASSED${NC}"
echo -e "${RED}失敗したテスト数: $TESTS_FAILED${NC}"

# 終了コードの設定
if [ $TESTS_FAILED -eq 0 ]; then
  echo -e "${GREEN}すべてのテストが成功しました！${NC}"
  exit 0
else
  echo -e "${RED}テストに失敗があります。ログを確認してください。${NC}"
  exit 1
fi
