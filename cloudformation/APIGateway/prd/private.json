{"openapi": "3.0.1", "info": {"title": "apigw-restapi-prd-dlpf-private", "version": "2025-04-17T08:44:56Z"}, "servers": [{"url": "https://dlpfapi.dhc.co.jp"}], "paths": {"/auth/login": {"post": {"responses": {"200": {"description": "200 response", "content": {}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://cognito-idp.ap-northeast-1.amazonaws.com/", "responses": {"default": {"statusCode": "200", "responseTemplates": {"application/json": "{\n  \"access_token\": $input.json('$.AuthenticationResult.AccessToken'),\n  \"expires_in\": $input.json('$.AuthenticationResult.ExpiresIn'),\n  \"token_type\": $input.json('$.AuthenticationResult.TokenType')\n}"}}}, "requestParameters": {"integration.request.header.X-Amz-Target": "'AWSCognitoIdentityProviderService.InitiateAuth'", "integration.request.header.Content-Type": "'application/x-amz-json-1.1'"}, "requestTemplates": {"application/json": "{\n  \"AuthFlow\": \"USER_PASSWORD_AUTH\",\n  \"ClientId\": $input.json('$.client_id'),\n  \"AuthParameters\": {\n    \"USERNAME\": $input.json('$.username'),\n    \"PASSWORD\": $input.json('$.password')\n  }\n}"}, "passthroughBehavior": "when_no_templates", "timeoutInMillis": 29000, "type": "http"}}}, "/api/point-and-rank-orders": {"post": {"security": [{"dlpf-auth": ["aws.cognito.signin.user.admin", "https://api.dlpf.com/read.data"]}], "x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "POST", "uri": "http://nlb-prd-dlpf-alb-api-01-5fa91dfd09462b68.elb.ap-northeast-1.amazonaws.com/api/point-and-rank-orders", "responses": {"default": {"statusCode": "200"}}, "connectionType": "VPC_LINK", "passthroughBehavior": "when_no_match", "timeoutInMillis": 29000, "type": "http_proxy"}}}, "/api/healthcheck": {"get": {"responses": {"400": {"description": "400 response", "content": {}}, "500": {"description": "500 response", "content": {}}, "200": {"description": "200 response", "content": {}}}, "x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "GET", "uri": "http://nlb-prd-dlpf-alb-api-01-5fa91dfd09462b68.elb.ap-northeast-1.amazonaws.com/api/healthcheck", "responses": {"4\\d{2}": {"statusCode": "400"}, "default": {"statusCode": "200"}, "5\\d{2}": {"statusCode": "500"}}, "connectionType": "VPC_LINK", "passthroughBehavior": "when_no_match", "timeoutInMillis": 29000, "type": "http"}}}, "/api/subscription-histories": {"get": {"security": [{"dlpf-auth": ["aws.cognito.signin.user.admin", "https://api.dlpf.com/read.data"]}], "x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "GET", "uri": "http://nlb-prd-dlpf-alb-api-01-5fa91dfd09462b68.elb.ap-northeast-1.amazonaws.com/api/subscription-histories", "responses": {"default": {"statusCode": "200"}}, "connectionType": "VPC_LINK", "passthroughBehavior": "when_no_templates", "timeoutInMillis": 29000, "type": "http_proxy"}}}, "/api/purchase-orders/details": {"get": {"security": [{"dlpf-auth": ["https://api.dlpf.com/read.data", "aws.cognito.signin.user.admin"]}], "x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "GET", "uri": "http://nlb-prd-dlpf-alb-api-01-5fa91dfd09462b68.elb.ap-northeast-1.amazonaws.com/api/purchase-orders/details", "responses": {"default": {"statusCode": "200"}}, "connectionType": "VPC_LINK", "passthroughBehavior": "when_no_templates", "timeoutInMillis": 29000, "type": "http_proxy"}}}, "/api/purchase-orders/headers": {"get": {"security": [{"dlpf-auth": ["https://api.dlpf.com/read.data", "aws.cognito.signin.user.admin"]}], "x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "GET", "uri": "http://nlb-prd-dlpf-alb-api-01-5fa91dfd09462b68.elb.ap-northeast-1.amazonaws.com/api/purchase-orders/headers", "responses": {"default": {"statusCode": "200"}}, "connectionType": "VPC_LINK", "passthroughBehavior": "when_no_templates", "timeoutInMillis": 29000, "type": "http_proxy"}}}, "/api/customers": {"post": {"security": [{"dlpf-auth": ["aws.cognito.signin.user.admin", "https://api.dlpf.com/read.data"]}], "x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "POST", "uri": "http://nlb-prd-dlpf-alb-api-01-5fa91dfd09462b68.elb.ap-northeast-1.amazonaws.com/api/customers", "responses": {"default": {"statusCode": "200"}}, "connectionType": "VPC_LINK", "passthroughBehavior": "when_no_match", "timeoutInMillis": 29000, "type": "http_proxy"}}}, "/api": {"get": {"x-amazon-apigateway-integration": {"connectionId": "yyvb4e", "httpMethod": "GET", "uri": "http://internal-alb-prd-dlpf-fargate-api-01-723245635.ap-northeast-1.elb.amazonaws.com:80", "requestParameters": {"integration.request.header.X-Authorization": "'static'", "integration.request.header.X-Foo": "'Bar'"}, "connectionType": "VPC_LINK", "requestTemplates": {"application/xml": "#set($inputRoot = $input.path('$'))\n{ }"}, "passthroughBehavior": "when_no_match", "timeoutInMillis": 29000, "contentHandling": "CONVERT_TO_TEXT", "type": "http"}}}, "/auth/login/oauth": {"post": {"x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://ap-northeast-1julq0l5ws.auth.ap-northeast-1.amazoncognito.com/oauth2/token", "responses": {"default": {"statusCode": "200"}}, "requestTemplates": {"application/json": "{\n  \"AuthFlow\": \"USER_PASSWORD_AUTH\",\n  \"ClientId\": \"$input.json('$.client_id')\",\n  \"AuthParameters\": {\n    \"USERNAME\": \"$input.json('$.username')\",\n    \"PASSWORD\": \"$input.json('$.password')\"\n  }\n}"}, "passthroughBehavior": "when_no_templates", "timeoutInMillis": 29000, "type": "http_proxy"}}}}, "components": {"securitySchemes": {"dlpf-auth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-amazon-apigateway-authtype": "cognito_user_pools", "x-amazon-apigateway-authorizer": {"providerARNs": ["arn:aws:cognito-idp:ap-northeast-1:879381279381:userpool/ap-northeast-1_JulQ0L5wS"], "type": "cognito_user_pools"}}}}, "x-amazon-apigateway-policy": {"Version": "2012-10-17", "Statement": [{"Effect": "<PERSON><PERSON>", "Principal": "*", "Action": "execute-api:Invoke", "Resource": "arn:aws:execute-api:ap-northeast-1:879381279381:r01gwbiuy6/*", "Condition": {"StringNotEquals": {"aws:SourceVpce": "vpce-067436d06640b9d70"}}}, {"Effect": "Allow", "Principal": "*", "Action": "execute-api:Invoke", "Resource": "arn:aws:execute-api:ap-northeast-1:879381279381:r01gwbiuy6/*"}]}}