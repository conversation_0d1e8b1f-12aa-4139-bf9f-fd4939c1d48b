# CloudFormation テンプレート

このディレクトリには、AWS リソースをデプロイするための CloudFormation テンプレートが含まれています。

## CodeBuild ランナー

CodeBuild ランナーの設定は `.github/workflows/runner-setup/cloudformation/templates/codebuild/codebuild-runner.yml` にあります。

## デプロイ方法

CloudFormation テンプレートのデプロイには、`cloudformation/scripts/cfn_deploy.sh` スクリプトを使用します。

```bash
# 開発環境へのデプロイ例
./cloudformation/scripts/cfn_deploy.sh -e dev -n -y <リソースタイプ> <リソース名>

# 検証環境へのデプロイ例
./cloudformation/scripts/cfn_deploy.sh -e stg -n -y <リソースタイプ> <リソース名>
```

## CodeBuild ランナーのデプロイ

CodeBuild ランナーのデプロイには、AWS CLI を直接使用します：

```bash
# テンプレートの検証
aws cloudformation validate-template --template-body file:///.github/workflows/runner-setup/cloudformation/templates/codebuild/codebuild-runner.yml

# スタックの更新
aws cloudformation update-stack --stack-name dlpf-dev-codebuild-runner --template-body file:///.github/workflows/runner-setup/cloudformation/templates/codebuild/codebuild-runner.yml --parameters <パラメータ> --capabilities CAPABILITY_NAMED_IAM
```
