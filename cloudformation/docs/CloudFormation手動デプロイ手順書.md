# CloudFormation手動デプロイ手順書

## 1. 概要

本手順書は、Pipeline経由ではなく手動でCloudFormationのデプロイを実施する方法について説明します。

### 1.1 前提条件

- AWS CLIがインストールされていること
- 適切なAWS認証情報が設定されていること
- デプロイ対象の環境（dev/stg）に対する適切な権限があること

## 2. ディレクトリ構成

```plaintext
cloudformation/
├── environments/          # 環境固有の設定
│   ├── dev/
│   │   └── parameters/   # 開発環境のパラメータファイル
│   │       ├── default.json              # 環境共通パラメータ
│   │       ├── glue-connector/           # リソースタイプごとの設定
│   │       │   └── default.json          # タイプ共通パラメータ
│   │       └── step-functions/
│   │           └── specific-stack.json   # リソース固有パラメータ
│   └── stg/              # ステージング環境（devと同様の構造）
├── templates/            # CloudFormationテンプレート
│   ├── step-functions/   # ステートマシン定義
│   │   └── dlpf_JN_CP001-DF01_001.yaml
│   ├── glue-job/        # Glueジョブ定義
│   ├── event-bridge/    # EventBridgeルール
│   └── ...
└── scripts/             # デプロイスクリプト
    └── cfn_deploy.sh    # メインのデプロイスクリプト
```

### 2.1 パラメータファイルの優先順位

1. リソース固有パラメータ（最優先）
   - 例：`environments/dev/parameters/step-functions/specific-stack.json`
2. リソースタイプ共通パラメータ
   - 例：`environments/dev/parameters/step-functions/default.json`
3. 環境共通パラメータ（最後に適用）
   - 例：`environments/dev/parameters/default.json`

## 3. デプロイスクリプトの使い方

### 3.1 基本的な使い方

```bash
./cloudformation/scripts/cfn_deploy.sh [オプション] <リソースタイプ> <リソース名>
```

### 3.2 主なオプション

| オプション | 説明 | 使用例 |
|----------|------|--------|
| -e, --environment | デプロイ環境の指定（dev/stg） | -e dev |
| -v, --validate | テンプレート検証のみ実行 | -v |
| -d, --dry-run | 実際のデプロイを行わず内容確認 | -d |
| --diff | 環境間のパラメータ差分を表示 | --diff dev stg |
| -y, --yes | 対話的な確認をスキップ | -y |
| -n, --no-changeset | 変更セットの作成をスキップし直接デプロイ | -n |
| -p, --params | カスタムパラメータファイル指定 | -p file.json |
| -t, --template | カスタムテンプレートファイル指定 | -t file.yaml |
| --stack-name | カスタムスタック名を指定 | --stack-name name |

### 3.3 GroupDeployモード（リソースタイプ一括デプロイ）

特定のリソースタイプに属する全テンプレートを一括でデプロイすることができます。

使用方法：
```bash
# リソース名を省略して実行
./cloudformation/scripts/cfn_deploy.sh [オプション] <リソースタイプ>

# 例：event-bridge の全テンプレートをデプロイ
./cloudformation/scripts/cfn_deploy.sh -e dev event-bridge

# 例：glue-job の全テンプレートをデプロイ（確認プロンプトスキップ）
./cloudformation/scripts/cfn_deploy.sh -e stg -y glue-job
```

注意事項：
- デプロイ前に、対象となるテンプレート一覧が表示されます
- インタラクティブモードでは、一括デプロイ前に確認プロンプトが表示されます
- エラーが発生した場合、その時点でデプロイ処理が中断されます
- 実行結果として、成功・失敗の集計と失敗したテンプレートのリストが表示されます

### 3.4 リソースタイプ一覧

- step-functions：ステートマシン
- glue-job：Glueジョブ
- event-bridge：EventBridgeルール
- glue-connector：Glue接続
- secrets-manager：Secrets
- system-manager：SSMパラメータ
- security-group：セキュリティグループ
- cognito：Cognito
- lambda：Lambda関数

## 4. デプロイ手順

### 4.1 新規デプロイの場合

1. テンプレートの検証
```bash
# 例：Step Functionsのテンプレート検証
./cloudformation/scripts/cfn_deploy.sh -v -e dev step-functions dlpf_JN_CP001-DF01_001
```

2. デプロイ内容の確認（ドライラン）
```bash
./cloudformation/scripts/cfn_deploy.sh -d -e dev step-functions dlpf_JN_CP001-DF01_001
```

3. 実際のデプロイ実行
```bash
# 変更セットをスキップする（推奨）
./cloudformation/scripts/cfn_deploy.sh -e dev -n step-functions dlpf_JN_CP001-DF01_001
```

### 4.2 更新デプロイの場合

1. 環境間の差分確認（必要に応じて）
```bash
./cloudformation/scripts/cfn_deploy.sh --diff dev stg step-functions dlpf_JN_CP001-DF01_001
```

2. テンプレートの検証
```bash
./cloudformation/scripts/cfn_deploy.sh -v -e dev step-functions dlpf_JN_CP001-DF01_001
```

3. 変更内容の確認と適用
```bash
# 変更セットをスキップする（推奨）
./cloudformation/scripts/cfn_deploy.sh -e dev -n step-functions dlpf_JN_CP001-DF01_001
```

## 5. よくあるトラブルと対処方法

### 5.1 テンプレートファイルが見つからない場合

エラーメッセージ:
```
ERROR (Code: 2): テンプレートファイルが見つかりません
```

対処方法:
- リソース名が正しいか確認
- テンプレートファイルが所定のディレクトリにあるか確認
- ファイル名の大文字小文字を確認
- Step Functionsの場合、プレフィックス（dlpf_JN_）が付いているか確認

### 5.2 パラメータエラーの場合

エラーメッセージ:
```
Required parameter 'XXX' is missing
```

対処方法:
1. 必要なパラメータファイルの確認
```bash
ls -l cloudformation/environments/dev/parameters/
ls -l cloudformation/environments/dev/parameters/<リソースタイプ>/
```

2. パラメータファイルの内容確認
```bash
cat cloudformation/environments/dev/parameters/default.json
```

3. 環境差分の確認
```bash
./cloudformation/scripts/cfn_deploy.sh --diff dev stg <リソースタイプ> <リソース名>
```

### 5.3 スタック名が規定のネーミングルールと異なる場合

既存のスタック名を確認
```bash
# すべてのスタックを一覧表示
aws cloudformation list-stacks \
  --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE \
  --query 'StackSummaries[*].{Name:StackName,Status:StackStatus}' \
  --output table

# 特定のキーワードを含むスタックを検索（例：dlpf）
aws cloudformation list-stacks \
  --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE \
  --query 'StackSummaries[?contains(StackName, `dlpf`)].{Name:StackName,Status:StackStatus}' \
  --output table
```

対応方法：
1. 既存のスタック名を使用したデプロイ
```bash
./cloudformation/scripts/cfn_deploy.sh --stack-name existing-stack-name -e dev step-functions dlpf_JN_CP001-DF01_001
```

2. 新しいスタック名でデプロイ（推奨）
```bash
./cloudformation/scripts/cfn_deploy.sh -e dev step-functions dlpf_JN_CP001-DF01_001
```

## 6. 注意事項

1. **本番環境へのデプロイ**
   - 本番環境へのデプロイは原則としてPipeline経由で実施
   - 手動デプロイは開発環境とステージング環境のみで実施

2. **デプロイ前の確認事項**
   - 必ずテンプレート検証（-v）を実施
   - 可能な限りドライラン（-d）で変更内容を確認
   - 更新の場合は環境間差分（--diff）で変更箇所を確認

## 7. 参考：リソースタイプ別のデプロイ例

### 7.1 Step Functions

```bash
# 開発環境へのデプロイ
./cloudformation/scripts/cfn_deploy.sh -e dev -n step-functions dlpf_JN_CP001-DF01_001

# ステージング環境へのデプロイ（検証後）
./cloudformation/scripts/cfn_deploy.sh -e stg -n step-functions dlpf_JN_CP001-DF01_001
```

### 7.2 Glue Job

```bash
# 開発環境へのデプロイ（変更セットスキップ）
./cloudformation/scripts/cfn_deploy.sh -e dev -n glue-job dlpf_job_db_to_file

# ステージング環境へのデプロイ（変更セットスキップ）
./cloudformation/scripts/cfn_deploy.sh -e stg -n glue-job dlpf_job_db_to_file

# パラメータ確認付きデプロイ
./cloudformation/scripts/cfn_deploy.sh -d -e dev glue-job dlpf_job_db_to_file
```

### 7.3 Glue Connector

```bash
# パラメータ確認
./cloudformation/scripts/cfn_deploy.sh --diff dev stg glue-connector dlpf_glue_connection

# 開発環境へのデプロイ（検証後、変更セットスキップ）
./cloudformation/scripts/cfn_deploy.sh -e dev -n glue-connector dlpf_glue_connection
```

### 7.4 Lambda関数

Lambda関数をデプロイする場合、CloudFormationテンプレートと実際のLambda関数のコードの両方がデプロイされます。

```bash
# 開発環境へのLambda関数デプロイ
./cloudformation/scripts/cfn_deploy.sh -e dev -n -y lambda DLPF_RETURN_PARALLEL_NUM_ARRAY

# ステージング環境へのLambda関数デプロイ
./cloudformation/scripts/cfn_deploy.sh -e stg -n -y lambda DLPF_RETURN_PARALLEL_NUM_ARRAY

# ドライランモードでデプロイ内容確認
./cloudformation/scripts/cfn_deploy.sh -d -e dev lambda DLPF_RETURN_PARALLEL_NUM_ARRAY
