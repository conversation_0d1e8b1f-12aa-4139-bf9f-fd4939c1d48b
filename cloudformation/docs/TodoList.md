# CloudFormation開発環境検証 - ToDo List

## 全般
- [x] スタック名の生成ロジックが正しく動作することを確認
- [x] パラメータファイルの優先順位（環境共通 → リソースタイプ共通 → リソース固有）が正しく機能

## StepFunctions
- [x] テンプレート検証が正常に完了
- [x] パラメータ（AccountId, Environment, StateMachineName）が正しく設定されている

## Glue Connector
- [x] テンプレート検証が正常に完了
- [x] パラメータファイルのマージが正常に動作
- [x] 開発環境のパラメータ値を実際の値に更新:
  - SubnetId: subnet-010fa66d42d0277ac (snet-dev-dlpf-pri-a)
  - SGForGlueId: sg-024f813acb5d88ad9 (sgroup-dev-dlpf-glue-01)
  - SGForDbId: sg-0653c5f4ff75fa9c7 (sgroup-dev-dlpf-dbins-101)
  - InstanceId: dbins-dev-dlpf-101-b.crm0cg6q8tmp.ap-northeast-1.rds.amazonaws.com
  - DatabasePassword: "password" (注: 本番環境では適切なパスワードに変更が必要)
  - SGForFTPId: sg-024f813acb5d88ad9 (temporarily using Glue SG)

## Glue Job
- [x] 全テンプレートの検証が正常に完了:
  - dlpf_job_db_to_file
  - dlpf_job_api_to_file
  - dlpf_job_bulk_api_register
  - dlpf_job_convert_character_encoding
  - dlpf_job_convert_format
  - dlpf_job_get_file
  - dlpf_job_internal_db_clear
  - dlpf_job_internal_db_import
  - dlpf_job_send_file
- [x] 環境共通パラメータ（AccountId, Environment）のみを使用
- [x] IAMリソースが含まれていないことを確認
- [x] すべてのテンプレートで同じパラメータパターンを使用していることを確認

## System Manager
- [x] テンプレート検証が正常に完了
- [x] AccountIdパラメータが適切に使用されている
- [x] 環境変数の設定が適切
- [x] セキュリティ上の問題なし

## Security Group
- [x] テンプレート検証が正常に完了
- [ ] VpcIdパラメータの追加が必要
  - 環境共通パラメータファイルに追加
  - または、リソースタイプ共通パラメータファイルの作成

## 要確認事項 (担当者と協議が必要)
### SecretsManager （優先度：高）
- [ ] セキュリティ上の重大な問題
  - パスワードがハードコードされている
  - 環境固有の値（ホスト名など）がハードコードされている
- [ ] DLPFDBSecretがコメントアウトされている理由の確認
- [ ] 推奨される対応方法：
  1. 機密情報のパラメータ化
     - 機密値はパラメータストアやSecretsManagerで管理
     - テンプレートからは参照のみ行う
     - 環境ごとに異なる値を設定可能に
  2. 環境固有の値の分離
     - ホスト名などの環境固有値はパラメータ化
     - 環境ごとのパラメータファイルで管理
     - テンプレート内にハードコードしない
  3. セキュアな値の管理方法
     - 本番環境の機密情報は専用のSecretsManagerで管理
     - 開発環境とステージング環境で異なる値を使用
     - アクセス制御を適切に設定

### EventBridge
- [ ] テンプレートの構造に問題（Resourcesセクションの欠落）
- [ ] StepFunction ARNの参照方法の確認
- [ ] IAMロールの設定方法の確認
- [ ] 各テンプレートの検証を担当者と実施

### Cognito
- [ ] テンプレートの内容確認
- [ ] 要件に合わせた設定の確認
- [ ] 検証方法の確認

## 検証結果サマリー
### 正常確認済み
- StepFunctions: すべてのテンプレートが正常
- Glue Jobs: すべてのテンプレートが正常
- Glue Connector: パラメータ値の設定要
- System Manager: 問題なし
- Security Group: VpcIdパラメータの追加要

### 要対応
- SecretsManager: セキュリティ上の重大な問題あり（最優先）
- EventBridge: テンプレート構造の問題
- Cognito: 要件確認必要

## 注記
- FTP用のSecurity Groupは一時的にGlue用のものを使用。必要に応じて専用のSGを作成する必要あり。
- DatabasePasswordは開発環境でも適切な値に変更することを推奨。
- Glue JobテンプレートはIAMリソースを含まないことを確認。
- すべてのGlue Jobテンプレートで同じパラメータパターンを使用していることを確認。
- SecretsManagerのテンプレートには重大なセキュリティ上の問題があり、早急な対応が必要。
- Security GroupのVpcIdパラメータ追加が必要。