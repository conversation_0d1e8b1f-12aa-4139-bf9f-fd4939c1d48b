#!/bin/bash
# set -x # デバッグ用トレース出力 (コメントアウト)
#
# CloudFormation デプロイスクリプト
#
# 概要:
#   AWS CloudFormationスタックのデプロイを管理するスクリプト
#   複数のテンプレートの一括デプロイ（GroupDeploy）をサポート
#
# 依存関係:
#   - AWS CLI v2
#   - jq

# スクリプトディレクトリの取得とグローバル変数の設定
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
CFN_ROOT="$(dirname "$SCRIPT_DIR")"
LIB_DIR="$SCRIPT_DIR/lib" # ライブラリディレクトリのパスを修正

# ライブラリの読み込み順序を依存関係に合わせて設定
source "$LIB_DIR/common.sh" || { echo "Error: Failed to load common.sh from $LIB_DIR"; exit 1; }
source "$LIB_DIR/parameter.sh" || { echo "Error: Failed to load parameter.sh from $LIB_DIR"; exit 1; }
source "$LIB_DIR/cloudformation.sh" || { echo "Error: Failed to load cloudformation.sh from $LIB_DIR"; exit 1; }
source "$LIB_DIR/template.sh" || { echo "Error: Failed to load template.sh from $LIB_DIR"; exit 1; }

# デフォルト値の設定
environment="dev"
interactive=true
auto_approve=false
use_changeset=true
dry_run=false
validate_only=false
custom_params_file=""
custom_template_file=""
custom_stack_name=""
diff_env1=""
diff_env2=""
resource_type=""
resource_name=""

# 引数の解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            environment="$2"
            shift 2
            ;;
        -p|--params)
            custom_params_file="$2"
            shift 2
            ;;
        -t|--template)
            custom_template_file="$2"
            shift 2
            ;;
        -y|--yes)
            interactive=false
            auto_approve=true
            shift
            ;;
        -n|--no-changeset)
            use_changeset=false
            shift
            ;;
        -d|--dry-run)
            dry_run=true
            shift
            ;;
        -v|--validate)
            validate_only=true
            shift
            ;;
        --diff)
            diff_env1="$2"
            diff_env2="$3"
            shift 3
            ;;
        --stack-name)
            custom_stack_name="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            if [[ -z "$resource_type" ]]; then
                resource_type="$1"
                shift
            elif [[ -z "$resource_name" ]]; then
                resource_name="$1"
                shift
            else
                echo -e "${RED}エラー: 不明なオプション「$1」${NC}"
                usage
            fi
            ;;
    esac
done

# リソースタイプの必須チェック
if [[ -z "$resource_type" ]]; then
    echo -e "${RED}エラー: リソースタイプを指定してください${NC}"
    usage
fi

# リソース名が指定されていない場合は、全テンプレートデプロイモード
deploy_all_templates=false
if [[ -z "$resource_name" ]]; then
    echo -e "${YELLOW}リソース名が指定されていません。リソースタイプ '$resource_type' の全テンプレートをデプロイします。${NC}"
    deploy_all_templates=true
fi

# 環境の検証
if [[ ! "$environment" =~ ^(dev|stg)$ ]]; then
    echo -e "${RED}エラー: 環境は 'dev' または 'stg' のみ指定可能です${NC}"
    exit 1
fi

# 差分表示モードの処理
if [[ -n "$diff_env1" && -n "$diff_env2" ]]; then
    echo -e "${BLUE}環境間の差分を表示します: $diff_env1 vs $diff_env2${NC}"

    # パラメータファイルのパスを生成
    params_file1="$CFN_ROOT/environments/$diff_env1/parameters/$resource_type/$resource_name.json"
    params_file2="$CFN_ROOT/environments/$diff_env2/parameters/$resource_type/$resource_name.json"

    # デフォルトパラメータを使用（指定されたリソース固有のパラメータがない場合）
    if [[ ! -f "$params_file1" ]]; then
        params_file1="$CFN_ROOT/environments/$diff_env1/parameters/$resource_type/default.json"
    fi

    if [[ ! -f "$params_file2" ]]; then
        params_file2="$CFN_ROOT/environments/$diff_env2/parameters/$resource_type/default.json"
    fi

    # 両方のファイルが存在するか確認
    if [[ ! -f "$params_file1" ]]; then
        echo -e "${RED}エラー: $diff_env1 環境のパラメータファイルが見つかりません${NC}"
        echo "パス: $params_file1"
        exit 1
    fi

    if [[ ! -f "$params_file2" ]]; then
        echo -e "${RED}エラー: $diff_env2 環境のパラメータファイルが見つかりません${NC}"
        echo "パス: $params_file2"
        exit 1
    fi

    # 差分表示
    echo -e "${YELLOW}$diff_env1 と $diff_env2 の差分:${NC}"
    diff -u "$params_file1" "$params_file2" || true
    exit 0
fi

echo -e "${BLUE}CloudFormation デプロイを準備しています...${NC}"
echo "環境: $environment"
echo "リソースタイプ: $resource_type"
if [[ "$deploy_all_templates" == false ]]; then
    echo "リソース名: $resource_name"
fi

# グローバル変数をexportして子関数で利用可能にする
export environment
export resource_type
export resource_name
export custom_params_file
export custom_template_file
export custom_stack_name
export interactive
export auto_approve
export use_changeset
export dry_run
export validate_only
export CFN_ROOT

# スクリプト終了時に一時パラメータファイルを削除するトラップ
cleanup_main_temp_file() {
  if [[ -n "$TEMP_PARAM_FILE_TO_CLEAN" && -f "$TEMP_PARAM_FILE_TO_CLEAN" ]]; then
    echo -e "${BLUE}メインの一時パラメータファイルをクリーンアップ: $TEMP_PARAM_FILE_TO_CLEAN${NC}" >&2
    rm -f "$TEMP_PARAM_FILE_TO_CLEAN"
  fi
}
trap cleanup_main_temp_file EXIT

# メイン処理
if [[ "$deploy_all_templates" == true ]]; then
    # GroupDeployモードの実行
    execute_group_deploy "$resource_type"
    exit $?
else
    # 単一テンプレートのデプロイ
    template_file=$(find_template_file "$resource_type" "$resource_name" "$custom_template_file")
    deploy_single_template "$template_file" "$resource_name"
    exit $?
fi
