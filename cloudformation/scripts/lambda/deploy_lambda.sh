#!/bin/bash
set -e

# Lambda関数をデプロイするスクリプト
# 使用方法: ./deploy_lambda.sh <環境> <関数名>
# 例: ./deploy_lambda.sh dev DLPF_RETURN_PARALLEL_NUM_ARRAY

# 色付きログ出力用の設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# エラーハンドリング関数
error_exit() {
  log_error "$1"
  echo "Lambda関数のデプロイを中止します"
  exit 1
}

# 引数の検証
if [ $# -lt 2 ]; then
  error_exit "引数が不足しています。使用方法: $0 <environment> <function_name> [dry_run]"
fi

ENVIRONMENT="$1"
FUNCTION_NAME="$2"
DRY_RUN="${3:-false}"

log_info "Lambda関数をデプロイします"
log_info "環境: ${ENVIRONMENT}"
log_info "関数名: ${FUNCTION_NAME}"
log_info "ドライラン: ${DRY_RUN}"

# プロジェクトルートディレクトリを取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# Lambda関数のソースディレクトリを確認
LAMBDA_DIR="${PROJECT_ROOT}/lambda/${FUNCTION_NAME}"
if [ ! -d "$LAMBDA_DIR" ]; then
  error_exit "Lambda関数のディレクトリが見つかりません: $LAMBDA_DIR"
fi

# Lambda関数のソースファイルを確認
LAMBDA_FUNCTION_PY="${LAMBDA_DIR}/lambda_function.py"
if [ ! -f "$LAMBDA_FUNCTION_PY" ]; then
  error_exit "Lambda関数のソースファイルが見つかりません: $LAMBDA_FUNCTION_PY"
fi

log_info "Lambda関数のソースディレクトリ: $LAMBDA_DIR"
log_info "Lambda関数のソースファイル: $LAMBDA_FUNCTION_PY"

# 一時ディレクトリを作成
TEMP_DIR="/tmp/lambda_${FUNCTION_NAME}"
mkdir -p "$TEMP_DIR"

# ソースファイルをコピー
cp "$LAMBDA_FUNCTION_PY" "$TEMP_DIR/"
log_info "ソースファイルを一時ディレクトリにコピーしました: $TEMP_DIR"

# ZIPファイルを作成
cd "$TEMP_DIR"
zip -r lambda.zip ./*
log_info "ZIPファイルを作成しました: $TEMP_DIR/lambda.zip"

# Lambda関数のコードを更新
if [ "$DRY_RUN" = "true" ]; then
  log_info "【ドライラン】Lambda関数のコードを更新します: $FUNCTION_NAME"
else
  log_info "Lambda関数のコードを更新しています: $FUNCTION_NAME"
  aws lambda update-function-code \
    --function-name "$FUNCTION_NAME" \
    --zip-file fileb://lambda.zip || {
    error_exit "Lambda関数のコード更新に失敗しました"
  }
  log_info "Lambda関数のコードを更新しました: $FUNCTION_NAME"
fi

# 一時ディレクトリをクリーンアップ
cd - > /dev/null
rm -rf "$TEMP_DIR"
log_info "一時ディレクトリをクリーンアップしました: $TEMP_DIR"

log_info "Lambda関数のデプロイが完了しました: $FUNCTION_NAME"
exit 0
