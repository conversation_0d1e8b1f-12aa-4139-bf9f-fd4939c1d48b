#!/bin/bash
#
# CloudFormation デプロイスクリプト - CloudFormation操作
#
# 概要:
#   CloudFormationスタックの操作機能を提供します
#
# 依存関係:
#   common.sh, parameter.sh

# CloudFormationデプロイ処理
# 使用方法: deploy_stack <テンプレートファイル> <スタック名> <パラメータオプション> <IAMケイパビリティ>
deploy_stack() {
    local template_file="$1"
    local stack_name="$2"
    local params_option="$3"
    local capability_option="$4"

    if [[ "$use_changeset" == true ]]; then
        deploy_with_changeset "$template_file" "$stack_name" "$params_option" "$capability_option"
    else
        deploy_direct "$template_file" "$stack_name" "$params_option" "$capability_option"
    fi

    # Lambda関数のデプロイ後にコードを更新
    if [[ "$resource_type" == "lambda" && "$dry_run" != "true" ]]; then
        local function_name=$(basename "$template_file" .yaml)
        function_name=${function_name%.yml}  # .yml拡張子にも対応

        echo -e "${BLUE}Lambda関数のコードを更新します: $function_name${NC}"

        # Lambda関数のコード更新スクリプトを実行
        local lambda_deploy_script="$(dirname "$SCRIPT_DIR")/lambda/deploy_lambda.sh"
        if [[ -f "$lambda_deploy_script" ]]; then
            chmod +x "$lambda_deploy_script"
            "$lambda_deploy_script" "$environment" "$function_name" "$dry_run"
            if [[ $? -eq 0 ]]; then
                echo -e "${GREEN}Lambda関数のコード更新が完了しました: $function_name${NC}"
            else
                echo -e "${YELLOW}警告: Lambda関数のコード更新に失敗しました: $function_name${NC}"
                echo -e "${YELLOW}CloudFormationスタックは正常にデプロイされましたが、Lambda関数のコードは更新されていない可能性があります。${NC}"
            fi
        else
            echo -e "${YELLOW}警告: Lambda関数のコード更新スクリプトが見つかりません: $lambda_deploy_script${NC}"
            echo -e "${YELLOW}CloudFormationスタックは正常にデプロイされましたが、Lambda関数のコードは更新されていません。${NC}"
        fi
    fi
}

# 変更セットを使用したデプロイ
deploy_with_changeset() {
    local template_file="$1"
    local stack_name="$2"
    # params_option は "--parameters file://..." 形式の文字列として受け取る
    local params_string="$3"
    # 特殊文字（CR）を除去してからスペースで分割して配列にする
    params_string=$(echo "$params_string" | tr -d '\r')
    read -r -a params_option_array <<< "$params_string"
    echo "DEBUG [changeset]: params_option_array size: ${#params_option_array[@]}" >&2
    declare -p params_option_array >&2
    local capability_option="$4"

    local change_set_name="${stack_name}-change-$(date +'%Y%m%d%H%M%S')"
    echo -e "${BLUE}変更セットを作成しています: $change_set_name${NC}"

    local change_set_type="UPDATE"
    if ! aws cloudformation describe-stacks --stack-name "$stack_name" &> /dev/null; then
        echo -e "${YELLOW}スタック '$stack_name' は存在しません。新規作成 (CREATE) タイプの変更セットを作成します。${NC}"
        change_set_type="CREATE"
    fi

    # 変更セット作成
    # コマンドを配列で構築
    local aws_command=("aws" "cloudformation" "create-change-set" \
        "--stack-name" "$stack_name" \
        "--change-set-name" "$change_set_name" \
        "--template-body" "file://$template_file" \
        "--region" "ap-northeast-1")
    # パラメータオプションを追加（配列が空でない場合）
    if [ ${#params_option_array[@]} -gt 0 ]; then
        aws_command+=("${params_option_array[@]}")
    fi
    # ケイパビリティオプションを追加（空でない場合）
    if [[ -n "$capability_option" ]]; then
        # capability_option が "--capabilities CAPABILITY_NAMED_IAM" のような形式を想定
        read -r -a cap_array <<< "$capability_option"
        aws_command+=("${cap_array[@]}")
    fi
    aws_command+=("--change-set-type" "$change_set_type")

    # コマンド実行
    "${aws_command[@]}"

    local create_cs_exit_code=$?
    if [[ $create_cs_exit_code -ne 0 ]]; then
        echo -e "${RED}変更セット '$change_set_name' の作成開始に失敗しました。${NC}"
        return $create_cs_exit_code
    fi

    echo -e "${BLUE}変更セットの作成完了を待機中...${NC}"
    aws cloudformation wait change-set-create-complete \
        --stack-name "$stack_name" \
        --change-set-name "$change_set_name" \
        --region ap-northeast-1

    local change_set_status=$(aws cloudformation describe-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1 --query "Status" --output text || echo "UNKNOWN")
    local status_reason=$(aws cloudformation describe-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1 --query "StatusReason" --output text || echo "")

    if [[ "$change_set_status" == "FAILED" ]]; then
        if [[ "$status_reason" == *"The submitted information didn't contain changes"* || "$status_reason" == *"No updates are to be performed"* ]]; then
            echo -e "${YELLOW}変更はありません。変更セット '$change_set_name' を削除します。${NC}"
            aws cloudformation delete-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1
            return 0
        else
            echo -e "${RED}変更セット '$change_set_name' の作成に失敗しました: $status_reason${NC}"
            aws cloudformation delete-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1
            return 1
        fi
    elif [[ "$change_set_status" != "CREATE_COMPLETE" ]]; then
        echo -e "${RED}変更セット '$change_set_name' の作成が予期せぬステータスで終了しました: $change_set_status${NC}"
        aws cloudformation delete-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1 &> /dev/null
        return 1
    fi

    echo -e "${GREEN}変更セット '$change_set_name' の作成完了！${NC}"
    echo -e "${BLUE}変更セットの詳細:${NC}"
    aws cloudformation describe-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1 --query "Changes"

    # 変更セット実行の確認
    local execute_change_set=false
    if [[ "$interactive" == true ]]; then
        echo -e "${YELLOW}変更セット '$change_set_name' を実行しますか？ (y/n)${NC}"
        read -r execute
        if [[ "$execute" =~ ^[Yy]$ ]]; then
            execute_change_set=true
        fi
    elif [[ "$auto_approve" == true ]]; then
        echo -e "${YELLOW}対話モード無効、自動承認モードのため変更セットを実行します${NC}"
        execute_change_set=true
    fi

    if [[ "$execute_change_set" == true ]]; then
        execute_changeset "$stack_name" "$change_set_name" "$change_set_type"
    else
        echo -e "${YELLOW}変更セット '$change_set_name' の実行をキャンセルしました。変更セットを削除します。${NC}"
        aws cloudformation delete-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1
        return 0
    fi
}

# 変更セットの実行
execute_changeset() {
    local stack_name="$1"
    local change_set_name="$2"
    local change_set_type="$3"

    echo -e "${BLUE}変更セット '$change_set_name' を実行しています...${NC}"
    aws cloudformation execute-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1

    local exec_cs_exit_code=$?
    if [[ $exec_cs_exit_code -ne 0 ]]; then
        echo -e "${RED}変更セット '$change_set_name' の実行開始に失敗しました。${NC}"
        aws cloudformation delete-change-set --stack-name "$stack_name" --change-set-name "$change_set_name" --region ap-northeast-1
        return $exec_cs_exit_code
    fi

    echo -e "${BLUE}スタック '$stack_name' の更新完了を待機中...${NC}"
    if [[ "$change_set_type" == "CREATE" ]]; then
        aws cloudformation wait stack-create-complete --stack-name "$stack_name" --region ap-northeast-1
    else
        aws cloudformation wait stack-update-complete --stack-name "$stack_name" --region ap-northeast-1
    fi

    local wait_stack_exit_code=$?
    local stack_status=$(aws cloudformation describe-stacks --stack-name "$stack_name" --region ap-northeast-1 --query "Stacks[0].StackStatus" --output text || echo "UNKNOWN")

    if [[ $wait_stack_exit_code -eq 0 && "$stack_status" == *"COMPLETE"* ]]; then
        echo -e "${GREEN}スタック '$stack_name' のデプロイ成功！${NC}"
        return 0
    else
        echo -e "${RED}スタック '$stack_name' のデプロイ失敗: 最終ステータス $stack_status${NC}"
        return 1
    fi
}

# 直接デプロイ（変更セットなし）
deploy_direct() {
    local template_file="$1"
    local stack_name="$2"
    # params_option は "--parameters file://..." 形式の文字列として受け取る
    local params_string="$3"
    # 特殊文字（CR）を除去してからスペースで分割して配列にする
    params_string=$(echo "$params_string" | tr -d '\r')
    read -r -a params_option_array <<< "$params_string"
    echo "DEBUG [direct]: params_option_array size: ${#params_option_array[@]}" >&2
    declare -p params_option_array >&2
    local capability_option="$4"

    echo -e "${BLUE}デプロイコマンドを実行します...${NC}"
    # コマンドを配列で構築
    local aws_command=("aws" "cloudformation" "deploy" \
        "--template-file" "$template_file" \
        "--stack-name" "$stack_name" \
        "--region" "ap-northeast-1")
    # パラメータオプションを追加（配列が空でない場合）
    if [ ${#params_option_array[@]} -gt 0 ]; then
        aws_command+=("${params_option_array[@]}")
    fi
    # ケイパビリティオプションを追加（空でない場合）
    if [[ -n "$capability_option" ]]; then
        read -r -a cap_array <<< "$capability_option"
        aws_command+=("${cap_array[@]}")
    fi
    aws_command+=("--no-fail-on-empty-changeset")

    # コマンド実行
    "${aws_command[@]}"

    local deploy_exit_code=$?
    if [ $deploy_exit_code -eq 0 ]; then
        echo -e "${GREEN}スタック '$stack_name' のデプロイ成功！${NC}"
        return 0
    else
        echo -e "${RED}スタック '$stack_name' のデプロイ失敗: 終了コード $deploy_exit_code${NC}"
        return $deploy_exit_code
    fi
}

# スタックの存在確認
# 使用方法: check_stack_exists <スタック名>
check_stack_exists() {
    local stack_name="$1"
    aws cloudformation describe-stacks --stack-name "$stack_name" &> /dev/null
    return $?
}

# スタックの状態を取得
# 使用方法: get_stack_status <スタック名>
get_stack_status() {
    local stack_name="$1"
    aws cloudformation describe-stacks --stack-name "$stack_name" --query "Stacks[0].StackStatus" --output text 2>/dev/null || echo "UNKNOWN"
}
