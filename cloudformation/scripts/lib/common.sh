#!/bin/bash
#
# CloudFormation デプロイスクリプト - 共通機能
#
# 概要:
#   CloudFormationデプロイスクリプトで使用する共通機能を提供します
#
# 依存関係:
#   なし

# 色の定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# エラーコード定義
ERROR_INVALID_ARGS=1       # 無効な引数
ERROR_FILE_NOT_FOUND=2     # ファイルが見つからない
ERROR_INVALID_STACK=3      # 無効なスタック名
ERROR_DIR_NOT_FOUND=4      # ディレクトリが見つからない
ERROR_NO_TEMPLATES=5       # テンプレートが見つからない
ERROR_PARAM_PROCESS=6      # パラメータ処理エラー

# エラーハンドリング関数
# 使用方法: handle_error <エラーコード> <エラーメッセージ> [<スタック名>]
handle_error() {
  local error_code=$1
  local message=$2
  local stack_name=$3

  echo -e "${RED}ERROR (Code: ${error_code}): ${message}${NC}"

  if [[ -n "${stack_name}" ]]; then
    echo "スタック情報:"
    echo "  スタック名: ${stack_name}"

    # スタックの状態を取得
    stack_status=$(aws cloudformation describe-stacks --stack-name ${stack_name} --query "Stacks[0].StackStatus" --output text 2>/dev/null)

    if [[ $? -eq 0 ]]; then
      echo "  現在の状態: ${stack_status}"

      if [[ "${stack_status}" == *FAILED* ]]; then
        # 失敗理由を表示
        failure_reason=$(aws cloudformation describe-stacks --stack-name ${stack_name} --query "Stacks[0].StackStatusReason" --output text)
        echo "  失敗理由: ${failure_reason}"
      fi

      # ロールバック手順の提案
      if [[ "${stack_status}" == *ROLLBACK* ]]; then
        echo ""
        echo "ロールバック実行中です。完了までお待ちください。"
      elif [[ "${stack_status}" == *COMPLETE* ]]; then
        echo ""
        echo "スタックを削除するには以下のコマンドを実行してください:"
        echo "aws cloudformation delete-stack --stack-name ${stack_name}"
      elif [[ "${stack_status}" == *FAILED* ]]; then
        echo ""
        echo "スタックの状態をクリーンアップするには以下のコマンドを実行してください:"
        echo "aws cloudformation delete-stack --stack-name ${stack_name}"
      fi
    else
      echo "  スタック情報の取得に失敗しました。"
    fi
  fi

  echo ""
  echo "エラーの詳細については CloudWatch Logs を確認してください。"
  exit ${error_code}
}

# 使用方法の表示
usage() {
    echo -e "${BLUE}CloudFormation デプロイスクリプト${NC}"
    echo ""
    echo -e "使用方法: $0 [オプション] <リソースタイプ> <リソース名>"
    echo ""
    echo "オプション:"
    echo "  -e, --environment ENV       デプロイ環境指定（dev/stg, デフォルト: dev）"
    echo "  -p, --params FILE          カスタムパラメータファイルのパス"
    echo "  -t, --template FILE        カスタムテンプレートファイルのパス"
    echo "  -y, --yes                  対話的な確認をスキップし、自動で承認"
    echo "  -n, --no-changeset         変更セットの作成をスキップし直接デプロイ"
    echo "  -d, --dry-run              実際のデプロイを行わず、変更内容のみ表示"
    echo "  -v, --validate             テンプレートの検証のみ実行"
    echo "      --diff ENV1 ENV2       2つの環境間のパラメータ差分を表示"
    echo "      --stack-name NAME      カスタムスタック名を指定"
    echo "  -h, --help                 このヘルプメッセージを表示"
    echo ""
    echo "リソースタイプ:"
    echo "  glue-job                   Glue Job リソース"
    echo "  step-functions             Step Functions リソース"
    echo "  secrets-manager            Secrets Manager リソース"
    echo "  system-manager             System Manager リソース"
    echo "  event-bridge               EventBridge リソース"
    echo "  glue-connector             Glue Connector リソース"
    echo "  security-group             Security Group リソース"
    echo "  cognito                    Cognito リソース"
    echo ""
    echo "リソース名:"
    echo "  リソースの識別子（例: job-db-to-file, jn_cp001-df01_001）"
    echo ""
    echo "例:"
    echo "  $0 -e dev step-functions jn-cp001-df01-001"
    echo "  $0 --environment stg --yes glue-job job-db-to-file"
    echo "  $0 --diff dev stg step-functions jn-cp001-df01-001"
    exit 1
}

# ディレクトリパスの設定
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
CFN_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
