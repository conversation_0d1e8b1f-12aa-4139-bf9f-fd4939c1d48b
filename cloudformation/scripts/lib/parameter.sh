#!/bin/bash
#
# CloudFormation デプロイスクリプト - パラメータ処理
#
# 概要:
#   CloudFormationデプロイスクリプトのパラメータ処理機能を提供します
#
# 依存関係:
#   common.sh

# パラメータファイルの形式変換関数
# 使用方法: convert_params_file <入力ファイル> <出力ファイル>
convert_params_file() {
    local input_file="$1"
    local output_file="$2"

    if [[ ! -f "$input_file" ]]; then
        return 1
    fi

    local params_format="unknown"
    if grep -q '"Parameters"' "$input_file"; then
        params_format="custom-json"
    elif grep -q '"ParameterKey"' "$input_file"; then
        params_format="cli-json"
    elif grep -q '=' "$input_file"; then
        params_format="properties"
    fi

    echo "パラメータ形式（$(basename "$input_file")）: $params_format" >&2 # 情報メッセージは標準エラー出力へ

    case "$params_format" in
        "custom-json")
            # .Parameters オブジェクトを ParameterKey/ParameterValue の配列に変換
            jq -r '[.Parameters | to_entries[] | {"ParameterKey": .key, "ParameterValue": (.value | tostring)}]' "$input_file" > "$output_file"
            ;;
        "cli-json")
            # 既に正しい形式なのでコピー
            cp "$input_file" "$output_file"
            ;;
        "properties")
            # properties 形式を ParameterKey/ParameterValue の配列に変換
            echo "[" > "$output_file"
            while IFS='=' read -r key value; do
                [[ -z "$key" || "$key" == \#* ]] && continue
                escaped_value=$(echo "$value" | sed -e 's/\\/\\\\/g' -e 's/"/\\"/g')
                echo "  {\"ParameterKey\": \"$key\", \"ParameterValue\": \"$escaped_value\"}," >> "$output_file"
            done < "$input_file"
            sed -i '$ s/,$//' "$output_file" # 最後のカンマを削除
            echo "]" >> "$output_file"
            ;;
        *)
            echo -e "${YELLOW}警告: 不明なパラメータ形式です（$(basename "$input_file")）${NC}" >&2 # 警告メッセージは標準エラー出力へ
            cp "$input_file" "$output_file" # 不明な場合はそのままコピー
            ;;
    esac

    # 変換後のファイルが空でないか、有効なJSONかを確認
    if [[ ! -s "$output_file" ]] || ! jq -e . "$output_file" > /dev/null 2>&1; then
        echo -e "${RED}エラー: パラメータファイルの変換または検証に失敗: $input_file${NC}" >&2 # エラーメッセージは標準エラー出力へ
        return 1
    fi

    return 0
}

# パラメータファイルの処理とマージ
# 使用方法: process_parameters <環境> <リソースタイプ> <リソース名> [<カスタムパラメータファイル>]
# 出力: 成功時はパラメータオプション文字列と一時ファイルパスをセミコロン区切りで標準出力へ (例: "--parameter-overrides file:///tmp/...;/tmp/...")
process_parameters() {
    local environment="$1"
    local resource_type="$2"
    local current_resource_name="$3"
    local custom_params_file="$4"
    local params_option="" # この関数内で設定する

    # 一時ファイルパスを格納する変数 (呼び出し元でクリーンアップ)
    local temp_file_to_return=""

    if [[ -n "$custom_params_file" ]]; then
        # カスタムパラメータファイル処理
        local custom_params_file_path="$custom_params_file"
        echo -e "カスタムパラメータファイル: ${GREEN}$custom_params_file_path${NC}" >&2 # 情報メッセージは標準エラー出力へ
        if [[ ! -f "$custom_params_file_path" ]]; then
            handle_error 2 "指定されたパラメータファイルが見つかりません: $custom_params_file_path" "" >&2
            return 1
        fi

        local temp_custom_file=$(mktemp "/tmp/cfn_params_custom.${current_resource_name}.XXXXXX")
        # temp_files_to_clean+=("$temp_custom_file") # ここではクリーンアップしない

        if convert_params_file "$custom_params_file_path" "$temp_custom_file"; then
            params_option="--parameter-overrides file://$temp_custom_file"
            temp_file_to_return="$temp_custom_file" # 戻り値用の一時ファイルパス
        else
            # convert_params_file がエラーメッセージを出力済み
            return 1
        fi
        echo "${params_option};${temp_file_to_return}" # オプションとファイルパスを区切って出力
        return 0
    fi

    # デフォルトパラメータファイルのパス
    local env_params_file="$CFN_ROOT/environments/$environment/parameters/default.json"
    local type_params_file="$CFN_ROOT/environments/$environment/parameters/$resource_type/default.json"
    local resource_params_file="$CFN_ROOT/environments/$environment/parameters/$resource_type/$current_resource_name.json"

    # パラメータファイルの存在確認
    local use_env_params=false
    local use_type_params=false
    local use_resource_params=false

    [[ -f "$env_params_file" ]] && use_env_params=true
    [[ -f "$type_params_file" ]] && use_type_params=true
    [[ -f "$resource_params_file" ]] && use_resource_params=true

    # 情報メッセージは標準エラー出力へ
    echo -e "環境共通パラメータ: ${GREEN}${env_params_file}${NC} (${use_env_params})" >&2
    echo -e "タイプ共通パラメータ: ${GREEN}${type_params_file}${NC} (${use_type_params})" >&2
    echo -e "リソース固有パラメータ: ${GREEN}${resource_params_file}${NC} (${use_resource_params})" >&2

    if [[ "$use_env_params" == false && "$use_type_params" == false && "$use_resource_params" == false ]]; then
        echo -e "${YELLOW}パラメータファイルが見つかりません。パラメータなしで続行します。${NC}" >&2 # 警告メッセージは標準エラー出力へ
        # params_option は空文字列のまま
        echo "${params_option};${temp_file_to_return}" # 空オプションと空ファイルパスを出力
        return 0
    fi

    # パラメータファイルの変換
    local temp_env_file=""
    local temp_type_file=""
    local temp_resource_file=""
    declare -a files_to_merge # マージ対象のファイルパスを格納する配列

    if [[ "$use_env_params" == true ]]; then
        temp_env_file=$(mktemp "/tmp/cfn_params_env.${current_resource_name}.XXXXXX")
        # temp_files_to_clean+=("$temp_env_file")
        if convert_params_file "$env_params_file" "$temp_env_file"; then
             files_to_merge+=("$temp_env_file")
        else return 1; fi
    fi

    if [[ "$use_type_params" == true ]]; then
        temp_type_file=$(mktemp "/tmp/cfn_params_type.${current_resource_name}.XXXXXX")
        # temp_files_to_clean+=("$temp_type_file")
        if convert_params_file "$type_params_file" "$temp_type_file"; then
            files_to_merge+=("$temp_type_file")
        else return 1; fi
    fi

    if [[ "$use_resource_params" == true ]]; then
        temp_resource_file=$(mktemp "/tmp/cfn_params_resource.${current_resource_name}.XXXXXX")
        # temp_files_to_clean+=("$temp_resource_file")
        if convert_params_file "$resource_params_file" "$temp_resource_file"; then
            files_to_merge+=("$temp_resource_file")
        else return 1; fi
    fi

    # パラメータのマージ
    local merged_params_file=$(mktemp "/tmp/cfn_params_merged.${current_resource_name}.XXXXXX")
    # temp_files_to_clean+=("$merged_params_file")

    # jq でマージ (後勝ち): 複数のファイルを結合し、ParameterKey でグループ化して最後の値を採用
    # 修正点: 各ファイルを配列として読み込み、結合してからマージする
    jq -s 'add | reduce .[] as $item ({}; .[$item.ParameterKey] = $item.ParameterValue) | to_entries | map({"ParameterKey": .key, "ParameterValue": .value})' "${files_to_merge[@]}" > "$merged_params_file"

    if [[ -s "$merged_params_file" ]] && jq -e . "$merged_params_file" > /dev/null 2>&1; then
        # echo -e "${BLUE}マージされたパラメータファイルの内容:${NC}" >&2 # デバッグ時以外はコメントアウト
        # cat "$merged_params_file" >&2 # デバッグ時以外はコメントアウト
        # echo "" >&2
        params_option="--parameter-overrides file://$merged_params_file"
        temp_file_to_return="$merged_params_file" # 戻り値用の一時ファイルパス
    else
        echo -e "${YELLOW}警告: 有効なパラメータが見つからなかったか、マージに失敗しました。パラメータなしで続行します。${NC}" >&2 # 警告メッセージは標準エラー出力へ
        params_option=""
        temp_file_to_return="" # ファイルパスも空
    fi
    # params_option は既に文字列として設定済み
    echo "${params_option};${temp_file_to_return}" # オプションとファイルパスを区切って出力
    return 0
}

# 王さんの既存スタック名解決（開発環境限定）
# 使用方法: resolve_ou_stack_name <リソース名>
# 出力: 成功時は王さんのスタック名を標準出力へ
resolve_ou_stack_name() {
    local resource_name="$1"

    # 開発環境以外では処理しない
    if [[ "$environment" != "dev" ]]; then
        return 1
    fi

    # default.jsonファイルのパス
    local default_params_file="$CFN_ROOT/environments/dev/parameters/default.json"

    if [[ ! -f "$default_params_file" ]]; then
        return 1
    fi

    # リソース名から期待される王さんのスタック名を生成
    local expected_ou_stack=""
    if [[ "$resource_name" =~ ^dlpf_job_ ]]; then
        # dlpf_job_xxx → ou-jobxxx
        expected_ou_stack="ou-job$(echo "$resource_name" | sed 's/^dlpf_job_//' | tr '_' '')"
    else
        return 1
    fi

    # default.jsonから王さんのマッピングを確認
    local ou_mapping
    ou_mapping=$(jq -r ".OuStackMappings.\"$expected_ou_stack\"" "$default_params_file" 2>/dev/null)

    if [[ "$ou_mapping" != "null" && -n "$ou_mapping" ]]; then
        # 王さんのスタックが実際に存在するか確認
        if aws cloudformation describe-stacks --stack-name "$expected_ou_stack" --region ap-northeast-1 >/dev/null 2>&1; then
            echo "$expected_ou_stack"
            return 0
        fi
    fi

    return 1
}

# スタック名の生成
# 使用方法: generate_stack_name <リソース名> [<カスタムスタック名>]
# 出力: 成功時はスタック名を標準出力へ
generate_stack_name() {
    local resource_name="$1"
    local custom_stack_name="$2"
    local stack_name=""

    if [[ -n "$custom_stack_name" ]]; then
        # deploy_all_templates はグローバル変数
        if [[ "$deploy_all_templates" == true ]]; then
            handle_error 99 "全テンプレートデプロイモードでは --stack-name オプションは使用できません。" "" >&2
            return 1
        fi
        stack_name="$custom_stack_name"
        echo -e "${YELLOW}指定されたカスタムスタック名を使用: $stack_name${NC}" >&2 # 情報メッセージは標準エラー出力へ
    else
        # 開発環境での王さんの既存スタック名解決
        if [[ "$environment" == "dev" ]]; then
            local ou_stack_name
            ou_stack_name=$(resolve_ou_stack_name "$resource_name")
            if [[ $? -eq 0 && -n "$ou_stack_name" ]]; then
                stack_name="$ou_stack_name"
                echo -e "${YELLOW}王さんの既存スタックを使用: $stack_name${NC}" >&2
            else
                # 通常のスタック名生成
                if [[ "$resource_name" =~ ^dlpf_ ]]; then
                    stack_name=$(echo "$resource_name" | tr '_' '-')
                else
                    stack_name="dlpf-$(echo "$resource_name" | tr '_' '-')"
                fi
                echo -e "${GREEN}生成されたスタック名: $stack_name${NC}" >&2 # 情報メッセージは標準エラー出力へ
            fi
        else
            # 開発環境以外は通常のスタック名生成
            if [[ "$resource_name" =~ ^dlpf_ ]]; then
                stack_name=$(echo "$resource_name" | tr '_' '-')
            else
                stack_name="dlpf-$(echo "$resource_name" | tr '_' '-')"
            fi
            echo -e "${GREEN}生成されたスタック名: $stack_name${NC}" >&2 # 情報メッセージは標準エラー出力へ
        fi
    fi

    # スタック名の検証
    if [[ ! "$stack_name" =~ ^[a-zA-Z][-a-zA-Z0-9]*$ ]]; then
        handle_error 3 "無効なスタック名です: $stack_name\nスタック名は英字で始まり、英数字とハイフンのみを含む必要があります。" "" >&2
        return 1
    fi

    if [ ${#stack_name} -gt 128 ]; then
        handle_error 3 "スタック名が長すぎます: $stack_name\n最大128文字までです。" "" >&2
        return 1
    fi

    echo "$stack_name" # スタック名を標準出力へ
    return 0
}
