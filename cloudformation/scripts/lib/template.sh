#!/bin/bash
#
# CloudFormation デプロイスクリプト - テンプレート処理
#
# 概要:
#   CloudFormationテンプレートの処理機能を提供します
#
# 依存関係:
#   common.sh, parameter.sh, cloudformation.sh

# テンプレートファイルの検索と検証
# 使用方法: find_template_file <リソースタイプ> <リソース名> [<カスタムテンプレートファイル>]
find_template_file() {
    local resource_type="$1"
    local resource_name="$2"
    local custom_template_file="$3"
    local template_file=""

    if [[ -n "$custom_template_file" ]]; then
        template_file="$custom_template_file"
    else
        template_file="$CFN_ROOT/templates/$resource_type/$resource_name.yaml"
        if [[ ! -f "$template_file" ]]; then
            template_file="$CFN_ROOT/templates/$resource_type/$resource_name.yml"
        fi
    fi

    if [[ ! -f "$template_file" ]]; then
        # エラーメッセージは標準エラー出力へ
        handle_error 2 "テンプレートファイルが見つかりません。試行したパス: $template_file" "" >&2
        return 1 # 失敗を示す終了コード
    fi

    echo "$template_file" # 成功時はファイルパスを標準出力へ
}

# テンプレートの検証
# 使用方法: validate_template <テンプレートファイル>
validate_template() {
    local template_file="$1"
    echo -e "${BLUE}テンプレートを検証しています: $(basename "$template_file")...${NC}" >&2 # 情報メッセージは標準エラー出力へ
    aws cloudformation validate-template --template-body "file://$template_file" --region ap-northeast-1
    local validation_exit_code=$?
    if [[ $validation_exit_code -eq 0 ]]; then
        echo -e "${GREEN}テンプレート '$(basename "$template_file")' は有効です。${NC}" >&2
    else
        echo -e "${RED}テンプレート '$(basename "$template_file")' の検証に失敗しました。${NC}" >&2
    fi
    return $validation_exit_code
}

# GroupDeploy用のテンプレートファイル検索
# 使用方法: find_group_templates <リソースタイプ>
# 出力: 見つかったテンプレートファイルのパスをNULL区切りで標準出力へ
find_group_templates() {
    local resource_type="$1"
    local template_dir="$CFN_ROOT/templates/$resource_type"

    if [[ ! -d "$template_dir" ]]; then
        # エラーメッセージは標準エラー出力へ
        handle_error 4 "リソースタイプ '$resource_type' のテンプレートディレクトリが見つかりません: $template_dir" "" >&2
        return 1 # 失敗を示す終了コード
    fi

    # 情報メッセージは標準エラー出力へ
    echo -e "${BLUE}ディレクトリ '$template_dir' 内の全テンプレートを検索しています...${NC}" >&2
    local template_files=()
    while IFS= read -r -d $'\0' file; do
        template_files+=("$file")
    done < <(find "$template_dir" -maxdepth 1 -type f \( -name "*.yaml" -o -name "*.yml" \) -print0)

    if [ ${#template_files[@]} -eq 0 ]; then
        # エラーメッセージは標準エラー出力へ
        handle_error 5 "リソースタイプ '$resource_type' にデプロイ対象のテンプレートファイルが見つかりません。" "" >&2
        return 1 # 失敗を示す終了コード
    fi

    # 情報メッセージは標準エラー出力へ
    echo -e "以下のテンプレートを順番にデプロイします:" >&2
    for file in "${template_files[@]}"; do
        echo "  - $(basename "$file")" >&2
    done

    # ファイルパスのみをNULL区切りで標準出力へ
    printf '%s\0' "${template_files[@]}"
}

# GroupDeployの実行
# 使用方法: execute_group_deploy <リソースタイプ>
execute_group_deploy() {
    local resource_type="$1"
    local template_files=() # 配列を初期化

    # mapfile を使用してNULL区切り出力を配列に読み込む
    mapfile -d $'\0' template_files < <(find_group_templates "$resource_type")
    local find_exit_code=$?

    if [[ $find_exit_code -ne 0 ]]; then
        # エラーメッセージは find_group_templates が標準エラー出力に出力済み
        return $find_exit_code
    fi

    # find が末尾に余分なNULLを追加する場合があるため、最後の空要素を削除
    if [[ -z "${template_files[-1]}" ]]; then
        unset 'template_files[-1]'
    fi

    # 念のため、配列が空でないか再確認
    if [ ${#template_files[@]} -eq 0 ]; then
         echo "エラー: 処理後にテンプレートファイルが見つかりませんでした。" >&2
         return 1
    fi

    if [[ "$interactive" == true && "$validate_only" == false && "$dry_run" == false ]]; then # 検証・ドライラン時は確認しない
        echo -e "${YELLOW}上記 ${#template_files[@]} 個のテンプレートをデプロイしますか？ (y/n)${NC}" >&2 # 確認メッセージは標準エラー出力へ
        read -r confirm_all < /dev/tty # 標準入力から読み取る
        if [[ ! "$confirm_all" =~ ^[Yy]$ ]]; then
            echo "デプロイをキャンセルしました。" >&2
            return 0
        fi
    fi

    local total_success=0
    local total_failed=0
    declare -a failed_templates

    # 各テンプレートのデプロイ/検証/ドライラン
    for template_path in "${template_files[@]}"; do
        # 配列要素が空でないことを確認
        if [[ -z "$template_path" ]]; then
            echo "警告: ループ内で空のテンプレートパスをスキップします。" >&2
            continue
        fi
        local template_basename=$(basename "$template_path")
        # 拡張子を除去 (.yaml または .yml)
        local current_resource_name=${template_basename%.*}

        # validate_only, dry_run フラグを考慮して deploy_single_template を呼び出す
        if deploy_single_template "$template_path" "$current_resource_name"; then
            ((total_success++))
        else
            local deploy_exit_code=$?
            # エラーメッセージは deploy_single_template 内で出力される想定
            # echo -e "${RED}テンプレート '$current_resource_name' の処理に失敗しました (終了コード: $deploy_exit_code)${NC}" >&2
            ((total_failed++))
            failed_templates+=("$current_resource_name")
            # GroupDeployではエラーが発生しても中断しないオプションも検討可能だが、デフォルトは中断
            echo -e "${RED}GroupDeploy処理を中断します。${NC}" >&2
            break # エラー発生時はループを中断
        fi
    done

    # 結果の表示 (標準エラー出力へ)
    echo -e "\n${BLUE}=== GroupDeploy 実行結果 ===${NC}" >&2
    if [[ "$validate_only" == true ]]; then
        echo -e "検証成功: ${GREEN}$total_success${NC} 件" >&2
        echo -e "検証失敗: ${RED}$total_failed${NC} 件" >&2
    elif [[ "$dry_run" == true ]]; then
        echo -e "ドライラン成功: ${GREEN}$total_success${NC} 件" >&2
        echo -e "ドライラン失敗: ${RED}$total_failed${NC} 件" >&2
    else
        echo -e "デプロイ成功: ${GREEN}$total_success${NC} 件" >&2
        echo -e "デプロイ失敗: ${RED}$total_failed${NC} 件" >&2
    fi

    if [ $total_failed -gt 0 ]; then
        echo -e "${RED}失敗したテンプレート:${NC}" >&2
        for failed in "${failed_templates[@]}"; do
            echo "  - $failed" >&2
        done
        return 1 # 失敗を示す終了コード
    fi
    return 0 # 成功を示す終了コード
}

# IAM関連のリソースチェック
# 使用方法: check_iam_capabilities <テンプレートファイル>
check_iam_capabilities() {
    local template_file="$1"
    local capability_option=""

    if grep -q -E "AWS::IAM::(Role|Policy|InstanceProfile|User|Group)" "$template_file"; then
        echo -e "${YELLOW}IAM関連のリソースが検出されました。CAPABILITY_NAMED_IAMを使用します。${NC}" >&2 # 情報メッセージは標準エラー出力へ
        capability_option="--capabilities CAPABILITY_NAMED_IAM"
    else
        echo "IAM関連のリソースは検出されませんでした。" >&2 # 情報メッセージは標準エラー出力へ
    fi

    echo "$capability_option" # capabilityオプション文字列を標準出力へ
}

# 単一テンプレートのデプロイ/検証/ドライラン
# 使用方法: deploy_single_template <テンプレートファイル> <リソース名>
# グローバル変数で一時ファイルパスを管理
TEMP_PARAM_FILE_TO_CLEAN=""

deploy_single_template() {
    local template_file="$1"
    local current_resource_name="$2"
    TEMP_PARAM_FILE_TO_CLEAN="" # 呼び出しごとに初期化

    echo -e "\n${BLUE}--- テンプレート処理開始: $current_resource_name ---${NC}" >&2 # 情報メッセージは標準エラー出力へ

    # テンプレートファイルの存在確認
    if [[ ! -f "$template_file" ]]; then
        # エラーメッセージは標準エラー出力へ
        handle_error 2 "テンプレートファイルが見つかりません: $template_file" "" >&2
        return 1 # 失敗を示す終了コード
    fi

    # --- 処理分岐 ---
    if [[ "$validate_only" == true ]]; then
        # 検証モード
        validate_template "$template_file"
        return $?
    fi

    # パラメータの処理 (ドライランと実際のデプロイで必要)
    local params_option_and_file
    params_option_and_file=$(process_parameters "$environment" "$resource_type" "$current_resource_name" "$custom_params_file")
    local process_params_exit_code=$?
    # 戻り値を分割
    local params_option="${params_option_and_file%;*}"
    TEMP_PARAM_FILE_TO_CLEAN="${params_option_and_file#*;}" # グローバル変数に格納
    # local process_params_exit_code=$? # 上で取得済み
    if [[ $process_params_exit_code -ne 0 ]]; then
        # エラーメッセージは process_parameters 内で出力される
        return $process_params_exit_code # パラメータ処理失敗
    fi

    # IAM関連のリソースチェック (ドライランと実際のデプロイで必要)
    local capability_option=$(check_iam_capabilities "$template_file")

    # スタック名の生成 (ドライランと実際のデプロイで必要)
    local stack_name=$(generate_stack_name "$current_resource_name" "$custom_stack_name")
    local generate_stack_name_exit_code=$?
     if [[ $generate_stack_name_exit_code -ne 0 ]]; then
        # エラーメッセージは generate_stack_name 内で出力される
        return $generate_stack_name_exit_code # スタック名生成失敗
    fi

    if [[ "$dry_run" == true ]]; then
        # ドライランモード
        echo -e "${BLUE}ドライラン ($current_resource_name): 実際のデプロイは行いません${NC}" >&2
        echo "以下のコマンドが実行されます:" >&2
        # deploy_stack を呼び出す代わりにコマンド文字列を構築して表示
        local deploy_command="aws cloudformation deploy \\\\\n"
        deploy_command+="  --template-file \"$template_file\" \\\\\n"
        deploy_command+="  --stack-name \"$stack_name\" \\\\\n"
        deploy_command+="  --region ap-northeast-1 \\\\\n"
        # パラメータオプション文字列をそのまま追加
        if [[ -n "$params_option" ]]; then deploy_command+="  $params_option \\\\\n"; fi
        # ケイパビリティオプション文字列をそのまま追加
        if [[ -n "$capability_option" ]]; then deploy_command+="  $capability_option \\\\\n"; fi
        deploy_command+="  --no-fail-on-empty-changeset"
        # echo -e "$deploy_command" >&2 # デバッグ時以外はコメントアウト
        return 0 # ドライランは成功
    fi

    # 実際のデプロイ
    deploy_stack "$template_file" "$stack_name" "$params_option" "$capability_option" # 文字列として渡す
    return $? # deploy_stack の終了コードを返す
}
