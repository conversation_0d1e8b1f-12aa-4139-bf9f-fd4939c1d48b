AWSTemplateFormatVersion: "2010-09-09"
Description: "Reusable Cognito User Pool Configuration for Multiple Environments"

Parameters:
  EnvironmentName:
    Type: String
    Default: "dev"
    Description: "Environment name (e.g., dev, test, prod)"
    AllowedValues:
      - "dev"
      - "stg"
      - "prd"
      - "test"
  
  UserPoolName:
    Type: String
    Default: "dlpf-api-pool"
    Description: "Base name for the Cognito User Pool"
  
  AdminEmail:
    Type: String
    Default: "<EMAIL>"
    Description: "Email address for the admin users"
    
  ResourceServerIdentifier:
    Type: String
    Default: "https://api.dlpf.com"
    Description: "Resource server identifier"
    
  CallbackURL:
    Type: String
    Default: "https://d84l1y8p4kdic.cloudfront.net"
    Description: "Callback URL used for OAuth flows"

Resources:
  CognitoUserPool:
    Type: "AWS::Cognito::UserPool"
    Properties:
      UserPoolName: !Sub "${UserPoolName}-${EnvironmentName}"
      Policies: 
        PasswordPolicy: 
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: true
          TemporaryPasswordValidityDays: 7
      LambdaConfig: {}
      Schema: 
        - Name: "profile"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "address"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "birthdate"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "10"
            MaxLength: "10"
        - Name: "gender"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "preferred_username"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "updated_at"
          AttributeDataType: "Number"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          NumberAttributeConstraints: 
            MinValue: "0"
        - Name: "website"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "picture"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "identities"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: {}
        - Name: "sub"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: false
          Required: true
          StringAttributeConstraints: 
            MinLength: "1"
            MaxLength: "2048"
        - Name: "phone_number"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "phone_verified"
          AttributeDataType: "Boolean"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
        - Name: "zoneinfo"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "locale"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "email"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "email_verified"
          AttributeDataType: "Boolean"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
        - Name: "given_name"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "family_name"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "middle_name"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "name"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
        - Name: "nickname"
          AttributeDataType: "String"
          DeveloperOnlyAttribute: false
          Mutable: true
          Required: false
          StringAttributeConstraints: 
            MinLength: "0"
            MaxLength: "2048"
      MfaConfiguration: "OFF"
      EmailConfiguration: 
        EmailSendingAccount: "COGNITO_DEFAULT"
      AdminCreateUserConfig: 
        AllowAdminCreateUserOnly: false
      UserPoolTags: 
        Environment: !Ref EnvironmentName
      AccountRecoverySetting: 
        RecoveryMechanisms: 
          - Priority: 1
            Name: "verified_email"
          - Priority: 2
            Name: "verified_phone_number"
      UsernameConfiguration: 
        CaseSensitive: false
      VerificationMessageTemplate: 
        DefaultEmailOption: "CONFIRM_WITH_CODE"

  CognitoUserPoolResourceServer:
    Type: "AWS::Cognito::UserPoolResourceServer"
    Properties:
      Identifier: !Ref ResourceServerIdentifier
      Name: "api-server"
      UserPoolId: !Ref CognitoUserPool
      Scopes: 
        - ScopeName: "read.data"
          ScopeDescription: "simple scope"

  CognitoUserPoolClientUser:
    Type: "AWS::Cognito::UserPoolClient"
    Properties:
      UserPoolId: !Ref CognitoUserPool
      ClientName: !Sub "dlpf-client-user-${EnvironmentName}"
      RefreshTokenValidity: 5
      ExplicitAuthFlows: 
        - "ALLOW_CUSTOM_AUTH"
        - "ALLOW_REFRESH_TOKEN_AUTH"
        - "ALLOW_USER_AUTH"
        - "ALLOW_USER_PASSWORD_AUTH"
        - "ALLOW_USER_SRP_AUTH"
      PreventUserExistenceErrors: "ENABLED"
      SupportedIdentityProviders: 
        - "COGNITO"
      CallbackURLs: 
        - !Ref CallbackURL
      AllowedOAuthFlows: 
        - "code"
        - "implicit"
      AllowedOAuthScopes: 
        - "aws.cognito.signin.user.admin"
        - !Sub "${CognitoUserPoolResourceServer}/read.data"
      AllowedOAuthFlowsUserPoolClient: true
      IdTokenValidity: 60
      AccessTokenValidity: 60
      TokenValidityUnits: 
        AccessToken: "minutes"
        IdToken: "minutes"
        RefreshToken: "days"

  CognitoUserPoolClientSystem:
    Type: "AWS::Cognito::UserPoolClient"
    Properties:
      UserPoolId: !Ref CognitoUserPool
      ClientName: !Sub "dlpf-client-${EnvironmentName}"
      RefreshTokenValidity: 5
      ExplicitAuthFlows: 
        - "ALLOW_CUSTOM_AUTH"
        - "ALLOW_REFRESH_TOKEN_AUTH"
        - "ALLOW_USER_PASSWORD_AUTH"
      GenerateSecret: true
      PreventUserExistenceErrors: "ENABLED"
      SupportedIdentityProviders: 
        - "COGNITO"
      AllowedOAuthFlows: 
        - "client_credentials"
      AllowedOAuthScopes: 
        - !Sub "${CognitoUserPoolResourceServer}/read.data"
      AllowedOAuthFlowsUserPoolClient: true
      IdTokenValidity: 60
      AccessTokenValidity: 60
      TokenValidityUnits: 
        AccessToken: "minutes"
        IdToken: "minutes"
        RefreshToken: "days"

  # System user for POS
  CognitoUserPoolUserPOS:
    Type: "AWS::Cognito::UserPoolUser"
    Properties:
      Username: !Sub "dlpf-client-pos-${EnvironmentName}"
      UserPoolId: !Ref CognitoUserPool
      UserAttributes: 
        - Name: "email"
          Value: !Ref AdminEmail
        - Name: "email_verified"
          Value: "true"

  # System user for OMS
  CognitoUserPoolUserOMS:
    Type: "AWS::Cognito::UserPoolUser"
    Properties:
      Username: !Sub "dlpf-client-oms-${EnvironmentName}"
      UserPoolId: !Ref CognitoUserPool
      UserAttributes: 
        - Name: "email"
          Value: !Ref AdminEmail
        - Name: "email_verified"
          Value: "true"

  # System user for EC
  CognitoUserPoolUserEC:
    Type: "AWS::Cognito::UserPoolUser"
    Properties:
      Username: !Sub "dlpf-client-ec-${EnvironmentName}"
      UserPoolId: !Ref CognitoUserPool
      UserAttributes: 
        - Name: "email"
          Value: !Ref AdminEmail
        - Name: "email_verified"
          Value: "true"

Outputs:
  UserPoolId:
    Description: "ID of the Cognito User Pool"
    Value: !Ref CognitoUserPool
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolId"
      
  UserPoolArn:
    Description: "ARN of the Cognito User Pool"
    Value: !GetAtt CognitoUserPool.Arn
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolArn"
      
  UserClientId:
    Description: "ID of the User Client"
    Value: !Ref CognitoUserPoolClientUser
    Export:
      Name: !Sub "${AWS::StackName}-UserClientId"
      
  SystemClientId:
    Description: "ID of the System Client"
    Value: !Ref CognitoUserPoolClientSystem
    Export:
      Name: !Sub "${AWS::StackName}-SystemClientId"