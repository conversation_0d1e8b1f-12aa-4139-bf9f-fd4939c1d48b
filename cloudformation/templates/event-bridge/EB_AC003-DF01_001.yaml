  # EventBridge Rule (Controlled by Cron Expression)
  MyScheduledEventRule:
    Type: AWS::Events::Rule
    Properties:
      Name: "EB_AC003-DF01_001"
      ScheduleExpression: "cron(0 12 * * ? *)"  # Runs every day at 12:00 PM UTC
      State: "DISABLED"
      Targets:
        - Arn: !GetAtt MyStepFunction.Arn
          Id: "MyStepFunctionTarget"
          RoleArn: arn:aws:iam::886436956581:role/MyEventBridgeRole
