# EventBridge Rule (Controlled by Cron Expression)
MyScheduledEventRule:
  Type: AWS::Events::Rule
  Properties:
    Name: "EB_SH002-FD01_001"
    ScheduleExpression:
      - "cron(0/15 9-22 ? * * *)" # 9:00から22:45まで
      - "cron(0 23 ? * * *)" # 23:00に実行
    State: "DISABLED"
    Targets:
      - Arn: !GetAtt MyStepFunction.Arn
        Id: "MyStepFunctionTarget"
        RoleArn: arn:aws:iam::886436956581:role/MyEventBridgeRole
