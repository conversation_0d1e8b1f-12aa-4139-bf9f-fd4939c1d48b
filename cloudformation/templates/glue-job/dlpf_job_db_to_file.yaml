AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  DbToFileJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_db_to_file
      Description: DLPF Glue job for DB to file conversion
      Role: !Sub arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_db_to_file.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        --TZ: Asia/Tokyo
        --disable-proxy: true
        --enable-job-insights: false
        --enable-observability-metrics: false
        --enable-continuous-cloudwatch-log: true
        --job-language: python
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-aurora-01"
          - !Sub "conn-${Environment}-dlpf-aurora-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
      ExecutionClass: STANDARD
# Parameters:
#   SecretName:
#     Type: String
#     Description: DB Secrets Manager secret name

#   ExecuteQuery:
#     Type: String
#     Description: SQL query ID for CSV/TSV, ETL process ID (yaml) for XML/fixed format

#   BatchSize:
#     Type: Number
#     Description: Number of records to retrieve at once
#     Default: 1000

#   OutputFileDir:
#     Type: String
#     Description: Output file directory path

#   FileSetting:
#     Type: String
#     Description: JSON format file settings
#     Default: '{"header":true,"delimiter":",","quote_char":"\"","line_ending":"¥n"}'

#   FileName:
#     Type: String
#     Description: Output file name

#   FileType:
#     Type: String
#     Description: Output file format
#     AllowedValues:
#       - csv
#       - tsv
#       - xml
#       - fixed

#   CooperationDestSystem:
#     Type: String
#     Description: Destination system name

#   S3BucketName:
#     Type: String
#     Description: S3 bucket name for Glue job scripts and data
#     Default: aws-glue-assets-{AccountId}-ap-northeast-1

#   GlueScriptPath:
#     Type: String
#     Description: S3 path to the Glue job script
#     Default: scripts/source/glue_job_db_to_file.py

#   GlueRole:
#     Type: String
#     Description: IAM role ARN for Glue job execution
#     Default: arn:aws:iam::{AccountId}:role/AWSGlueServiceRole-{Environment}-dlpf-glue-job

#   JobnetId:
#     Type: String
#     Description: Jobnet ID
