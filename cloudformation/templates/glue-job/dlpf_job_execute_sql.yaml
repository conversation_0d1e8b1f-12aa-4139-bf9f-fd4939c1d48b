AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  ExecuteSqlJob:
    Type: AWS::Glue::Job
    Properties:
      Name: job_execute_sql
      Description: DLPF Glue job for excuting sql
      Role: !Sub "arn:aws:iam::${AccountId}:role/AWSGlueServiceRole-${Environment}-dlpf-glue-job"
      ExecutionProperty:
        MaxConcurrentRuns: 100
      Command:
        Name: pythonshell
        ScriptLocation: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/source/glue_job_execute_sql.py"
        PythonVersion: 3.9
      DefaultArguments:
        --enable-metrics: true
        --extra-py-files: !Sub "s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-${AccountId}-ap-northeast-1/scripts/deps.zip"
        --enable-job-insights: false
        --enable-observability-metrics: false
        --enable-continuous-cloudwatch-log: true
        --job-language: python
      Connections:
        Connections:
          - !Sub "conn-${Environment}-dlpf-aurora-01"
          - !Sub "conn-${Environment}-dlpf-aurora-02"
      MaxRetries: 0
      AllocatedCapacity: 0
      Timeout: 2880
      MaxCapacity: 0.0625
      GlueVersion: 3.0
      ExecutionClass: STANDARD
