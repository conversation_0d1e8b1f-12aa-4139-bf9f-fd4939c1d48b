AWSTemplateFormatVersion: "2010-09-09"
Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)

Resources:
    LambdaFunction:
        Type: "AWS::Lambda::Function"
        DeletionPolicy: Delete
        Properties:
            Description: ""
            FunctionName: "DLPF_RETURN_PARALLEL_NUM_ARRAY"
            Handler: "lambda_function.lambda_handler"
            Architectures: 
              - "x86_64"
            Code: 
                ZipFile: "def handler(event, context): return 'placeholder'"
            MemorySize: 128
            Role: !Sub "arn:aws:iam::${AWS::AccountId}:role/role-${Environment}-dlpf-lambda-invokesfn"
            Runtime: "python3.13"
            Timeout: 3
            TracingConfig: 
                Mode: "PassThrough"
            EphemeralStorage: 
                Size: 512