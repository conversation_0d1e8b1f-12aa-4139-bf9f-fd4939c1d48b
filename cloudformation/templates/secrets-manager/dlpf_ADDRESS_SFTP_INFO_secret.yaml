AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretAddressSftpInfo:
    Type: String
    Default: ''
    Description: 'input ADDRESS_SFTP_INFO secret value'

Resources:
  SeacretAddressSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: ADDRESS_SFTP_INFO
      Description: 'Database credentials for DB user dlpf_api'
      SecretString:  !Ref ParamSeacretAddressSftpInfo