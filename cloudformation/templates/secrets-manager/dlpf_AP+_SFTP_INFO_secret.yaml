AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretApPSftpInfo:
    Type: String
    Default: ''
    Description: 'input AP+_SFTP_INFO secret value'

Resources:
  SeacretApPSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: AP+_SFTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretApPSftpInfo