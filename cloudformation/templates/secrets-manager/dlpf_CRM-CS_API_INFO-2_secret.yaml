AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretCrm-CsApiInfo-2:
    Type: String
    Default: ''
    Description: 'input CRM-CS_API_INFO-2 secret value'

Resources:
  SeacretCrm-CsApiInfo-2Secret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: CRM-CS_API_INFO-2
      Description: ''
      SecretString:  !Ref ParamSeacretCrm-CsApiInfo-2