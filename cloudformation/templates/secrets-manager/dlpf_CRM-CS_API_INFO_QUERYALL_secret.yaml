AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretCrm-CsApiInfoQueryall:
    Type: String
    Default: ''
    Description: 'input CRM-CS_API_INFO_QUERYALL secret value'

Resources:
  SeacretCrm-CsApiInfoQueryallSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: CRM-CS_API_INFO_QUERYALL
      Description: ''
      SecretString:  !Ref ParamSeacretCrm-CsApiInfoQueryall