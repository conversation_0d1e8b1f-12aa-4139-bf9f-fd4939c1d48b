AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretCrm-CsApiInfo:
    Type: String
    Default: ''
    Description: 'input CRM-CS_API_INFO secret value'

Resources:
  SeacretCrm-CsApiInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: CRM-CS_API_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretCrm-CsApiInfo