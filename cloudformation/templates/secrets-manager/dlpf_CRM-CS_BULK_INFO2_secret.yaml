AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretCrm-CsBulkInfo2:
    Type: String
    Default: ''
    Description: 'input CRM-CS_BULK_INFO2 secret value'

Resources:
  SeacretCrm-CsBulkInfo2Secret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: CRM-CS_BULK_INFO2
      Description: ''
      SecretString:  !Ref ParamSeacretCrm-CsBulkInfo2