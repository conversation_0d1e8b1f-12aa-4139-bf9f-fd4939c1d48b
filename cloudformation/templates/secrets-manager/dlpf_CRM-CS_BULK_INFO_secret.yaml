AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamCrm-CsBulkInfo:
    Type: String
    Default: ''
    Description: 'input CRM-CS_BULK_INFO secret value'

Resources:
  Crm-CsBulkInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: CRM-CS_BULK_INFO
      Description: ''
      SecretString:  !Ref ParamCrm-CsBulkInfo