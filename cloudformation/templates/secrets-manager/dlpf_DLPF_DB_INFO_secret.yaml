AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretDlpfDbInfo:
    Type: String
    Default: ''
    Description: 'input DLPF_DB_INFO secret value'

Resources:
  SeacretDlpfDbInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DLPF_DB_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretDlpfDbInfo