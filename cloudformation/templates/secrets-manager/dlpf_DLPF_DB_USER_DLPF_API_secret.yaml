AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretDlpfDbUserDlpfApi:
    Type: String
    Default: ''
    Description: 'input DLPF_DB_USER_DLPF_API secret value'

Resources:
  SeacretDlpfDbUserDlpfApiSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DLPF_DB_USER_DLPF_API
      Description: ''
      SecretString:  !Ref ParamSeacretDlpfDbUserDlpfApi