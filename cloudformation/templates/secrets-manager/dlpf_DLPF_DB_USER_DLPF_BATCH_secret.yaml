AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretDlpfDbUserDlpfBatch:
    Type: String
    Default: ''
    Description: 'input DLPF_DB_USER_DLPF_BATCH secret value'

Resources:
  SeacretDlpfDbUserDlpfBatchSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DLPF_DB_USER_DLPF_BATCH
      Description: ''
      SecretString:  !Ref ParamSeacretDlpfDbUserDlpfBatch