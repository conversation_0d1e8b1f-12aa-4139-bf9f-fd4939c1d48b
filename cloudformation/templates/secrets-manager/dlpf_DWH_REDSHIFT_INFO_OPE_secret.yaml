AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretDwhRedshiftInfoOpe:
    Type: String
    Default: ''
    Description: 'input DWH_REDSHIFT_INFO_OPE secret value'

Resources:
  SeacretDwhRedshiftInfoOpeSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DWH_REDSHIFT_INFO_OPE
      Description: ''
      SecretString:  !Ref ParamSeacretDwhRedshiftInfoOpe