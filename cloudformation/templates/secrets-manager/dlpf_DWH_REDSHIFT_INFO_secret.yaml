AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretDwhRedshiftInfo:
    Type: String
    Default: ''
    Description: 'input DWH_REDSHIFT_INFO secret value'

Resources:
  SeacretDwhRedshiftInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DWH_REDSHIFT_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretDwhRedshiftInfo