AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretGloviaFtpInfo:
    Type: String
    Default: ''
    Description: 'input GLOVIA_FTP_INFO secret value'

Resources:
  SeacretGloviaFtpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: GLOVIA_FTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretGloviaFtpInfo