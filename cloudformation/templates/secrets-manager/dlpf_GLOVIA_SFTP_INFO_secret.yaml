AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretGloviaSftpInfo:
    Type: String
    Default: ''
    Description: 'input GLOVIA_SFTP_INFO secret value'

Resources:
  SeacretGloviaSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: GLOVIA_SFTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretGloviaSftpInfo