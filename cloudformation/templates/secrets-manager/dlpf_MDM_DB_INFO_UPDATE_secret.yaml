AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretMdmDbInfoUpdate:
    Type: String
    Default: ''
    Description: 'input MDM_DB_INFO_UPDATE secret value'

Resources:
  SeacretMdmDbInfoUpdateSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: MDM_DB_INFO_UPDATE
      Description: ''
      SecretString:  !Ref ParamSeacretMdmDbInfoUpdate