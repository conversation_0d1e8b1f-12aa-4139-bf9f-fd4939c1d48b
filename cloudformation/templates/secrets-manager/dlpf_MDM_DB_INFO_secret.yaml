AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretMdmDbInfo:
    Type: String
    Default: ''
    Description: 'input MDM_DB_INFO secret value'

Resources:
  SeacretMdmDbInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: MDM_DB_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretMdmDbInfo