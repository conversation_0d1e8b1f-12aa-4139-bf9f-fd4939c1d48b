AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretOmsCustomerApiInfo:
    Type: String
    Default: ''
    Description: 'input OMS_CUSTOMER_API_INFO secret value'

Resources:
  SeacretOmsCustomerApiInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: OMS_CUSTOMER_API_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretOmsCustomerApiInfo