AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretOmsDbInfo:
    Type: String
    Default: ''
    Description: 'input OMS_DB_INFO secret value'

Resources:
  SeacretOmsDbInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: OMS_DB_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretOmsDbInfo