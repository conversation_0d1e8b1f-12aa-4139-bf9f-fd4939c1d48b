AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretSagawaSftpInfo:
    Type: String
    Default: ''
    Description: 'input SAGAWA_SFTP_INFO secret value'

Resources:
  SeacretSagawaSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: SAGAWA_SFTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretSagawaSftpInfo