AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretSasSftpInfo:
    Type: String
    Default: ''
    Description: 'input SAS_SFTP_INFO secret value'

Resources:
  SeacretSasSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: SAS_SFTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretSasSftpInfo