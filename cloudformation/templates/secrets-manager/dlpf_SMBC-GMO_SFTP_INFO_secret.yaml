AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretSmbc-GmoSftpInfo:
    Type: String
    Default: ''
    Description: 'input SMBC-GMO_SFTP_INFO secret value'

Resources:
  SeacretSmbc-GmoSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: SMBC-GMO_SFTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretSmbc-GmoSftpInfo