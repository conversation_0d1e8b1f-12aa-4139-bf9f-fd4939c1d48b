AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretWorkspaceFtpInfo:
    Type: String
    Default: ''
    Description: 'input WORKSPACE_FTP_INFO secret value'

Resources:
  SeacretWorkspaceFtpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: WORKSPACE_FTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretWorkspaceFtpInfo