AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets

Parameters:
  ParamSeacretYamatoSftpInfo:
    Type: String
    Default: ''
    Description: 'input YAMATO_SFTP_INFO secret value'

Resources:
  SeacretYamatoSftpInfoSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: YAMATO_SFTP_INFO
      Description: ''
      SecretString:  !Ref ParamSeacretYamatoSftpInfo