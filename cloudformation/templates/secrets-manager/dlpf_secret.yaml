AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for creating Secrets Manager secrets for
  database and SFTP connections

Resources:
  DLPFDBSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DLPF_DB_INFO
      Description: Database credentials for DLPF
      SecretString: !Sub |
        {
          "host": "aurorapri.dlpf.dhcaws.local",
          "port": "5432",
          "dbname": "dlpf",
          "username": "dlpf_batch",
          "password": "環境定義書に参照"
        }

  # OMSDBSecret:
  #   Type: AWS::SecretsManager::Secret
  #   Properties:
  #     Name: OMS_DB_INFO
  #     Description: Database credentials for OMS
  #     SecretString: !Sub |
  #       {
  #         "host":"oms.cluster-ro-c9oguq08u76t.ap-northeast-1.rds.amazonaws.com",
  #         "port":"5432",
  #         "dbname":"webshopdb",
  #         "username":"dlpf",
  #         "password":"?7f7}YyGt<G5$!nfFPHERef)"
  #       }

  DLPFDBUSERDLPFBATCHSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DLPF_DB_USER_DLPF_BATCH
      Description: Database credentials for DB user dlpf_batch
      SecretString: !Sub |
        {
          "username": "dlpf_batch",
          "password": "環境定義書に参照"
        }

  DLPFDBUSERDLPFAPISecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: DLPF_DB_USER_DLPF_API
      Description: Database credentials for DB user dlpf_api
      SecretString: !Sub |
        {
          "username": "dlpf_api",
          "password": "環境定義書に参照"
        }

  OMSCUSTOMERAPISecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: OMS_CUSTOMER_API_INFO
      Description: API credentials for OMS customer api
      SecretString: !Sub |
        {
          "api.customer.domain": "環境定義書に参照",
          "api.customer.authentication": "環境定義書に参照"
        }