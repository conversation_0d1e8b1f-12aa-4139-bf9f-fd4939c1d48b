AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudFormation template to create security group for DLPF Glue jobs'

Parameters:
  Environment:
    Type: String
    Description: Environment name (e.g., dev, stg, prd)

  VpcId:
    Type: String
    Description: VPC where the security group will be created

Resources:
  DLPFGlueSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub sgroup-${Environment}-dlpf-glue-01
      GroupDescription: Security group for DLPF Glue jobs
      VpcId: !Ref VpcId
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: Allow all outbound traffic
      Tags:
        - Key: Name
          Value: !Sub sgroup-${Environment}-dlpf-glue-01
        - Key: Environment
          Value: !Ref Environment

  # Create a separate resource for the self-referencing ingress rule
  SelfReferencingIngressRule:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !GetAtt DLPFGlueSecurityGroup.GroupId
      IpProtocol: tcp
      FromPort: 0
      ToPort: 65535
      SourceSecurityGroupId: !GetAtt DLPFGlueSecurityGroup.GroupId
      Description: Allow all TCP traffic from self
