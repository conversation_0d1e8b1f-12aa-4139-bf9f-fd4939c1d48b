AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Campaign Data Integration Jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_CP001-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: |
        {
          "Comment": "State machine for Campaign Data Integration JN_CP001-DF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "parallel_csv_generation"
            },
            "parallel_csv_generation": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_job_db_to_file",
                  "States": {
                    "1_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_001",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_instructions_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_001",
                          "file_id": "campaign_instructions.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_job_db_to_file",
                  "States": {
                    "2_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_002",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_customer_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_002",
                          "file_id": "campaign_customer.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "3_job_db_to_file",
                  "States": {
                    "3_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_003",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_order_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_003",
                          "file_id": "campaign_order.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "4_job_db_to_file",
                  "States": {
                    "4_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_004",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_order_group_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_004",
                          "file_id": "campaign_order_group.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "5_job_db_to_file",
                  "States": {
                    "5_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_005",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_promotion_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_005",
                          "file_id": "campaign_promotion.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "6_job_db_to_file",
                  "States": {
                    "6_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_006",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_instructions_commodity_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_006",
                          "file_id": "campaign_instructions_commodity.csv"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "7_job_db_to_file",
                  "States": {
                    "7_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "OMS_DB_INFO",
                          "execute_query": "sql_CP001-DF01_select_007",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'tmp/job_db_to_file/JN_CP001-DF01_001_' & $full_date & '/' %}",
                          "file_name": "{% 'campaign_combi_limit_' & $full_date & '.csv' %}",
                          "file_type": "csv",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "file_setting": "{\"quote_char\":\"\\\"\"}",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_007",
                          "file_id": "campaign_combi_limit.csv"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "PrepareDateParameters"
            },
            "PrepareDateParameters": {
              "Type": "Pass",
              "InputPath": "$[0]",
              "Parameters": {
                "timestamp.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($.Arguments.file_name, '_'), 2), '.'), 0)"
              },
              "Next": "parallel_db_import"
            },
            "parallel_db_import": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "8_job_internal_db_import",
                  "States": {
                    "8_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_instructions_{}.csv', $.timestamp)",
                          "import_table": "campaign_instructions_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_001",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "9_job_internal_db_import",
                  "States": {
                    "9_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_customer_{}.csv', $.timestamp)",
                          "import_table": "campaign_customer_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_002",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "10_job_internal_db_import",
                  "States": {
                    "10_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_order_{}.csv', $.timestamp)",
                          "import_table": "campaign_order_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_003",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "11_job_internal_db_import",
                  "States": {
                    "11_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_order_group_{}.csv', $.timestamp)",
                          "import_table": "campaign_order_group_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_004",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "12_job_internal_db_import",
                  "States": {
                    "12_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_promotion_{}.csv', $.timestamp)",
                          "import_table": "campaign_promotion_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_005",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "13_job_internal_db_import",
                  "States": {
                    "13_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_instructions_commodity_{}.csv', $.timestamp)",
                          "import_table": "campaign_instructions_commodity_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_006",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "14_job_internal_db_import",
                  "States": {
                    "14_job_internal_db_import": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "input_file_name.$": "States.Format('campaign_combi_limit_{}.csv', $.timestamp)",
                          "import_table": "campaign_combi_limit_work",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "query_upsert": "sql_CP001-DF01_upsert_007",
                          "input_format_options": "(FORMAT CSV)"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "PrepareDateParametersForXml"
            },
            "PrepareDateParametersForXml": {
              "Type": "Pass",
              "InputPath": "$[0]",
              "Parameters": {
                "timestamp.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($.Arguments.input_file_name, '_'), 2), '.'), 0)"
              },
              "Next": "15_job_db_to_file"
            },
            "15_job_db_to_file": {
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Parameters": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "etl_CP001-DF01_txCampaignPromotions",
                  "batch_size": "1000",
                  "output_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                  "file_name.$": "States.Format('txCampaignPromotions_{}.xml', $.timestamp)",
                  "file_type": "xml",
                  "jobnet_id": "JN_CP001-DF01_001",
                  "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_008",
                  "file_id": "txCampaignPromotions.xml"
                }
              },
              "InputPath": "$",
              "ResultPath": "$.15_job_db_to_file_result",
              "Next": "parallel_xml_generation"
            },
            "parallel_xml_generation": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "16_job_db_to_file",
                  "States": {
                    "16_job_db_to_file": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "etl_CP001-DF01_txCampaignPricebooks",
                          "batch_size": "1000",
                          "output_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txCampaignPricebooks_{}.xml', $.timestamp)",
                          "file_type": "xml",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_009",
                          "file_id": "txCampaignPricebooks.xml"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "17_job_db_to_file",
                  "States": {
                    "17_job_db_to_file": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "etl_CP001-DF01_txStaticCustomerGroups",
                          "batch_size": "1000",
                          "output_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txStaticCustomerGroups_{}.xml', $.timestamp)",
                          "file_type": "xml",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_010",
                          "file_id": "txStaticCustomerGroups.xml"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "18_job_db_to_file",
                  "States": {
                    "18_job_db_to_file": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "etl_CP001-DF01_txDynamicCustomerGroups",
                          "batch_size": "1000",
                          "output_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txDynamicCustomerGroups_{}.xml', $.timestamp)",
                          "file_type": "xml",
                          "jobnet_id": "JN_CP001-DF01_001",
                          "diff_base_timestamp_query": "sql_CP001-DF01_timestamp_011",
                          "file_id": "txDynamicCustomerGroups.xml"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "PrepareDateParametersForZip"
            },
            "PrepareDateParametersForZip": {
              "Type": "Pass",
              "InputPath": "$[0]",
              "Parameters": {
                "timestamp.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($.Arguments.file_name, '_'), 1), '.'), 0)"
              },
              "Next": "parallel_compress_file"
            },
            "parallel_compress_file": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "19_job_file_compress",
                  "States": {
                    "19_job_file_compress": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_file_compress",
                        "Arguments": {
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txCampaignPromotions_{}.xml', $.timestamp)",
                          "output_file_dir.$": "States.Format('input-output/EC_OUT/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "output_file_name.$": "States.Format('txCampaignPromotions_{}.zip', $.timestamp)",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "20_job_file_compress",
                  "States": {
                    "20_job_file_compress": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_file_compress",
                        "Arguments": {
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txCampaignPricebooks_{}.xml', $.timestamp)",
                          "output_file_dir.$": "States.Format('input-output/EC_OUT/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "output_file_name.$": "States.Format('txCampaignPricebooks_{}.zip', $.timestamp)",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "21_job_file_compress",
                  "States": {
                    "21_job_file_compress": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_file_compress",
                        "Arguments": {
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txStaticCustomerGroups_{}.xml', $.timestamp)",
                          "output_file_dir.$": "States.Format('input-output/EC_OUT/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "output_file_name.$": "States.Format('txStaticCustomerGroups_{}.zip', $.timestamp)",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "22_job_file_compress",
                  "States": {
                    "22_job_file_compress": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Parameters": {
                        "JobName": "job_file_compress",
                        "Arguments": {
                          "input_file_dir.$": "States.Format('tmp/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "file_name.$": "States.Format('txDynamicCustomerGroups_{}.xml', $.timestamp)",
                          "output_file_dir.$": "States.Format('input-output/EC_OUT/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "output_file_name.$": "States.Format('txDynamicCustomerGroups_{}.zip', $.timestamp)",
                          "backup_flag": "True",
                          "backup_file_dir.$": "States.Format('back-up/job_db_to_file/JN_CP001-DF01_001_{}/', $.timestamp)",
                          "jobnet_id": "JN_CP001-DF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "End": true
            }
          }
        }
