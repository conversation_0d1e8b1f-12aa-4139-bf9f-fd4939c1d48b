AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_CS008_DD01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_CS008_DD01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_db_to_file"
            },
            "1_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "OMS_DB_INFO",
                  "execute_query": "sql_CS008_DD01_select_001",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'tmp/job_db_to_file/JN_CS008_DD01_001_' & $full_date & '/' %}",
                  "file_setting": "{\"header\": false, \"delimiter\": \",\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\n\"}",
                  "file_name": "{% 'customer_' & $full_date & '.csv' %}",
                  "file_type": "csv",
                  "jobnet_id": "JN_CS008_DD01_001",
                  "diff_base_timestamp_query": "sql_CS008_DD01_timestamp_001",
                  "file_id": "customer"
                }
              },
              "Next": "2_job_internal_db_import"
            },
            "2_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT csv, HEADER false)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_db_to_file/JN_CS008_DD01_001_' & $full_date & '/' %}",
                  "input_file_name": "{% 'customer_' & $full_date & '.csv' %}",
                  "import_table": "customer_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_db_to_file/JN_CS008_DD01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_CS008_DD01_001",
                  "query_upsert": "sql_CS008_DD01_upsert_001"
                }
              },
              "End": true
            }
          }
        }
