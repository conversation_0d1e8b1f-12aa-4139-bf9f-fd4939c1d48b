AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prod)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_DW100-AF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  # お気に入り商品_001
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_PT001-AF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                  "now_timestamp": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}",
                  "extract_from": "{% $fromMillis($toMillis($fromMillis($toMillis($now()), '[Y0001]-[M01]-[D01]', '+0900')) - 57*60*60*1000) %}"
              },
              "Next": "1_job_api_to_file"
            },
            "1_job_api_to_file": {
              "Type": "Task",
              "QueryLanguage": "JSONata",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_api_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "api_secret_name": "CRM-CS_API_INFO_QUERYALL",
                  "diff_flag": "False",
                  "file_name": "IF-CRM-FA-001.csv",
                  "output_file_dir": "tmp/job_api_to_file/JN_DW100-AF01_001/",
                  "file_setting": "{ \"sep\": \",\", \"quoting\": \"csv.QUOTE_MINIMAL\", \"lineterminator\": \"\\n\", \"header\": \"True\"  }",
                  "jobnet_id": "JN_DW100-AF01_001",
                  "api_request_param": "{% 'q=SELECT+Id%2COwnerId%2CIsDeleted%2CName%2CCreatedDate%2CCreatedById%2CLastModifiedDate%2CLastModifiedById%2CSystemModstamp%2CAccountId__c%2CProductId__c%2CProductCode__c%2CProductName__c%2CIsDeleted__c%20FROM%20FavoriteProduct__c%20WHERE%20LastModifiedDate%3E%3D' & $extract_from  %}",
                  "api_request_method": "GET",
                  "records_key": "records",
                  "output_keys": "Id,OwnerId,IsDeleted,Name,CreatedDate,CreatedById,LastModifiedDate,LastModifiedById,SystemModstamp,AccountId__c,ProductId__c,ProductCode__c,ProductName__c,IsDeleted__c"
                }
              },
              "Next": "2_job_internal_db_import"
            },
            "2_job_internal_db_import": {
              "Type": "Task",
              "QueryLanguage": "JSONata",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT csv, HEADER true)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "tmp/job_api_to_file/JN_DW100-AF01_001/",
                  "input_file_name": "IF-CRM-FA-001.csv",
                  "import_table": "favorite_product_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/CRM-CS_IN/IF-DW100-AF01_' & $now_timestamp & '/' %}",
                  "jobnet_id": "JN_DW100-AF01_001",
                  "query_upsert": "sql_DW100-AF01_upsert_001"
                }
              },
              "Next": "3_job_db_to_file"
            },
            "3_job_db_to_file": {
              "Type": "Task",
              "QueryLanguage": "JSONata",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_DW100-AF01_select_001",
                  "output_file_dir": "{% 'input-output/DLPF_DWH/IF-CRM-FA-001_' & $now_timestamp & '/' %}",
                  "file_setting": "{\"delimiter\" : \",\",\"enclosed text\" : \"\\\"\",\"new line code\" : \"LF\",\"header\" :true} ",
                  "file_name": "IF-CRM-FA-001.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_DW100-AF01_001",
                  "diff_base_timestamp_query": "sql_DWcommon_select_001",
                  "file_id": "IF-CRM-FA-001"
                }
              },
              "Next": "4_job_internal_db_clear"
            },
            "4_job_internal_db_clear": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_clear",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "query_delete": "sql_DW100-AF01_delete_001",
                  "truncate_table": "favorite_product",
                  "delete_start": "{% '' & $now_timestamp & '' %}",
                  "delete_end": "{% '' & $now_timestamp & '' %}",
                  "jobnet_id": "JN_DW100-AF01_001"
                }
              },
              "End": true
            }
          }
        }
  EventRule:
    Type: "AWS::Events::Rule"
    Properties:
      Name: "EB_DW100-FF01_001"
      ScheduleExpression: "cron(30 15 * * ? *)"
      State: "DISABLED"
      Targets:
        - Arn: !GetAtt GlueOrchestrationStateMachine.Arn
          Id: "StepFunctionsTarget"
          RoleArn: !Sub "arn:aws:iam::${AccountId}:role/MyEventBridgeRole"
