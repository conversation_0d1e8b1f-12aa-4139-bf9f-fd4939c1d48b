AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prod)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_DW117-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_DW117-DF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                  "now_timestamp": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_db_to_file"
            },
            "1_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "OMS_DB_INFO",
                  "execute_query": "sql_DW117-DF01_select_001",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'input-output/DLPF_DWH/IF-OMS-SP-003_' & $now_timestamp & '/' %}",
                  "file_setting": "{\"delimiter\" : \",\",\"enclosed text\" : \"\\\"\",\"new line code\" : \"LF\",\"header\" :true} ",
                  "file_name": "IF-OMS-SP-003.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_DW117-DF01_001",
                  "diff_base_timestamp_query": "sql_DWcommon_select_001",
                  "file_id": "IF-OMS-SP-003"
                }
              },
              "End": true
            }
          }
        }
  EventRule:
    Type: "AWS::Events::Rule"
    Properties:
      Name: "EB_DW117-DF01_001"
      ScheduleExpression: "cron(30 15 * * ? *)"
      State: "DISABLED"
      Targets:
        - Arn: !GetAtt GlueOrchestrationStateMachine.Arn
          Id: "StepFunctionsTarget"
          RoleArn: !Sub "arn:aws:iam::${AccountId}:role/MyEventBridgeRole"
