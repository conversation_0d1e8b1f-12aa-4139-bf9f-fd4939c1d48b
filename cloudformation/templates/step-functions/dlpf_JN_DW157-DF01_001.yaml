AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prod)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_DW157-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_DW157-DF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                  "now_timestamp": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_get_file"
            },
            "1_job_get_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_get_file",
                "Arguments": {
                  "source_secret_name": "COMMONS_SFTP_INFO",
                  "source_remote_file_full_path": "Commons/ToInterface/0069_DWHZaikoMove_*.tsv",
                  "s3_storage_file_full_path": "tmp/job_get_file/JN_DW157-DF01_001/IF-CMN-ST-002.csv",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/COMMONS_IN/IF-DW157-DF01_' & $now_timestamp & '/' %}",
                  "jobnet_id": "JN_DW157-DF01_001",
                  "multi_file_mode": "0"
                }
              },
              "Next": "2_job_internal_db_import"
            },
            "2_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT csv, HEADER true,DELIMITER E'\\t')",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "tmp/job_get_file/JN_DW157-DF01_001/",
                  "input_file_name": "IF-CMN-ST-002.csv",
                  "import_table": "seruring_move_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/COMMONS_IN/IF-DW157-DF01_' & $now_timestamp & '/' %}",
                  "jobnet_id": "JN_DW157-DF01_001",
                  "query_upsert": "sql_DW157-DF01_upsert_001"
                }
              },
              "Next": "3_job_db_to_file"
            },
            "3_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_DW157-DF01_select_001",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'input-output/DLPF_DWH/IF-CMN-ST-002_' & $now_timestamp & '/' %}",
                  "file_setting": "{\"delimiter\" : \",\",\"enclosed text\" : \"\\\"\",\"new line code\" : \"LF\",\"header\" :true} ",
                  "file_name": "IF-CMN-ST-002.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_DW157-DF01_001",
                  "diff_base_timestamp_query": "sql_DWcommon_select_001",
                  "file_id": "IF-CMN-ST-002"
                }
              },
              "Next": "4_job_internal_db_clear"
            },
            "4_job_internal_db_clear": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_clear",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "query_delete": "sql_DW157-DF01_delete_001",
                  "truncate_table": "seruring_move",
                  "delete_start": "{% '' & $now_timestamp & '' %}",
                  "delete_end": "{% '' & $now_timestamp & '' %}",
                  "jobnet_id": "JN_DW157-DF01_001"
                }
              },
              "End": true
            }
          }
        }
  EventRule:
    Type: "AWS::Events::Rule"
    Properties:
      Name: "EB_DW157-DF01_001"
      ScheduleExpression: "cron(30 16 * * ? *)"
      State: "DISABLED"
      Targets:
        - Arn: !GetAtt GlueOrchestrationStateMachine.Arn
          Id: "StepFunctionsTarget"
          RoleArn: !Sub "arn:aws:iam::${AccountId}:role/MyEventBridgeRole"
