AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_PR001-FF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF_PR001-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
            "Next": "parallel_1_to_4"
          },
            "parallel_1_to_4": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_job_db_to_file",
                  "States": {
                    "1_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_PR001-FF01_select_001",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/DLPF_OMS/JN_PR001-FF01_001_' & $full_date & '/' %}",
                          "file_setting": "{ \"delimiter\": \"\\t\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\r\\n\", \"header\": true }",
                          "file_name": "EISR0020.tsv",
                          "file_type": "tsv",
                          "jobnet_id": "JN_PR001-FF01_001",
                          "diff_base_timestamp_query": "sql_PR001-FF01_timestamp_001",
                          "file_id": "EISR0020"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_job_db_to_file",
                  "States": {
                    "2_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_PR001-FF01_select_002",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/DLPF_OMS/JN_PR001-FF01_001_' & $full_date & '/' %}",
                          "file_setting": "{ \"delimiter\": \"\\t\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\r\\n\", \"header\": true }",
                          "file_name": "EISR0030.tsv",
                          "file_type": "tsv",
                          "jobnet_id": "JN_PR001-FF01_001",
                          "diff_base_timestamp_query": "sql_PR001-FF01_timestamp_001",
                          "file_id": "EISR0030"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "3_job_db_to_file",
                  "States": {
                    "3_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_PR001-FF01_select_003",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/DLPF_OMS/JN_PR001-FF01_001_' & $full_date & '/' %}",
                          "file_setting": "{ \"delimiter\": \"\\t\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\r\\n\", \"header\": true }",
                          "file_name": "EISR0110.tsv",
                          "file_type": "tsv",
                          "jobnet_id": "JN_PR001-FF01_001",
                          "diff_base_timestamp_query": "sql_PR001-FF01_timestamp_002",
                          "file_id": "EISR0110"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "4_job_db_to_file",
                  "States": {
                    "4_job_db_to_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_db_to_file",
                        "Arguments": {
                          "secret_name": "DLPF_DB_INFO",
                          "execute_query": "sql_PR001-FF01_select_004",
                          "batch_size": "1000",
                          "output_file_dir": "{% 'input-output/DLPF_OMS/JN_PR001-FF01_001_' & $full_date & '/' %}",
                          "file_setting": "{ \"delimiter\": \"\\t\", \"quote_char\": \"\\\"\", \"line_ending\": \"\\r\\n\", \"header\": true }",
                          "file_name": "EISR0120.tsv",
                          "file_type": "tsv",
                          "jobnet_id": "JN_PR001-FF01_001",
                          "diff_base_timestamp_query": "sql_PR001-FF01_timestamp_003",
                          "file_id": "EISR0120"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "End": true
            }
          }
        }
