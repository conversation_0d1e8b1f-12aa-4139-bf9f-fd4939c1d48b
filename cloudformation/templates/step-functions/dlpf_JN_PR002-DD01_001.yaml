AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_PR002-DD01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  # 商品マスタ_001
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_PR002-DD01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_db_to_file"
            },
            "1_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_PR002-DD01_001_select_001",
                  "batch_size": "1000",
                  "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR002-DD01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"delimiter\": \",\", \"line_ending\": \"\\n\", \"header\": false }",
                  "file_name": "{% 'TGOODS_' & $full_date & '.CSV' %}",
                  "file_type": "csv",
                  "jobnet_id": "JN_PR002-DD01_001",
                  "diff_base_timestamp_query": "sql_PR002-DD01_timestamp_001",
                  "file_id": "TGOODS"
                }
              },
              "Next": "2_job_convert_character_encoding"
            },
            "2_job_convert_character_encoding": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_convert_character_encoding",
                "Arguments": {
                  "input_character_encoding": "utf-8",
                  "output_character_encoding": "ms932",
                  "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR002-DD01_001_' & $full_date & '/' %}",
                  "file_name": "{% 'TGOODS_' & $full_date & '.CSV' %}",
                  "output_file_dir": "ext-input-output/DLPF_WMS/JN_PR002-DD01_001/",
                  "backup_flag": "True",
                  "backup_file_dir": "back-up/job_db_to_file/JN_PR002-DD01_001/",
                  "jobnet_id": "JN_PR002-DD01_001"
                }
              },
              "End": true
            }
          }
        }
