AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_PR003-DF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF_PR003-DF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "GetParallelNum"
            },
            "GetParallelNum": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "parallel_num_master_catalog": 3,
                "parallel_num_price": 3
              },
              "Next": "1_sql_execute"
            },
            "1_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_PR003-DF01_001",
                  "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_PR003-DF01_before_001\",\"params\": {\"split_num\":\"' & $parallel_num_master_catalog & '\"}},{\"query_id\": \"sql_PR003-DF01_csv_export_001\", \"params\": {\"object_path\":\"tmp/job_db_to_file/JN_PR003-DF01_001/JN_PR003-DF01_001_temp_csv.csv\"}},{\"query_id\": \"sql_PR003-DF01_csv_export_002\", \"params\": {\"object_path\":\"tmp/job_db_to_file/JN_PR003-DF01_001/JN_PR003-DF01_002_temp_csv.csv\"}}]}' %}",
                  "file_id": "txProduct"
                }
              },
              "Next": "2_DLPF_RETURN_PARALLEL_NUM_ARRAY"
            },
            "2_DLPF_RETURN_PARALLEL_NUM_ARRAY": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Next": "XML_Map_Master_Catalog",
              "QueryLanguage": "JSONata",
              "Arguments": {
                "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST",
                "Payload": {
                  "parallel_num": "{% $parallel_num_master_catalog %}"
                }
              },
              "Assign": {
                "parallel_num_array": "{% $states.result.Payload %}"
              }
            },
            "XML_Map_Master_Catalog": {
              "Type": "Map",
              "ItemProcessor": {
                "ProcessorConfig": {
                  "Mode": "INLINE"
                },
                "StartAt": "job_db_to_file_Master_Catalog",
                "States": {
                  "job_db_to_file_Master_Catalog": {
                    "QueryLanguage": "JSONata",
                    "Type": "Task",
                    "Resource": "arn:aws:states:::glue:startJobRun.sync",
                    "Arguments": {
                      "JobName": "job_db_to_file",
                      "Arguments": {
                        "secret_name": "DLPF_DB_INFO",
                        "execute_query": "etl_PR003-DF01_select_001",
                        "batch_size": "1000",
                        "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "file_name": "{% 'txProduct_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\")  & '.xml' %}",
                        "file_type": "xml",
                        "jobnet_id": "JN_PR003-DF01_001",
                        "diff_base_timestamp_query": "sql_PR003-DF01_timestamp_001",
                        "file_id": "txProduct",
                        "split_num": "{% $string($states.input.split_num) %}",
                        "tmp_csv_file": "{\"bundled_products_query\":\"/tmp/job_db_to_file/JN_PR003-DF01_001/JN_PR003-DF01_001_temp_csv.csv\", \"regular_products_query\":\"/tmp/job_db_to_file/JN_PR003-DF01_001/JN_PR003-DF01_002_temp_csv.csv\"}",
                        "parent_column": "mail_order_product_cd"
                      }
                    },
                    "End": true
                  }
                }
              },
              "QueryLanguage": "JSONata",
              "Items": "{% $parallel_num_array %}",
              "ItemSelector": {
                "split_num": "{% $states.context.Map.Item.Value %}"
              },
              "Next": "ZIP_Map_Master_Catalog"
            },
            "ZIP_Map_Master_Catalog": {
              "Type": "Map",
              "ItemProcessor": {
                "ProcessorConfig": {
                  "Mode": "INLINE"
                },
                "StartAt": "job_file_compress_Master_Catalog",
                "States": {
                  "job_file_compress_Master_Catalog": {
                    "QueryLanguage": "JSONata",
                    "Type": "Task",
                    "Resource": "arn:aws:states:::glue:startJobRun.sync",
                    "Arguments": {
                      "JobName": "job_file_compress",
                      "Arguments": {
                        "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "file_name": "{% 'txProduct_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                        "output_file_dir": "{% 'input-output/EC_OUT/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "output_file_name": "{% 'txProduct_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\")  & '.zip' %}",
                        "backup_flag": "True",
                        "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "jobnet_id": "JN_PR003-DF01_001"
                      }
                    },
                    "End": true
                  }
                }
              },
              "QueryLanguage": "JSONata",
              "Items": "{% $parallel_num_array %}",
              "ItemSelector": {
                "split_num": "{% $states.context.Map.Item.Value %}"
              },
              "Next": "2_sql_execute"
            },
            "2_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_PR003-DF01_001",
                  "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_PR003-DF01_before_002\",\"params\": {\"split_num\":\"' & $parallel_num_price & '\"}}]}' %}",
                  "file_id": "txPricebook"
                }
              },
              "Next": "3_DLPF_RETURN_PARALLEL_NUM_ARRAY"
            },
            "3_DLPF_RETURN_PARALLEL_NUM_ARRAY": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Next": "XML_Map_Price",
              "QueryLanguage": "JSONata",
              "Arguments": {
                "FunctionName": "arn:aws:lambda:ap-northeast-1:${AccountId}:function:DLPF_RETURN_PARALLEL_NUM_ARRAY:$LATEST",
                "Payload": {
                  "parallel_num": "{% $parallel_num_price %}"
                }
              },
              "Assign": {
                "parallel_num_array": "{% $states.result.Payload %}"
              }
            },
            "XML_Map_Price": {
              "Type": "Map",
              "ItemProcessor": {
                "ProcessorConfig": {
                  "Mode": "INLINE"
                },
                "StartAt": "job_db_to_file_Price",
                "States": {
                  "job_db_to_file_Price": {
                    "QueryLanguage": "JSONata",
                    "Type": "Task",
                    "Resource": "arn:aws:states:::glue:startJobRun.sync",
                    "Arguments": {
                      "JobName": "job_db_to_file",
                      "Arguments": {
                        "secret_name": "DLPF_DB_INFO",
                        "execute_query": "etl_PR003-DF01_select_002",
                        "batch_size": "1000",
                        "output_file_dir": "{% 'tmp/job_db_to_file/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "file_name": "{% 'txPricebook_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\")  & '.xml' %}",
                        "file_type": "xml",
                        "jobnet_id": "JN_PR003-DF01_001",
                        "diff_base_timestamp_query": "sql_PR003-DF01_timestamp_002",
                        "file_id": "txPricebook",
                        "split_num": "{% $string($states.input.split_num) %}"
                      }
                    },
                    "End": true
                  }
                }
              },
              "QueryLanguage": "JSONata",
              "Items": "{% $parallel_num_array %}",
              "ItemSelector": {
                "split_num": "{% $states.context.Map.Item.Value %}"
              },
              "Next": "ZIP_Map_Price"
            },
            "ZIP_Map_Price": {
              "Type": "Map",
              "ItemProcessor": {
                "ProcessorConfig": {
                  "Mode": "INLINE"
                },
                "StartAt": "job_file_compress_Price",
                "States": {
                  "job_file_compress_Price": {
                    "QueryLanguage": "JSONata",
                    "Type": "Task",
                    "Resource": "arn:aws:states:::glue:startJobRun.sync",
                    "Arguments": {
                      "JobName": "job_file_compress",
                      "Arguments": {
                        "input_file_dir": "{% 'tmp/job_db_to_file/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "file_name": "{% 'txPricebook_' & $full_date & '_' & $pad($string($states.input.split_num), -2, \"0\") & '.xml' %}",
                        "output_file_dir": "{% 'input-output/EC_OUT/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "output_file_name": "{% 'txPricebook_' & $full_date &  '_' & $pad($string($states.input.split_num), -2, \"0\")  & '.zip' %}",
                        "backup_flag": "True",
                        "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PR003-DF01_001_' & $full_date & '/' %}",
                        "jobnet_id": "JN_PR003-DF01_001"
                      }
                    },
                    "End": true
                  }
                }
              },
              "QueryLanguage": "JSONata",
              "Items": "{% $parallel_num_array %}",
              "ItemSelector": {
                "split_num": "{% $states.context.Map.Item.Value %}"
              },
              "Next": "4_sql_execute"
            },
            "4_sql_execute": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_execute_sql",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "jobnet_id": "JN_PR003-DF01_001",
                  "sql_info": "{% '{\"sql_info\": [{\"query_id\": \"sql_PR003-DF01_after_001\"}]}' %}"
                }
              },
              "End": true
            }
          }
        }
