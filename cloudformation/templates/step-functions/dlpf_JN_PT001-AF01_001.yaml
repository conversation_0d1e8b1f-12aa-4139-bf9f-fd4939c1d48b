AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_PT001-AF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  # ポイント履歴配信_001
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_PT001-AF01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_api_to_file"
            },
            "1_job_api_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_api_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "api_secret_name": "CRM-CS_API_INFO-2",
                  "diff_flag": "True",
                  "file_name": "point.csv",
                  "output_file_dir": "{% 'tmp/job_api_to_file/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"sep\": \",\", \"quoting\": \"csv.QUOTE_ALL\", \"quotechar\": \"\\\"\", \"header\": false, \"index\": false  }",
                  "jobnet_id": "JN_PT001-AF01_001",
                  "api_request_param": "{\"inputs\":[{\"LastGetDatetimeFrom\":\"__LAST_TIMESTAMP__\",\"LastGetDatetimeTo\":\"__CURRENT_TIMESTAMP__\"}] }",
                  "api_request_method": "POST",
                  "records_key": "PointHistoryData",
                  "output_keys": "CreatedDate,LineMiniAppUserId,TradeType,Reason,Point,PointsBalance,ExpirationDate,Id",
                  "object_key": "outputValues",
                  "file_id": "point"
                }
              },
              "Next": "2_job_internal_db_import"
            },
            "2_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT CSV, HEADER false)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_api_to_file/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "input_file_name": "point.csv",
                  "import_table": "point_history_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_api_to_file/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_PT001-AF01_001",
                  "query_upsert": "sql_PT001-AF01_upsert_001"
                }
              },
              "Next": "3_job_db_to_file"
            },
            "3_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_PT001-AF01_select_001",
                  "output_file_dir": "{% 'input-output/DLPF_FANNARY/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"header\": true, \"delimiter\": \",\", \"quote_char\": \"\", \"line_ending\": \"\\n\" }",
                  "file_name": "point.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_PT001-AF01_001",
                  "diff_base_timestamp_query": "sql_PT001-AF01_timestamp_001",
                  "file_id": "point_db"
                }
              },
              "Next": "4_job_api_to_file"
            },
            "4_job_api_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_api_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "api_secret_name": "CRM-CS_API_INFO",
                  "diff_flag": "True",
                  "file_name": "customer.csv",
                  "output_file_dir": "{% 'tmp/job_api_to_file/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"sep\": \",\", \"quoting\": \"csv.QUOTE_ALL\", \"quotechar\": \"\\\"\", \"header\": false, \"index\": false }",
                  "jobnet_id": "JN_PT001-AF01_001",
                  "api_request_param": "q=SELECT+LineMiniAppUserId__c%2CNumber__c%2CStatus__c%2CLastName%2CFirstName%2CLastNameKana__c%2CFirstNameKana__c%2CPersonMailingPostalCode%2CPersonMailingState%2CPersonMailingCity%2CPersonMailingStreet%2CPhone%2CPersonBirthdate%2CGender__c%2CPersonEmail%0D%0AFROM+Account+%0D%0AWHERE+LineMiniAppUserId__c+!=+null%0AAND+LastModifiedDate+%3E__LAST_TIMESTAMP__+AND+LastModifiedDate+%3C%3D__CURRENT_TIMESTAMP__",
                  "api_request_method": "GET",
                  "records_key": "records",
                  "output_keys": "LineMiniAppUserId__c,Number__c,Status__c,LastName,FirstName,LastNameKana__c,FirstNameKana__c,PersonMailingPostalCode,PersonMailingState,PersonMailingCity,PersonMailingStreet,Phone,PersonBirthdate,Gender__c,PersonEmail",
                  "file_id": "customer"
                }
              },
              "Next": "5_job_internal_db_import"
            },
            "5_job_internal_db_import": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_internal_db_import",
                "Arguments": {
                  "input_format_options": "(FORMAT CSV, HEADER false)",
                  "secret_name": "DLPF_DB_INFO",
                  "input_file_dir": "{% 'tmp/job_api_to_file/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "input_file_name": "customer.csv",
                  "import_table": "account_work",
                  "backup_flag": "True",
                  "backup_file_dir": "{% 'back-up/job_api_to_file/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_PT001-AF01_001",
                  "query_upsert": "sql_PT001-AF01_upsert_002"
                }
              },
              "Next": "6_job_db_to_file"
            },
            "6_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_PT001-AF01_select_002",
                  "output_file_dir": "{% 'input-output/DLPF_FANNARY/JN_PT001-AF01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"header\": true, \"delimiter\": \",\", \"quote_char\": \"\", \"line_ending\": \"\\n\" }",
                  "file_name": "customer.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_PT001-AF01_001",
                  "diff_base_timestamp_query": "sql_PT001-AF01_timestamp_002",
                  "file_id": "customer_db"
                }
              },
              "End": true
            }
          }
        }
