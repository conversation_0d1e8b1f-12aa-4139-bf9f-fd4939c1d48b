AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_PT002-FA01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  # ポイント付与・ランク計算一括処理IF（2508）_001
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for JN_PT002-FA01_001",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "1_job_db_to_file"
            },
            "1_job_db_to_file": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_db_to_file",
                "Arguments": {
                  "secret_name": "DLPF_DB_INFO",
                  "execute_query": "sql_PT002-FA01_select_001",
                  "batch_size": "10000",
                  "output_file_dir": "{% 'tmp/job_db_to_file/JN_PT002-FA01_001_' & $full_date & '/' %}",
                  "file_setting": "{ \"header\": true }",
                  "file_name": "JN_PT002-FA01_001.csv",
                  "file_type": "csv",
                  "jobnet_id": "JN_PT002-FA01_001",
                  "diff_base_timestamp_query": "sql_PT002-FA01_timestamp_001",
                  "file_id": "JN_PT002-FA01_001"
                }
              },
              "Next": "2_job_bulk_api_register"
            },
            "2_job_bulk_api_register": {
              "QueryLanguage": "JSONata",
              "Type": "Task",
              "Resource": "arn:aws:states:::glue:startJobRun.sync",
              "Arguments": {
                "JobName": "job_bulk_api_register",
                "Arguments": {
                  "api_secret_name": "CRM-CS_BULK_INFO",
                  "bulk_api_setting": "{\"interval\":\"60\",\"request\":{ \"operation\": \"insert\", \"object\": \"PointRankConfRecv__c\", \"lineEnding\": \"LF\"}}",
                  "input_character_encoding": "utf-8",
                  "file_name": "JN_PT002-FA01_001.csv",
                  "input_file_dir": "{% 'tmp/job_db_to_file/JN_PT002-FA01_001_' & $full_date & '/' %}",
                  "backup_flag": "TRUE",
                  "backup_file_dir": "{% 'back-up/job_db_to_file/JN_PT002-FA01_001_' & $full_date & '/' %}",
                  "jobnet_id": "JN_PT002-FA01_001"
                }
              },
              "End": true
            }
          }
        }
