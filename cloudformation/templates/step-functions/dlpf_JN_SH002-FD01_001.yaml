AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_SH002-FD01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF_SH002-FD01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "parallel_1_to_3"
            },
            "parallel_1_to_3": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_job_internal_db_import",
                  "States": {
                    "1_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT CSV, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "input-output/SQG_IN/JN_SH002-FD01_001_/",
                          "input_file_name": "posHeaderInformation_*.csv",
                          "import_table": "sales_info_alignment_header_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/SQG_IN/JN_SH002-FD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH002-FD01_001",
                          "query_upsert": "sql_SH002-FD01_upsert_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_job_internal_db_import",
                  "States": {
                    "2_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT CSV, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "input-output/SQG_IN/JN_SH002-FD01_001_/",
                          "input_file_name": "posOrderInformation_*.csv",
                          "import_table": "sales_info_alignment_detail_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/SQG_IN/JN_SH002-FD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH002-FD01_001",
                          "query_upsert": "sql_SH002-FD01_upsert_002"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "3_job_internal_db_import",
                  "States": {
                    "3_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT CSV, HEADER false)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "input-output/SQG_IN/JN_SH002-FD01_001_/",
                          "input_file_name": "posPayInformation_*.csv",
                          "import_table": "sales_info_alignment_payment_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/SQG_IN/JN_SH002-FD01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SH002-FD01_001",
                          "query_upsert": "sql_SH002-FD01_upsert_003"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "End": true
            }
          }
        }
