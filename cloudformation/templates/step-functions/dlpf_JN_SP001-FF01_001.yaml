AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for Step Functions orchestrating Glue jobs

Parameters:
  Environment:
    Type: String
    Description: Deployment environment (dev, stg, prd)
  AccountId:
    Type: String
    Description: AWS Account ID
  StateMachineName:
    Type: String
    Default: JN_SP001-FF01_001
    Description: Name of the Step Functions state machine

Resources:
  StepFunctionsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/vendedlogs/states/${StateMachineName}

  # Step Functions State Machine
  GlueOrchestrationStateMachine:
    Type: AWS::StepFunctions::StateMachine
    Properties:
      StateMachineName: !Ref StateMachineName
      RoleArn: !Sub arn:aws:iam::${AccountId}:role/role-${Environment}-dlpf-sfn-01
      StateMachineType: STANDARD
      LoggingConfiguration:
        Level: ALL
        IncludeExecutionData: true
        Destinations:
          - CloudWatchLogsLogGroup:
              LogGroupArn: !GetAtt StepFunctionsLogGroup.Arn
      DefinitionString: !Sub |-
        {
          "Comment": "State machine for IF-SP001-FF01",
          "StartAt": "ListExecutions",
          "States": {
            "ListExecutions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::aws-sdk:sfn:listExecutions",
              "Parameters": {
                "StateMachineArn.$": "$$.StateMachine.Id",
                "StatusFilter": "RUNNING"
              },
              "Next": "CheckMultipleExecutions"
            },
            "CheckMultipleExecutions": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.Executions[1]",
                  "IsPresent": true,
                  "Next": "ExecutionAlreadyRunning"
                }
              ],
              "Default": "GetCurrentDate"
            },
            "ExecutionAlreadyRunning": {
              "Type": "Fail",
              "Error": "ExecutionAlreadyRunning",
              "Cause": "Step Function execution already in progress"
            },
            "GetCurrentDate": {
              "QueryLanguage": "JSONata",
              "Type": "Pass",
              "Assign": {
                "full_date": "{% $fromMillis($toMillis($now()) + 9*60*60*1000, '[Y0001][M01][D01][H01][m01][s01]') %}"
              },
              "Next": "parallel_1_to_2"
            },
            "parallel_1_to_2": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "1_job_send_file",
                  "States": {
                    "1_job_send_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_send_file",
                        "Arguments": {
                          "s3_full_path": "input-output/DLPF_OMS/JN_SP001-FF01_001_/output_work_shipping_header_*.tsv",
                          "secret_name": "COMMONS_SFTP_INFO",
                          "external_system_destination_directory": "Interface/ToCommons/",
                          "external_system_transmission_file_name": "{% '0069_TrOrder_H_OMS_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_s3_directory": "{% 'tmp/job_send_file/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "2_job_send_file",
                  "States": {
                    "2_job_send_file": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_send_file",
                        "Arguments": {
                          "s3_full_path": "input-output/DLPF_OMS/JN_SP001-FF01_001_/output_work_shipping_detail_*.tsv",
                          "secret_name": "COMMONS_SFTP_INFO",
                          "external_system_destination_directory": "Interface/ToCommons/",
                          "external_system_transmission_file_name": "{% '0069_TrOrder_D_OMS_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_s3_directory": "{% 'tmp/job_send_file/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "parallel_3_to_16"
            },
            "parallel_3_to_16": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "3_job_convert_character_encoding",
                  "States": {
                    "3_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "{% 'tmp/job_send_file/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "file_name": "output_work_shipping_header_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'ORDER_H_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "4_job_convert_format"
                    },
                    "4_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'ORDER_H_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'ORDER_H_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "5_job_convert_character_encoding",
                  "States": {
                    "5_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "{% 'tmp/job_send_file/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "file_name": "output_work_shipping_detail_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'ORDER_D_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "6_job_convert_format"
                    },
                    "6_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'ORDER_D_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'ORDER_D_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "7_job_convert_character_encoding",
                  "States": {
                    "7_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "input-output/DLPF_OMS/JN_SP001-FF01_001_/",
                          "file_name": "output_work_shipping_instructions_monitor_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'TORDERMONITORING_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/DLPF_OMS/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "8_job_convert_format"
                    },
                    "8_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'TORDERMONITORING_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'TORDERMONITORING_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "9_job_convert_character_encoding",
                  "States": {
                    "9_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "input-output/DLPF_OMS/JN_SP001-FF01_001_/",
                          "file_name": "output_work_delivery_note_header_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'TSLIPPRINTM_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/DLPF_OMS/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "10_job_convert_format"
                    },
                    "10_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'TSLIPPRINTM_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'TSLIPPRINTM_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "11_job_convert_character_encoding",
                  "States": {
                    "11_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "input-output/DLPF_OMS/JN_SP001-FF01_001_/",
                          "file_name": "output_work_delivery_note_detail_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'TSLIPPRINDT_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/DLPF_OMS/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "12_job_convert_format"
                    },
                    "12_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'TSLIPPRINDT_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'TSLIPPRINDT_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "13_job_convert_character_encoding",
                  "States": {
                    "13_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "input-output/DLPF_OMS/JN_SP001-FF01_001_/",
                          "file_name": "output_work_delivery_note_information_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'TSLIPMSG_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/DLPF_OMS/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "14_job_convert_format"
                    },
                    "14_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'TSLIPMSG_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'TSLIPMSG_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "15_job_convert_character_encoding",
                  "States": {
                    "15_job_convert_character_encoding": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_character_encoding",
                        "Arguments": {
                          "input_character_encoding": "UTF-8",
                          "output_character_encoding": "cp932",
                          "input_file_dir": "input-output/DLPF_OMS/JN_SP001-FF01_001_/",
                          "file_name": "output_work_return_change_detail_*.tsv",
                          "output_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "output_file_name": "{% 'TSLIPRETURNDT_' & $full_date & '.tsv' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/DLPF_OMS/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "Next": "16_job_convert_format"
                    },
                    "16_job_convert_format": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_convert_format",
                        "Arguments": {
                          "etl_yaml_file_id": "tsv2csv/etl_tsv2csv_SP001-FF01_001",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "{% 'TSLIPRETURNDT_' & $full_date & '.tsv' %}",
                          "output_file_dir": "ext-input-output/DLPF_WMS/JN_SP001-FF01_001/",
                          "output_file_name": "{% 'TSLIPRETURNDT_' & $full_date & '.CSV' %}",
                          "backup_flag": "True",
                          "backup_file_dir": "back-up/job_convert_character_encoding/JN_SP001-FF01_001/",
                          "jobnet_id": "JN_SP001-FF01_001"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "Next": "parallel_17_to_18"
            },
            "parallel_17_to_18": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "17_job_internal_db_import",
                  "States": {
                    "17_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, DELIMITER E'\t', HEADER true)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "output_work_shipping_header_*.tsv",
                          "import_table": "out_indicate_header_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001",
                          "query_upsert": "sql_SP001-FF01_upsert_001"
                        }
                      },
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "18_job_internal_db_import",
                  "States": {
                    "18_job_internal_db_import": {
                      "QueryLanguage": "JSONata",
                      "Type": "Task",
                      "Resource": "arn:aws:states:::glue:startJobRun.sync",
                      "Arguments": {
                        "JobName": "job_internal_db_import",
                        "Arguments": {
                          "input_format_options": "(FORMAT csv, DELIMITER E'\t', HEADER true)",
                          "secret_name": "DLPF_DB_INFO",
                          "input_file_dir": "{% 'tmp/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "input_file_name": "output_work_shipping_detail_*.tsv",
                          "import_table": "out_indicate_detail_work",
                          "backup_flag": "True",
                          "backup_file_dir": "{% 'back-up/job_convert_character_encoding/JN_SP001-FF01_001_' & $full_date & '/' %}",
                          "jobnet_id": "JN_SP001-FF01_001",
                          "query_upsert": "sql_SP001-FF01_upsert_002"
                        }
                      },
                      "End": true
                    }
                  }
                }
              ],
              "End": true
            }
          }
        }
