AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for DLPF Parameter Store Settings

Parameters:
  AccountId:
    Type: String
    Description: AWS Account ID

Resources:
  GlueJobEnvironmentConfig:
    Type: AWS::SSM::Parameter
    Properties:
      Name: /glue/job/environment-config
      Type: String
      Value: !Sub |
        {
          "process": {
            "TZ": "Asia/Tokyo"
          },
          "aws": {
            "S3_BUCKET_NAME": "s3-dev-dlpf-if-${AccountId}",
            "AWS_DEFAULT_REGION": "ap-northeast-1",
            "S3_RETRY_LIMIT": "3",
            "S3_RETRY_INTERVAL": "40",
            "LOG_LEVEL": "DEBUG"
          }
        }
      Description: Environment configuration for Glue jobs
