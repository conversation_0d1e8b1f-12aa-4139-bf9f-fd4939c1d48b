# SQL スクリプト実行ツール

## 概要
このシェルスクリプトは、指定されたフォルダ内のすべてのSQLファイルをPostgreSQLデータベースに対して実行するツールです。エラーが発生した場合でも、次のSQLファイルの処理を続行し、失敗したファイルをログに記録します。

## 前提条件
- bash シェル環境
- PostgreSQL クライアント（psql）がインストールされていること

## 使い方

### スクリプトの準備
1. スクリプトファイル `run_sql_scripts.sh` を作成します
2. 実行権限を付与します：
   ```
   chmod +x run_sql_scripts.sh
   ```

### 実行方法
以下の形式でスクリプトを実行します：

```
./run_sql_scripts.sh <データベース名> <ユーザー名> <パスワード> <SQLフォルダのパス>
```

### 実行例
```
./run_sql_scripts.sh mydb postgres mypassword ./sql_files
```

## パラメータ説明
- `<データベース名>` - 接続するデータベースの名前
- `<ユーザー名>` - PostgreSQLユーザー名
- `<パスワード>` - PostgreSQLパスワード
- `<SQLフォルダのパス>` - SQLファイル（.sql）が格納されているフォルダのパス

## 機能
- localhost:15432 で稼働しているPostgreSQLサーバーに接続
- 指定されたフォルダ内のすべてのSQLファイルを順番に実行
- エラーが発生した場合は次のファイルの処理に進む
- 失敗したSQLファイルの名前を `failed_sql_scripts.log` に記録
- 実行完了時に結果サマリーを表示

## エラーハンドリング
スクリプトは実行に失敗したSQLファイルをスキップし、`failed_sql_scripts.log`というログファイルに記録します。すべてのファイルの処理が完了すると、失敗したファイルの一覧を表示します。

## 注意事項
- パスワードはコマンドライン引数として渡されるため、本番環境では注意が必要です
- スクリプトはフォルダ内の `.sql` 拡張子を持つファイルのみを処理します