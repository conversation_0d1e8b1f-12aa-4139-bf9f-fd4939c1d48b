-- データベースの作成
CREATE DATABASE test_db;

-- スキーマの作成
CREATE SCHEMA dlpf;

-- aws_s3&aws_commonをインストール
CREATE EXTENSION aws_s3 CASCADE;

-- 管理者ロールの作成
CREATE ROLE admin WITH
    LOGIN
    SUPERUSER
    CREATEDB
    CREATEROLE
    PASSWORD 'password';

-- DDLロールの作成
CREATE ROLE dlpf_ope WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- DMLロール1の作成
CREATE ROLE dlpf_api WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- D<PERSON>ロール2の作成
CREATE ROLE dlpf_batch WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    PASSWORD 'password';

-- デフォルトの検索パスの設定
ALTER ROLE dlpf_ope SET search_path TO dlpf;
ALTER ROLE dlpf_api SET search_path TO dlpf;
ALTER ROLE dlpf_batch SET search_path TO dlpf;

-- スキーマに対する権限の付与
GRANT USAGE ON SCHEMA dlpf TO dlpf_ope;
GRANT USAGE ON SCHEMA dlpf TO dlpf_api;
GRANT USAGE ON SCHEMA dlpf TO dlpf_batch;
GRANT USAGE ON SCHEMA aws_s3 TO dlpf_batch;
GRANT USAGE ON SCHEMA aws_commons TO dlpf_batch;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO dlpf_batch;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_commons TO dlpf_batch;

-- DDLロールに対する権限の付与
GRANT CREATE ON SCHEMA dlpf TO dlpf_ope;

-- 将来作成されるテーブルに対するDML権限の事前付与
ALTER DEFAULT PRIVILEGES IN SCHEMA dlpf
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO dlpf_api;

ALTER DEFAULT PRIVILEGES IN SCHEMA dlpf
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO dlpf_batch;

GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA dlpf TO dlpf_api;
GRANT ALL ON ALL SEQUENCES IN SCHEMA dlpf TO dlpf_api;
GRANT SELECT,INSERT,UPDATE,DELETE ON ALL TABLES IN SCHEMA dlpf TO dlpf_batch;
GRANT ALL ON ALL SEQUENCES IN SCHEMA dlpf TO dlpf_batch;
