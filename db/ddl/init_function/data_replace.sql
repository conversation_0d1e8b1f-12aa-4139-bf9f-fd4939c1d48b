CREATE OR REPLACE FUNCTION data_replace(
    backup_table text,
    target_table text
) RETURNS void AS $$
DECLARE
    common_cols text;
BEGIN
    -- 共通カラム取得
    SELECT string_agg(quote_ident(a.column_name), ', ')
      INTO common_cols
      FROM information_schema.columns a
      JOIN information_schema.columns b
        ON a.column_name = b.column_name
     WHERE a.table_name = backup_table
       AND b.table_name = target_table;

    -- データ移行
    IF common_cols IS NOT NULL THEN
        EXECUTE format('INSERT INTO %I (%s) SELECT %s FROM %I', target_table, common_cols, common_cols, backup_table);
    END IF;
END;
$$ LANGUAGE plpgsql;