CREATE TABLE account (
    external_customer_id VARCHAR(33) NOT NULL,
    alternative_customer_code VARCHAR(8) NOT NULL,
    customer_status VARCHAR(255) NOT NULL,
    last_name VARCHAR(120) NOT NULL,
    first_name VARCHAR(120) NOT NULL,
    last_name_hiragana VARCHAR(60) NOT NULL,
    first_name_hiragana VARCHAR(60) NOT NULL,
    zip_code VARCHAR(7) NOT NULL,
    state_type VARCHAR(10) NOT NULL,
    city VARCHAR(40) NOT NULL,
    address1 VARCHAR(255) NOT NULL,
    tel_number VARCHAR(16) NOT NULL,
    birthday VARCHAR(10),
    sex_type VARCHAR(255),
    mail_address VARCHAR(100),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (alternative_customer_code)
);
COMMENT ON TABLE account IS '会員';
COMMENT ON COLUMN account.external_customer_id IS 'LINEミニアプリユーザーID';
COMMENT ON COLUMN account.alternative_customer_code IS '会員番号';
COMMENT ON COLUMN account.customer_status IS '会員ステータス';
COMMENT ON COLUMN account.last_name IS '姓';
COMMENT ON COLUMN account.first_name IS '名';
COMMENT ON COLUMN account.last_name_hiragana IS '姓（かな）';
COMMENT ON COLUMN account.first_name_hiragana IS '名（かな）';
COMMENT ON COLUMN account.zip_code IS '郵便番号(自宅)';
COMMENT ON COLUMN account.state_type IS '都道府県(自宅)';
COMMENT ON COLUMN account.city IS '市区郡(自宅)';
COMMENT ON COLUMN account.address1 IS '町名・番地(自宅)';
COMMENT ON COLUMN account.tel_number IS '電話';
COMMENT ON COLUMN account.birthday IS '生年月日';
COMMENT ON COLUMN account.sex_type IS '性別';
COMMENT ON COLUMN account.mail_address IS 'メールアドレス(PC)';
COMMENT ON COLUMN account.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN account.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN account.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN account.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN account.d_version IS 'デ連バージョン';

