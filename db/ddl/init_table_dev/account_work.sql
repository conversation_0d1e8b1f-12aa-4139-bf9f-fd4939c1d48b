CREATE TABLE account_work (
    external_customer_id VARCHAR(33) NOT NULL,
    alternative_customer_code VARCHAR(8) NOT NULL,
    customer_status VARCHAR(255) NOT NULL,
    last_name VARCHAR(120) NOT NULL,
    first_name VARCHAR(120) NOT NULL,
    last_name_hiragana VARCHAR(60) NOT NULL,
    first_name_hiragana VARCHAR(60) NOT NULL,
    zip_code VARCHAR(7) NOT NULL,
    state_type VARCHAR(10) NOT NULL,
    city VARCHAR(40) NOT NULL,
    address1 VARCHAR(255) NOT NULL,
    tel_number VARCHAR(16) NOT NULL,
    birthday VARCHAR(10),
    sex_type VARCHAR(255),
    mail_address VARCHAR(100),
    PRIMARY KEY (alternative_customer_code)
);
COMMENT ON TABLE account_work IS '会員ワーク';
COMMENT ON COLUMN account_work.external_customer_id IS 'LINEミニアプリユーザーID';
COMMENT ON COLUMN account_work.alternative_customer_code IS '会員番号';
COMMENT ON COLUMN account_work.customer_status IS '会員ステータス';
COMMENT ON COLUMN account_work.last_name IS '姓';
COMMENT ON COLUMN account_work.first_name IS '名';
COMMENT ON COLUMN account_work.last_name_hiragana IS '姓（かな）';
COMMENT ON COLUMN account_work.first_name_hiragana IS '名（かな）';
COMMENT ON COLUMN account_work.zip_code IS '郵便番号(自宅)';
COMMENT ON COLUMN account_work.state_type IS '都道府県(自宅)';
COMMENT ON COLUMN account_work.city IS '市区郡(自宅)';
COMMENT ON COLUMN account_work.address1 IS '町名・番地(自宅)';
COMMENT ON COLUMN account_work.tel_number IS '電話';
COMMENT ON COLUMN account_work.birthday IS '生年月日';
COMMENT ON COLUMN account_work.sex_type IS '性別';
COMMENT ON COLUMN account_work.mail_address IS 'メールアドレス(PC)';

