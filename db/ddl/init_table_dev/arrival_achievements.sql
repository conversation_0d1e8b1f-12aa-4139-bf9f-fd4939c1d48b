CREATE TABLE arrival_achievements (
    warehouse_cd VARCHAR(4),
    receiving_stock_date VARCHAR(10) NOT NULL,
    receiving_stock_former_cd VARCHAR(13),
    receiving_stock_slip_no VARCHAR(13) NOT NULL,
    receiving_stock_slip_row_no NUMERIC(5,0) NOT NULL,
    po_no VARCHAR(15),
    product_cd VARCHAR(25) NOT NULL,
    receiving_stock_cnt NUMERIC(13,0),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (receiving_stock_slip_no, receiving_stock_slip_row_no)
);
COMMENT ON TABLE arrival_achievements IS '入荷実績';
COMMENT ON COLUMN arrival_achievements.warehouse_cd IS '倉庫コード';
COMMENT ON COLUMN arrival_achievements.receiving_stock_date IS '入庫日付';
COMMENT ON COLUMN arrival_achievements.receiving_stock_former_cd IS '入庫元コード';
COMMENT ON COLUMN arrival_achievements.receiving_stock_slip_no IS '入庫伝票番号';
COMMENT ON COLUMN arrival_achievements.receiving_stock_slip_row_no IS '入庫伝票行番号';
COMMENT ON COLUMN arrival_achievements.po_no IS '発注番号';
COMMENT ON COLUMN arrival_achievements.product_cd IS '商品コード';
COMMENT ON COLUMN arrival_achievements.receiving_stock_cnt IS '入庫数量';
COMMENT ON COLUMN arrival_achievements.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN arrival_achievements.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN arrival_achievements.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN arrival_achievements.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN arrival_achievements.d_version IS 'デ連バージョン';

