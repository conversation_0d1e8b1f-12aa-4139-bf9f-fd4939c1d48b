CREATE TABLE arrival_achievements_work (
    warehouse_cd VARCHAR(4),
    receiving_stock_date VARCHAR(10) NOT NULL,
    receiving_stock_former_cd VARCHAR(13),
    receiving_stock_slip_no VARCHAR(13) NOT NULL,
    receiving_stock_slip_row_no NUMERIC(5,0) NOT NULL,
    po_no VARCHAR(15),
    product_cd VARCHAR(25) NOT NULL,
    receiving_stock_cnt NUMERIC(13,0),
    PRIMARY KEY (receiving_stock_slip_no, receiving_stock_slip_row_no)
);
COMMENT ON TABLE arrival_achievements_work IS '入荷実績ワーク';
COMMENT ON COLUMN arrival_achievements_work.warehouse_cd IS '倉庫コード';
COMMENT ON COLUMN arrival_achievements_work.receiving_stock_date IS '入庫日付';
COMMENT ON COLUMN arrival_achievements_work.receiving_stock_former_cd IS '入庫元コード';
COMMENT ON COLUMN arrival_achievements_work.receiving_stock_slip_no IS '入庫伝票番号';
COMMENT ON COLUMN arrival_achievements_work.receiving_stock_slip_row_no IS '入庫伝票行番号';
COMMENT ON COLUMN arrival_achievements_work.po_no IS '発注番号';
COMMENT ON COLUMN arrival_achievements_work.product_cd IS '商品コード';
COMMENT ON COLUMN arrival_achievements_work.receiving_stock_cnt IS '入庫数量';

