CREATE TABLE arrival_schedule_apparel (
    stock_arrival_date TIMESTAMP(10) NOT NULL,
    warehouse_cd VARCHAR(3) NOT NULL,
    po_no VARCHAR(12) NOT NULL,
    warehouse_management_no VARCHAR(10) NOT NULL,
    order_count NUMERIC(7,0) NOT NULL,
    agent_cd VARCHAR(2) NOT NULL,
    order_registrant_name VARCHAR(20),
    comment_code VARCHAR(50),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (po_no, warehouse_management_no)
);
COMMENT ON TABLE arrival_schedule_apparel IS '入荷予定（アパレル）';
COMMENT ON COLUMN arrival_schedule_apparel.stock_arrival_date IS '入荷予定日';
COMMENT ON COLUMN arrival_schedule_apparel.warehouse_cd IS '倉庫コード';
COMMENT ON COLUMN arrival_schedule_apparel.po_no IS '発注番号';
COMMENT ON COLUMN arrival_schedule_apparel.warehouse_management_no IS '倉庫管理番号';
COMMENT ON COLUMN arrival_schedule_apparel.order_count IS '発注数';
COMMENT ON COLUMN arrival_schedule_apparel.agent_cd IS '取扱店CD';
COMMENT ON COLUMN arrival_schedule_apparel.order_registrant_name IS '発注登録者名称';
COMMENT ON COLUMN arrival_schedule_apparel.comment_code IS 'コメントCODE';
COMMENT ON COLUMN arrival_schedule_apparel.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN arrival_schedule_apparel.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN arrival_schedule_apparel.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN arrival_schedule_apparel.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN arrival_schedule_apparel.d_version IS 'デ連バージョン';

