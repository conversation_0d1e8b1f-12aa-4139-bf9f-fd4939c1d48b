CREATE TABLE arrival_schedule_apparel_work (
    stock_arrival_date TIMESTAMP(10) NOT NULL,
    warehouse_cd VARCHAR(3) NOT NULL,
    po_no VARCHAR(12) NOT NULL,
    warehouse_management_no VARCHAR(10) NOT NULL,
    order_count NUMERIC(7,0) NOT NULL,
    agent_cd VARCHAR(2) NOT NULL,
    order_registrant_name VARCHAR(20),
    comment_code VARCHAR(50),
    PRIMARY KEY (po_no, warehouse_management_no)
);
COMMENT ON TABLE arrival_schedule_apparel_work IS '入荷予定（アパレル）ワーク';
COMMENT ON COLUMN arrival_schedule_apparel_work.stock_arrival_date IS '入荷予定日';
COMMENT ON COLUMN arrival_schedule_apparel_work.warehouse_cd IS '倉庫コード';
COMMENT ON COLUMN arrival_schedule_apparel_work.po_no IS '発注番号';
COMMENT ON COLUMN arrival_schedule_apparel_work.warehouse_management_no IS '倉庫管理番号';
COMMENT ON COLUMN arrival_schedule_apparel_work.order_count IS '発注数';
COMMENT ON COLUMN arrival_schedule_apparel_work.agent_cd IS '取扱店CD';
COMMENT ON COLUMN arrival_schedule_apparel_work.order_registrant_name IS '発注登録者名称';
COMMENT ON COLUMN arrival_schedule_apparel_work.comment_code IS 'コメントCODE';

