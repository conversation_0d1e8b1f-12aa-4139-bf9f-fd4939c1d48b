CREATE TABLE campaign_combi_limit_work (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_combi_limit_code VARCHAR(16) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, campaign_combi_limit_code)
);
COMMENT ON TABLE campaign_combi_limit_work IS 'キャンペーン併用不可ワーク';
COMMENT ON COLUMN campaign_combi_limit_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_combi_limit_work.campaign_combi_limit_code IS '併用不可キャンペーン設定コード';
COMMENT ON COLUMN campaign_combi_limit_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_combi_limit_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_combi_limit_work.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_combi_limit_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_combi_limit_work.updated_datetime IS '更新日時';

