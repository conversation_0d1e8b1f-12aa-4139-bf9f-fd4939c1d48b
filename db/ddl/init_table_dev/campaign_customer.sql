CREATE TABLE campaign_customer (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    customer_code VARCHAR(16) NOT NULL,
    joken_type VARCHAR(1) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, customer_code)
);
COMMENT ON TABLE campaign_customer IS 'キャンペーン設定顧客';
COMMENT ON COLUMN campaign_customer.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_customer.customer_code IS '顧客コード';
COMMENT ON COLUMN campaign_customer.joken_type IS '条件分類';
COMMENT ON COLUMN campaign_customer.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_customer.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_customer.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_customer.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_customer.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_customer.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN campaign_customer.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN campaign_customer.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN campaign_customer.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN campaign_customer.d_version IS 'デ連バージョン';

