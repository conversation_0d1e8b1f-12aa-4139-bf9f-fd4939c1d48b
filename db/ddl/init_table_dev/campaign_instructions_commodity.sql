CREATE TABLE campaign_instructions_commodity (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    shop_code VA<PERSON>HAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    joken_type VARCHAR(1) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, shop_code, commodity_code)
);
COMMENT ON TABLE campaign_instructions_commodity IS 'キャンペーン設定商品';
COMMENT ON COLUMN campaign_instructions_commodity.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_instructions_commodity.shop_code IS 'ショップコード';
COMMENT ON COLUMN campaign_instructions_commodity.commodity_code IS '商品コード';
COMMENT ON COLUMN campaign_instructions_commodity.joken_type IS '条件分類';
COMMENT ON COLUMN campaign_instructions_commodity.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_instructions_commodity.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_instructions_commodity.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_instructions_commodity.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_instructions_commodity.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_instructions_commodity.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN campaign_instructions_commodity.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN campaign_instructions_commodity.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN campaign_instructions_commodity.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN campaign_instructions_commodity.d_version IS 'デ連バージョン';

