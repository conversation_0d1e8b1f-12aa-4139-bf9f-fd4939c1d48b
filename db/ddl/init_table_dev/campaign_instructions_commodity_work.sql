CREATE TABLE campaign_instructions_commodity_work (
    campaign_instructions_code VA<PERSON>HAR(16) NOT NULL,
    shop_code VA<PERSON>HAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    joken_type VARCHAR(1) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, shop_code, commodity_code)
);
COMMENT ON TABLE campaign_instructions_commodity_work IS 'キャンペーン設定商品ワーク';
COMMENT ON COLUMN campaign_instructions_commodity_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_instructions_commodity_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN campaign_instructions_commodity_work.commodity_code IS '商品コード';
COMMENT ON COLUMN campaign_instructions_commodity_work.joken_type IS '条件分類';
COMMENT ON COLUMN campaign_instructions_commodity_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_instructions_commodity_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_instructions_commodity_work.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_instructions_commodity_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_instructions_commodity_work.updated_datetime IS '更新日時';

