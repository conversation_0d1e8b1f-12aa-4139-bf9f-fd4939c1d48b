CREATE TABLE campaign_order_group (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_group_no NUMERIC(8,0) NOT NULL,
    campaign_joken_disp VARCHAR(50),
    exclude_joken_disp VARCHAR(50),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, campaign_group_no)
);
COMMENT ON TABLE campaign_order_group IS 'キャンペーン設定条件グループ';
COMMENT ON COLUMN campaign_order_group.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_order_group.campaign_group_no IS 'キャンペーン設定グループ番号';
COMMENT ON COLUMN campaign_order_group.campaign_joken_disp IS 'キャンペーン条件（一覧表示用）';
COMMENT ON COLUMN campaign_order_group.exclude_joken_disp IS 'キャンペーン除外条件（一覧表示用）';
COMMENT ON COLUMN campaign_order_group.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_order_group.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_order_group.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_order_group.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_order_group.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_order_group.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN campaign_order_group.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN campaign_order_group.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN campaign_order_group.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN campaign_order_group.d_version IS 'デ連バージョン';

