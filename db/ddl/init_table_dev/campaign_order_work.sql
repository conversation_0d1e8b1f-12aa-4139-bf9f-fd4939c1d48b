CREATE TABLE campaign_order_work (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_group_no NUMERIC(8,0) NOT NULL,
    joken_type VARCHAR(1) NOT NULL,
    campaign_joken_no NUMERIC(8,0) NOT NULL,
    joken_kind1 VARCHAR(1),
    joken_kind2 VARCHAR(3),
    joken VARCHAR(16),
    joken_min NUMERIC(8,0),
    joken_max NUMERIC(8,0),
    regular_kaiji NUMERIC(5,0),
    joken_month_num NUMERIC(3,0),
    commodity_name VARCHAR(100),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, campaign_group_no, joken_type, campaign_joken_no)
);
COMMENT ON TABLE campaign_order_work IS 'キャンペーン設定条件ワーク';
COMMENT ON COLUMN campaign_order_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_order_work.campaign_group_no IS 'キャンペーン設定グループ番号';
COMMENT ON COLUMN campaign_order_work.joken_type IS '条件分類';
COMMENT ON COLUMN campaign_order_work.campaign_joken_no IS 'キャンペーン設定条件番号';
COMMENT ON COLUMN campaign_order_work.joken_kind1 IS '条件種別１';
COMMENT ON COLUMN campaign_order_work.joken_kind2 IS '条件種別２';
COMMENT ON COLUMN campaign_order_work.joken IS '条件内容';
COMMENT ON COLUMN campaign_order_work.joken_min IS '条件内容下限';
COMMENT ON COLUMN campaign_order_work.joken_max IS '条件内容上限';
COMMENT ON COLUMN campaign_order_work.regular_kaiji IS '定期回次';
COMMENT ON COLUMN campaign_order_work.joken_month_num IS '期限月数';
COMMENT ON COLUMN campaign_order_work.commodity_name IS '商品名称';
COMMENT ON COLUMN campaign_order_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_order_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_order_work.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_order_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_order_work.updated_datetime IS '更新日時';

