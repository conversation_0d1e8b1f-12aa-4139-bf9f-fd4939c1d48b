CREATE TABLE campaign_promotion (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    promotion_no NUMERIC(9) NOT NULL,
    promotion_type VARCHAR(2) NOT NULL,
    shop_code VARCHAR(16),
    commodity_code VARCHAR(16),
    commodity_name VARCHAR(100),
    present_qt NUMERIC(3,0),
    discount_rate NUMERIC(3,0),
    discount_amount NUMERIC(8,0),
    discount_retail_price NUMERIC(8,0),
    shipping_charge NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (campaign_instructions_code, promotion_no)
);
COMMENT ON TABLE campaign_promotion IS 'キャンペーン設定プロモーション';
COMMENT ON COLUMN campaign_promotion.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN campaign_promotion.promotion_no IS 'プロモーション番号';
COMMENT ON COLUMN campaign_promotion.promotion_type IS 'プロモーション種別';
COMMENT ON COLUMN campaign_promotion.shop_code IS 'ショップコード';
COMMENT ON COLUMN campaign_promotion.commodity_code IS '商品コード';
COMMENT ON COLUMN campaign_promotion.commodity_name IS '商品名称';
COMMENT ON COLUMN campaign_promotion.present_qt IS 'プレゼント数量';
COMMENT ON COLUMN campaign_promotion.discount_rate IS '値引率';
COMMENT ON COLUMN campaign_promotion.discount_amount IS '値引額';
COMMENT ON COLUMN campaign_promotion.discount_retail_price IS '値引後販売価格';
COMMENT ON COLUMN campaign_promotion.shipping_charge IS '送料';
COMMENT ON COLUMN campaign_promotion.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN campaign_promotion.created_user IS '作成ユーザ';
COMMENT ON COLUMN campaign_promotion.created_datetime IS '作成日時';
COMMENT ON COLUMN campaign_promotion.updated_user IS '更新ユーザ';
COMMENT ON COLUMN campaign_promotion.updated_datetime IS '更新日時';
COMMENT ON COLUMN campaign_promotion.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN campaign_promotion.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN campaign_promotion.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN campaign_promotion.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN campaign_promotion.d_version IS 'デ連バージョン';

