CREATE TABLE center_stock_all (
    center_code VARCHAR(4) NOT NULL,
    stock_kind VARCHAR(1) NOT NULL,
    sh_control_number VARCHAR(25) NOT NULL,
    stock_quantity NUMERIC(20,0) NOT NULL,
    allocated_quantity NUMERIC(20,0) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (center_code, stock_kind, sh_control_number)
);
COMMENT ON TABLE center_stock_all IS '在庫連携（全件）';
COMMENT ON COLUMN center_stock_all.center_code IS 'センターコード';
COMMENT ON COLUMN center_stock_all.stock_kind IS '在庫区分';
COMMENT ON COLUMN center_stock_all.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN center_stock_all.stock_quantity IS '在庫数';
COMMENT ON COLUMN center_stock_all.allocated_quantity IS '引当数';
COMMENT ON COLUMN center_stock_all.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN center_stock_all.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN center_stock_all.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN center_stock_all.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN center_stock_all.d_version IS 'デ連バージョン';

