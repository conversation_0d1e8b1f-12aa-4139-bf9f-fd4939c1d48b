CREATE TABLE center_stock_all_work (
    center_code VARCHAR(4) NOT NULL,
    stock_kind VARCHAR(1) NOT NULL,
    sh_control_number VARCHAR(25) NOT NULL,
    stock_quantity NUMERIC(20,0) NOT NULL,
    allocated_quantity NUMERIC(20,0) NOT NULL,
    PRIMARY KEY (center_code, stock_kind, sh_control_number)
);
COMMENT ON TABLE center_stock_all_work IS '在庫連携（全件）ワーク';
COMMENT ON COLUMN center_stock_all_work.center_code IS 'センターコード';
COMMENT ON COLUMN center_stock_all_work.stock_kind IS '在庫区分';
COMMENT ON COLUMN center_stock_all_work.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN center_stock_all_work.stock_quantity IS '在庫数';
COMMENT ON COLUMN center_stock_all_work.allocated_quantity IS '引当数';

