CREATE TABLE commodity_detail (
    shop_code VARCHAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    unit_price NUMERIC(8,0) NOT NULL,
    discount_price NUMERIC(8,0),
    reservation_price NUMERIC(8,0),
    jan_code VARCHAR(16),
    standard_detail1_name VARCHAR(20),
    standard_detail2_name VARCHAR(20),
    hinban_code VARCHAR(24) NOT NULL,
    hinban_kind VARCHAR(2) NOT NULL,
    member_price_applied_flg NUMERIC(1,0) NOT NULL,
    member_price_discount_rate NUMERIC(3,0) NOT NULL,
    member_price NUMERIC(8,0) NOT NULL,
    air_transport_flg NUMERIC(1,0) NOT NULL,
    commodity_prod_pack_type VARCHAR(2) NOT NULL,
    delivery_note_no_disp_flg NUMERIC(1,0) NOT NULL,
    reduction_point NUMERIC(10,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, sku_code)
);
COMMENT ON TABLE commodity_detail IS '商品詳細';
COMMENT ON COLUMN commodity_detail.shop_code IS 'ショップコード';
COMMENT ON COLUMN commodity_detail.sku_code IS 'SKUコード';
COMMENT ON COLUMN commodity_detail.commodity_code IS '商品コード';
COMMENT ON COLUMN commodity_detail.unit_price IS '商品単価';
COMMENT ON COLUMN commodity_detail.discount_price IS '特別価格';
COMMENT ON COLUMN commodity_detail.reservation_price IS '予約価格';
COMMENT ON COLUMN commodity_detail.jan_code IS 'JANコード';
COMMENT ON COLUMN commodity_detail.standard_detail1_name IS '規格詳細1名称';
COMMENT ON COLUMN commodity_detail.standard_detail2_name IS '規格詳細2名称';
COMMENT ON COLUMN commodity_detail.hinban_code IS '品番コード';
COMMENT ON COLUMN commodity_detail.hinban_kind IS '品番種別';
COMMENT ON COLUMN commodity_detail.member_price_applied_flg IS '会員価格適用フラグ';
COMMENT ON COLUMN commodity_detail.member_price_discount_rate IS '会員価格値引率';
COMMENT ON COLUMN commodity_detail.member_price IS '会員価格';
COMMENT ON COLUMN commodity_detail.air_transport_flg IS '空輸可否フラグ';
COMMENT ON COLUMN commodity_detail.commodity_prod_pack_type IS '商品包装種類';
COMMENT ON COLUMN commodity_detail.delivery_note_no_disp_flg IS '納品書非表示フラグ';
COMMENT ON COLUMN commodity_detail.reduction_point IS '利用ポイント数';
COMMENT ON COLUMN commodity_detail.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN commodity_detail.created_user IS '作成ユーザ';
COMMENT ON COLUMN commodity_detail.created_datetime IS '作成日時';
COMMENT ON COLUMN commodity_detail.updated_user IS '更新ユーザ';
COMMENT ON COLUMN commodity_detail.updated_datetime IS '更新日時';
COMMENT ON COLUMN commodity_detail.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN commodity_detail.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN commodity_detail.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN commodity_detail.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN commodity_detail.d_version IS 'デ連バージョン';

