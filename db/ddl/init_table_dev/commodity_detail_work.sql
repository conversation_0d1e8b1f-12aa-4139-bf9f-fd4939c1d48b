CREATE TABLE commodity_detail_work (
    shop_code VARCHAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    unit_price NUMERIC(8,0) NOT NULL,
    discount_price NUMERIC(8,0),
    reservation_price NUMERIC(8,0),
    jan_code VARCHAR(16),
    standard_detail1_name VARCHAR(20),
    standard_detail2_name VARCHAR(20),
    hinban_code VARCHAR(24) NOT NULL,
    hinban_kind VARCHAR(2) NOT NULL,
    member_price_applied_flg NUMERIC(1,0) NOT NULL,
    member_price_discount_rate NUMERIC(3,0) NOT NULL,
    member_price NUMERIC(8,0) NOT NULL,
    air_transport_flg NUMERIC(1,0) NOT NULL,
    commodity_prod_pack_type VARCHAR(2) NOT NULL,
    delivery_note_no_disp_flg NUMERIC(1,0) NOT NULL,
    reduction_point NUMERIC(10,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (shop_code, sku_code)
);
COMMENT ON TABLE commodity_detail_work IS '商品詳細ワーク';
COMMENT ON COLUMN commodity_detail_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN commodity_detail_work.sku_code IS 'SKUコード';
COMMENT ON COLUMN commodity_detail_work.commodity_code IS '商品コード';
COMMENT ON COLUMN commodity_detail_work.unit_price IS '商品単価';
COMMENT ON COLUMN commodity_detail_work.discount_price IS '特別価格';
COMMENT ON COLUMN commodity_detail_work.reservation_price IS '予約価格';
COMMENT ON COLUMN commodity_detail_work.jan_code IS 'JANコード';
COMMENT ON COLUMN commodity_detail_work.standard_detail1_name IS '規格詳細1名称';
COMMENT ON COLUMN commodity_detail_work.standard_detail2_name IS '規格詳細2名称';
COMMENT ON COLUMN commodity_detail_work.hinban_code IS '品番コード';
COMMENT ON COLUMN commodity_detail_work.hinban_kind IS '品番種別';
COMMENT ON COLUMN commodity_detail_work.member_price_applied_flg IS '会員価格適用フラグ';
COMMENT ON COLUMN commodity_detail_work.member_price_discount_rate IS '会員価格値引率';
COMMENT ON COLUMN commodity_detail_work.member_price IS '会員価格';
COMMENT ON COLUMN commodity_detail_work.air_transport_flg IS '空輸可否フラグ';
COMMENT ON COLUMN commodity_detail_work.commodity_prod_pack_type IS '商品包装種類';
COMMENT ON COLUMN commodity_detail_work.delivery_note_no_disp_flg IS '納品書非表示フラグ';
COMMENT ON COLUMN commodity_detail_work.reduction_point IS '利用ポイント数';
COMMENT ON COLUMN commodity_detail_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN commodity_detail_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN commodity_detail_work.created_datetime IS '作成日時';
COMMENT ON COLUMN commodity_detail_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN commodity_detail_work.updated_datetime IS '更新日時';

