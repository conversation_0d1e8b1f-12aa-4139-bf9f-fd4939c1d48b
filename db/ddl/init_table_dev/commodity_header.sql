CREATE TABLE commodity_header (
    shop_code VARCHAR(16) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    commodity_name VARCHAR(100) NOT NULL,
    commodity_type NUMERIC(1,0) NOT NULL,
    represent_sku_code VARCHAR(24) NOT NULL,
    represent_sku_unit_price NUMERIC(8,0) NOT NULL,
    stock_status_no NUMERIC(8,0),
    stock_management_type NUMERIC(1,0) NOT NULL,
    age_limit_code NUMERIC(8,0) NOT NULL,
    commodity_tax_type NUMERIC(1,0) NOT NULL,
    tax_group_code VARCHAR(8) NOT NULL,
    short_description VARCHAR(100),
    commodity_search_words VARCHAR(500),
    prior_printing_description VARCHAR(1000),
    posterior_printing_description VARCHAR(1000),
    delivery_description VARCHAR(1000),
    sale_start_datetime TIMESTAMP(0) NOT NULL,
    sale_end_datetime TIMESTAMP(0) NOT NULL,
    discount_price_start_datetime TIMESTAMP(0),
    discount_price_end_datetime TIMESTAMP(0),
    reservation_start_datetime TIMESTAMP(0) NOT NULL,
    reservation_end_datetime TIMESTAMP(0) NOT NULL,
    prior_printing_start_date TIMESTAMP(0),
    prior_printing_end_date TIMESTAMP(0),
    posterior_printing_start_date TIMESTAMP(0),
    posterior_printing_end_date TIMESTAMP(0),
    delivery_type_no NUMERIC(8,0) NOT NULL,
    sales_method_type NUMERIC(1,0) NOT NULL,
    manufacturer_model_no VARCHAR(50),
    link_url VARCHAR(256),
    recommend_commodity_rank NUMERIC(8,0) NOT NULL,
    commodity_popular_rank NUMERIC(8,0) NOT NULL,
    commodity_standard1_name VARCHAR(20),
    commodity_standard2_name VARCHAR(20),
    commodity_point_rate NUMERIC(3,0),
    commodity_point_start_datetime TIMESTAMP(0),
    commodity_point_end_datetime TIMESTAMP(0),
    sale_flg NUMERIC(1,0) NOT NULL,
    noshi_effective_flg NUMERIC(1,0) NOT NULL,
    arrival_goods_flg NUMERIC(1,0) NOT NULL,
    oneshot_order_limit NUMERIC(8,0),
    standard_image_type NUMERIC(1,0) NOT NULL,
    purchasing_confirm_flg_pc NUMERIC(1,0) NOT NULL,
    purchasing_confirm_flg_sp NUMERIC(1,0) NOT NULL,
    commodity_kind VARCHAR(2) NOT NULL,
    keihi_hurikae_target_flg NUMERIC(1,0) NOT NULL,
    charge_user_code NUMERIC(38,0),
    commodity_remark VARCHAR(50),
    channel_cc_sale_flg NUMERIC(1,0) NOT NULL,
    channel_ec_sale_flg NUMERIC(1,0) NOT NULL,
    shipping_charge_target_flg NUMERIC(1,0) NOT NULL,
    first_purchase_limit_flg NUMERIC(1,0) NOT NULL,
    purchase_hold_flg NUMERIC(1,0) NOT NULL,
    commodity_exclude_flg NUMERIC(1,0) NOT NULL,
    commodity_subsubcategory_code VARCHAR(16),
    pack_calc_pattern NUMERIC(2,0) NOT NULL,
    pad_type NUMERIC(2,0) NOT NULL,
    fall_down_flg NUMERIC(1,0) NOT NULL,
    height NUMERIC(5,1),
    width NUMERIC(5,1),
    deepness NUMERIC(5,1),
    weight NUMERIC(7,1),
    tracking_out_flg NUMERIC(1,0) NOT NULL,
    mdm_management_code NUMERIC(20,0),
    commodity_segment VARCHAR(5),
    business_segment VARCHAR(5),
    commodity_group VARCHAR(5),
    commodity_series VARCHAR(5),
    core_department VARCHAR(2),
    accounting_pattern_type VARCHAR(2),
    return_enabled_flg VARCHAR(1),
    exchange_enabled_flg VARCHAR(1),
    exterior_box_weight VARCHAR(10),
    nekoposu_volume_rate NUMERIC(3,0),
    warehouse_assembly_flg VARCHAR(1),
    mail_delivery_flg VARCHAR(1),
    before_renewal_commodity_code VARCHAR(200),
    preorder_enable_days NUMERIC(2,0),
    main_product_no VARCHAR(24),
    product_no VARCHAR(24),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, commodity_code)
);
COMMENT ON TABLE commodity_header IS '商品ヘッダ';
COMMENT ON COLUMN commodity_header.shop_code IS 'ショップコード';
COMMENT ON COLUMN commodity_header.commodity_code IS '商品コード';
COMMENT ON COLUMN commodity_header.commodity_name IS '商品名称';
COMMENT ON COLUMN commodity_header.commodity_type IS '商品区分';
COMMENT ON COLUMN commodity_header.represent_sku_code IS '代表SKUコード';
COMMENT ON COLUMN commodity_header.represent_sku_unit_price IS '代表SKU単価';
COMMENT ON COLUMN commodity_header.stock_status_no IS '在庫状況番号';
COMMENT ON COLUMN commodity_header.stock_management_type IS '在庫管理区分';
COMMENT ON COLUMN commodity_header.age_limit_code IS '年齢制限コード';
COMMENT ON COLUMN commodity_header.commodity_tax_type IS '商品消費税区分';
COMMENT ON COLUMN commodity_header.tax_group_code IS '消費税グループコード';
COMMENT ON COLUMN commodity_header.short_description IS '概要説明';
COMMENT ON COLUMN commodity_header.commodity_search_words IS '商品検索ワード';
COMMENT ON COLUMN commodity_header.prior_printing_description IS '事前掲載説明';
COMMENT ON COLUMN commodity_header.posterior_printing_description IS '事後掲載説明';
COMMENT ON COLUMN commodity_header.delivery_description IS '納期説明';
COMMENT ON COLUMN commodity_header.sale_start_datetime IS '販売開始日時';
COMMENT ON COLUMN commodity_header.sale_end_datetime IS '販売終了日時';
COMMENT ON COLUMN commodity_header.discount_price_start_datetime IS '特別価格開始日時';
COMMENT ON COLUMN commodity_header.discount_price_end_datetime IS '特別価格終了日時';
COMMENT ON COLUMN commodity_header.reservation_start_datetime IS '予約開始日時';
COMMENT ON COLUMN commodity_header.reservation_end_datetime IS '予約終了日時';
COMMENT ON COLUMN commodity_header.prior_printing_start_date IS '事前掲載開始日時';
COMMENT ON COLUMN commodity_header.prior_printing_end_date IS '事前掲載終了日時';
COMMENT ON COLUMN commodity_header.posterior_printing_start_date IS '事後掲載開始日時';
COMMENT ON COLUMN commodity_header.posterior_printing_end_date IS '事後掲載終了日時';
COMMENT ON COLUMN commodity_header.delivery_type_no IS '配送種別番号';
COMMENT ON COLUMN commodity_header.sales_method_type IS '販売方法区分';
COMMENT ON COLUMN commodity_header.manufacturer_model_no IS 'メーカー型番';
COMMENT ON COLUMN commodity_header.link_url IS 'リンクURL';
COMMENT ON COLUMN commodity_header.recommend_commodity_rank IS 'おすすめ商品順位';
COMMENT ON COLUMN commodity_header.commodity_popular_rank IS '人気順位';
COMMENT ON COLUMN commodity_header.commodity_standard1_name IS '規格名称1';
COMMENT ON COLUMN commodity_header.commodity_standard2_name IS '規格名称2';
COMMENT ON COLUMN commodity_header.commodity_point_rate IS '商品別ポイント付与率';
COMMENT ON COLUMN commodity_header.commodity_point_start_datetime IS '商品別ポイント付与開始日時';
COMMENT ON COLUMN commodity_header.commodity_point_end_datetime IS '商品別ポイント付与終了日時';
COMMENT ON COLUMN commodity_header.sale_flg IS '販売フラグ';
COMMENT ON COLUMN commodity_header.noshi_effective_flg IS '熨斗有効フラグ';
COMMENT ON COLUMN commodity_header.arrival_goods_flg IS '入荷お知らせ可能フラグ';
COMMENT ON COLUMN commodity_header.oneshot_order_limit IS '注文毎注文上限数';
COMMENT ON COLUMN commodity_header.standard_image_type IS '規格画像区分';
COMMENT ON COLUMN commodity_header.purchasing_confirm_flg_pc IS 'PC用購入確認フラグ';
COMMENT ON COLUMN commodity_header.purchasing_confirm_flg_sp IS 'スマートフォン用購入確認フラグ';
COMMENT ON COLUMN commodity_header.commodity_kind IS '商品種別';
COMMENT ON COLUMN commodity_header.keihi_hurikae_target_flg IS '経費振替対象フラグ';
COMMENT ON COLUMN commodity_header.charge_user_code IS '担当ユーザコード';
COMMENT ON COLUMN commodity_header.commodity_remark IS '商品備考';
COMMENT ON COLUMN commodity_header.channel_cc_sale_flg IS 'チャネル別販売フラグ_コールセンター';
COMMENT ON COLUMN commodity_header.channel_ec_sale_flg IS 'チャネル別販売フラグ_Webサイト';
COMMENT ON COLUMN commodity_header.shipping_charge_target_flg IS '送料計算対象フラグ';
COMMENT ON COLUMN commodity_header.first_purchase_limit_flg IS '初回購入限定フラグ';
COMMENT ON COLUMN commodity_header.purchase_hold_flg IS '購入時発送保留フラグ';
COMMENT ON COLUMN commodity_header.commodity_exclude_flg IS '特定商品除外フラグ';
COMMENT ON COLUMN commodity_header.commodity_subsubcategory_code IS '商品小分類';
COMMENT ON COLUMN commodity_header.pack_calc_pattern IS '荷姿計算パターン';
COMMENT ON COLUMN commodity_header.pad_type IS 'パッド使用区分';
COMMENT ON COLUMN commodity_header.fall_down_flg IS '転倒不可フラグ';
COMMENT ON COLUMN commodity_header.height IS '縦';
COMMENT ON COLUMN commodity_header.width IS '横';
COMMENT ON COLUMN commodity_header.deepness IS '奥行き';
COMMENT ON COLUMN commodity_header.weight IS '重量';
COMMENT ON COLUMN commodity_header.tracking_out_flg IS '送り状不要フラグ';
COMMENT ON COLUMN commodity_header.mdm_management_code IS 'MDM統合管理コード';
COMMENT ON COLUMN commodity_header.commodity_segment IS '商品セグメント';
COMMENT ON COLUMN commodity_header.business_segment IS '事業セグメント';
COMMENT ON COLUMN commodity_header.commodity_group IS '商品分類';
COMMENT ON COLUMN commodity_header.commodity_series IS '商品シリーズ';
COMMENT ON COLUMN commodity_header.core_department IS '基幹部門';
COMMENT ON COLUMN commodity_header.accounting_pattern_type IS '会計パターン区分';
COMMENT ON COLUMN commodity_header.return_enabled_flg IS '返品可能フラグ';
COMMENT ON COLUMN commodity_header.exchange_enabled_flg IS '交換可能フラグ';
COMMENT ON COLUMN commodity_header.exterior_box_weight IS '外装箱重量';
COMMENT ON COLUMN commodity_header.nekoposu_volume_rate IS 'ネコポス体積率';
COMMENT ON COLUMN commodity_header.warehouse_assembly_flg IS '倉庫組立セット商品フラグ';
COMMENT ON COLUMN commodity_header.mail_delivery_flg IS 'メール便フラグ';
COMMENT ON COLUMN commodity_header.before_renewal_commodity_code IS 'リニューアル前商品番号';
COMMENT ON COLUMN commodity_header.preorder_enable_days IS '先付け受注可能日数';
COMMENT ON COLUMN commodity_header.main_product_no IS 'オリジナル商品No';
COMMENT ON COLUMN commodity_header.product_no IS '商品No';
COMMENT ON COLUMN commodity_header.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN commodity_header.created_user IS '作成ユーザ';
COMMENT ON COLUMN commodity_header.created_datetime IS '作成日時';
COMMENT ON COLUMN commodity_header.updated_user IS '更新ユーザ';
COMMENT ON COLUMN commodity_header.updated_datetime IS '更新日時';
COMMENT ON COLUMN commodity_header.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN commodity_header.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN commodity_header.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN commodity_header.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN commodity_header.d_version IS 'デ連バージョン';

