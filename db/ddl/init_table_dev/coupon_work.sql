CREATE TABLE coupon_work (
    coupon_management_code VARCHAR(16) NOT NULL,
    coupon_code VARCHAR(16),
    coupon_name VARCHAR(50) NOT NULL,
    coupon_invalid_flag NUMERIC(1,0) NOT NULL,
    coupon_type NUMERIC(1,0) NOT NULL,
    coupon_issue_type NUMERIC(1,0) NOT NULL,
    coupon_use_limit NUMERIC(1,0) NOT NULL,
    coupon_use_purchase_price NUMERIC(8,0) NOT NULL,
    coupon_discount_type NUMERIC(1,0) NOT NULL,
    coupon_discount_rate NUMERIC(3,0),
    coupon_discount_price NUMERIC(8,0),
    coupon_start_datetime TIMESTAMP(0) NOT NULL,
    coupon_end_datetime TIMESTAMP(0) NOT NULL,
    coupon_limit_display_period NUMERIC(3,0),
    coupon_limit_display VARCHAR(50),
    coupon_description VARCHAR(200),
    coupon_message VARCHAR(200),
    coupon_kbn VARCHAR(2),
    coupon_post_in_charge VARCHAR(16) NOT NULL,
    coupon_commodity_flag NUMERIC(1,0) NOT NULL,
    marketing_channel_list VARCHAR(100),
    goods_group VARCHAR(16),
    commodity_category_code VARCHAR(16),
    commodity_series VARCHAR(5),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (coupon_management_code)
);
COMMENT ON TABLE coupon_work IS 'クーポンワーク';
COMMENT ON COLUMN coupon_work.coupon_management_code IS 'クーポン管理コード';
COMMENT ON COLUMN coupon_work.coupon_code IS 'クーポンコード';
COMMENT ON COLUMN coupon_work.coupon_name IS 'クーポン名称';
COMMENT ON COLUMN coupon_work.coupon_invalid_flag IS 'クーポン無効フラグ';
COMMENT ON COLUMN coupon_work.coupon_type IS 'クーポン種別';
COMMENT ON COLUMN coupon_work.coupon_issue_type IS 'クーポン自動発行種別';
COMMENT ON COLUMN coupon_work.coupon_use_limit IS 'クーポン利用制限';
COMMENT ON COLUMN coupon_work.coupon_use_purchase_price IS 'クーポン利用最低購入金額';
COMMENT ON COLUMN coupon_work.coupon_discount_type IS 'クーポン値引区分';
COMMENT ON COLUMN coupon_work.coupon_discount_rate IS 'クーポン値引率';
COMMENT ON COLUMN coupon_work.coupon_discount_price IS 'クーポン値引額';
COMMENT ON COLUMN coupon_work.coupon_start_datetime IS 'クーポン開始日時';
COMMENT ON COLUMN coupon_work.coupon_end_datetime IS 'クーポン終了日時';
COMMENT ON COLUMN coupon_work.coupon_limit_display_period IS 'クーポン期限表示日数';
COMMENT ON COLUMN coupon_work.coupon_limit_display IS 'クーポン期限表示説明';
COMMENT ON COLUMN coupon_work.coupon_description IS 'クーポン説明';
COMMENT ON COLUMN coupon_work.coupon_message IS '連絡事項';
COMMENT ON COLUMN coupon_work.coupon_kbn IS 'クーポン区分';
COMMENT ON COLUMN coupon_work.coupon_post_in_charge IS 'クーポン担当部署';
COMMENT ON COLUMN coupon_work.coupon_commodity_flag IS 'クーポン特定商品指定フラグ';
COMMENT ON COLUMN coupon_work.marketing_channel_list IS '販売経路指定';
COMMENT ON COLUMN coupon_work.goods_group IS 'クーポン部門';
COMMENT ON COLUMN coupon_work.commodity_category_code IS 'クーポン大分類';
COMMENT ON COLUMN coupon_work.commodity_series IS 'クーポン商品シリーズ';
COMMENT ON COLUMN coupon_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN coupon_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN coupon_work.created_datetime IS '作成日時';
COMMENT ON COLUMN coupon_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN coupon_work.updated_datetime IS '更新日時';

