CREATE TABLE customer (
    customer_code VARCHAR(16) NOT NULL,
    customer_no VARCHAR(12) NOT NULL,
    customer_group_code VARCHAR(16) NOT NULL,
    last_name VARCHAR(20) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(20),
    last_name_kana VARCHAR(40) NOT NULL,
    first_name_kana VARCHAR(40),
    login_id VARCHAR(256) NOT NULL,
    email VARCHAR(256),
    password VARCHAR(128) NOT NULL,
    birth_date TIMESTAMP(0) NOT NULL,
    sex NUMERIC(1) NOT NULL,
    request_mail_type NUMERIC(1) NOT NULL,
    client_mail_type NUMERIC(1) NOT NULL,
    caution VARCHAR(200),
    login_datetime TIMESTAMP(0),
    login_error_count NUMERIC(2) NOT NULL,
    login_locked_flg NUMERIC(1) NOT NULL,
    customer_status NUMERIC(1) NOT NULL,
    customer_valid_status VARCHAR(2) NOT NULL,
    customer_attribute_reply_date TIMESTAMP(0),
    latest_point_acquired_date TIMESTAMP(0),
    rest_point NUMERIC(8),
    temporary_point NUMERIC(8),
    withdrawal_request_date TIMESTAMP(0),
    withdrawal_date TIMESTAMP(0),
    auth_secret_key VARCHAR(128),
    customer_type VARCHAR(2) NOT NULL,
    black_customer_kbn VARCHAR(1) NOT NULL,
    black_reason_kbn VARCHAR(2),
    black_register_date TIMESTAMP(0),
    mail_advisability_flg VARCHAR(2) NOT NULL,
    bd_advisability_flg VARCHAR(2) NOT NULL,
    mail_magazine_flg NUMERIC(1) NOT NULL Default 0,
    shipped_mail_flg NUMERIC(1) NOT NULL,
    receipt_to VARCHAR(50),
    receipt_detail VARCHAR(50),
    demand_exclude_flg NUMERIC(1) NOT NULL,
    crm_customer_id VARCHAR(20),
    unity_customer_code VARCHAR(16),
    unity_datetime TIMESTAMP(0),
    niyose_flg NUMERIC(1) NOT NULL,
    shipping_method_kbn VARCHAR(2),
    ec_login_id VARCHAR(256),
    guest_flg NUMERIC(1),
    member_no VARCHAR(10),
    member_status VARCHAR(20),
    shortage_declare_flg NUMERIC(1),
    order_monitor_flg NUMERIC(1),
    member_memo VARCHAR(1000),
    crm_customer_updated_datetime VARCHAR(24),
    member_rank VARCHAR(1),
    order_ng_flg NUMERIC(1),
    free_only_purchase_flg NUMERIC(1),
    orm_rowid NUMERIC(38) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (customer_code)
);
COMMENT ON TABLE customer IS '顧客';
COMMENT ON COLUMN customer.customer_code IS '顧客コード';
COMMENT ON COLUMN customer.customer_no IS '顧客番号';
COMMENT ON COLUMN customer.customer_group_code IS '顧客グループコード';
COMMENT ON COLUMN customer.last_name IS '姓';
COMMENT ON COLUMN customer.first_name IS '名';
COMMENT ON COLUMN customer.last_name_kana IS '姓かな';
COMMENT ON COLUMN customer.first_name_kana IS '名かな';
COMMENT ON COLUMN customer.login_id IS 'ログインID';
COMMENT ON COLUMN customer.email IS 'メールアドレス';
COMMENT ON COLUMN customer.password IS 'パスワード';
COMMENT ON COLUMN customer.birth_date IS '生年月日';
COMMENT ON COLUMN customer.sex IS '性別';
COMMENT ON COLUMN customer.request_mail_type IS '希望メール区分';
COMMENT ON COLUMN customer.client_mail_type IS 'クライアントメール区分';
COMMENT ON COLUMN customer.caution IS '注意事項（管理側のみ参照）';
COMMENT ON COLUMN customer.login_datetime IS 'ログイン日時';
COMMENT ON COLUMN customer.login_error_count IS 'ログイン失敗回数';
COMMENT ON COLUMN customer.login_locked_flg IS 'ログインロックフラグ';
COMMENT ON COLUMN customer.customer_status IS '顧客ステータス';
COMMENT ON COLUMN customer.customer_valid_status IS '顧客有効ステータス';
COMMENT ON COLUMN customer.customer_attribute_reply_date IS '顧客属性回答日';
COMMENT ON COLUMN customer.latest_point_acquired_date IS 'ポイント最終獲得日';
COMMENT ON COLUMN customer.rest_point IS 'ポイント残高';
COMMENT ON COLUMN customer.temporary_point IS '仮発行ポイント';
COMMENT ON COLUMN customer.withdrawal_request_date IS '退会希望日';
COMMENT ON COLUMN customer.withdrawal_date IS '退会日';
COMMENT ON COLUMN customer.auth_secret_key IS '認証シークレットキー';
COMMENT ON COLUMN customer.customer_type IS '顧客種別';
COMMENT ON COLUMN customer.black_customer_kbn IS 'ブラック顧客区分';
COMMENT ON COLUMN customer.black_reason_kbn IS 'ブラック理由区分';
COMMENT ON COLUMN customer.black_register_date IS 'ブラック登録日';
COMMENT ON COLUMN customer.mail_advisability_flg IS 'メール可否区分';
COMMENT ON COLUMN customer.bd_advisability_flg IS 'BDクーポン可否区分';
COMMENT ON COLUMN customer.mail_magazine_flg IS 'メールマガジン配信設定フラグ';
COMMENT ON COLUMN customer.shipped_mail_flg IS '発送メール配信設定フラグ';
COMMENT ON COLUMN customer.receipt_to IS '領収書宛名';
COMMENT ON COLUMN customer.receipt_detail IS '領収書但し書き';
COMMENT ON COLUMN customer.demand_exclude_flg IS '督促除外フラグ';
COMMENT ON COLUMN customer.crm_customer_id IS 'CRM顧客ID';
COMMENT ON COLUMN customer.unity_customer_code IS '名寄せ統合先顧客コード';
COMMENT ON COLUMN customer.unity_datetime IS '名寄せ統合日時';
COMMENT ON COLUMN customer.niyose_flg IS '荷寄せ可能フラグ';
COMMENT ON COLUMN customer.shipping_method_kbn IS '配送方法指定区分';
COMMENT ON COLUMN customer.ec_login_id IS 'ECログインID';
COMMENT ON COLUMN customer.guest_flg IS 'ゲストフラグ';
COMMENT ON COLUMN customer.member_no IS '会員番号';
COMMENT ON COLUMN customer.member_status IS '会員ステータス';
COMMENT ON COLUMN customer.shortage_declare_flg IS '不足申告フラグ';
COMMENT ON COLUMN customer.order_monitor_flg IS '注文監視フラグ';
COMMENT ON COLUMN customer.member_memo IS '会員メモ';
COMMENT ON COLUMN customer.crm_customer_updated_datetime IS 'CRM顧客更新日時';
COMMENT ON COLUMN customer.member_rank IS '会員ランク';
COMMENT ON COLUMN customer.order_ng_flg IS '注文不可フラグ';
COMMENT ON COLUMN customer.free_only_purchase_flg IS 'フリーのみ購入可フラグ';
COMMENT ON COLUMN customer.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN customer.created_user IS '作成ユーザ';
COMMENT ON COLUMN customer.created_datetime IS '作成日時';
COMMENT ON COLUMN customer.updated_user IS '更新ユーザ';
COMMENT ON COLUMN customer.updated_datetime IS '更新日時';
COMMENT ON COLUMN customer.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN customer.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN customer.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN customer.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN customer.d_version IS 'デ連バージョン';

