CREATE TABLE customer_address_work (
    customer_code VARCHAR(16) NOT NULL,
    address_no NUMERIC(8, 0) NOT NULL,
    address_alias VARCHAR(20) NOT NULL,
    address_last_name VARCHAR(20) NOT NULL,
    address_first_name VARCHAR(20),
    address_last_name_kana VARCHAR(40) NOT NULL,
    address_first_name_kana VARCHAR(40),
    postal_code VARCHAR(7) NOT NULL,
    prefecture_code VARCHAR(2) NOT NULL,
    address1 VARCHAR(4) NOT NULL,
    address2 VARCHAR(50) NOT NULL,
    address3 VARCHAR(255) NOT NULL,
    address4 VARCHAR(100),
    phone_number VARCHAR(16),
    corporation_post_name VARCHAR(40),
    address_id VARCHAR(20),
    crm_address_id VARCHAR(20),
    crm_address_updated_datetime VARCHAR(24),
    orm_rowid NUMERIC(38, 0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP NOT NULL,
    <PERSON><PERSON>ARY KEY (customer_code, address_no)
);
CO<PERSON>ENT ON TABLE customer_address_work IS '顧客アドレス帳ワーク';
COMMENT ON COLUMN customer_address_work.customer_code IS '顧客コード';
COMMENT ON COLUMN customer_address_work.address_no IS 'アドレス帳番号';
COMMENT ON COLUMN customer_address_work.address_alias IS 'アドレス呼称';
COMMENT ON COLUMN customer_address_work.address_last_name IS '宛名：姓';
COMMENT ON COLUMN customer_address_work.address_first_name IS '宛名：名';
COMMENT ON COLUMN customer_address_work.address_last_name_kana IS '宛名姓かな';
COMMENT ON COLUMN customer_address_work.address_first_name_kana IS '宛名名かな';
COMMENT ON COLUMN customer_address_work.postal_code IS '郵便番号';
COMMENT ON COLUMN customer_address_work.prefecture_code IS '都道府県コード';
COMMENT ON COLUMN customer_address_work.address1 IS '住所1';
COMMENT ON COLUMN customer_address_work.address2 IS '住所2';
COMMENT ON COLUMN customer_address_work.address3 IS '住所3';
COMMENT ON COLUMN customer_address_work.address4 IS '住所4';
COMMENT ON COLUMN customer_address_work.phone_number IS '電話番号';
COMMENT ON COLUMN customer_address_work.corporation_post_name IS '会社部署名';
COMMENT ON COLUMN customer_address_work.address_id IS '住所ID';
COMMENT ON COLUMN customer_address_work.crm_address_id IS 'CRMアドレスID';
COMMENT ON COLUMN customer_address_work.crm_address_updated_datetime IS 'CRMアドレスの更新日時';
COMMENT ON COLUMN customer_address_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN customer_address_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN customer_address_work.created_datetime IS '作成日時';
COMMENT ON COLUMN customer_address_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN customer_address_work.updated_datetime IS '更新日時';