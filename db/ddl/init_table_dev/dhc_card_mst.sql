CREATE TABLE dhc_card_mst (
    record_gb  VARCHAR(1) NOT NULL,
    smcc_gb  VARCHAR(2),
    card_no  VARCHAR(16),
    admission_date  VARCHAR(8),
    subscription_date  VARCHAR(8),
    cust_name  VARCHAR(20),
    valid_date  VARCHAR(4),
    trust_withdrawal_yn  VARCHAR(1),
    trust_accident_yn  VARCHAR(1),
    trust_attention_yn  VARCHAR(1),
    shop_code  VARCHAR(5),
    subscription_gb  VARCHAR(1),
    staff_code  VARCHAR(19),
    memb_no  VARCHAR(20),
    cust_no  VARCHAR(20),
    modify_cust_name_yn  VARCHAR(1),
    modify_valid_date_yn  VARCHAR(1),
    modify_cooperation_yn  VARCHAR(1),
    modify_trust_yn  VARCHAR(1),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL
);
COMMENT ON TABLE dhc_card_mst IS 'DHCカードマスタ';
COMMENT ON COLUMN dhc_card_mst.record_gb  IS 'レコード区分';
COMMENT ON COLUMN dhc_card_mst.smcc_gb  IS 'SMCC区分';
COMMENT ON COLUMN dhc_card_mst.card_no  IS 'カード番号';
COMMENT ON COLUMN dhc_card_mst.admission_date  IS '承認日';
COMMENT ON COLUMN dhc_card_mst.subscription_date  IS '申込日';
COMMENT ON COLUMN dhc_card_mst.cust_name  IS '顧客名';
COMMENT ON COLUMN dhc_card_mst.valid_date  IS '有効期限';
COMMENT ON COLUMN dhc_card_mst.trust_withdrawal_yn  IS '解約';
COMMENT ON COLUMN dhc_card_mst.trust_accident_yn  IS '信頼事故';
COMMENT ON COLUMN dhc_card_mst.trust_attention_yn  IS '要注意';
COMMENT ON COLUMN dhc_card_mst.shop_code  IS 'ショップコード';
COMMENT ON COLUMN dhc_card_mst.subscription_gb  IS '申込区分';
COMMENT ON COLUMN dhc_card_mst.staff_code  IS 'スタッフコード';
COMMENT ON COLUMN dhc_card_mst.memb_no  IS '会員番号';
COMMENT ON COLUMN dhc_card_mst.cust_no  IS '顧客番号';
COMMENT ON COLUMN dhc_card_mst.modify_cust_name_yn  IS '氏名変更';
COMMENT ON COLUMN dhc_card_mst.modify_valid_date_yn  IS '有効期限変更';
COMMENT ON COLUMN dhc_card_mst.modify_cooperation_yn  IS '協力変更';
COMMENT ON COLUMN dhc_card_mst.modify_trust_yn  IS '信頼変更';
COMMENT ON COLUMN dhc_card_mst.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN dhc_card_mst.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN dhc_card_mst.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN dhc_card_mst.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN dhc_card_mst.d_version IS 'デ連バージョン';

