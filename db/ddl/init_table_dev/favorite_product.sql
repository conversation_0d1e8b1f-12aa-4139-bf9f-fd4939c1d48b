CREATE TABLE favorite_product (
    id VARCHAR(18) NOT NULL,
    owner_id VARCHAR(18) NOT NULL,
    is_deleted VARCHAR(5),
    name <PERSON><PERSON><PERSON><PERSON>(80),
    created_date TIMESTAMP NOT NULL,
    created_by_id VARCHAR(18) NOT NULL,
    last_modified_date TIMESTAMP NOT NULL,
    last_modified_by_id VARCHAR(18) NOT NULL,
    system_modstamp TIMESTAMP NOT NULL,
    account_id__c VARCHAR(18) NOT NULL,
    product_id__c VARCHAR(18) NOT NULL,
    product_code__c VARCHAR(1300),
    product_name_c VARCHAR(1300),
    is_deleted__c VARCHAR(5),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (id)
);
COMMENT ON TABLE favorite_product IS 'お気に入り商品';
COMMENT ON COLUMN favorite_product.id IS 'カスタムオブジェクト ID';
COMMENT ON COLUMN favorite_product.owner_id IS '所有者 ID';
COMMENT ON COLUMN favorite_product.is_deleted IS '削除';
COMMENT ON COLUMN favorite_product.name IS 'お気に入り商品名';
COMMENT ON COLUMN favorite_product.created_date IS '作成日';
COMMENT ON COLUMN favorite_product.created_by_id IS '作成者 ID';
COMMENT ON COLUMN favorite_product.last_modified_date IS '最終更新日';
COMMENT ON COLUMN favorite_product.last_modified_by_id IS '最終更新者 ID';
COMMENT ON COLUMN favorite_product.system_modstamp IS 'System Modstamp';
COMMENT ON COLUMN favorite_product.account_id__c IS '会員';
COMMENT ON COLUMN favorite_product.product_id__c IS '商品';
COMMENT ON COLUMN favorite_product.product_code__c IS '商品コード';
COMMENT ON COLUMN favorite_product.product_name_c IS '商品名';
COMMENT ON COLUMN favorite_product.is_deleted__c IS '削除フラグ';
COMMENT ON COLUMN favorite_product.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN favorite_product.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN favorite_product.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN favorite_product.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN favorite_product.d_version IS 'デ連バージョン';

