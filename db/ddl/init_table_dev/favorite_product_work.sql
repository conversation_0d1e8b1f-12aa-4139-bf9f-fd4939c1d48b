CREATE TABLE favorite_product_work (
    id VARCHAR(18) NOT NULL,
    owner_id VARCHAR(18) NOT NULL,
    is_deleted VARCHAR(5),
    name VA<PERSON>HAR(80),
    created_date TIMESTAMP NOT NULL,
    created_by_id VARCHAR(18) NOT NULL,
    last_modified_date TIMESTAMP NOT NULL,
    last_modified_by_id VARCHAR(18) NOT NULL,
    system_modstamp TIMESTAMP NOT NULL,
    account_id__c VARCHAR(18) NOT NULL,
    product_id__c VARCHAR(18) NOT NULL,
    product_code__c VARCHAR(1300),
    product_name_c VARCHAR(1300),
    is_deleted__c VARCHAR(5),
    PRIMARY KEY (id)
);
COMMENT ON TABLE favorite_product_work IS 'お気に入り商品ワーク';
COMMENT ON COLUMN favorite_product_work.id IS 'カスタムオブジェクト ID';
COMMENT ON COLUMN favorite_product_work.owner_id IS '所有者 ID';
COMMENT ON COLUMN favorite_product_work.is_deleted IS '削除';
COMMENT ON COLUMN favorite_product_work.name IS 'お気に入り商品名';
COMMENT ON COLUMN favorite_product_work.created_date IS '作成日';
COMMENT ON COLUMN favorite_product_work.created_by_id IS '作成者 ID';
COMMENT ON COLUMN favorite_product_work.last_modified_date IS '最終更新日';
COMMENT ON COLUMN favorite_product_work.last_modified_by_id IS '最終更新者 ID';
COMMENT ON COLUMN favorite_product_work.system_modstamp IS 'System Modstamp';
COMMENT ON COLUMN favorite_product_work.account_id__c IS '会員';
COMMENT ON COLUMN favorite_product_work.product_id__c IS '商品';
COMMENT ON COLUMN favorite_product_work.product_code__c IS '商品コード';
COMMENT ON COLUMN favorite_product_work.product_name_c IS '商品名';
COMMENT ON COLUMN favorite_product_work.is_deleted__c IS '削除フラグ';

