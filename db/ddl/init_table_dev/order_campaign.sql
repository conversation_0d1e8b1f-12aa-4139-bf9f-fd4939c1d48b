CREATE TABLE order_campaign (
    order_no VARCHAR(16) NOT NULL,
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_instructions_name VARCHAR(50) NOT NULL,
    campaign_description VARCHAR(100),
    campaign_end_date TIMESTAMP(0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no, campaign_instructions_code)
);
COMMENT ON TABLE order_campaign IS '受注キャンペーン適用';
COMMENT ON COLUMN order_campaign.order_no IS '受注番号';
COMMENT ON COLUMN order_campaign.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN order_campaign.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN order_campaign.campaign_description IS 'キャンペーン内容';
COMMENT ON COLUMN order_campaign.campaign_end_date IS 'キャンペーン適用終了日';
COMMENT ON COLUMN order_campaign.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_campaign.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_campaign.created_datetime IS '作成日時';
COMMENT ON COLUMN order_campaign.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_campaign.updated_datetime IS '更新日時';
COMMENT ON COLUMN order_campaign.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN order_campaign.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN order_campaign.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN order_campaign.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN order_campaign.d_version IS 'デ連バージョン';

