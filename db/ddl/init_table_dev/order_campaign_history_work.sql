CREATE TABLE order_campaign_history_work (
    order_history_id NUMERIC(38,0) NOT NULL,
    order_no VARCHAR(16) NOT NULL,
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_instructions_name VARCHAR(50) NOT NULL,
    campaign_description VARCHAR(100),
    campaign_end_date TIMESTAMP(0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (order_history_id, order_no, campaign_instructions_code)
);
COMMENT ON TABLE order_campaign_history_work IS '受注キャンペーン適用履歴ワーク';
COMMENT ON COLUMN order_campaign_history_work.order_history_id IS '受注履歴ID';
COMMENT ON COLUMN order_campaign_history_work.order_no IS '受注番号';
COMMENT ON COLUMN order_campaign_history_work.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN order_campaign_history_work.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN order_campaign_history_work.campaign_description IS 'キャンペーン内容';
COMMENT ON COLUMN order_campaign_history_work.campaign_end_date IS 'キャンペーン適用終了日';
COMMENT ON COLUMN order_campaign_history_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_campaign_history_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_campaign_history_work.created_datetime IS '作成日時';
COMMENT ON COLUMN order_campaign_history_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_campaign_history_work.updated_datetime IS '更新日時';

