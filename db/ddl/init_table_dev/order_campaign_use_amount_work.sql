CREATE TABLE order_campaign_use_amount_work (
    order_no VARCHAR(16) NOT NULL,
    tax_group_code VARCHAR(8) NOT NULL,
    tax_no NUMERIC(3,0) NOT NULL,
    use_code_type VARCHAR(1) NOT NULL,
    use_code VARCHAR(16) NOT NULL,
    use_amount NUMERIC(8,0) NOT NULL,
    tax_rate NUMERIC(3,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (order_no, tax_group_code, tax_no, use_code_type, use_code)
);
COMMENT ON TABLE order_campaign_use_amount_work IS '受注キャンペーンクーポン利用額ワーク';
COMMENT ON COLUMN order_campaign_use_amount_work.order_no IS '受注番号';
COMMENT ON COLUMN order_campaign_use_amount_work.tax_group_code IS '消費税グループコード';
COMMENT ON COLUMN order_campaign_use_amount_work.tax_no IS '消費税番号';
COMMENT ON COLUMN order_campaign_use_amount_work.use_code_type IS '利用コード種別';
COMMENT ON COLUMN order_campaign_use_amount_work.use_code IS '利用コード';
COMMENT ON COLUMN order_campaign_use_amount_work.use_amount IS '利用額';
COMMENT ON COLUMN order_campaign_use_amount_work.tax_rate IS '消費税率';
COMMENT ON COLUMN order_campaign_use_amount_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_campaign_use_amount_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_campaign_use_amount_work.created_datetime IS '作成日時';
COMMENT ON COLUMN order_campaign_use_amount_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_campaign_use_amount_work.updated_datetime IS '更新日時';

