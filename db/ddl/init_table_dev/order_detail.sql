CREATE TABLE order_detail (
    order_no VARCHAR(16) NOT NULL,
    order_detail_no NUMERIC(16,0) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    commodity_name VARCHAR(100) NOT NULL,
    commodity_kind VARCHAR(2) NOT NULL,
    baitai_code VARCHAR(10) NOT NULL,
    baitai_name VARCHAR(50) NOT NULL,
    hinban_code VARCHAR(24) NOT NULL,
    standard_detail1_name VARCHAR(20),
    standard_detail2_name VARCHAR(20),
    purchasing_amount NUMERIC(8,0) NOT NULL,
    unit_price NUMERIC(8,0) NOT NULL,
    retail_price NUMERIC(8,0) NOT NULL,
    retail_tax NUMERIC(10,2) NOT NULL,
    commodity_tax_group_code VARCHAR(8) NOT NULL,
    commodity_tax_no NUMERIC(3,0) NOT NULL,
    commodity_tax_rate NUMERIC(3,0) NOT NULL,
    commodity_tax NUMERIC(10,2) NOT NULL,
    commodity_tax_type NUMERIC(1,0) NOT NULL,
    campaign_code VARCHAR(16),
    campaign_name VARCHAR(50),
    campaign_instructions_code VARCHAR(16),
    campaign_instructions_name VARCHAR(50),
    campaign_discount_rate NUMERIC(3,0),
    campaign_discount_price NUMERIC(8,0),
    present_campaign_instructions_code VARCHAR(16),
    present_order_detail_no NUMERIC(16,0),
    age_limit_code NUMERIC(8,0),
    age_limit_name VARCHAR(50),
    age NUMERIC(3,0),
    age_limit_confirm_type NUMERIC(1,0),
    applied_point_rate NUMERIC(8,0) NOT NULL,
    benefits_code VARCHAR(10),
    benefits_name VARCHAR(50),
    benefits_commodity_code VARCHAR(16),
    stock_management_type NUMERIC(1,0) NOT NULL,
    stock_allocated_kbn VARCHAR(2) NOT NULL,
    allocated_warehouse_code VARCHAR(6),
    allocated_quantity NUMERIC(8,0) NOT NULL,
    arrival_reserved_quantity NUMERIC(8,0) NOT NULL,
    cancel_quantity NUMERIC(5,0) NOT NULL,
    henpin_qt NUMERIC(5,0) NOT NULL,
    coupon_management_code VARCHAR(16),
    coupon_code VARCHAR(16),
    coupon_name VARCHAR(50),
    coupon_discount_rate NUMERIC(3,0),
    coupon_discount_price NUMERIC(8,0),
    ec_promotion_id VARCHAR(256),
    ec_promotion_name VARCHAR(256),
    ec_promotion_discount_price NUMERIC(8,0),
    ec_campaign_id VARCHAR(256),
    ec_campaign_name VARCHAR(256),
    adjustment_price NUMERIC(8,0),
    keihi_hurikae_target_flg NUMERIC(1,0) NOT NULL,
    member_price_applied_flg NUMERIC(1,0) NOT NULL,
    shipping_charge_target_flg NUMERIC(1,0) NOT NULL,
    regular_contract_no VARCHAR(14),
    regular_contract_detail_no NUMERIC(3,0),
    regular_kaiji NUMERIC(5,0) NOT NULL,
    regular_check_memo VARCHAR(1000),
    total_commodity_buy_count NUMERIC(5,0) NOT NULL,
    total_commodity_regular_kaiji NUMERIC(5,0) NOT NULL,
    regular_total_commodity_regular_kaiji NUMERIC(5,0) NOT NULL,
    commodity_category_code VARCHAR(16),
    total_category_buy_count NUMERIC(5,0) NOT NULL,
    total_categoryregular_kaiji NUMERIC(5,0) NOT NULL,
    regular_total_categoryregular_kaiji NUMERIC(5,0) NOT NULL,
    commodity_subcategory_code VARCHAR(16),
    total_subcategory_buy_count NUMERIC(5,0) NOT NULL,
    total_subcategoryregular_kaiji NUMERIC(5,0) NOT NULL,
    regular_total_subcategoryregular_kaiji NUMERIC(5,0) NOT NULL,
    commodity_subsubcategory_code VARCHAR(16),
    total_subsubcategory_buy_count NUMERIC(5,0) NOT NULL,
    total_subsubcategoryregular_kaiji NUMERIC(5,0) NOT NULL,
    regular_total_subsubcategoryregular_kaiji NUMERIC(5,0) NOT NULL,
    grant_plan_point_prod_detail NUMERIC(10,0),
    reduction_plan_point_prod_detail NUMERIC(10,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no, order_detail_no)
);
COMMENT ON TABLE order_detail IS '受注明細';
COMMENT ON COLUMN order_detail.order_no IS '受注番号';
COMMENT ON COLUMN order_detail.order_detail_no IS '受注明細番号';
COMMENT ON COLUMN order_detail.shop_code IS 'ショップコード';
COMMENT ON COLUMN order_detail.sku_code IS 'SKUコード';
COMMENT ON COLUMN order_detail.commodity_code IS '商品コード';
COMMENT ON COLUMN order_detail.commodity_name IS '商品名称';
COMMENT ON COLUMN order_detail.commodity_kind IS '商品種別';
COMMENT ON COLUMN order_detail.baitai_code IS '媒体コード';
COMMENT ON COLUMN order_detail.baitai_name IS '媒体名称';
COMMENT ON COLUMN order_detail.hinban_code IS '品番コード';
COMMENT ON COLUMN order_detail.standard_detail1_name IS '規格詳細1名称';
COMMENT ON COLUMN order_detail.standard_detail2_name IS '規格詳細2名称';
COMMENT ON COLUMN order_detail.purchasing_amount IS '購入商品数';
COMMENT ON COLUMN order_detail.unit_price IS '商品単価';
COMMENT ON COLUMN order_detail.retail_price IS '販売価格';
COMMENT ON COLUMN order_detail.retail_tax IS '販売時消費税額';
COMMENT ON COLUMN order_detail.commodity_tax_group_code IS '商品消費税グループコード';
COMMENT ON COLUMN order_detail.commodity_tax_no IS '商品消費税番号';
COMMENT ON COLUMN order_detail.commodity_tax_rate IS '商品消費税率';
COMMENT ON COLUMN order_detail.commodity_tax IS '商品消費税額';
COMMENT ON COLUMN order_detail.commodity_tax_type IS '商品消費税区分';
COMMENT ON COLUMN order_detail.campaign_code IS 'キャンペーンコード';
COMMENT ON COLUMN order_detail.campaign_name IS 'キャンペーン名称';
COMMENT ON COLUMN order_detail.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN order_detail.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN order_detail.campaign_discount_rate IS 'キャンペーン値引率';
COMMENT ON COLUMN order_detail.campaign_discount_price IS 'キャンペーン値引額';
COMMENT ON COLUMN order_detail.present_campaign_instructions_code IS 'プレゼント付与元キャンペーン設定コード';
COMMENT ON COLUMN order_detail.present_order_detail_no IS 'プレゼント付与元受注明細番号';
COMMENT ON COLUMN order_detail.age_limit_code IS '年齢制限コード';
COMMENT ON COLUMN order_detail.age_limit_name IS '年齢制限名称';
COMMENT ON COLUMN order_detail.age IS '年齢';
COMMENT ON COLUMN order_detail.age_limit_confirm_type IS '年齢制限自動確認区分';
COMMENT ON COLUMN order_detail.applied_point_rate IS '適用ポイント付与率';
COMMENT ON COLUMN order_detail.benefits_code IS '特典コード';
COMMENT ON COLUMN order_detail.benefits_name IS '特典名称';
COMMENT ON COLUMN order_detail.benefits_commodity_code IS '特典対象商品コード';
COMMENT ON COLUMN order_detail.stock_management_type IS '在庫管理区分';
COMMENT ON COLUMN order_detail.stock_allocated_kbn IS '在庫引当区分';
COMMENT ON COLUMN order_detail.allocated_warehouse_code IS '引当倉庫コード';
COMMENT ON COLUMN order_detail.allocated_quantity IS '引当数量';
COMMENT ON COLUMN order_detail.arrival_reserved_quantity IS '入荷予定予約数量';
COMMENT ON COLUMN order_detail.cancel_quantity IS 'キャンセル数量';
COMMENT ON COLUMN order_detail.henpin_qt IS '返品数量';
COMMENT ON COLUMN order_detail.coupon_management_code IS 'クーポン管理コード';
COMMENT ON COLUMN order_detail.coupon_code IS 'クーポンコード';
COMMENT ON COLUMN order_detail.coupon_name IS 'クーポン名称';
COMMENT ON COLUMN order_detail.coupon_discount_rate IS 'クーポン値引率';
COMMENT ON COLUMN order_detail.coupon_discount_price IS 'クーポン値引額';
COMMENT ON COLUMN order_detail.ec_promotion_id IS 'ECプロモーションID';
COMMENT ON COLUMN order_detail.ec_promotion_name IS 'ECプロモーション名';
COMMENT ON COLUMN order_detail.ec_promotion_discount_price IS 'ECプロモーション値引額';
COMMENT ON COLUMN order_detail.ec_campaign_id IS 'ECキャンペーンID';
COMMENT ON COLUMN order_detail.ec_campaign_name IS 'ECキャンペーン名';
COMMENT ON COLUMN order_detail.adjustment_price IS '調整額';
COMMENT ON COLUMN order_detail.keihi_hurikae_target_flg IS '経費振替対象フラグ';
COMMENT ON COLUMN order_detail.member_price_applied_flg IS '会員価格適用フラグ';
COMMENT ON COLUMN order_detail.shipping_charge_target_flg IS '送料計算対象フラグ';
COMMENT ON COLUMN order_detail.regular_contract_no IS '定期契約番号';
COMMENT ON COLUMN order_detail.regular_contract_detail_no IS '定期契約明細番号';
COMMENT ON COLUMN order_detail.regular_kaiji IS '定期回次';
COMMENT ON COLUMN order_detail.regular_check_memo IS '定期チェックメモ';
COMMENT ON COLUMN order_detail.total_commodity_buy_count IS '累計商品購入回数';
COMMENT ON COLUMN order_detail.total_commodity_regular_kaiji IS '累計商品定期回次';
COMMENT ON COLUMN order_detail.regular_total_commodity_regular_kaiji IS '累計商品定期回次（同一定期内）';
COMMENT ON COLUMN order_detail.commodity_category_code IS '商品大分類';
COMMENT ON COLUMN order_detail.total_category_buy_count IS '累計大分類購入回数';
COMMENT ON COLUMN order_detail.total_categoryregular_kaiji IS '累計大分類定期回次';
COMMENT ON COLUMN order_detail.regular_total_categoryregular_kaiji IS '累計大分類定期回次（同一定期内）';
COMMENT ON COLUMN order_detail.commodity_subcategory_code IS '商品中分類';
COMMENT ON COLUMN order_detail.total_subcategory_buy_count IS '累計中分類購入回数';
COMMENT ON COLUMN order_detail.total_subcategoryregular_kaiji IS '累計中分類定期回次';
COMMENT ON COLUMN order_detail.regular_total_subcategoryregular_kaiji IS '累計中分類定期回次（同一定期内）';
COMMENT ON COLUMN order_detail.commodity_subsubcategory_code IS '商品小分類';
COMMENT ON COLUMN order_detail.total_subsubcategory_buy_count IS '累計小分類購入回数';
COMMENT ON COLUMN order_detail.total_subsubcategoryregular_kaiji IS '累計小分類定期回次';
COMMENT ON COLUMN order_detail.regular_total_subsubcategoryregular_kaiji IS '累計小分類定期回次（同一定期内）';
COMMENT ON COLUMN order_detail.grant_plan_point_prod_detail IS '付与予定ポイント数';
COMMENT ON COLUMN order_detail.reduction_plan_point_prod_detail IS '利用予定ポイント数';
COMMENT ON COLUMN order_detail.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_detail.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_detail.created_datetime IS '作成日時';
COMMENT ON COLUMN order_detail.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_detail.updated_datetime IS '更新日時';
COMMENT ON COLUMN order_detail.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN order_detail.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN order_detail.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN order_detail.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN order_detail.d_version IS 'デ連バージョン';

