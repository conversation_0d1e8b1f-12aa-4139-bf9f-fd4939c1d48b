CREATE TABLE order_header (
    order_no VARCHAR(16) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    order_datetime TIMESTAMP(0) NOT NULL,
    customer_code VARCHAR(16) NOT NULL,
    neo_customer_no VARCHAR(12),
    guest_flg NUMERIC(1,0) NOT NULL,
    last_name VARCHAR(20) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(20),
    last_name_kana VARCHAR(40) NOT NULL,
    first_name_kana VARCHAR(40),
    email VARCHAR(256),
    birth_date TIMESTAMP(0) NOT NULL,
    sex NUMERIC(1,0) NOT NULL,
    postal_code VARCHAR(7) NOT NULL,
    prefecture_code VARCHAR(2) NOT NULL,
    address1 VARCHAR(4) NOT NULL,
    address2 VARCHAR(50) NOT NULL,
    address3 VARCHAR(255) NOT NULL,
    address4 VARCHAR(100),
    corporation_post_name VARCHAR(40),
    phone_number VARCHAR(16) NOT NULL,
    advance_later_flg NUMERIC(1,0) NOT NULL,
    payment_method_no NUMERIC(8,0) NOT NULL,
    payment_method_type VARCHAR(2) NOT NULL,
    payment_method_name VARCHAR(25),
    ext_payment_method_type VARCHAR(2) NOT NULL,
    payment_commission NUMERIC(8,0) NOT NULL,
    payment_commission_tax_gr_code VARCHAR(8) NOT NULL,
    payment_commission_tax_no NUMERIC(3,0) NOT NULL,
    payment_commission_tax_rate NUMERIC(3,0) NOT NULL,
    payment_commission_tax NUMERIC(10,2) NOT NULL,
    payment_commission_tax_type NUMERIC(1,0) NOT NULL,
    coupon_management_code VARCHAR(16),
    coupon_code VARCHAR(16),
    coupon_name VARCHAR(50),
    coupon_type NUMERIC(1,0),
    coupon_use_purchase_price NUMERIC(8,0),
    coupon_discount_type NUMERIC(1,0),
    coupon_discount_price NUMERIC(8,0),
    coupon_discount_rate NUMERIC(3,0),
    coupon_used_amount NUMERIC(8,0) NOT NULL,
    coupon_start_datetime TIMESTAMP(0),
    coupon_end_datetime TIMESTAMP(0),
    coupon_kbn VARCHAR(2),
    goods_group VARCHAR(16),
    commodity_category_code VARCHAR(16),
    commodity_series VARCHAR(5),
    coupon_commodity_code_display VARCHAR(20),
    baitai_name VARCHAR(50),
    used_point NUMERIC(8,0),
    total_amount NUMERIC(8,0) NOT NULL,
    ec_promotion_id VARCHAR(256),
    ec_promotion_name VARCHAR(256),
    ec_promotion_discount_price NUMERIC(8,0),
    ec_campaign_id VARCHAR(256),
    ec_campaign_name VARCHAR(256),
    payment_date TIMESTAMP(0),
    payment_limit_date TIMESTAMP(0),
    payment_status NUMERIC(1,0) NOT NULL,
    ext_payment_status VARCHAR(2) NOT NULL,
    customer_group_code VARCHAR(16),
    data_transport_status NUMERIC(1,0) NOT NULL,
    order_status NUMERIC(1,0) NOT NULL,
    ext_order_status VARCHAR(2) NOT NULL,
    tax_reference_date TIMESTAMP(0) NOT NULL,
    cancel_date TIMESTAMP(0),
    client_group VARCHAR(2) NOT NULL,
    caution VARCHAR(200),
    message VARCHAR(200),
    payment_order_id VARCHAR(100),
    cvs_code VARCHAR(2),
    payment_receipt_no VARCHAR(64),
    payment_receipt_url VARCHAR(500),
    receipt_no VARCHAR(20),
    customer_no VARCHAR(50),
    confirm_no VARCHAR(50),
    career_key VARCHAR(1),
    order_create_error_code VARCHAR(8) NOT NULL,
    order_display_status NUMERIC(1,0) NOT NULL,
    order_kind_kbn VARCHAR(1) NOT NULL,
    marketing_channel VARCHAR(2) NOT NULL,
    original_order_no VARCHAR(16),
    external_order_no VARCHAR(50),
    order_recieve_datetime TIMESTAMP(0) NOT NULL,
    order_update_datetime TIMESTAMP(0) NOT NULL,
    order_update_reason_kbn VARCHAR(2),
    cancel_reason_kbn VARCHAR(2),
    uncollectible_date TIMESTAMP(0),
    order_total_price NUMERIC(10,0) NOT NULL,
    account_receivable_balance NUMERIC(10,0) NOT NULL,
    appropriate_amount NUMERIC(10,0) NOT NULL,
    bill_address_kbn VARCHAR(1) NOT NULL,
    receipt_flg NUMERIC(1,0) NOT NULL,
    receipt_to VARCHAR(50),
    receipt_detail VARCHAR(50),
    bill_price NUMERIC(10,0) NOT NULL,
    bill_no VARCHAR(17),
    bill_print_count NUMERIC(3,0) NOT NULL,
    authority_result_kbn VARCHAR(1),
    authority_no VARCHAR(32),
    card_password VARCHAR(32),
    authority_approval_no VARCHAR(10),
    authority_date TIMESTAMP(0),
    authority_price NUMERIC(10,0),
    authority_cancel_approval_no VARCHAR(10),
    authority_cancel_date TIMESTAMP(0),
    credit_payment_no VARCHAR(10),
    credit_payment_date TIMESTAMP(0),
    credit_payment_price NUMERIC(10,0),
    credit_cancel_payment_no VARCHAR(10),
    credit_cancel_payment_date TIMESTAMP(0),
    credit_result_kbn VARCHAR(1),
    card_brand VARCHAR(2),
    credit_card_kanri_no VARCHAR(12),
    credit_card_kanri_detail_no VARCHAR(4),
    credit_card_no VARCHAR(50),
    credit_card_meigi VARCHAR(150),
    credit_card_valid_year VARCHAR(4),
    credit_card_valid_month VARCHAR(2),
    credit_card_pay_count VARCHAR(2),
    payment_bar_code VARCHAR(44),
    amzn_charge_permission_id TEXT,
    amzn_charge_id TEXT,
    amzn_charge_status VARCHAR(2),
    amzn_authorization_datetime TIMESTAMP(0),
    amzn_capture_initiated_datetime TIMESTAMP(0),
    amzn_captured_datetime TIMESTAMP(0),
    amzn_canceled_datetime TIMESTAMP(0),
    order_user_code NUMERIC(38,0),
    order_user VARCHAR(20),
    change_user_code NUMERIC(38,0),
    change_user VARCHAR(20),
    demand_kbn VARCHAR(1) NOT NULL,
    demand1_ref_date TIMESTAMP(0),
    demand1_date TIMESTAMP(0),
    demand1_limit_date TIMESTAMP(0),
    demand1_amount NUMERIC(8,0),
    demand1_bar_code VARCHAR(44),
    demand2_ref_date TIMESTAMP(0),
    demand2_date TIMESTAMP(0),
    demand2_limit_date TIMESTAMP(0),
    demand2_amount NUMERIC(8,0),
    demand2_bar_code VARCHAR(44),
    demand3_ref_date TIMESTAMP(0),
    demand3_date TIMESTAMP(0),
    demand3_limit_date TIMESTAMP(0),
    demand3_amount NUMERIC(8,0),
    demand3_bar_code VARCHAR(44),
    kashidaore_date TIMESTAMP(0),
    demand_exclude_reason_kbn VARCHAR(2),
    demand_exclude_start_date TIMESTAMP(0),
    demand_exclude_end_date TIMESTAMP(0),
    bill_sei_kj VARCHAR(50) NOT NULL,
    bill_mei_kj VARCHAR(50),
    bill_sei_kn VARCHAR(100) NOT NULL,
    bill_mei_kn VARCHAR(100),
    bill_tel_no VARCHAR(16) NOT NULL,
    bill_zipcd VARCHAR(7) NOT NULL,
    bill_addr1 VARCHAR(4) NOT NULL,
    bill_addr2 VARCHAR(50) NOT NULL,
    bill_addr3 VARCHAR(255) NOT NULL,
    bill_addr4 VARCHAR(100),
    bill_corporation_post_name VARCHAR(40),
    nohinsyo_uketsuke_tanto VARCHAR(300),
    grant_plan_point_prod NUMERIC(10,0),
    grant_plan_point_other NUMERIC(10,0),
    grant_plan_point_total NUMERIC(10,0),
    grant_point_prod NUMERIC(10,0),
    grant_point_other NUMERIC(10,0),
    grant_point_total NUMERIC(10,0),
    reduction_plan_point_total NUMERIC(10,0),
    reduction_point_total NUMERIC(10,0),
    subtotal_before_campaign NUMERIC(10,0),
    subtotal_after_campaign NUMERIC(10,0),
    total_before_campaign NUMERIC(10,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no)
);
COMMENT ON TABLE order_header IS '受注ヘッダ';
COMMENT ON COLUMN order_header.order_no IS '受注番号';
COMMENT ON COLUMN order_header.shop_code IS 'ショップコード';
COMMENT ON COLUMN order_header.order_datetime IS '受注日時';
COMMENT ON COLUMN order_header.customer_code IS '顧客コード';
COMMENT ON COLUMN order_header.neo_customer_no IS '顧客番号';
COMMENT ON COLUMN order_header.guest_flg IS 'ゲストフラグ';
COMMENT ON COLUMN order_header.last_name IS '姓';
COMMENT ON COLUMN order_header.first_name IS '名';
COMMENT ON COLUMN order_header.last_name_kana IS '姓かな';
COMMENT ON COLUMN order_header.first_name_kana IS '名かな';
COMMENT ON COLUMN order_header.email IS 'メールアドレス';
COMMENT ON COLUMN order_header.birth_date IS '生年月日';
COMMENT ON COLUMN order_header.sex IS '性別';
COMMENT ON COLUMN order_header.postal_code IS '郵便番号';
COMMENT ON COLUMN order_header.prefecture_code IS '都道府県コード';
COMMENT ON COLUMN order_header.address1 IS '住所1';
COMMENT ON COLUMN order_header.address2 IS '住所2';
COMMENT ON COLUMN order_header.address3 IS '住所3';
COMMENT ON COLUMN order_header.address4 IS '住所4';
COMMENT ON COLUMN order_header.corporation_post_name IS '会社部署名';
COMMENT ON COLUMN order_header.phone_number IS '電話番号';
COMMENT ON COLUMN order_header.advance_later_flg IS '先後払フラグ';
COMMENT ON COLUMN order_header.payment_method_no IS '支払方法番号';
COMMENT ON COLUMN order_header.payment_method_type IS '支払方法区分';
COMMENT ON COLUMN order_header.payment_method_name IS '支払方法名称';
COMMENT ON COLUMN order_header.ext_payment_method_type IS '支払方法区分（拡張）';
COMMENT ON COLUMN order_header.payment_commission IS '支払手数料';
COMMENT ON COLUMN order_header.payment_commission_tax_gr_code IS '支払手数料消費税グループコード';
COMMENT ON COLUMN order_header.payment_commission_tax_no IS '支払手数料消費税番号';
COMMENT ON COLUMN order_header.payment_commission_tax_rate IS '支払手数料消費税率';
COMMENT ON COLUMN order_header.payment_commission_tax IS '支払手数料消費税額';
COMMENT ON COLUMN order_header.payment_commission_tax_type IS '支払手数料消費税区分';
COMMENT ON COLUMN order_header.coupon_management_code IS 'クーポン管理コード';
COMMENT ON COLUMN order_header.coupon_code IS 'クーポンコード';
COMMENT ON COLUMN order_header.coupon_name IS 'クーポン名称';
COMMENT ON COLUMN order_header.coupon_type IS 'クーポン種別';
COMMENT ON COLUMN order_header.coupon_use_purchase_price IS 'クーポン利用最低購入金額';
COMMENT ON COLUMN order_header.coupon_discount_type IS 'クーポン値引区分';
COMMENT ON COLUMN order_header.coupon_discount_price IS 'クーポン値引額';
COMMENT ON COLUMN order_header.coupon_discount_rate IS 'クーポン値引率';
COMMENT ON COLUMN order_header.coupon_used_amount IS 'クーポン利用額';
COMMENT ON COLUMN order_header.coupon_start_datetime IS 'クーポン開始日時';
COMMENT ON COLUMN order_header.coupon_end_datetime IS 'クーポン終了日時';
COMMENT ON COLUMN order_header.coupon_kbn IS 'クーポン区分';
COMMENT ON COLUMN order_header.goods_group IS 'クーポン部門';
COMMENT ON COLUMN order_header.commodity_category_code IS 'クーポン商品大分類';
COMMENT ON COLUMN order_header.commodity_series IS 'クーポン商品シリーズ';
COMMENT ON COLUMN order_header.coupon_commodity_code_display IS 'クーポン商品コード表示値';
COMMENT ON COLUMN order_header.baitai_name IS 'クーポン媒体名称';
COMMENT ON COLUMN order_header.used_point IS '利用ポイント';
COMMENT ON COLUMN order_header.total_amount IS '支払合計金額';
COMMENT ON COLUMN order_header.ec_promotion_id IS 'ECプロモーションID';
COMMENT ON COLUMN order_header.ec_promotion_name IS 'ECプロモーション名';
COMMENT ON COLUMN order_header.ec_promotion_discount_price IS 'ECプロモーション値引額';
COMMENT ON COLUMN order_header.ec_campaign_id IS 'ECキャンペーンID';
COMMENT ON COLUMN order_header.ec_campaign_name IS 'ECキャンペーン名';
COMMENT ON COLUMN order_header.payment_date IS '入金日';
COMMENT ON COLUMN order_header.payment_limit_date IS '支払期限日';
COMMENT ON COLUMN order_header.payment_status IS '入金ステータス';
COMMENT ON COLUMN order_header.ext_payment_status IS '入金ステータス（拡張）';
COMMENT ON COLUMN order_header.customer_group_code IS '顧客グループコード';
COMMENT ON COLUMN order_header.data_transport_status IS 'データ連携ステータス';
COMMENT ON COLUMN order_header.order_status IS '受注ステータス';
COMMENT ON COLUMN order_header.ext_order_status IS '受注ステータス（拡張）';
COMMENT ON COLUMN order_header.tax_reference_date IS '消費税適用基準日';
COMMENT ON COLUMN order_header.cancel_date IS 'キャンセル日';
COMMENT ON COLUMN order_header.client_group IS 'クライアントグループ';
COMMENT ON COLUMN order_header.caution IS '注意事項（管理側のみ参照）';
COMMENT ON COLUMN order_header.message IS '連絡事項';
COMMENT ON COLUMN order_header.payment_order_id IS '決済ID';
COMMENT ON COLUMN order_header.cvs_code IS 'コンビニコード';
COMMENT ON COLUMN order_header.payment_receipt_no IS '承認番号';
COMMENT ON COLUMN order_header.payment_receipt_url IS '払込URL';
COMMENT ON COLUMN order_header.receipt_no IS 'ATM決済収納機関番号';
COMMENT ON COLUMN order_header.customer_no IS 'ATM決済お客様番号';
COMMENT ON COLUMN order_header.confirm_no IS 'ATM決済確認番号';
COMMENT ON COLUMN order_header.career_key IS '携帯キャリア種別区分';
COMMENT ON COLUMN order_header.order_create_error_code IS '受注作成エラーコード';
COMMENT ON COLUMN order_header.order_display_status IS '受注表示区分';
COMMENT ON COLUMN order_header.order_kind_kbn IS '受注種別区分';
COMMENT ON COLUMN order_header.marketing_channel IS '販売経路';
COMMENT ON COLUMN order_header.original_order_no IS '元受注番号';
COMMENT ON COLUMN order_header.external_order_no IS '外部受注番号';
COMMENT ON COLUMN order_header.order_recieve_datetime IS '受注受付日時';
COMMENT ON COLUMN order_header.order_update_datetime IS '受注変更日時';
COMMENT ON COLUMN order_header.order_update_reason_kbn IS '受注変更理由区分';
COMMENT ON COLUMN order_header.cancel_reason_kbn IS 'キャンセル理由区分';
COMMENT ON COLUMN order_header.uncollectible_date IS '回収不能処理日';
COMMENT ON COLUMN order_header.order_total_price IS '注文合計額';
COMMENT ON COLUMN order_header.account_receivable_balance IS '売掛残金';
COMMENT ON COLUMN order_header.appropriate_amount IS '充当金額';
COMMENT ON COLUMN order_header.bill_address_kbn IS '請求先区分';
COMMENT ON COLUMN order_header.receipt_flg IS '領収書フラグ';
COMMENT ON COLUMN order_header.receipt_to IS '領収書宛名';
COMMENT ON COLUMN order_header.receipt_detail IS '領収書但し書き';
COMMENT ON COLUMN order_header.bill_price IS '請求額';
COMMENT ON COLUMN order_header.bill_no IS '請求番号';
COMMENT ON COLUMN order_header.bill_print_count IS '請求書発行回数';
COMMENT ON COLUMN order_header.authority_result_kbn IS 'オーソリ結果';
COMMENT ON COLUMN order_header.authority_no IS 'オーソリ識別番号';
COMMENT ON COLUMN order_header.card_password IS 'カードパスワード';
COMMENT ON COLUMN order_header.authority_approval_no IS 'オーソリ承認番号';
COMMENT ON COLUMN order_header.authority_date IS 'オーソリ処理日';
COMMENT ON COLUMN order_header.authority_price IS 'オーソリ金額';
COMMENT ON COLUMN order_header.authority_cancel_approval_no IS 'オーソリキャンセル承認番号';
COMMENT ON COLUMN order_header.authority_cancel_date IS 'オーソリキャンセル処理日';
COMMENT ON COLUMN order_header.credit_payment_no IS 'クレジット決済承認番号';
COMMENT ON COLUMN order_header.credit_payment_date IS 'クレジット決済処理日';
COMMENT ON COLUMN order_header.credit_payment_price IS 'クレジット決済金額';
COMMENT ON COLUMN order_header.credit_cancel_payment_no IS 'クレジット決済キャンセル承認番号';
COMMENT ON COLUMN order_header.credit_cancel_payment_date IS 'クレジット決済キャンセル処理日';
COMMENT ON COLUMN order_header.credit_result_kbn IS 'クレジット処理結果';
COMMENT ON COLUMN order_header.card_brand IS 'カードブランド';
COMMENT ON COLUMN order_header.credit_card_kanri_no IS 'クレジットカードお預かり管理番号';
COMMENT ON COLUMN order_header.credit_card_kanri_detail_no IS 'クレジットカードお預かり管理明細番号';
COMMENT ON COLUMN order_header.credit_card_no IS 'クレジットカード番号';
COMMENT ON COLUMN order_header.credit_card_meigi IS 'クレジットカード名義人';
COMMENT ON COLUMN order_header.credit_card_valid_year IS 'クレジットカード有効期限年';
COMMENT ON COLUMN order_header.credit_card_valid_month IS 'クレジットカード有効期限月';
COMMENT ON COLUMN order_header.credit_card_pay_count IS 'クレジットカード支払回数';
COMMENT ON COLUMN order_header.payment_bar_code IS '払込票バーコード番号';
COMMENT ON COLUMN order_header.amzn_charge_permission_id IS 'AmazonPay注文ID';
COMMENT ON COLUMN order_header.amzn_charge_id IS 'AmazonPay取引ID';
COMMENT ON COLUMN order_header.amzn_charge_status IS 'AmazonPay取引ステータス';
COMMENT ON COLUMN order_header.amzn_authorization_datetime IS 'AmazonPayオーソリ処理日時';
COMMENT ON COLUMN order_header.amzn_capture_initiated_datetime IS 'AmazonPay売上請求処理日時';
COMMENT ON COLUMN order_header.amzn_captured_datetime IS 'AmazonPay売上請求確定日時';
COMMENT ON COLUMN order_header.amzn_canceled_datetime IS 'AmazonPayキャンセル処理日時';
COMMENT ON COLUMN order_header.order_user_code IS '受注担当ユーザコード';
COMMENT ON COLUMN order_header.order_user IS '受注担当ユーザ名';
COMMENT ON COLUMN order_header.change_user_code IS '入力担当ユーザコード';
COMMENT ON COLUMN order_header.change_user IS '入力担当ユーザ名';
COMMENT ON COLUMN order_header.demand_kbn IS '督促区分';
COMMENT ON COLUMN order_header.demand1_ref_date IS '督促１抽出基準日';
COMMENT ON COLUMN order_header.demand1_date IS '督促１処理日';
COMMENT ON COLUMN order_header.demand1_limit_date IS '督促１支払期限';
COMMENT ON COLUMN order_header.demand1_amount IS '督促１金額';
COMMENT ON COLUMN order_header.demand1_bar_code IS '督促１払込票バーコード番号';
COMMENT ON COLUMN order_header.demand2_ref_date IS '督促２抽出基準日';
COMMENT ON COLUMN order_header.demand2_date IS '督促２処理日';
COMMENT ON COLUMN order_header.demand2_limit_date IS '督促２支払期限';
COMMENT ON COLUMN order_header.demand2_amount IS '督促２金額';
COMMENT ON COLUMN order_header.demand2_bar_code IS '督促２払込票バーコード番号';
COMMENT ON COLUMN order_header.demand3_ref_date IS '督促３抽出基準日';
COMMENT ON COLUMN order_header.demand3_date IS '督促３処理日';
COMMENT ON COLUMN order_header.demand3_limit_date IS '督促３支払期限';
COMMENT ON COLUMN order_header.demand3_amount IS '督促３金額';
COMMENT ON COLUMN order_header.demand3_bar_code IS '督促３払込票バーコード番号';
COMMENT ON COLUMN order_header.kashidaore_date IS '貸倒処理日';
COMMENT ON COLUMN order_header.demand_exclude_reason_kbn IS '督促除外理由区分';
COMMENT ON COLUMN order_header.demand_exclude_start_date IS '督促除外開始日';
COMMENT ON COLUMN order_header.demand_exclude_end_date IS '督促除外終了日';
COMMENT ON COLUMN order_header.bill_sei_kj IS '請求先＿姓（漢字）';
COMMENT ON COLUMN order_header.bill_mei_kj IS '請求先＿名（漢字）';
COMMENT ON COLUMN order_header.bill_sei_kn IS '請求先＿姓（かな）';
COMMENT ON COLUMN order_header.bill_mei_kn IS '請求先＿名（かな）';
COMMENT ON COLUMN order_header.bill_tel_no IS '請求先＿電話番号';
COMMENT ON COLUMN order_header.bill_zipcd IS '請求先＿郵便番号';
COMMENT ON COLUMN order_header.bill_addr1 IS '請求先＿住所１';
COMMENT ON COLUMN order_header.bill_addr2 IS '請求先＿住所２';
COMMENT ON COLUMN order_header.bill_addr3 IS '請求先＿住所３';
COMMENT ON COLUMN order_header.bill_addr4 IS '請求先＿住所４';
COMMENT ON COLUMN order_header.bill_corporation_post_name IS '請求先＿会社部署役職名';
COMMENT ON COLUMN order_header.nohinsyo_uketsuke_tanto IS '納品書受付担当者';
COMMENT ON COLUMN order_header.grant_plan_point_prod IS '付与予定ポイント数（商品）';
COMMENT ON COLUMN order_header.grant_plan_point_other IS '付与予定ポイント数（その他）';
COMMENT ON COLUMN order_header.grant_plan_point_total IS '付与予定ポイント数（合計）';
COMMENT ON COLUMN order_header.grant_point_prod IS '付与ポイント数（商品）';
COMMENT ON COLUMN order_header.grant_point_other IS '付与ポイント数（その他）';
COMMENT ON COLUMN order_header.grant_point_total IS '付与ポイント数（合計）';
COMMENT ON COLUMN order_header.reduction_plan_point_total IS '利用予定ポイント数（合計）';
COMMENT ON COLUMN order_header.reduction_point_total IS '利用ポイント数（合計）';
COMMENT ON COLUMN order_header.subtotal_before_campaign IS 'キャンペーン適用前商品小計額';
COMMENT ON COLUMN order_header.subtotal_after_campaign IS 'キャンペーン適用後商品小計額';
COMMENT ON COLUMN order_header.total_before_campaign IS 'キャンペーン適用前金額';
COMMENT ON COLUMN order_header.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN order_header.created_user IS '作成ユーザ';
COMMENT ON COLUMN order_header.created_datetime IS '作成日時';
COMMENT ON COLUMN order_header.updated_user IS '更新ユーザ';
COMMENT ON COLUMN order_header.updated_datetime IS '更新日時';
COMMENT ON COLUMN order_header.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN order_header.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN order_header.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN order_header.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN order_header.d_version IS 'デ連バージョン';

