CREATE TABLE out_achievements_mail_order (
    core_out_indicate_no VARCHAR(22) NOT NULL,
    box_no VARCHAR(6) NOT NULL,
    slip_no VARCHAR(14) NOT NULL,
    core_system_regist_flg VARCHAR(1),
    regist_datetime TIMESTAMP,
    update_datetime TIMESTAMP,
    stock_io_slip_no VARCHAR(13) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (core_out_indicate_no, box_no)
);
COMMENT ON TABLE out_achievements_mail_order IS '出荷実績（通販）';
COMMENT ON COLUMN out_achievements_mail_order.core_out_indicate_no IS '基幹出荷指示番号';
COMMENT ON COLUMN out_achievements_mail_order.box_no IS '箱連番';
COMMENT ON COLUMN out_achievements_mail_order.slip_no IS '伝票番号';
COMMENT ON COLUMN out_achievements_mail_order.core_system_regist_flg IS '基幹システム登録フラグ';
COMMENT ON COLUMN out_achievements_mail_order.regist_datetime IS '登録日時';
COMMENT ON COLUMN out_achievements_mail_order.update_datetime IS '更新日時';
COMMENT ON COLUMN out_achievements_mail_order.stock_io_slip_no IS '入出庫伝票番号';
COMMENT ON COLUMN out_achievements_mail_order.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN out_achievements_mail_order.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN out_achievements_mail_order.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN out_achievements_mail_order.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN out_achievements_mail_order.d_version IS 'デ連バージョン';

