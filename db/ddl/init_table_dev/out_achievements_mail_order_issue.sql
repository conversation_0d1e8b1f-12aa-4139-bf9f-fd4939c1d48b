CREATE TABLE out_achievements_mail_order_issue (
    wh_code VARCHAR(3) NOT NULL,
    accept_no VARCHAR(22) NOT NULL,
    logimane_slip_no VARCHAR(13) NOT NULL,
    goods_code VARCHAR(10) NOT NULL,
    out_qty NUMERIC(9) NOT NULL,
    close_date VARCHAR(8) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (accept_no, goods_code)
);
COMMENT ON TABLE out_achievements_mail_order_issue IS '出荷実績（通販）_出庫';
COMMENT ON COLUMN out_achievements_mail_order_issue.wh_code IS '倉庫コード';
COMMENT ON COLUMN out_achievements_mail_order_issue.accept_no IS '基幹出荷指示番号';
COMMENT ON COLUMN out_achievements_mail_order_issue.logimane_slip_no IS '入出庫伝票番号';
COMMENT ON COLUMN out_achievements_mail_order_issue.goods_code IS '商品番号';
COMMENT ON COLUMN out_achievements_mail_order_issue.out_qty IS '出庫数';
COMMENT ON COLUMN out_achievements_mail_order_issue.close_date IS '出庫確定日';
COMMENT ON COLUMN out_achievements_mail_order_issue.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN out_achievements_mail_order_issue.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN out_achievements_mail_order_issue.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN out_achievements_mail_order_issue.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN out_achievements_mail_order_issue.d_version IS 'デ連バージョン';

