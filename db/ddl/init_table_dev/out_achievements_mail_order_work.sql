CREATE TABLE out_achievements_mail_order_work (
    core_out_indicate_no VARCHAR(22) NOT NULL,
    box_no VARCHAR(6) NOT NULL,
    slip_no VARCHAR(14) NOT NULL,
    core_system_regist_flg VARCHAR(1),
    regist_datetime TIMESTAMP,
    update_datetime TIMESTAMP,
    stock_io_slip_no VARCHAR(13) NOT NULL,
    PRIMARY KEY (core_out_indicate_no, box_no)
);
COMMENT ON TABLE out_achievements_mail_order_work IS '出荷実績（通販）ワーク';
COMMENT ON COLUMN out_achievements_mail_order_work.core_out_indicate_no IS '基幹出荷指示番号';
COMMENT ON COLUMN out_achievements_mail_order_work.box_no IS '箱連番';
COMMENT ON COLUMN out_achievements_mail_order_work.slip_no IS '伝票番号';
COMMENT ON COLUMN out_achievements_mail_order_work.core_system_regist_flg IS '基幹システム登録フラグ';
COMMENT ON COLUMN out_achievements_mail_order_work.regist_datetime IS '登録日時';
COMMENT ON COLUMN out_achievements_mail_order_work.update_datetime IS '更新日時';
COMMENT ON COLUMN out_achievements_mail_order_work.stock_io_slip_no IS '入出庫伝票番号';

