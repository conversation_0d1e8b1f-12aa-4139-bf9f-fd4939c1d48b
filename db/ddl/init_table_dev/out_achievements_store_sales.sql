CREATE TABLE out_achievements_store_sales (
    wh_code VARCHAR(4) NOT NULL,
    accept_no VARCHAR(8) NOT NULL,
    logimane_slip_no VARCHAR(6) NOT NULL,
    sh_control_number VARCHAR(6) NOT NULL,
    out_qty NUMERIC(10,0) NOT NULL,
    close_date TIMESTAMP NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (wh_code, accept_no)
);
COMMENT ON TABLE out_achievements_store_sales IS '出荷実績（直営）';
COMMENT ON COLUMN out_achievements_store_sales.wh_code IS '倉庫コード';
COMMENT ON COLUMN out_achievements_store_sales.accept_no IS '受付番号';
COMMENT ON COLUMN out_achievements_store_sales.logimane_slip_no IS '出荷実績番号';
COMMENT ON COLUMN out_achievements_store_sales.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN out_achievements_store_sales.out_qty IS '出庫数';
COMMENT ON COLUMN out_achievements_store_sales.close_date IS '出庫日';
COMMENT ON COLUMN out_achievements_store_sales.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN out_achievements_store_sales.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN out_achievements_store_sales.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN out_achievements_store_sales.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN out_achievements_store_sales.d_version IS 'デ連バージョン';

