CREATE TABLE out_achievements_store_sales_work (
    wh_code VARCHAR(4) NOT NULL,
    accept_no VARCHAR(8) NOT NULL,
    logimane_slip_no VARCHAR(6) NOT NULL,
    sh_control_number VARCHAR(6) NOT NULL,
    out_qty NUMERIC(10,0) NOT NULL,
    close_date TIMESTAMP NOT NULL,
    PRIMARY KEY (wh_code, accept_no)
);
COMMENT ON TABLE out_achievements_store_sales_work IS '出荷実績（直営）ワーク';
COMMENT ON COLUMN out_achievements_store_sales_work.wh_code IS '倉庫コード';
COMMENT ON COLUMN out_achievements_store_sales_work.accept_no IS '受付番号';
COMMENT ON COLUMN out_achievements_store_sales_work.logimane_slip_no IS '出荷実績番号';
COMMENT ON COLUMN out_achievements_store_sales_work.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN out_achievements_store_sales_work.out_qty IS '出庫数';
COMMENT ON COLUMN out_achievements_store_sales_work.close_date IS '出庫日';

