CREATE TABLE out_indicate_detail (
    accept_no VARCHAR(22) NOT NULL,
    seq NUMERIC(5,0) NOT NULL,
    prod_no NUMERIC(10,0) NOT NULL,
    qty NUMERIC(7,0) NOT NULL,
    chit_print_date TIMESTAMP,
    yamato_bar_code VARCHAR(14),
    gyosha_flg VARCHAR(1),
    invoice_branch_number VARCHAR(1),
    bumon_kbn VARCHAR(1),
    prod_price NUMERIC(6,0),
    order_type VARCHAR(1),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (accept_no, seq)
);
COMMENT ON TABLE out_indicate_detail IS '出荷指示明細';
COMMENT ON COLUMN out_indicate_detail.accept_no IS '受付番号';
COMMENT ON COLUMN out_indicate_detail.seq IS '連番';
COMMENT ON COLUMN out_indicate_detail.prod_no IS '商品コード';
COMMENT ON COLUMN out_indicate_detail.qty IS '数量';
COMMENT ON COLUMN out_indicate_detail.chit_print_date IS '伝票出力日';
COMMENT ON COLUMN out_indicate_detail.yamato_bar_code IS 'バーコード';
COMMENT ON COLUMN out_indicate_detail.gyosha_flg IS '業者フラグ';
COMMENT ON COLUMN out_indicate_detail.invoice_branch_number IS '送り状ブランチNO.';
COMMENT ON COLUMN out_indicate_detail.bumon_kbn IS '部門区分';
COMMENT ON COLUMN out_indicate_detail.prod_price IS '商品金額';
COMMENT ON COLUMN out_indicate_detail.order_type IS '注文区分';
COMMENT ON COLUMN out_indicate_detail.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN out_indicate_detail.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN out_indicate_detail.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN out_indicate_detail.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN out_indicate_detail.d_version IS 'デ連バージョン';

