CREATE TABLE out_indicate_detail_work (
    accept_no VARCHAR(22) NOT NULL,
    seq NUMERIC(5,0) NOT NULL,
    prod_no NUMERIC(10,0) NOT NULL,
    qty NUMERIC(7,0) NOT NULL,
    chit_print_date TIMESTAMP,
    yamato_bar_code VARCHAR(14),
    gyosha_flg VARCHAR(1),
    invoice_branch_number VARCHAR(1),
    bumon_kbn VARCHAR(1),
    prod_price NUMERIC(6,0),
    order_type VARCHAR(1),
    PRIMARY KEY (accept_no, seq)
);
COMMENT ON TABLE out_indicate_detail_work IS '出荷指示明細ワーク';
COMMENT ON COLUMN out_indicate_detail_work.accept_no IS '受付番号';
COMMENT ON COLUMN out_indicate_detail_work.seq IS '連番';
COMMENT ON COLUMN out_indicate_detail_work.prod_no IS '商品コード';
COMMENT ON COLUMN out_indicate_detail_work.qty IS '数量';
COMMENT ON COLUMN out_indicate_detail_work.chit_print_date IS '伝票出力日';
COMMENT ON COLUMN out_indicate_detail_work.yamato_bar_code IS 'バーコード';
COMMENT ON COLUMN out_indicate_detail_work.gyosha_flg IS '業者フラグ';
COMMENT ON COLUMN out_indicate_detail_work.invoice_branch_number IS '送り状ブランチNO.';
COMMENT ON COLUMN out_indicate_detail_work.bumon_kbn IS '部門区分';
COMMENT ON COLUMN out_indicate_detail_work.prod_price IS '商品金額';
COMMENT ON COLUMN out_indicate_detail_work.order_type IS '注文区分';

