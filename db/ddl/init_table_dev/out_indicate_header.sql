CREATE TABLE out_indicate_header (
    accept_no VARCHAR(22) NOT NULL,
    cust_no VARCHAR(10),
    record_no VARCHAR(6),
    cust_name VARCHAR(100),
    post_no VARCHAR(8),
    addr1 VARCHAR(50),
    addr2 VARCHAR(50),
    addr3 VARCHAR(50),
    tel_no VARCHAR(15),
    prefecture_code NUMERIC(2, 0),
    cust_flg NUMERIC(1, 0),
    order_date TIMESTAMP,
    pay_kb VARCHAR(2),
    total_price NUMERIC(10, 0),
    delive_cust_name VARCHAR(100),
    delive_post_no VARCHAR(8),
    delive_addr1 VARCHAR(50),
    delive_addr2 VARCHAR(50),
    delive_addr3 VARCHAR(50),
    delive_tel_no VARCHAR(15),
    gift_flg VARCHAR(1),
    kibou_ymd TIMESTAMP,
    night_flg VARCHAR(2),
    cosme_price NUMERIC(10, 0),
    health_price NUMERIC(10, 0),
    inner_price NUMERIC(10, 0),
    update_date TIMESTAMP,
    chit_print_date TIMESTAMP,
    yamato_bar_code VARCHAR(14),
    gyosha_flg VARCHAR(1),
    status_flg VARCHAR(1),
    slip_ono VARCHAR(20),
    order_no VARCHAR(14),
    clinic_name VARCHAR(100),
    shipment_date TIMESTAMP,
    shipment_plan_date TIMESTAMP,
    pack_cnt NUMERIC(10),
    store_code VARCHAR(8),
    period_flg VARCHAR(2),
    delivery_box_gb VARCHAR(2),
    air_delivery_yn VARCHAR(1),
    tax_amt NUMERIC(10, 0),
    conveni_yn VARCHAR(1),
    pudo_yn VARCHAR(1),
    inplan_yn VARCHAR(1),
    over_stock_yn VARCHAR(1),
    reserve_order_yn VARCHAR(1),
    gift_rapping_yn VARCHAR(1),
    kanshi_yn VARCHAR(1),
    fusoku_yn VARCHAR(1),
    airplane_yn VARCHAR(1),
    satofuru_yn VARCHAR(1),
    tokusha_yn VARCHAR(1),
    rakugaki_yn VARCHAR(1),
    multi_sample_yn VARCHAR(1),
    slip_size_code VARCHAR(1),
    wh_code VARCHAR(3),
    agent_cd VARCHAR(13),
    import_yn VARCHAR(1),
    import_date TIMESTAMP,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8, 0) NOT NULL,
    PRIMARY KEY (accept_no)
);
COMMENT ON TABLE out_indicate_header IS '出荷指示ヘッダ';
COMMENT ON COLUMN out_indicate_header.accept_no IS '受付番号';
COMMENT ON COLUMN out_indicate_header.cust_no IS '顧客番号';
COMMENT ON COLUMN out_indicate_header.record_no IS 'レコード番号';
COMMENT ON COLUMN out_indicate_header.cust_name IS '顧客名';
COMMENT ON COLUMN out_indicate_header.post_no IS '郵便番号';
COMMENT ON COLUMN out_indicate_header.addr1 IS '顧客住所１';
COMMENT ON COLUMN out_indicate_header.addr2 IS '顧客住所２';
COMMENT ON COLUMN out_indicate_header.addr3 IS '顧客住所３';
COMMENT ON COLUMN out_indicate_header.tel_no IS '電話番号';
COMMENT ON COLUMN out_indicate_header.prefecture_code IS '都道府県コード';
COMMENT ON COLUMN out_indicate_header.cust_flg IS '顧客フラグ';
COMMENT ON COLUMN out_indicate_header.order_date IS '注文日';
COMMENT ON COLUMN out_indicate_header.pay_kb IS '支払区分';
COMMENT ON COLUMN out_indicate_header.total_price IS '合計金額';
COMMENT ON COLUMN out_indicate_header.delive_cust_name IS '配達先顧客名';
COMMENT ON COLUMN out_indicate_header.delive_post_no IS '配達先郵便番号';
COMMENT ON COLUMN out_indicate_header.delive_addr1 IS '配達先住所１';
COMMENT ON COLUMN out_indicate_header.delive_addr2 IS '配達先住所２';
COMMENT ON COLUMN out_indicate_header.delive_addr3 IS '配達先住所３';
COMMENT ON COLUMN out_indicate_header.delive_tel_no IS '配達先電話番号';
COMMENT ON COLUMN out_indicate_header.gift_flg IS 'ギフトフラグ';
COMMENT ON COLUMN out_indicate_header.kibou_ymd IS '配達希望日';
COMMENT ON COLUMN out_indicate_header.night_flg IS '夜間可否';
COMMENT ON COLUMN out_indicate_header.cosme_price IS '化粧品金額';
COMMENT ON COLUMN out_indicate_header.health_price IS '健康食品金額';
COMMENT ON COLUMN out_indicate_header.inner_price IS 'インナーウェア金額';
COMMENT ON COLUMN out_indicate_header.update_date IS '更新日時';
COMMENT ON COLUMN out_indicate_header.chit_print_date IS '伝票出力日';
COMMENT ON COLUMN out_indicate_header.yamato_bar_code IS 'バーコード';
COMMENT ON COLUMN out_indicate_header.gyosha_flg IS '業者フラグ';
COMMENT ON COLUMN out_indicate_header.status_flg IS 'ステータスフラグ';
COMMENT ON COLUMN out_indicate_header.slip_ono IS '出庫伝票番号';
COMMENT ON COLUMN out_indicate_header.order_no IS '注文番号';
COMMENT ON COLUMN out_indicate_header.clinic_name IS '病院名';
COMMENT ON COLUMN out_indicate_header.shipment_date IS '出荷日';
COMMENT ON COLUMN out_indicate_header.shipment_plan_date IS '出荷予定日';
COMMENT ON COLUMN out_indicate_header.pack_cnt IS '包装数';
COMMENT ON COLUMN out_indicate_header.store_code IS '着店コード';
COMMENT ON COLUMN out_indicate_header.period_flg IS '定期フラグ';
COMMENT ON COLUMN out_indicate_header.delivery_box_gb IS '配達BOX区分';
COMMENT ON COLUMN out_indicate_header.air_delivery_yn IS '航空輸送可否';
COMMENT ON COLUMN out_indicate_header.tax_amt IS '消費税';
COMMENT ON COLUMN out_indicate_header.conveni_yn IS 'コンビニ受取可否';
COMMENT ON COLUMN out_indicate_header.pudo_yn IS 'PUDO利用可否';
COMMENT ON COLUMN out_indicate_header.inplan_yn IS '入庫予定可否';
COMMENT ON COLUMN out_indicate_header.over_stock_yn IS '過受注可否';
COMMENT ON COLUMN out_indicate_header.reserve_order_yn IS '予約受注可否';
COMMENT ON COLUMN out_indicate_header.gift_rapping_yn IS 'ギフトラッピング可否';
COMMENT ON COLUMN out_indicate_header.kanshi_yn IS '注文監視可否';
COMMENT ON COLUMN out_indicate_header.fusoku_yn IS '不足可否';
COMMENT ON COLUMN out_indicate_header.airplane_yn IS '航空便不可可否';
COMMENT ON COLUMN out_indicate_header.satofuru_yn IS 'さとふる可否';
COMMENT ON COLUMN out_indicate_header.tokusha_yn IS '特別社販可否';
COMMENT ON COLUMN out_indicate_header.rakugaki_yn IS 'らくがき板可否';
COMMENT ON COLUMN out_indicate_header.multi_sample_yn IS '複数サンプル可否';
COMMENT ON COLUMN out_indicate_header.slip_size_code IS '伝票サイズコード';
COMMENT ON COLUMN out_indicate_header.wh_code IS '倉庫コード';
COMMENT ON COLUMN out_indicate_header.agent_cd IS '取扱店ＣＤ';
COMMENT ON COLUMN out_indicate_header.import_yn IS '取込可否';
COMMENT ON COLUMN out_indicate_header.import_date IS '取込日';
COMMENT ON COLUMN out_indicate_header.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN out_indicate_header.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN out_indicate_header.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN out_indicate_header.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN out_indicate_header.d_version IS 'デ連バージョン';