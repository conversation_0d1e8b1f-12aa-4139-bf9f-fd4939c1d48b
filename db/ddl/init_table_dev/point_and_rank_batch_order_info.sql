CREATE TABLE point_and_rank_batch_order_info (
    order_no VARCHAR(255) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (order_no)
);
COMMENT ON TABLE point_and_rank_batch_order_info IS 'ポイント付与・ランク計算用注文情報';
COMMENT ON COLUMN point_and_rank_batch_order_info.order_no IS '注文番号';
COMMENT ON COLUMN point_and_rank_batch_order_info.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN point_and_rank_batch_order_info.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN point_and_rank_batch_order_info.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN point_and_rank_batch_order_info.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN point_and_rank_batch_order_info.d_version IS 'デ連バージョン';

