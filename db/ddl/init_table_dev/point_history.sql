CREATE TABLE point_history (
    tran_timestamp VARCHAR(13) NOT NULL,
    external_customer_id VARCHAR(33) NOT NULL,
    trade_type VARCHAR(6) NOT NULL,
    reason VARCHAR(8) NOT NULL,
    point VARCHAR(30) NOT NULL,
    point_amount VARCHAR(30) NOT NULL,
    point_expiration_timestamp VARCHAR(13),
    tran_id VARCHAR(18) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (tran_id)
);
COMMENT ON TABLE point_history IS 'ポイント履歴';
COMMENT ON COLUMN point_history.tran_timestamp IS '取引日時				';
COMMENT ON COLUMN point_history.external_customer_id IS 'LINEユーザーID';
COMMENT ON COLUMN point_history.trade_type IS '区分';
COMMENT ON COLUMN point_history.reason IS 'ポイント付与・利用理由';
COMMENT ON COLUMN point_history.point IS 'ポイント';
COMMENT ON COLUMN point_history.point_amount IS '付与後ポイント数';
COMMENT ON COLUMN point_history.point_expiration_timestamp IS '付与後ポイント有効期限';
COMMENT ON COLUMN point_history.tran_id IS '取引ID';
COMMENT ON COLUMN point_history.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN point_history.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN point_history.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN point_history.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN point_history.d_version IS 'デ連バージョン';

