CREATE TABLE point_history_work (
    tran_timestamp VARCHAR(13) NOT NULL,
    external_customer_id VARCHAR(33) NOT NULL,
    trade_type VARCHAR(6) NOT NULL,
    reason VARCHAR(8) NOT NULL,
    point VARCHAR(30) NOT NULL,
    point_amount VARCHAR(30) NOT NULL,
    point_expiration_timestamp VARCHAR(13),
    tran_id VARCHAR(18) NOT NULL,
    PRIMARY KEY (tran_id)
);
COMMENT ON TABLE point_history_work IS 'ポイント履歴ワーク';
COMMENT ON COLUMN point_history_work.tran_timestamp IS '取引日時				';
COMMENT ON COLUMN point_history_work.external_customer_id IS 'LINEユーザーID';
COMMENT ON COLUMN point_history_work.trade_type IS '区分';
COMMENT ON COLUMN point_history_work.reason IS 'ポイント付与・利用理由';
COMMENT ON COLUMN point_history_work.point IS 'ポイント';
COMMENT ON COLUMN point_history_work.point_amount IS '付与後ポイント数';
COMMENT ON COLUMN point_history_work.point_expiration_timestamp IS '付与後ポイント有効期限';
COMMENT ON COLUMN point_history_work.tran_id IS '取引ID';

