CREATE TABLE regular_sale_composition (
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_code VARCHAR(16) NOT NULL,
    regular_sale_composition_no VARCHAR(8) NOT NULL,
    regular_sale_composition_name VARCHAR(50),
    regular_order_count_min_limit NUMERIC(5,0) NOT NULL,
    regular_order_count_max_limit NUMERIC(5,0),
    regular_order_count_interval NUMERIC(5,0) NOT NULL,
    retail_price NUMERIC(8,0) NOT NULL,
    regular_sale_commodity_point NUMERIC(3,0),
    display_order NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, regular_sale_code, regular_sale_composition_no)
);
COMMENT ON TABLE regular_sale_composition IS '定期便構成情報';
COMMENT ON COLUMN regular_sale_composition.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_composition.regular_sale_code IS '定期便コード';
COMMENT ON COLUMN regular_sale_composition.regular_sale_composition_no IS '定期便構成グループコード';
COMMENT ON COLUMN regular_sale_composition.regular_sale_composition_name IS '定期便構成名称';
COMMENT ON COLUMN regular_sale_composition.regular_order_count_min_limit IS '定期回次下限';
COMMENT ON COLUMN regular_sale_composition.regular_order_count_max_limit IS '定期回次上限';
COMMENT ON COLUMN regular_sale_composition.regular_order_count_interval IS '定期回次間隔';
COMMENT ON COLUMN regular_sale_composition.retail_price IS '販売価格';
COMMENT ON COLUMN regular_sale_composition.regular_sale_commodity_point IS '定期便商品構成ポイント';
COMMENT ON COLUMN regular_sale_composition.display_order IS '表示順';
COMMENT ON COLUMN regular_sale_composition.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_composition.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_composition.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_composition.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_composition.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_composition.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN regular_sale_composition.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN regular_sale_composition.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN regular_sale_composition.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN regular_sale_composition.d_version IS 'デ連バージョン';

