CREATE TABLE regular_sale_cont_header (
    regular_contract_no VARCHAR(14) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_cont_datetime TIMESTAMP(0) NOT NULL,
    customer_code VARCHAR(16) NOT NULL,
    neo_customer_no VARCHAR(12),
    payment_method_no NUMERIC(8,0) NOT NULL,
    address_no NUMERIC(8,0),
    regular_sale_cont_status NUMERIC(1,0) NOT NULL,
    next_delivery_request_date TIMESTAMP(0),
    external_order_no VARCHAR(50),
    order_user_code NUMERIC(38,0),
    regular_update_datetime TIMESTAMP(0),
    change_user_code NUMERIC(38,0),
    regular_update_reason_kbn VARCHAR(2),
    otodoke_hope_time_kbn VARCHAR(2),
    marketing_channel VARCHAR(2),
    delivery_type_no NUMERIC(8,0),
    shipping_method_flg NUMERIC(1,0) NOT NULL,
    ext_payment_method_type VARCHAR(2) NOT NULL,
    card_brand VARCHAR(2),
    credit_card_kanri_no VARCHAR(12),
    credit_card_kanri_detail_no VARCHAR(4),
    credit_card_no VARCHAR(50),
    credit_card_meigi VARCHAR(150),
    credit_card_valid_year VARCHAR(4),
    credit_card_valid_month VARCHAR(2),
    credit_card_pay_count VARCHAR(2),
    amzn_charge_permission_id TEXT,
    bill_address_kbn VARCHAR(1) NOT NULL,
    bill_print_otodoke_id VARCHAR(10),
    o_name_disp_kbn VARCHAR(1) NOT NULL,
    delivery_note_flg NUMERIC(1,0) NOT NULL,
    include_flg NUMERIC(1,0) NOT NULL,
    receipt_flg NUMERIC(1,0) NOT NULL,
    receipt_to VARCHAR(50),
    receipt_detail VARCHAR(50),
    first_shipping_date TIMESTAMP(0),
    lastest_shipping_date TIMESTAMP(0),
    first_delivery_date TIMESTAMP(0),
    lastest_delivery_date TIMESTAMP(0),
    regular_stop_date TIMESTAMP(0),
    regular_stop_reason_kbn VARCHAR(2),
    regular_hold_date TIMESTAMP(0),
    regular_hold_clear_date TIMESTAMP(0),
    regular_kaiji NUMERIC(5,0) NOT NULL,
    shipped_regular_count NUMERIC(3,0) NOT NULL,
    delivery_memo VARCHAR(20),
    regular_hold_reason_kbn VARCHAR(2),
    niyose_flg NUMERIC(1,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (regular_contract_no)
);
COMMENT ON TABLE regular_sale_cont_header IS '定期契約ヘッダ';
COMMENT ON COLUMN regular_sale_cont_header.regular_contract_no IS '定期契約番号';
COMMENT ON COLUMN regular_sale_cont_header.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_cont_header.regular_sale_cont_datetime IS '定期契約日時';
COMMENT ON COLUMN regular_sale_cont_header.customer_code IS '顧客コード';
COMMENT ON COLUMN regular_sale_cont_header.neo_customer_no IS '顧客番号';
COMMENT ON COLUMN regular_sale_cont_header.payment_method_no IS '支払方法番号';
COMMENT ON COLUMN regular_sale_cont_header.address_no IS 'アドレス帳番号';
COMMENT ON COLUMN regular_sale_cont_header.regular_sale_cont_status IS '定期ステータス';
COMMENT ON COLUMN regular_sale_cont_header.next_delivery_request_date IS '次回お届け希望日';
COMMENT ON COLUMN regular_sale_cont_header.external_order_no IS '外部受注番号';
COMMENT ON COLUMN regular_sale_cont_header.order_user_code IS '受注担当ユーザコード';
COMMENT ON COLUMN regular_sale_cont_header.regular_update_datetime IS '定期変更日時';
COMMENT ON COLUMN regular_sale_cont_header.change_user_code IS '入力担当ユーザコード';
COMMENT ON COLUMN regular_sale_cont_header.regular_update_reason_kbn IS '定期変更理由';
COMMENT ON COLUMN regular_sale_cont_header.otodoke_hope_time_kbn IS 'お届け希望時間帯';
COMMENT ON COLUMN regular_sale_cont_header.marketing_channel IS '販売経路';
COMMENT ON COLUMN regular_sale_cont_header.delivery_type_no IS '配送種別番号';
COMMENT ON COLUMN regular_sale_cont_header.shipping_method_flg IS '配送方法顧客指定フラグ';
COMMENT ON COLUMN regular_sale_cont_header.ext_payment_method_type IS '支払方法区分（拡張）';
COMMENT ON COLUMN regular_sale_cont_header.card_brand IS 'カードブランド';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_kanri_no IS 'クレジットカードお預かり管理番号';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_kanri_detail_no IS 'クレジットカードお預かり管理明細番号';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_no IS 'クレジットカード番号';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_meigi IS 'クレジットカード名義人';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_valid_year IS 'クレジットカード有効期限年';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_valid_month IS 'クレジットカード有効期限月';
COMMENT ON COLUMN regular_sale_cont_header.credit_card_pay_count IS 'クレジットカード支払回数';
COMMENT ON COLUMN regular_sale_cont_header.amzn_charge_permission_id IS 'AmazonPay注文ID';
COMMENT ON COLUMN regular_sale_cont_header.bill_address_kbn IS '請求先区分';
COMMENT ON COLUMN regular_sale_cont_header.bill_print_otodoke_id IS '請求お届け先ＩＤ';
COMMENT ON COLUMN regular_sale_cont_header.o_name_disp_kbn IS '依頼主＿名前表示区分';
COMMENT ON COLUMN regular_sale_cont_header.delivery_note_flg IS '納品書不要フラグ';
COMMENT ON COLUMN regular_sale_cont_header.include_flg IS '同梱不要フラグ';
COMMENT ON COLUMN regular_sale_cont_header.receipt_flg IS '領収書フラグ';
COMMENT ON COLUMN regular_sale_cont_header.receipt_to IS '領収書宛名';
COMMENT ON COLUMN regular_sale_cont_header.receipt_detail IS '領収書但し書き';
COMMENT ON COLUMN regular_sale_cont_header.first_shipping_date IS '初回発送日';
COMMENT ON COLUMN regular_sale_cont_header.lastest_shipping_date IS '最新発送日';
COMMENT ON COLUMN regular_sale_cont_header.first_delivery_date IS '初回お届け日';
COMMENT ON COLUMN regular_sale_cont_header.lastest_delivery_date IS '最新お届け日';
COMMENT ON COLUMN regular_sale_cont_header.regular_stop_date IS '定期休止日';
COMMENT ON COLUMN regular_sale_cont_header.regular_stop_reason_kbn IS '定期休止理由区分';
COMMENT ON COLUMN regular_sale_cont_header.regular_hold_date IS '定期保留日';
COMMENT ON COLUMN regular_sale_cont_header.regular_hold_clear_date IS '定期保留解除日';
COMMENT ON COLUMN regular_sale_cont_header.regular_kaiji IS '定期回次';
COMMENT ON COLUMN regular_sale_cont_header.shipped_regular_count IS '発送済定期回数';
COMMENT ON COLUMN regular_sale_cont_header.delivery_memo IS '配送メモ';
COMMENT ON COLUMN regular_sale_cont_header.regular_hold_reason_kbn IS '定期保留理由区分';
COMMENT ON COLUMN regular_sale_cont_header.niyose_flg IS '荷寄せ可能フラグ';
COMMENT ON COLUMN regular_sale_cont_header.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_cont_header.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_cont_header.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_cont_header.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_cont_header.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_cont_header.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN regular_sale_cont_header.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN regular_sale_cont_header.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN regular_sale_cont_header.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN regular_sale_cont_header.d_version IS 'デ連バージョン';

