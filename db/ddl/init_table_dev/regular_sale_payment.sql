CREATE TABLE regular_sale_payment (
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_code VARCHAR(16) NOT NULL,
    payment_method_no NUMERIC(8,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, regular_sale_code, payment_method_no)
);
COMMENT ON TABLE regular_sale_payment IS '定期便支払方法';
COMMENT ON COLUMN regular_sale_payment.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_payment.regular_sale_code IS '定期便コード';
COMMENT ON COLUMN regular_sale_payment.payment_method_no IS '支払方法番号';
COMMENT ON COLUMN regular_sale_payment.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_payment.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_payment.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_payment.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_payment.updated_datetime IS '更新日時';
COMMENT ON COLUMN regular_sale_payment.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN regular_sale_payment.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN regular_sale_payment.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN regular_sale_payment.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN regular_sale_payment.d_version IS 'デ連バージョン';

