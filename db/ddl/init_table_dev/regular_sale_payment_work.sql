CREATE TABLE regular_sale_payment_work (
    shop_code VARCHAR(16) NOT NULL,
    regular_sale_code VARCHAR(16) NOT NULL,
    payment_method_no NUMERIC(8,0) NOT NULL,
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (shop_code, regular_sale_code, payment_method_no)
);
COMMENT ON TABLE regular_sale_payment_work IS '定期便支払方法ワーク';
COMMENT ON COLUMN regular_sale_payment_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN regular_sale_payment_work.regular_sale_code IS '定期便コード';
COMMENT ON COLUMN regular_sale_payment_work.payment_method_no IS '支払方法番号';
COMMENT ON COLUMN regular_sale_payment_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN regular_sale_payment_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN regular_sale_payment_work.created_datetime IS '作成日時';
COMMENT ON COLUMN regular_sale_payment_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN regular_sale_payment_work.updated_datetime IS '更新日時';

