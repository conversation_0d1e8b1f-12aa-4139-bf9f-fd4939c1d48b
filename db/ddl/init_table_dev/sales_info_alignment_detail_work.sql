CREATE TABLE sales_info_alignment_detail_work (
    shop_cd VARCHAR(12),
    register_num NUMERIC(11,0),
    business_date TIMESTAMP,
    receipt_num NUMERIC(11,0),
    line_num NUMERIC(11,0),
    line_kind NUMERIC(4,0),
    is_in_store_marking NUMERIC(4,0),
    brand_cd VARCHAR(5),
    dept_cd VARCHAR(4),
    class_cd VARCHAR(5),
    sub_class_cd VARCHAR(5),
    item_cd VARCHAR(30),
    item_name VARCHAR(90),
    dept_group_cd VARCHAR(4),
    parent_item_cd VARCHAR(30),
    jan_cd VARCHAR(20),
    item_num VARCHAR(20),
    color_cd VARCHAR(5),
    size_cd VARCHAR(5),
    year_cd VARCHAR(4),
    season_cd VARCHAR(4),
    attribute_cd1 VARCHAR(4),
    attribute_cd2 VARCHAR(4),
    attribute_cd3 VARCHAR(4),
    attribute_cd4 VARCHAR(4),
    attribute_cd5 VARCHAR(4),
    scan_bar_cd1 VARCHAR(20),
    scan_bar_cd2 VARCHAR(20),
    discount_cd VARCHAR(6),
    bmplan_cd VARCHAR(11),
    coupon_num VARCHAR(10),
    is_include_sales NUMERIC(4,0),
    item_kind NUMERIC(4,0),
    tax_kind NUMERIC(4,0),
    tax_rate NUMERIC(4,1),
    is_reduced_tax_rate NUMERIC(4,0),
    is_proper_item NUMERIC(4,0),
    is_change_price NUMERIC(4,0),
    master_unit_price NUMERIC(11,0),
    body_price NUMERIC(11,0),
    fixed_price NUMERIC(11,0),
    unit_price NUMERIC(11,0),
    quantity NUMERIC(11,0),
    amount NUMERIC(11,0),
    tax_inclusive NUMERIC(11,0),
    tax_outside NUMERIC(11,0),
    tax NUMERIC(11,0),
    disc_rate NUMERIC(4,0),
    line_minus_amount NUMERIC(11,0),
    line_minus_tax_inclusive NUMERIC(11,0),
    bmset_minus_amount NUMERIC(11,0),
    bmset_minus_tax_inclusive NUMERIC(11,0),
    point_minus_amount NUMERIC(11,0),
    point_minus_tax_inclusive NUMERIC(11,0),
    coupon_minus_amount NUMERIC(11,0),
    coupon_minus_tax_inclusive NUMERIC(11,0),
    sub_total_minus_amount NUMERIC(11,0),
    sub_total_minus_tax_inclusive NUMERIC(11,0),
    before_disc_tax_inclusive NUMERIC(11,0),
    sales_man_cd VARCHAR(10),
    stock_kind NUMERIC(4,0),
    is_inventory_counted NUMERIC(4,0),
    is_promotion_ticket NUMERIC(4,0),
    promotion_ticket_bar_cd VARCHAR(32),
    is_promotion_ticket_allow_combination NUMERIC(4,0),
    oes_item_flag NUMERIC(4,0),
    oes_slip_no VARCHAR(6),
    oes_slip_sub_no VARCHAR(2),
    grand_classification NUMERIC(4,0),
    menu_classification VARCHAR(1),
    oes_line_num NUMERIC(11,0),
    oes_quantity NUMERIC(11,0),
    oes_minus NUMERIC(11,0),
    pos_minus NUMERIC(11,0),
    takeout_flag NUMERIC(4,0),
    service_charge_minus_amount NUMERIC(11,0),
    service_charge_minus_tax_inclusive NUMERIC(11,0),
    service_charge_flag NUMERIC(4,0),
    service_charge1_flag NUMERIC(4,0),
    service_charge2_flag NUMERIC(4,0),
    service_charge1_manually_flag NUMERIC(4,0),
    service_charge2_manually_flag NUMERIC(4,0),
    oes_service_charge1 NUMERIC(4,0),
    oes_service_charge2 NUMERIC(4,0),
    cooking_directions_time NUMERIC(11,0),
    cooking_complete_time NUMERIC(11,0),
    offer_complete_time NUMERIC(11,0),
    acceptance_time TIMESTAMP,
    menu_cook_cmp_time TIMESTAMP,
    menu_offer_cmp_time TIMESTAMP,
    disc_amount_limit NUMERIC(11,0),
    is_follow_disc NUMERIC(4,0),
    is_allow_credit NUMERIC(4,0),
    is_employee_acnt_recv NUMERIC(4,0),
    employee_cd VARCHAR(8),
    sub_menu_kind VARCHAR(1),
    grand_menu_code VARCHAR(20),
    grand_menu_index NUMERIC(11,0),
    select_kind VARCHAR(1),
    is_quantity_count NUMERIC(4,0),
    is_order_entry NUMERIC(4,0),
    modifier_kind NUMERIC(4,0),
    return_target_line_num NUMERIC(11,0),
    numeric_reserve1 NUMERIC(11,0),
    numeric_reserve2 NUMERIC(11,0),
    numeric_reserve3 NUMERIC(11,0),
    numeric_reserve4 NUMERIC(11,0),
    numeric_reserve5 NUMERIC(11,0),
    numeric_reserve6 NUMERIC(11,0),
    numeric_reserve7 NUMERIC(11,0),
    numeric_reserve8 NUMERIC(11,0),
    numeric_reserve9 NUMERIC(11,0),
    numeric_reserve10 NUMERIC(11,0),
    string_reserve1 VARCHAR(255),
    string_reserve2 VARCHAR(255),
    string_reserve3 VARCHAR(255),
    string_reserve4 VARCHAR(255),
    string_reserve5 VARCHAR(255),
    string_reserve6 VARCHAR(255),
    string_reserve7 VARCHAR(255),
    string_reserve8 VARCHAR(255),
    string_reserve9 VARCHAR(255),
    string_reserve10 VARCHAR(255),
    PRIMARY KEY (shop_cd, register_num, business_date, receipt_num, line_num)
);
COMMENT ON TABLE sales_info_alignment_detail_work IS '売上情報連携（明細）';
COMMENT ON COLUMN sales_info_alignment_detail_work.shop_cd IS '店舗コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.register_num IS 'レジ番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.business_date IS '営業日付';
COMMENT ON COLUMN sales_info_alignment_detail_work.receipt_num IS 'レシート番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.line_num IS '行番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.line_kind IS '行種別';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_in_store_marking IS 'インストアマーキングフラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.brand_cd IS 'ブランドコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.dept_cd IS '部門コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.class_cd IS 'クラスコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.sub_class_cd IS 'サブクラスコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.item_cd IS '商品コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.item_name IS '商品名';
COMMENT ON COLUMN sales_info_alignment_detail_work.dept_group_cd IS '部門グループコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.parent_item_cd IS '親商品コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.jan_cd IS 'Janコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.item_num IS '品番コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.color_cd IS 'カラーコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.size_cd IS 'サイズコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.year_cd IS '年度コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.season_cd IS 'シーズンコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.attribute_cd1 IS '属性コード１';
COMMENT ON COLUMN sales_info_alignment_detail_work.attribute_cd2 IS '属性コード２';
COMMENT ON COLUMN sales_info_alignment_detail_work.attribute_cd3 IS '属性コード３';
COMMENT ON COLUMN sales_info_alignment_detail_work.attribute_cd4 IS '属性コード４';
COMMENT ON COLUMN sales_info_alignment_detail_work.attribute_cd5 IS '属性コード５';
COMMENT ON COLUMN sales_info_alignment_detail_work.scan_bar_cd1 IS '読取バーコード１';
COMMENT ON COLUMN sales_info_alignment_detail_work.scan_bar_cd2 IS '読取バーコード２';
COMMENT ON COLUMN sales_info_alignment_detail_work.discount_cd IS '値割引コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.bmplan_cd IS 'ＢＭ企画コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.coupon_num IS 'クーポン管理番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_include_sales IS '純売区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.item_kind IS '商品区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.tax_kind IS '税区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.tax_rate IS '税率';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_reduced_tax_rate IS '軽減税率フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_proper_item IS 'セールプロパー区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_change_price IS '臨時売価変更フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.master_unit_price IS 'マスタ単価';
COMMENT ON COLUMN sales_info_alignment_detail_work.body_price IS '本体価格';
COMMENT ON COLUMN sales_info_alignment_detail_work.fixed_price IS '上代';
COMMENT ON COLUMN sales_info_alignment_detail_work.unit_price IS '単価';
COMMENT ON COLUMN sales_info_alignment_detail_work.quantity IS '点数';
COMMENT ON COLUMN sales_info_alignment_detail_work.amount IS '金額';
COMMENT ON COLUMN sales_info_alignment_detail_work.tax_inclusive IS '内税額';
COMMENT ON COLUMN sales_info_alignment_detail_work.tax_outside IS '外税額';
COMMENT ON COLUMN sales_info_alignment_detail_work.tax IS '消費税額';
COMMENT ON COLUMN sales_info_alignment_detail_work.disc_rate IS '割引率';
COMMENT ON COLUMN sales_info_alignment_detail_work.line_minus_amount IS '単品値引額';
COMMENT ON COLUMN sales_info_alignment_detail_work.line_minus_tax_inclusive IS '単品値引内税額';
COMMENT ON COLUMN sales_info_alignment_detail_work.bmset_minus_amount IS 'ＢＭセット値引按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.bmset_minus_tax_inclusive IS 'ＢＭセット値引内税按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.point_minus_amount IS 'ポイント値引按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.point_minus_tax_inclusive IS 'ポイント値引内税按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.coupon_minus_amount IS 'クーポン還元値割按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.coupon_minus_tax_inclusive IS 'クーポン還元値割内税按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.sub_total_minus_amount IS '小計値引按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.sub_total_minus_tax_inclusive IS '小計値引内税按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.before_disc_tax_inclusive IS '値割引前内税額';
COMMENT ON COLUMN sales_info_alignment_detail_work.sales_man_cd IS '販売員コード';
COMMENT ON COLUMN sales_info_alignment_detail_work.stock_kind IS '在庫区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_inventory_counted IS '棚卸カウント済区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_promotion_ticket IS '販促券区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.promotion_ticket_bar_cd IS '販促券バーコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_promotion_ticket_allow_combination IS '販促券併用可否区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_item_flag IS 'オーダリングデータフラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_slip_no IS '伝票番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_slip_sub_no IS '伝票番号枝番';
COMMENT ON COLUMN sales_info_alignment_detail_work.grand_classification IS 'グランドフラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.menu_classification IS '明細区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_line_num IS '登録インデックス';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_quantity IS 'OESオーダ数量';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_minus IS 'OESマイナス数量';
COMMENT ON COLUMN sales_info_alignment_detail_work.pos_minus IS 'POSマイナス数量';
COMMENT ON COLUMN sales_info_alignment_detail_work.takeout_flag IS 'テイクアウトフラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge_minus_amount IS 'サービス料値引按分額';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge_minus_tax_inclusive IS 'サービス料値引内税額';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge_flag IS 'サービス料フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge1_flag IS 'サービス料１フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge2_flag IS 'サービス料２フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge1_manually_flag IS 'サービス料１手動設定フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.service_charge2_manually_flag IS 'サービス料２手動設定フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_service_charge1 IS 'OESからのサービス料１';
COMMENT ON COLUMN sales_info_alignment_detail_work.oes_service_charge2 IS 'OESからのサービス料２';
COMMENT ON COLUMN sales_info_alignment_detail_work.cooking_directions_time IS '調理開始指示時間';
COMMENT ON COLUMN sales_info_alignment_detail_work.cooking_complete_time IS '調理完了時間';
COMMENT ON COLUMN sales_info_alignment_detail_work.offer_complete_time IS '提供完了時間';
COMMENT ON COLUMN sales_info_alignment_detail_work.acceptance_time IS '受付日時';
COMMENT ON COLUMN sales_info_alignment_detail_work.menu_cook_cmp_time IS '調理完了時間';
COMMENT ON COLUMN sales_info_alignment_detail_work.menu_offer_cmp_time IS '提供時間';
COMMENT ON COLUMN sales_info_alignment_detail_work.disc_amount_limit IS '割引限度額';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_follow_disc IS '従食割引区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_allow_credit IS 'クレジット許可区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_employee_acnt_recv IS '社掛';
COMMENT ON COLUMN sales_info_alignment_detail_work.employee_cd IS '社員番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.sub_menu_kind IS 'サブメニュー区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.grand_menu_code IS 'グランドメニューコード';
COMMENT ON COLUMN sales_info_alignment_detail_work.grand_menu_index IS 'グランドメニュー登録インデックス';
COMMENT ON COLUMN sales_info_alignment_detail_work.select_kind IS 'セレクト区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_quantity_count IS '点数カウントフラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.is_order_entry IS 'オーダー送信フラグ';
COMMENT ON COLUMN sales_info_alignment_detail_work.modifier_kind IS 'モディファイア区分';
COMMENT ON COLUMN sales_info_alignment_detail_work.return_target_line_num IS '返品対象番号';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve1 IS '数値リザーブ１';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve2 IS '数値リザーブ２';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve3 IS '数値リザーブ３';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve4 IS '数値リザーブ４';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve5 IS '数値リザーブ５';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve6 IS '数値リザーブ６';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve7 IS '数値リザーブ７';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve8 IS '数値リザーブ８';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve9 IS '数値リザーブ９';
COMMENT ON COLUMN sales_info_alignment_detail_work.numeric_reserve10 IS '数値リザーブ１０';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve1 IS '文字列リザーブ１';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve2 IS '文字列リザーブ２';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve3 IS '文字列リザーブ３';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve4 IS '文字列リザーブ４';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve5 IS '文字列リザーブ５';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve6 IS '文字列リザーブ６';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve7 IS '文字列リザーブ７';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve8 IS '文字列リザーブ８';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve9 IS '文字列リザーブ９';
COMMENT ON COLUMN sales_info_alignment_detail_work.string_reserve10 IS '文字列リザーブ１０';

