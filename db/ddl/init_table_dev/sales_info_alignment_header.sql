CREATE TABLE sales_info_alignment_header (
    shop_cd VARCHAR(12) NOT NULL,
    register_num NUMERIC(11,0) NOT NULL,
    business_date TIMESTAMP NOT NULL,
    receipt_num NUMERIC(11,0) NOT NULL,
    chit_num VARCHAR(20) NOT NULL,
    system_datetime TIMESTAMP NOT NULL,
    open_count NUMERIC(4,0) NOT NULL,
    is_void NUMERIC(4,0) NOT NULL,
    check_kind NUMERIC(4,0) NOT NULL,
    return_kind NUMERIC(4,0) NOT NULL,
    sub_check_kind NUMERIC(4,0) NOT NULL,
    sales_group_cd NUMERIC(4,0) NOT NULL,
    deposit_kind NUMERIC(4,0),
    operator_cd VARCHAR(10) NOT NULL,
    operator_name VARCHAR(50) NOT NULL,
    sales_man_cd VARCHAR(10) NOT NULL,
    sales_man_name VARCHAR(50) NOT NULL,
    return_employee_cd VARCHAR(10),
    return_employee_name VA<PERSON><PERSON><PERSON>(50),
    void_employee_cd VARCHAR(10),
    void_employee_name VARCHAR(50),
    staff_sale_employee_cd VARCHAR(10),
    staff_sale_employee_name VARCHAR(50),
    customer_cd VARCHAR(20),
    customer_layer_cd VARCHAR(4),
    customer_layer_cd2 VARCHAR(4),
    purchase_motive_cd VARCHAR(4),
    return_reason_cd VARCHAR(4),
    void_reason_cd VARCHAR(4),
    net_sales_amount_of_outside_tax NUMERIC(11,0) NOT NULL,
    net_sales_amount_of_inside_tax NUMERIC(11,0) NOT NULL,
    net_sales_amount_of_tax_free NUMERIC(11,0) NOT NULL,
    net_sales_outside_tax NUMERIC(11,0) NOT NULL,
    net_sales_inside_tax NUMERIC(11,0) NOT NULL,
    net_sales_quantity NUMERIC(11,0) NOT NULL,
    outside_sales_amount_of_outside_tax NUMERIC(11,0) NOT NULL,
    outside_sales_amount_of_inside_tax NUMERIC(11,0) NOT NULL,
    outside_sales_amount_of_tax_free NUMERIC(11,0) NOT NULL,
    outside_sales_outside_tax NUMERIC(11,0) NOT NULL,
    outside_sales_inside_tax NUMERIC(11,0) NOT NULL,
    outside_sales_quantity NUMERIC(11,0) NOT NULL,
    total_amount NUMERIC(11,0) NOT NULL,
    discount_amount NUMERIC(11,0) NOT NULL,
    discount_tax_inclusive NUMERIC(11,0) NOT NULL,
    is_revenue_stamp NUMERIC(4,0) NOT NULL,
    order_line_count NUMERIC(11,0) NOT NULL,
    pay_line_count NUMERIC(4,0) NOT NULL,
    is_total_display NUMERIC(4,0) NOT NULL,
    is_reduced_tax_rate_trade NUMERIC(4,0) NOT NULL,
    is_tax_free NUMERIC(4,0) NOT NULL,
    customers_num NUMERIC(11,0) NOT NULL,
    deliver_date TIMESTAMP,
    sale_attribute_cd1 VARCHAR(4),
    sale_attribute_cd2 VARCHAR(4),
    sale_attribute_cd3 VARCHAR(4),
    sale_attribute_cd4 VARCHAR(10),
    campaign_no VARCHAR(8),
    closing_date TIMESTAMP,
    return_date TIMESTAMP,
    order_number VARCHAR(10),
    employee_meal NUMERIC(4,0) NOT NULL,
    employee_code VARCHAR(10),
    table_no VARCHAR(12),
    acceptance_time TIMESTAMP,
    menu_cook_cmp_time TIMESTAMP,
    menu_offer_cmp_time TIMESTAMP,
    service_charge_amount_outside_tax NUMERIC(11,0) NOT NULL,
    service_charge_amount_inside_tax NUMERIC(11,0) NOT NULL,
    service_charge_tax_exclusive NUMERIC(11,0) NOT NULL,
    service_charge_tax_inclusive NUMERIC(11,0) NOT NULL,
    service_charge_amount1 NUMERIC(11,0) NOT NULL,
    service_charge_amount2 NUMERIC(11,0) NOT NULL,
    service_charge_minus_amount NUMERIC(11,0) NOT NULL,
    service_charge_minus_tax_inclusive NUMERIC(11,0) NOT NULL,
    service_charge_target NUMERIC(4,0) NOT NULL,
    service_charge_button NUMERIC(4,0) NOT NULL,
    service_charge1_button NUMERIC(4,0) NOT NULL,
    service_charge2_button NUMERIC(4,0) NOT NULL,
    eat_in_amount NUMERIC(11,0) NOT NULL,
    takeout_amount NUMERIC(11,0) NOT NULL,
    vein_employee_cd VARCHAR(10),
    is_vein_authentication NUMERIC(4,0),
    out_calc_flg NUMERIC(4,0),
    sale_goods_flg VARCHAR(4),
    point_linkage_kind NUMERIC(4,0),
    numeric_reserve1 NUMERIC(11,0),
    numeric_reserve2 NUMERIC(11,0),
    numeric_reserve3 NUMERIC(11,0),
    numeric_reserve4 NUMERIC(11,0),
    numeric_reserve5 NUMERIC(11,0),
    numeric_reserve6 NUMERIC(11,0),
    numeric_reserve7 NUMERIC(11,0),
    numeric_reserve8 NUMERIC(11,0),
    numeric_reserve9 NUMERIC(11,0),
    numeric_reserve10 NUMERIC(11,0),
    string_reserve1 VARCHAR(255),
    string_reserve2 VARCHAR(255),
    string_reserve3 VARCHAR(255),
    string_reserve4 VARCHAR(255),
    string_reserve5 VARCHAR(255),
    string_reserve6 VARCHAR(255),
    string_reserve7 VARCHAR(255),
    string_reserve8 VARCHAR(255),
    string_reserve9 VARCHAR(255),
    string_reserve10 VARCHAR(255),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    v_order_no varchar(100) GENERATED ALWAYS AS (((((((shop_cd::text || register_num) || lpad(date_part('year'::text, business_date)::text, 4, '0'::text)) || lpad(date_part('month'::text, business_date)::text, 2, '0'::text)) || lpad(date_part('day'::text, business_date)::text, 2, '0'::text)) || chit_num::text))) STORED NULL,
    PRIMARY KEY (shop_cd, register_num, business_date, receipt_num)
);
COMMENT ON TABLE sales_info_alignment_header IS '売上情報連携（ヘッダ）';
COMMENT ON COLUMN sales_info_alignment_header.shop_cd IS '店舗コード';
COMMENT ON COLUMN sales_info_alignment_header.register_num IS 'レジ番号';
COMMENT ON COLUMN sales_info_alignment_header.business_date IS '営業日付';
COMMENT ON COLUMN sales_info_alignment_header.receipt_num IS 'レシート番号';
COMMENT ON COLUMN sales_info_alignment_header.chit_num IS '伝票番号';
COMMENT ON COLUMN sales_info_alignment_header.system_datetime IS 'システム日時';
COMMENT ON COLUMN sales_info_alignment_header.open_count IS '開設回数';
COMMENT ON COLUMN sales_info_alignment_header.is_void IS 'Voidフラグ';
COMMENT ON COLUMN sales_info_alignment_header.check_kind IS '伝票種別';
COMMENT ON COLUMN sales_info_alignment_header.return_kind IS '返品区分';
COMMENT ON COLUMN sales_info_alignment_header.sub_check_kind IS 'サブ伝票種別';
COMMENT ON COLUMN sales_info_alignment_header.sales_group_cd IS '売上区分コード';
COMMENT ON COLUMN sales_info_alignment_header.deposit_kind IS '入出金種別';
COMMENT ON COLUMN sales_info_alignment_header.operator_cd IS '責任者コード';
COMMENT ON COLUMN sales_info_alignment_header.operator_name IS '責任者名称';
COMMENT ON COLUMN sales_info_alignment_header.sales_man_cd IS '販売員コード';
COMMENT ON COLUMN sales_info_alignment_header.sales_man_name IS '販売員名称';
COMMENT ON COLUMN sales_info_alignment_header.return_employee_cd IS '返品実施社員コード';
COMMENT ON COLUMN sales_info_alignment_header.return_employee_name IS '返品実施社員名称';
COMMENT ON COLUMN sales_info_alignment_header.void_employee_cd IS '取消実施社員コード';
COMMENT ON COLUMN sales_info_alignment_header.void_employee_name IS '取消実施社員名称';
COMMENT ON COLUMN sales_info_alignment_header.staff_sale_employee_cd IS '社員販売社員コード';
COMMENT ON COLUMN sales_info_alignment_header.staff_sale_employee_name IS '社員販売社員名称';
COMMENT ON COLUMN sales_info_alignment_header.customer_cd IS '顧客コード';
COMMENT ON COLUMN sales_info_alignment_header.customer_layer_cd IS '客層コード';
COMMENT ON COLUMN sales_info_alignment_header.customer_layer_cd2 IS '客層コード2';
COMMENT ON COLUMN sales_info_alignment_header.purchase_motive_cd IS '購入動機コード';
COMMENT ON COLUMN sales_info_alignment_header.return_reason_cd IS '返品理由コード';
COMMENT ON COLUMN sales_info_alignment_header.void_reason_cd IS '取消理由コード';
COMMENT ON COLUMN sales_info_alignment_header.net_sales_amount_of_outside_tax IS '純売上外税税抜き金額';
COMMENT ON COLUMN sales_info_alignment_header.net_sales_amount_of_inside_tax IS '純売上内税税抜き金額';
COMMENT ON COLUMN sales_info_alignment_header.net_sales_amount_of_tax_free IS '純売上非課税金額';
COMMENT ON COLUMN sales_info_alignment_header.net_sales_outside_tax IS '純売上外税金額';
COMMENT ON COLUMN sales_info_alignment_header.net_sales_inside_tax IS '純売上内税金額';
COMMENT ON COLUMN sales_info_alignment_header.net_sales_quantity IS '純売上点数';
COMMENT ON COLUMN sales_info_alignment_header.outside_sales_amount_of_outside_tax IS '純売上外外税税抜き金額';
COMMENT ON COLUMN sales_info_alignment_header.outside_sales_amount_of_inside_tax IS '純売上外内税税抜き金額';
COMMENT ON COLUMN sales_info_alignment_header.outside_sales_amount_of_tax_free IS '純売上外非課税金額';
COMMENT ON COLUMN sales_info_alignment_header.outside_sales_outside_tax IS '純売上外外税消費税額';
COMMENT ON COLUMN sales_info_alignment_header.outside_sales_inside_tax IS '純売上外内税消費税額';
COMMENT ON COLUMN sales_info_alignment_header.outside_sales_quantity IS '純売上外点数';
COMMENT ON COLUMN sales_info_alignment_header.total_amount IS 'サ込税込売上';
COMMENT ON COLUMN sales_info_alignment_header.discount_amount IS '値引額';
COMMENT ON COLUMN sales_info_alignment_header.discount_tax_inclusive IS '値引内税額';
COMMENT ON COLUMN sales_info_alignment_header.is_revenue_stamp IS '収入印紙フラグ';
COMMENT ON COLUMN sales_info_alignment_header.order_line_count IS 'オーダー行数';
COMMENT ON COLUMN sales_info_alignment_header.pay_line_count IS '支払行数';
COMMENT ON COLUMN sales_info_alignment_header.is_total_display IS '総額表示フラグ';
COMMENT ON COLUMN sales_info_alignment_header.is_reduced_tax_rate_trade IS '軽減税率対応フラグ';
COMMENT ON COLUMN sales_info_alignment_header.is_tax_free IS '免税フラグ';
COMMENT ON COLUMN sales_info_alignment_header.customers_num IS '客数';
COMMENT ON COLUMN sales_info_alignment_header.deliver_date IS '引渡し日時';
COMMENT ON COLUMN sales_info_alignment_header.sale_attribute_cd1 IS '取引属性コード１';
COMMENT ON COLUMN sales_info_alignment_header.sale_attribute_cd2 IS '取引属性コード２';
COMMENT ON COLUMN sales_info_alignment_header.sale_attribute_cd3 IS '取引属性コード３';
COMMENT ON COLUMN sales_info_alignment_header.sale_attribute_cd4 IS '取引属性コード４';
COMMENT ON COLUMN sales_info_alignment_header.campaign_no IS 'キャンペーン番号';
COMMENT ON COLUMN sales_info_alignment_header.closing_date IS '計上日付';
COMMENT ON COLUMN sales_info_alignment_header.return_date IS '返品日付';
COMMENT ON COLUMN sales_info_alignment_header.order_number IS '前捌き番号';
COMMENT ON COLUMN sales_info_alignment_header.employee_meal IS '従食フラグ';
COMMENT ON COLUMN sales_info_alignment_header.employee_code IS '従業員番号';
COMMENT ON COLUMN sales_info_alignment_header.table_no IS 'テーブル番号';
COMMENT ON COLUMN sales_info_alignment_header.acceptance_time IS '受付日時';
COMMENT ON COLUMN sales_info_alignment_header.menu_cook_cmp_time IS '調理完了時間';
COMMENT ON COLUMN sales_info_alignment_header.menu_offer_cmp_time IS '提供時間';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_amount_outside_tax IS 'サービス料-外税';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_amount_inside_tax IS 'サービス料-内税';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_tax_exclusive IS 'サービス料消費税-外税';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_tax_inclusive IS 'サービス料消費税-内税';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_amount1 IS 'サービス料金額・固定額';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_amount2 IS 'サービス料金額・固定額比率';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_minus_amount IS 'サービス料値引額';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_minus_tax_inclusive IS 'サービス料値引内税額';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_target IS 'サービス料対象';
COMMENT ON COLUMN sales_info_alignment_header.service_charge_button IS 'サービス料ボタン';
COMMENT ON COLUMN sales_info_alignment_header.service_charge1_button IS 'サービス料１ボタン';
COMMENT ON COLUMN sales_info_alignment_header.service_charge2_button IS 'サービス料２ボタン';
COMMENT ON COLUMN sales_info_alignment_header.eat_in_amount IS '店内飲食金額';
COMMENT ON COLUMN sales_info_alignment_header.takeout_amount IS 'テイクアウト金額';
COMMENT ON COLUMN sales_info_alignment_header.vein_employee_cd IS '静脈認証社員コード';
COMMENT ON COLUMN sales_info_alignment_header.is_vein_authentication IS '静脈認証成功可否フラグ';
COMMENT ON COLUMN sales_info_alignment_header.out_calc_flg IS '計算対象外フラグ';
COMMENT ON COLUMN sales_info_alignment_header.sale_goods_flg IS '物販会計フラグ';
COMMENT ON COLUMN sales_info_alignment_header.point_linkage_kind IS 'ポイント連動先区分';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve1 IS '数値リザーブ１';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve2 IS '数値リザーブ２';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve3 IS '数値リザーブ３';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve4 IS '数値リザーブ４';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve5 IS '数値リザーブ５';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve6 IS '数値リザーブ６';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve7 IS '数値リザーブ７';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve8 IS '数値リザーブ８';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve9 IS '数値リザーブ９';
COMMENT ON COLUMN sales_info_alignment_header.numeric_reserve10 IS '数値リザーブ１０';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve1 IS '文字列リザーブ１';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve2 IS '文字列リザーブ２';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve3 IS '文字列リザーブ３';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve4 IS '文字列リザーブ４';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve5 IS '文字列リザーブ５';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve6 IS '文字列リザーブ６';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve7 IS '文字列リザーブ７';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve8 IS '文字列リザーブ８';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve9 IS '文字列リザーブ９';
COMMENT ON COLUMN sales_info_alignment_header.string_reserve10 IS '文字列リザーブ１０';
COMMENT ON COLUMN sales_info_alignment_header.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN sales_info_alignment_header.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN sales_info_alignment_header.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN sales_info_alignment_header.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN sales_info_alignment_header.d_version IS 'デ連バージョン';
COMMENT ON COLUMN sales_info_alignment_header.v_order_no IS '生成注文番号';
