CREATE TABLE seruring_move (
    corp_cd VARCHAR(4),
    stock_io_type VARCHAR(1),
    move_date TIMESTAMP,
    center_code VARCHAR(4),
    stock_group_code VARCHAR(4),
    shop_code_swh VARCHAR(16),
    sh_control_number VARCHAR(10),
    move_quantity NUMERIC(7),
    move_center_code VARCHAR(4),
    move_stock_group_code VARCHAR(4),
    move_shop_code_swh VARCHAR(16),
    upd_user_id VARCHAR(30),
    data_date TIMESTAMP,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL
);
COMMENT ON TABLE seruring_move IS '確保移動';
COMMENT ON COLUMN seruring_move.corp_cd IS '会社コード';
COMMENT ON COLUMN seruring_move.stock_io_type IS '入出庫区分';
COMMENT ON COLUMN seruring_move.move_date IS '移動日';
COMMENT ON COLUMN seruring_move.center_code IS 'センターコード';
COMMENT ON COLUMN seruring_move.stock_group_code IS '在庫グループコード';
COMMENT ON COLUMN seruring_move.shop_code_swh IS '店舗コード（確保倉庫）';
COMMENT ON COLUMN seruring_move.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN seruring_move.move_quantity IS '移動数量';
COMMENT ON COLUMN seruring_move.move_center_code IS '移動先・元センターコード';
COMMENT ON COLUMN seruring_move.move_stock_group_code IS '移動先・元在庫グループコード';
COMMENT ON COLUMN seruring_move.move_shop_code_swh IS '移動先・元店舗コード（確保倉庫）';
COMMENT ON COLUMN seruring_move.upd_user_id IS '更新ユーザーID';
COMMENT ON COLUMN seruring_move.data_date IS 'データ発生日付';
COMMENT ON COLUMN seruring_move.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN seruring_move.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN seruring_move.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN seruring_move.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN seruring_move.d_version IS 'デ連バージョン';

