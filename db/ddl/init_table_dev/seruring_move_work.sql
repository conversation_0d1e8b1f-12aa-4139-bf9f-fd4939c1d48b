CREATE TABLE seruring_move_work (
    corp_cd VARCHAR(4),
    stock_io_type VARCHAR(1),
    move_date TIMESTAMP,
    center_code VARCHAR(4),
    stock_group_code VARCHAR(4),
    shop_code_swh VARCHAR(16),
    sh_control_number VARCHAR(10),
    move_quantity NUMERIC(7),
    move_center_code VARCHAR(4),
    move_stock_group_code VARCHAR(4),
    move_shop_code_swh VARCHAR(16),
    upd_user_id VARCHAR(30),
    data_date TIMESTAMP
);
COMMENT ON TABLE seruring_move_work IS '確保移動ワーク';
COMMENT ON COLUMN seruring_move_work.corp_cd IS '会社コード';
COMMENT ON COLUMN seruring_move_work.stock_io_type IS '入出庫区分';
COMMENT ON COLUMN seruring_move_work.move_date IS '移動日';
COMMENT ON COLUMN seruring_move_work.center_code IS 'センターコード';
COMMENT ON COLUMN seruring_move_work.stock_group_code IS '在庫グループコード';
COMMENT ON COLUMN seruring_move_work.shop_code_swh IS '店舗コード（確保倉庫）';
COMMENT ON COLUMN seruring_move_work.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN seruring_move_work.move_quantity IS '移動数量';
COMMENT ON COLUMN seruring_move_work.move_center_code IS '移動先・元センターコード';
COMMENT ON COLUMN seruring_move_work.move_stock_group_code IS '移動先・元在庫グループコード';
COMMENT ON COLUMN seruring_move_work.move_shop_code_swh IS '移動先・元店舗コード（確保倉庫）';
COMMENT ON COLUMN seruring_move_work.upd_user_id IS '更新ユーザーID';
COMMENT ON COLUMN seruring_move_work.data_date IS 'データ発生日付';

