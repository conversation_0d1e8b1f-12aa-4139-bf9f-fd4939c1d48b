CREATE TABLE shipping_header (
    shipping_no VARCHAR(16) NOT NULL,
    order_no VARCHAR(16) NOT NULL,
    shop_code VARCHAR(16) NOT NULL,
    customer_code VARCHAR(16),
    neo_customer_no VARCHAR(12),
    address_no NUMERIC(8,0) NOT NULL,
    address_last_name <PERSON><PERSON><PERSON><PERSON>(20) NOT NULL,
    address_first_name <PERSON><PERSON><PERSON><PERSON>(20),
    address_last_name_kana VARCHAR(40) NOT NULL,
    address_first_name_kana VARCHAR(40),
    postal_code VARCHAR(7) NOT NULL,
    prefecture_code VARCHAR(2) NOT NULL,
    address1 VARCHAR(4) NOT NULL,
    address2 VARCHAR(50) NOT NULL,
    address3 VARCHAR(255) NOT NULL,
    address4 VARCHAR(100),
    corporation_post_name VARCHAR(40),
    phone_number VARCHAR(16),
    delivery_remark VARCHAR(500),
    acquired_point NUMERIC(9,0),
    delivery_slip_no VARCHAR(30),
    shipping_charge NUMERIC(8,0) NOT NULL,
    shipping_charge_tax_type NUMERIC(1,0) NOT NULL,
    shipping_charge_tax_group_code VA<PERSON>HAR(8) NOT NULL,
    shipping_charge_tax_no NUMERIC(3,0) NOT NULL,
    shipping_charge_tax_rate NUMERIC(3,0) NOT NULL,
    shipping_charge_tax NUMERIC(10,2) NOT NULL,
    delivery_type_no NUMERIC(8,0) NOT NULL,
    shipping_method VARCHAR(2) NOT NULL,
    delivery_type_name VARCHAR(40),
    delivery_appointed_date TIMESTAMP(0),
    delivery_appointed_time_start NUMERIC(2,0),
    delivery_appointed_time_end NUMERIC(2,0),
    arrival_date TIMESTAMP(0),
    arrival_time_start NUMERIC(2,0),
    arrival_time_end NUMERIC(2,0),
    fixed_sales_status NUMERIC(1,0) NOT NULL,
    shipping_status NUMERIC(1,0) NOT NULL,
    shipping_direct_date TIMESTAMP(0),
    shipping_date TIMESTAMP(0),
    original_shipping_no VARCHAR(16),
    return_item_date TIMESTAMP(0),
    return_item_type NUMERIC(1,0),
    shipping_area VARCHAR(2) NOT NULL,
    delivery_note_flg NUMERIC(1,0) NOT NULL,
    include_flg NUMERIC(1,0) NOT NULL,
    delivery_memo VARCHAR(20),
    shipping_bill_price NUMERIC(10,0) NOT NULL,
    shipping_dokon_shiji_code VARCHAR(80),
    o_name_disp_kbn VARCHAR(1) NOT NULL,
    member_stage VARCHAR(2) NOT NULL,
    possession_point NUMERIC(10,0),
    sales_recording_date TIMESTAMP(0),
    prod_pack_type VARCHAR(2),
    shipping_method_kbn VARCHAR(2),
    box_code VARCHAR(2),
    delivery_note_message TEXT,
    sagawa_collect_date TIMESTAMP(0),
    delivery_info_no VARCHAR(8),
    receive_shop_type VARCHAR(1),
    receive_shop_id VARCHAR(12),
    receive_shop_name VARCHAR(30),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shipping_no)
);
COMMENT ON TABLE shipping_header IS '出荷ヘッダ';
COMMENT ON COLUMN shipping_header.shipping_no IS '出荷番号';
COMMENT ON COLUMN shipping_header.order_no IS '受注番号';
COMMENT ON COLUMN shipping_header.shop_code IS 'ショップコード';
COMMENT ON COLUMN shipping_header.customer_code IS '顧客コード';
COMMENT ON COLUMN shipping_header.neo_customer_no IS '顧客番号';
COMMENT ON COLUMN shipping_header.address_no IS 'アドレス帳番号';
COMMENT ON COLUMN shipping_header.address_last_name IS '宛名：姓';
COMMENT ON COLUMN shipping_header.address_first_name IS '宛名：名';
COMMENT ON COLUMN shipping_header.address_last_name_kana IS '宛名姓かな';
COMMENT ON COLUMN shipping_header.address_first_name_kana IS '宛名名かな';
COMMENT ON COLUMN shipping_header.postal_code IS '郵便番号';
COMMENT ON COLUMN shipping_header.prefecture_code IS '都道府県コード';
COMMENT ON COLUMN shipping_header.address1 IS '住所1';
COMMENT ON COLUMN shipping_header.address2 IS '住所2';
COMMENT ON COLUMN shipping_header.address3 IS '住所3';
COMMENT ON COLUMN shipping_header.address4 IS '住所4';
COMMENT ON COLUMN shipping_header.corporation_post_name IS '会社部署名';
COMMENT ON COLUMN shipping_header.phone_number IS '電話番号';
COMMENT ON COLUMN shipping_header.delivery_remark IS '配送先備考';
COMMENT ON COLUMN shipping_header.acquired_point IS '獲得ポイント';
COMMENT ON COLUMN shipping_header.delivery_slip_no IS '宅配便伝票番号';
COMMENT ON COLUMN shipping_header.shipping_charge IS '送料';
COMMENT ON COLUMN shipping_header.shipping_charge_tax_type IS '送料消費税区分';
COMMENT ON COLUMN shipping_header.shipping_charge_tax_group_code IS '送料消費税グループコード';
COMMENT ON COLUMN shipping_header.shipping_charge_tax_no IS '送料消費税番号';
COMMENT ON COLUMN shipping_header.shipping_charge_tax_rate IS '送料消費税率';
COMMENT ON COLUMN shipping_header.shipping_charge_tax IS '送料消費税額';
COMMENT ON COLUMN shipping_header.delivery_type_no IS '配送種別番号';
COMMENT ON COLUMN shipping_header.shipping_method IS '配送方法';
COMMENT ON COLUMN shipping_header.delivery_type_name IS '配送種別名称';
COMMENT ON COLUMN shipping_header.delivery_appointed_date IS '配送指定日';
COMMENT ON COLUMN shipping_header.delivery_appointed_time_start IS '配送指定時間開始';
COMMENT ON COLUMN shipping_header.delivery_appointed_time_end IS '配送指定時間終了';
COMMENT ON COLUMN shipping_header.arrival_date IS '到着予定日';
COMMENT ON COLUMN shipping_header.arrival_time_start IS '到着時間開始';
COMMENT ON COLUMN shipping_header.arrival_time_end IS '到着時間終了';
COMMENT ON COLUMN shipping_header.fixed_sales_status IS '売上確定ステータス';
COMMENT ON COLUMN shipping_header.shipping_status IS '出荷ステータス';
COMMENT ON COLUMN shipping_header.shipping_direct_date IS '出荷指示日';
COMMENT ON COLUMN shipping_header.shipping_date IS '発送日';
COMMENT ON COLUMN shipping_header.original_shipping_no IS '元出荷番号';
COMMENT ON COLUMN shipping_header.return_item_date IS '返品日';
COMMENT ON COLUMN shipping_header.return_item_type IS '返品区分';
COMMENT ON COLUMN shipping_header.shipping_area IS '発送場所';
COMMENT ON COLUMN shipping_header.delivery_note_flg IS '納品書不要フラグ';
COMMENT ON COLUMN shipping_header.include_flg IS '同梱不要フラグ';
COMMENT ON COLUMN shipping_header.delivery_memo IS '配送メモ';
COMMENT ON COLUMN shipping_header.shipping_bill_price IS '発送時請求額';
COMMENT ON COLUMN shipping_header.shipping_dokon_shiji_code IS '発送同梱指示コード';
COMMENT ON COLUMN shipping_header.o_name_disp_kbn IS '依頼主＿名前表示区分';
COMMENT ON COLUMN shipping_header.member_stage IS '会員ステージ';
COMMENT ON COLUMN shipping_header.possession_point IS '保有ポイント数';
COMMENT ON COLUMN shipping_header.sales_recording_date IS '売上計上日';
COMMENT ON COLUMN shipping_header.prod_pack_type IS '包装種類';
COMMENT ON COLUMN shipping_header.shipping_method_kbn IS '配送方法指定区分';
COMMENT ON COLUMN shipping_header.box_code IS 'ボックスコード';
COMMENT ON COLUMN shipping_header.delivery_note_message IS '納品書メッセージ';
COMMENT ON COLUMN shipping_header.sagawa_collect_date IS '佐川集荷日';
COMMENT ON COLUMN shipping_header.delivery_info_no IS '配送情報番号';
COMMENT ON COLUMN shipping_header.receive_shop_type IS '受取店舗区分';
COMMENT ON COLUMN shipping_header.receive_shop_id IS '受取店舗ID';
COMMENT ON COLUMN shipping_header.receive_shop_name IS '受取店舗名称';
COMMENT ON COLUMN shipping_header.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN shipping_header.created_user IS '作成ユーザ';
COMMENT ON COLUMN shipping_header.created_datetime IS '作成日時';
COMMENT ON COLUMN shipping_header.updated_user IS '更新ユーザ';
COMMENT ON COLUMN shipping_header.updated_datetime IS '更新日時';
COMMENT ON COLUMN shipping_header.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN shipping_header.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN shipping_header.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN shipping_header.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN shipping_header.d_version IS 'デ連バージョン';

