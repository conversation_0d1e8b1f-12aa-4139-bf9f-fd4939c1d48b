CREATE TABLE shop_mst (
    shop_cd VARCHAR(12) NOT NULL,
    group_cd VARCHAR(10),
    area_cd VARCHAR(10),
    district_cd VARCHAR(10),
    shop_kind VARCHAR(2) NOT NULL,
    shop_name_full VARCHAR(60) NOT NULL,
    shop_name_half VARCHAR(60),
    shop_name_short VARCHAR(60),
    shop_name_english VARCHAR(60),
    zip_cd VARCHAR(15),
    prefecture_cd VARCHAR(10),
    address1 VARCHAR(60),
    address2 VARCHAR(60),
    address3 VARCHAR(60),
    address4 VARCHAR(60),
    tel_num VARCHAR(15),
    fax_num VARCHAR(15),
    tax_inclusive_round_kind NUMERIC(4,0),
    tax_exclusive_round_kind NUMERIC(4,0),
    condition_kind VARCHAR(2) NOT NULL,
    condition_detail_kind VARCHAR(2) NOT NULL,
    channel_kind VARCHAR(10),
    is_stock_control NUMERIC(4,0) NOT NULL,
    begin_date TIMESTAMP,
    end_date TIMESTAMP,
    shop_floor_space NUMERIC(6,2),
    is_abandon_price_change NUMERIC(4,0) NOT NULL,
    is_abandon_stock_transfer NUMERIC(4,0) NOT NULL,
    form_management_kind VARCHAR(2),
    is_shop_terminal NUMERIC(4,0) NOT NULL,
    allow_transfer_group_cd VARCHAR(5),
    main_brand_cd VARCHAR(5) NOT NULL,
    disc_round_position NUMERIC(4,0) NOT NULL,
    disc_round_kind NUMERIC(4,0) NOT NULL,
    is_emp_salse_place NUMERIC(4,0) NOT NULL,
    is_move_operation NUMERIC(4,0) NOT NULL,
    is_sales_register NUMERIC(4,0) NOT NULL,
    is_stock_display NUMERIC(4,0) NOT NULL,
    brock_cd VARCHAR(20),
    pricechange_disc_round_position NUMERIC(4,0),
    pricechange_disc_round_kind NUMERIC(4,0),
    numeric_reserve1 NUMERIC(11,0),
    numeric_reserve2 NUMERIC(11,0),
    numeric_reserve3 NUMERIC(11,0),
    numeric_reserve4 NUMERIC(11,0),
    numeric_reserve5 NUMERIC(11,0),
    string_reserve1 VARCHAR(20),
    string_reserve2 VARCHAR(20),
    string_reserve3 VARCHAR(20),
    string_reserve4 VARCHAR(20),
    string_reserve5 VARCHAR(20),
    is_deleted NUMERIC(4,0),
    spare_numeric_reserve1 NUMERIC(11,0),
    spare_numeric_reserve2 NUMERIC(11,0),
    spare_numeric_reserve3 NUMERIC(11,0),
    apare_numeric_reserve4 NUMERIC(11,0),
    spare_numeric_reserve5 NUMERIC(11,0),
    spare_numeric_reserve6 NUMERIC(11,0),
    spare_numeric_reserve7 NUMERIC(11,0),
    spare_numeric_reserve8 NUMERIC(11,0),
    spare_numeric_reserve9 NUMERIC(11,0),
    spare_numeric_reserve10 NUMERIC(11,0),
    spare_numeric_reserve11 NUMERIC(11,0),
    spare_numeric_reserve12 NUMERIC(11,0),
    spare_numeric_reserve13 NUMERIC(11,0),
    spare_numeric_reserve14 NUMERIC(11,0),
    spare_numeric_reserve15 NUMERIC(11,0),
    spare_numeric_reserve16 NUMERIC(11,0),
    spare_numeric_reserve17 NUMERIC(11,0),
    spare_numeric_reserve18 NUMERIC(11,0),
    spare_numeric_reserve19 NUMERIC(11,0),
    spare_numeric_reserve20 NUMERIC(11,0),
    spare_numeric_reserve21 NUMERIC(11,0),
    spare_numeric_reserve22 NUMERIC(11,0),
    spare_numeric_reserve23 NUMERIC(11,0),
    spare_numeric_reserve24 NUMERIC(11,0),
    spare_numeric_reserve25 NUMERIC(11,0),
    spare_numeric_reserve26 NUMERIC(11,0),
    spare_numeric_reserve27 NUMERIC(11,0),
    spare_numeric_reserve28 NUMERIC(11,0),
    spare_numeric_reserve29 NUMERIC(11,0),
    spare_numeric_reserve30 NUMERIC(11,0),
    spare_string_reserve1 VARCHAR(255),
    spare_string_reserve2 VARCHAR(255),
    spare_string_reserve3 VARCHAR(255),
    spare_string_reserve4 VARCHAR(255),
    spare_string_reserve5 VARCHAR(255),
    spare_string_reserve6 VARCHAR(255),
    spare_string_reserve7 VARCHAR(255),
    spare_string_reserve8 VARCHAR(255),
    spare_string_reserve9 VARCHAR(255),
    spare_string_reserve10 VARCHAR(255),
    spare_string_reserve11 VARCHAR(255),
    spare_string_reserve12 VARCHAR(255),
    spare_string_reserve13 VARCHAR(255),
    spare_string_reserve14 VARCHAR(255),
    spare_string_reserve15 VARCHAR(255),
    spare_string_reserve16 VARCHAR(255),
    spare_string_reserve17 VARCHAR(255),
    spare_string_reserve18 VARCHAR(255),
    spare_string_reserve19 VARCHAR(255),
    spare_string_reserve20 VARCHAR(255),
    spare_string_reserve21 VARCHAR(255),
    spare_string_reserve22 VARCHAR(255),
    spare_string_reserve23 VARCHAR(255),
    spare_string_reserve24 VARCHAR(255),
    spare_string_reserve25 VARCHAR(255),
    spare_string_reserve26 VARCHAR(255),
    spare_string_reserve27 VARCHAR(255),
    spare_string_reserve28 VARCHAR(255),
    spare_string_reserve29 VARCHAR(255),
    spare_string_reserve30 VARCHAR(255),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_cd)
);
COMMENT ON TABLE shop_mst IS '店舗マスタ';
COMMENT ON COLUMN shop_mst.shop_cd IS '店舗コード';
COMMENT ON COLUMN shop_mst.group_cd IS '組織コード';
COMMENT ON COLUMN shop_mst.area_cd IS 'エリアコード';
COMMENT ON COLUMN shop_mst.district_cd IS 'ディストリクトコード';
COMMENT ON COLUMN shop_mst.shop_kind IS '店舗区分';
COMMENT ON COLUMN shop_mst.shop_name_full IS '店舗名全角';
COMMENT ON COLUMN shop_mst.shop_name_half IS '店舗名半角';
COMMENT ON COLUMN shop_mst.shop_name_short IS '店舗名略称';
COMMENT ON COLUMN shop_mst.shop_name_english IS '店舗名英字';
COMMENT ON COLUMN shop_mst.zip_cd IS '郵便番号';
COMMENT ON COLUMN shop_mst.prefecture_cd IS '都道府県コード';
COMMENT ON COLUMN shop_mst.address1 IS '住所1';
COMMENT ON COLUMN shop_mst.address2 IS '住所2';
COMMENT ON COLUMN shop_mst.address3 IS '住所3';
COMMENT ON COLUMN shop_mst.address4 IS '住所4';
COMMENT ON COLUMN shop_mst.tel_num IS '電話番号';
COMMENT ON COLUMN shop_mst.fax_num IS 'Fax番号';
COMMENT ON COLUMN shop_mst.tax_inclusive_round_kind IS '内税端数処理区分';
COMMENT ON COLUMN shop_mst.tax_exclusive_round_kind IS '外税端数処理区分';
COMMENT ON COLUMN shop_mst.condition_kind IS '業態区分';
COMMENT ON COLUMN shop_mst.condition_detail_kind IS '業態詳細区分';
COMMENT ON COLUMN shop_mst.channel_kind IS 'チャネル区分';
COMMENT ON COLUMN shop_mst.is_stock_control IS '在庫管理フラグ';
COMMENT ON COLUMN shop_mst.begin_date IS '開店日';
COMMENT ON COLUMN shop_mst.end_date IS '閉店日';
COMMENT ON COLUMN shop_mst.shop_floor_space IS '店舗坪数';
COMMENT ON COLUMN shop_mst.is_abandon_price_change IS '売価変更禁止フラグ';
COMMENT ON COLUMN shop_mst.is_abandon_stock_transfer IS '在庫振替禁止フラグ';
COMMENT ON COLUMN shop_mst.form_management_kind IS '運営形態区分';
COMMENT ON COLUMN shop_mst.is_shop_terminal IS '店舗端末導入区分';
COMMENT ON COLUMN shop_mst.allow_transfer_group_cd IS '移動可能グループコード';
COMMENT ON COLUMN shop_mst.main_brand_cd IS '主幹ブランド';
COMMENT ON COLUMN shop_mst.disc_round_position IS '下代端数桁';
COMMENT ON COLUMN shop_mst.disc_round_kind IS '下代端数処理区分';
COMMENT ON COLUMN shop_mst.is_emp_salse_place IS '社販場所フラグ';
COMMENT ON COLUMN shop_mst.is_move_operation IS '入荷業務フラグ';
COMMENT ON COLUMN shop_mst.is_sales_register IS '基幹売上登録可否フラグ';
COMMENT ON COLUMN shop_mst.is_stock_display IS '在庫検索公開フラグ';
COMMENT ON COLUMN shop_mst.brock_cd IS 'ブロックコード';
COMMENT ON COLUMN shop_mst.pricechange_disc_round_position IS '売価変更端数処理位置';
COMMENT ON COLUMN shop_mst.pricechange_disc_round_kind IS '売価変更端数処理区分';
COMMENT ON COLUMN shop_mst.numeric_reserve1 IS '数値リザーブ1';
COMMENT ON COLUMN shop_mst.numeric_reserve2 IS '数値リザーブ2';
COMMENT ON COLUMN shop_mst.numeric_reserve3 IS '数値リザーブ3';
COMMENT ON COLUMN shop_mst.numeric_reserve4 IS '数値リザーブ4';
COMMENT ON COLUMN shop_mst.numeric_reserve5 IS '数値リザーブ5';
COMMENT ON COLUMN shop_mst.string_reserve1 IS '文字列リザーブ1';
COMMENT ON COLUMN shop_mst.string_reserve2 IS '文字列リザーブ2';
COMMENT ON COLUMN shop_mst.string_reserve3 IS '文字列リザーブ3';
COMMENT ON COLUMN shop_mst.string_reserve4 IS '文字列リザーブ4';
COMMENT ON COLUMN shop_mst.string_reserve5 IS '文字列リザーブ5';
COMMENT ON COLUMN shop_mst.is_deleted IS '削除済区分';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve1 IS '（予備）数値リザーブ1';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve2 IS '（予備）数値リザーブ2';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve3 IS '（予備）数値リザーブ3';
COMMENT ON COLUMN shop_mst.apare_numeric_reserve4 IS '（予備）数値リザーブ4';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve5 IS '（予備）数値リザーブ5';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve6 IS '（予備）数値リザーブ6';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve7 IS '（予備）数値リザーブ7';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve8 IS '（予備）数値リザーブ8';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve9 IS '（予備）数値リザーブ9';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve10 IS '（予備）数値リザーブ10';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve11 IS '（予備）数値リザーブ11';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve12 IS '（予備）数値リザーブ12';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve13 IS '（予備）数値リザーブ13';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve14 IS '（予備）数値リザーブ14';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve15 IS '（予備）数値リザーブ15';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve16 IS '（予備）数値リザーブ16';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve17 IS '（予備）数値リザーブ17';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve18 IS '（予備）数値リザーブ18';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve19 IS '（予備）数値リザーブ19';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve20 IS '（予備）数値リザーブ20';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve21 IS '（予備）数値リザーブ21';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve22 IS '（予備）数値リザーブ22';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve23 IS '（予備）数値リザーブ23';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve24 IS '（予備）数値リザーブ24';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve25 IS '（予備）数値リザーブ25';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve26 IS '（予備）数値リザーブ26';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve27 IS '（予備）数値リザーブ27';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve28 IS '（予備）数値リザーブ28';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve29 IS '（予備）数値リザーブ29';
COMMENT ON COLUMN shop_mst.spare_numeric_reserve30 IS '（予備）数値リザーブ30';
COMMENT ON COLUMN shop_mst.spare_string_reserve1 IS '（予備）文字列リザーブ1';
COMMENT ON COLUMN shop_mst.spare_string_reserve2 IS '（予備）文字列リザーブ2';
COMMENT ON COLUMN shop_mst.spare_string_reserve3 IS '（予備）文字列リザーブ3';
COMMENT ON COLUMN shop_mst.spare_string_reserve4 IS '（予備）文字列リザーブ4';
COMMENT ON COLUMN shop_mst.spare_string_reserve5 IS '（予備）文字列リザーブ5';
COMMENT ON COLUMN shop_mst.spare_string_reserve6 IS '（予備）文字列リザーブ6';
COMMENT ON COLUMN shop_mst.spare_string_reserve7 IS '（予備）文字列リザーブ7';
COMMENT ON COLUMN shop_mst.spare_string_reserve8 IS '（予備）文字列リザーブ8';
COMMENT ON COLUMN shop_mst.spare_string_reserve9 IS '（予備）文字列リザーブ9';
COMMENT ON COLUMN shop_mst.spare_string_reserve10 IS '（予備）文字列リザーブ10';
COMMENT ON COLUMN shop_mst.spare_string_reserve11 IS '（予備）文字列リザーブ11';
COMMENT ON COLUMN shop_mst.spare_string_reserve12 IS '（予備）文字列リザーブ12';
COMMENT ON COLUMN shop_mst.spare_string_reserve13 IS '（予備）文字列リザーブ13';
COMMENT ON COLUMN shop_mst.spare_string_reserve14 IS '（予備）文字列リザーブ14';
COMMENT ON COLUMN shop_mst.spare_string_reserve15 IS '（予備）文字列リザーブ15';
COMMENT ON COLUMN shop_mst.spare_string_reserve16 IS '（予備）文字列リザーブ16';
COMMENT ON COLUMN shop_mst.spare_string_reserve17 IS '（予備）文字列リザーブ17';
COMMENT ON COLUMN shop_mst.spare_string_reserve18 IS '（予備）文字列リザーブ18';
COMMENT ON COLUMN shop_mst.spare_string_reserve19 IS '（予備）文字列リザーブ19';
COMMENT ON COLUMN shop_mst.spare_string_reserve20 IS '（予備）文字列リザーブ20';
COMMENT ON COLUMN shop_mst.spare_string_reserve21 IS '（予備）文字列リザーブ21';
COMMENT ON COLUMN shop_mst.spare_string_reserve22 IS '（予備）文字列リザーブ22';
COMMENT ON COLUMN shop_mst.spare_string_reserve23 IS '（予備）文字列リザーブ23';
COMMENT ON COLUMN shop_mst.spare_string_reserve24 IS '（予備）文字列リザーブ24';
COMMENT ON COLUMN shop_mst.spare_string_reserve25 IS '（予備）文字列リザーブ25';
COMMENT ON COLUMN shop_mst.spare_string_reserve26 IS '（予備）文字列リザーブ26';
COMMENT ON COLUMN shop_mst.spare_string_reserve27 IS '（予備）文字列リザーブ27';
COMMENT ON COLUMN shop_mst.spare_string_reserve28 IS '（予備）文字列リザーブ28';
COMMENT ON COLUMN shop_mst.spare_string_reserve29 IS '（予備）文字列リザーブ29';
COMMENT ON COLUMN shop_mst.spare_string_reserve30 IS '（予備）文字列リザーブ30';
COMMENT ON COLUMN shop_mst.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN shop_mst.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN shop_mst.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN shop_mst.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN shop_mst.d_version IS 'デ連バージョン';

