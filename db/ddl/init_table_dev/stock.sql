CREATE TABLE stock (
    shop_code VARCHAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    allocated_warehouse_code VARCHAR(6) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    wms_stock_quantity NUMERIC(8,0),
    stock_quantity NUMERIC(8,0) NOT NULL,
    allocated_quantity NUMERIC(8,0) NOT NULL,
    reserved_quantity NUMERIC(8,0) NOT NULL,
    temporary_allocated_quantity NUMERIC(8,0) NOT NULL,
    arrival_reserved_quantity NUMERIC(8,0) NOT NULL,
    temporary_reserved_quantity NUMERIC(8,0) NOT NULL,
    reservation_limit NUMERIC(8,0),
    stock_threshold NUMERIC(8,0) NOT NULL,
    stock_arrival_date TIMESTAMP(0),
    arrival_quantity NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (shop_code, sku_code, allocated_warehouse_code)
);
COMMENT ON TABLE stock IS '在庫';
COMMENT ON COLUMN stock.shop_code IS 'ショップコード';
COMMENT ON COLUMN stock.sku_code IS 'SKUコード';
COMMENT ON COLUMN stock.allocated_warehouse_code IS '引当倉庫コード';
COMMENT ON COLUMN stock.commodity_code IS '商品コード';
COMMENT ON COLUMN stock.wms_stock_quantity IS 'WMS在庫数量';
COMMENT ON COLUMN stock.stock_quantity IS '在庫数量';
COMMENT ON COLUMN stock.allocated_quantity IS '引当数量';
COMMENT ON COLUMN stock.reserved_quantity IS '予約数量';
COMMENT ON COLUMN stock.temporary_allocated_quantity IS '仮引当数量';
COMMENT ON COLUMN stock.arrival_reserved_quantity IS '入荷予定予約数量';
COMMENT ON COLUMN stock.temporary_reserved_quantity IS '仮予約数量';
COMMENT ON COLUMN stock.reservation_limit IS '予約上限数';
COMMENT ON COLUMN stock.stock_threshold IS '安全在庫';
COMMENT ON COLUMN stock.stock_arrival_date IS '入荷予定日';
COMMENT ON COLUMN stock.arrival_quantity IS '入荷予定数';
COMMENT ON COLUMN stock.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN stock.created_user IS '作成ユーザ';
COMMENT ON COLUMN stock.created_datetime IS '作成日時';
COMMENT ON COLUMN stock.updated_user IS '更新ユーザ';
COMMENT ON COLUMN stock.updated_datetime IS '更新日時';
COMMENT ON COLUMN stock.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN stock.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN stock.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN stock.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN stock.d_version IS 'デ連バージョン';

