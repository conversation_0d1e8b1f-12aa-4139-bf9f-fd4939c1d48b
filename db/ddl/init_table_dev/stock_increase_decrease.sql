CREATE TABLE stock_increase_decrease (
    wms_stock_io_type VARCHAR(11) NOT NULL,
    slip_date TIMESTAMP NOT NULL,
    slip_no VARCHAR(13) NOT NULL,
    sh_control_number VARCHAR(25) NOT NULL,
    quantity NUMERIC(13,3) NOT NULL,
    stock_io_type NUMERIC(1,0) NOT NULL,
    center_code VARCHAR(4) NOT NULL,
    stock_type NUMERIC(1,0) NOT NULL,
    swh_cmns_target_shop VARCHAR(3),
    center_code_partner VARCHAR(4),
    stock_type_partner VARCHAR(1),
    swh_counter VARCHAR(3),
    receiving_shipping VARCHAR(13),
    receiving_shipping_name VA<PERSON>HAR(40),
    reason_code_items VARCHAR(3),
    reason_code_name VARCHAR(40),
    reason_code_item VARCHAR(50),
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (slip_no, sh_control_number)
);
COMMENT ON TABLE stock_increase_decrease IS '在庫増減';
COMMENT ON COLUMN stock_increase_decrease.wms_stock_io_type IS 'ロジマネ入出庫区分';
COMMENT ON COLUMN stock_increase_decrease.slip_date IS '伝票日付';
COMMENT ON COLUMN stock_increase_decrease.slip_no IS '伝票番号';
COMMENT ON COLUMN stock_increase_decrease.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN stock_increase_decrease.quantity IS '数量';
COMMENT ON COLUMN stock_increase_decrease.stock_io_type IS '入出庫区分';
COMMENT ON COLUMN stock_increase_decrease.center_code IS 'センターコード';
COMMENT ON COLUMN stock_increase_decrease.stock_type IS '在庫区分';
COMMENT ON COLUMN stock_increase_decrease.swh_cmns_target_shop IS '確保倉庫（Commons対象店舗）';
COMMENT ON COLUMN stock_increase_decrease.center_code_partner IS 'センターコード（相手）';
COMMENT ON COLUMN stock_increase_decrease.stock_type_partner IS '在庫区分（相手）';
COMMENT ON COLUMN stock_increase_decrease.swh_counter IS '確保倉庫（相手）';
COMMENT ON COLUMN stock_increase_decrease.receiving_shipping IS '入出荷先';
COMMENT ON COLUMN stock_increase_decrease.receiving_shipping_name IS '入出荷先名称';
COMMENT ON COLUMN stock_increase_decrease.reason_code_items IS '事由コード';
COMMENT ON COLUMN stock_increase_decrease.reason_code_name IS '事由コード名称';
COMMENT ON COLUMN stock_increase_decrease.reason_code_item IS '事由コード内訳';
COMMENT ON COLUMN stock_increase_decrease.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN stock_increase_decrease.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN stock_increase_decrease.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN stock_increase_decrease.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN stock_increase_decrease.d_version IS 'デ連バージョン';

