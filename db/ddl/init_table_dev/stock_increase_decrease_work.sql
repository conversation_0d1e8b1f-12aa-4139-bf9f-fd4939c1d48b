CREATE TABLE stock_increase_decrease_work (
    wms_stock_io_type VARCHAR(11) NOT NULL,
    slip_date TIMESTAMP NOT NULL,
    slip_no VARCHAR(13) NOT NULL,
    sh_control_number VARCHAR(25) NOT NULL,
    quantity NUMERIC(13,3) NOT NULL,
    stock_io_type NUMERIC(1,0) NOT NULL,
    center_code VARCHAR(4) NOT NULL,
    stock_type NUMERIC(1,0) NOT NULL,
    swh_cmns_target_shop VARCHAR(3),
    center_code_partner VARCHAR(4),
    stock_type_partner VARCHAR(1),
    swh_counter VARCHAR(3),
    receiving_shipping VARCHAR(13),
    receiving_shipping_name VARCHAR(40),
    reason_code_items VARCHAR(3),
    reason_code_name VARCHAR(40),
    reason_code_item VARCHAR(50),
    PRIMARY KEY (slip_no, sh_control_number)
);
COMMENT ON TABLE stock_increase_decrease_work IS '在庫増減ワーク';
COMMENT ON COLUMN stock_increase_decrease_work.wms_stock_io_type IS 'ロジマネ入出庫区分';
COMMENT ON COLUMN stock_increase_decrease_work.slip_date IS '伝票日付';
COMMENT ON COLUMN stock_increase_decrease_work.slip_no IS '伝票番号';
COMMENT ON COLUMN stock_increase_decrease_work.sh_control_number IS '倉庫管理番号';
COMMENT ON COLUMN stock_increase_decrease_work.quantity IS '数量';
COMMENT ON COLUMN stock_increase_decrease_work.stock_io_type IS '入出庫区分';
COMMENT ON COLUMN stock_increase_decrease_work.center_code IS 'センターコード';
COMMENT ON COLUMN stock_increase_decrease_work.stock_type IS '在庫区分';
COMMENT ON COLUMN stock_increase_decrease_work.swh_cmns_target_shop IS '確保倉庫（Commons対象店舗）';
COMMENT ON COLUMN stock_increase_decrease_work.center_code_partner IS 'センターコード（相手）';
COMMENT ON COLUMN stock_increase_decrease_work.stock_type_partner IS '在庫区分（相手）';
COMMENT ON COLUMN stock_increase_decrease_work.swh_counter IS '確保倉庫（相手）';
COMMENT ON COLUMN stock_increase_decrease_work.receiving_shipping IS '入出荷先';
COMMENT ON COLUMN stock_increase_decrease_work.receiving_shipping_name IS '入出荷先名称';
COMMENT ON COLUMN stock_increase_decrease_work.reason_code_items IS '事由コード';
COMMENT ON COLUMN stock_increase_decrease_work.reason_code_name IS '事由コード名称';
COMMENT ON COLUMN stock_increase_decrease_work.reason_code_item IS '事由コード内訳';

