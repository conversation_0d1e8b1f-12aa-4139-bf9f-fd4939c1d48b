CREATE TABLE stock_work (
    shop_code VARCHAR(16) NOT NULL,
    sku_code VARCHAR(24) NOT NULL,
    allocated_warehouse_code VARCHAR(6) NOT NULL,
    commodity_code VARCHAR(16) NOT NULL,
    wms_stock_quantity NUMERIC(8,0),
    stock_quantity NUMERIC(8,0) NOT NULL,
    allocated_quantity NUMERIC(8,0) NOT NULL,
    reserved_quantity NUMERIC(8,0) NOT NULL,
    temporary_allocated_quantity NUMERIC(8,0) NOT NULL,
    arrival_reserved_quantity NUMERIC(8,0) NOT NULL,
    temporary_reserved_quantity NUMERIC(8,0) NOT NULL,
    reservation_limit NUMERIC(8,0),
    stock_threshold NUMERIC(8,0) NOT NULL,
    stock_arrival_date TIMESTAMP(0),
    arrival_quantity NUMERIC(8,0),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (shop_code, sku_code, allocated_warehouse_code)
);
COMMENT ON TABLE stock_work IS '在庫';
COMMENT ON COLUMN stock_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN stock_work.sku_code IS 'SKUコード';
COMMENT ON COLUMN stock_work.allocated_warehouse_code IS '引当倉庫コード';
COMMENT ON COLUMN stock_work.commodity_code IS '商品コード';
COMMENT ON COLUMN stock_work.wms_stock_quantity IS 'WMS在庫数量';
COMMENT ON COLUMN stock_work.stock_quantity IS '在庫数量';
COMMENT ON COLUMN stock_work.allocated_quantity IS '引当数量';
COMMENT ON COLUMN stock_work.reserved_quantity IS '予約数量';
COMMENT ON COLUMN stock_work.temporary_allocated_quantity IS '仮引当数量';
COMMENT ON COLUMN stock_work.arrival_reserved_quantity IS '入荷予定予約数量';
COMMENT ON COLUMN stock_work.temporary_reserved_quantity IS '仮予約数量';
COMMENT ON COLUMN stock_work.reservation_limit IS '予約上限数';
COMMENT ON COLUMN stock_work.stock_threshold IS '安全在庫';
COMMENT ON COLUMN stock_work.stock_arrival_date IS '入荷予定日';
COMMENT ON COLUMN stock_work.arrival_quantity IS '入荷予定数';
COMMENT ON COLUMN stock_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN stock_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN stock_work.created_datetime IS '作成日時';
COMMENT ON COLUMN stock_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN stock_work.updated_datetime IS '更新日時';

