CREATE TABLE store_stock_alignment_diff (
    company_cd VARCHAR(4) NOT NULL,
    store_cd VARCHAR(16) NOT NULL,
    warehouse_management_no VARCHAR(60) NOT NULL,
    stock_quantity NUMERIC(10,0) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (company_cd, store_cd, warehouse_management_no, stock_quantity)
);
COMMENT ON TABLE store_stock_alignment_diff IS '店舗在庫連携（差分）';
COMMENT ON COLUMN store_stock_alignment_diff.company_cd IS '会社コード';
COMMENT ON COLUMN store_stock_alignment_diff.store_cd IS '店舗コード';
COMMENT ON COLUMN store_stock_alignment_diff.warehouse_management_no IS '倉庫管理番号';
COMMENT ON COLUMN store_stock_alignment_diff.stock_quantity IS '在庫数量';
COMMENT ON COLUMN store_stock_alignment_diff.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN store_stock_alignment_diff.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN store_stock_alignment_diff.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN store_stock_alignment_diff.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN store_stock_alignment_diff.d_version IS 'デ連バージョン';

