CREATE TABLE store_stock_alignment_diff_work (
    company_cd VARCHAR(4) NOT NULL,
    store_cd VARCHAR(16) NOT NULL,
    warehouse_management_no VARCHAR(60) NOT NULL,
    stock_quantity NUMERIC(10,0) NOT NULL,
    PRIMARY KEY (company_cd, store_cd, warehouse_management_no, stock_quantity)
);
COMMENT ON TABLE store_stock_alignment_diff_work IS '店舗在庫連携（差分）ワーク';
COMMENT ON COLUMN store_stock_alignment_diff_work.company_cd IS '会社コード';
COMMENT ON COLUMN store_stock_alignment_diff_work.store_cd IS '店舗コード';
COMMENT ON COLUMN store_stock_alignment_diff_work.warehouse_management_no IS '倉庫管理番号';
COMMENT ON COLUMN store_stock_alignment_diff_work.stock_quantity IS '在庫数量';

