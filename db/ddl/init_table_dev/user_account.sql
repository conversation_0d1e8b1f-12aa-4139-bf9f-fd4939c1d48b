CREATE TABLE user_account (
    user_code NUMERIC(38,0) NOT NULL,
    shop_code VA<PERSON>HAR(16) NOT NULL,
    user_login_id VARCHAR(20) NOT NULL,
    password VARCHAR(128) NOT NULL,
    user_name VARCHAR(20) NOT NULL,
    email VARCHAR(256),
    login_error_count NUMERIC(10,0) NOT NULL,
    login_locked_flg NUMERIC(1,0) NOT NULL,
    login_datetime TIMESTAMP(0),
    memo VARCHAR(200),
    password_last_updated_datetime TIMESTAMP(0),
    auth_secret_key VARCHAR(128),
    role_id VARCHAR(8) NOT NULL,
    login_token VARCHAR(100),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    d_created_user VARCHAR(100) NOT NULL,
    d_created_datetime TIMESTAMP NOT NULL,
    d_updated_user VARCHAR(100) NOT NULL,
    d_updated_datetime TIMESTAMP NOT NULL,
    d_version NUMERIC(8,0) NOT NULL,
    PRIMARY KEY (user_code)
);
COMMENT ON TABLE user_account IS '管理ユーザ';
COMMENT ON COLUMN user_account.user_code IS 'ユーザコード';
COMMENT ON COLUMN user_account.shop_code IS 'ショップコード';
COMMENT ON COLUMN user_account.user_login_id IS 'ユーザログインID';
COMMENT ON COLUMN user_account.password IS 'パスワード';
COMMENT ON COLUMN user_account.user_name IS 'ユーザ名';
COMMENT ON COLUMN user_account.email IS 'メールアドレス';
COMMENT ON COLUMN user_account.login_error_count IS 'ログイン失敗回数';
COMMENT ON COLUMN user_account.login_locked_flg IS 'ログインロックフラグ';
COMMENT ON COLUMN user_account.login_datetime IS 'ログイン日時';
COMMENT ON COLUMN user_account.memo IS 'メモ';
COMMENT ON COLUMN user_account.password_last_updated_datetime IS 'パスワード最終更新日時';
COMMENT ON COLUMN user_account.auth_secret_key IS '認証シークレットキー';
COMMENT ON COLUMN user_account.role_id IS 'ロールID';
COMMENT ON COLUMN user_account.login_token IS 'ログイントークン';
COMMENT ON COLUMN user_account.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN user_account.created_user IS '作成ユーザ';
COMMENT ON COLUMN user_account.created_datetime IS '作成日時';
COMMENT ON COLUMN user_account.updated_user IS '更新ユーザ';
COMMENT ON COLUMN user_account.updated_datetime IS '更新日時';
COMMENT ON COLUMN user_account.d_created_user IS 'デ連登録ユーザ';
COMMENT ON COLUMN user_account.d_created_datetime IS 'デ連登録日時';
COMMENT ON COLUMN user_account.d_updated_user IS 'デ連更新ユーザ';
COMMENT ON COLUMN user_account.d_updated_datetime IS 'デ連更新日時';
COMMENT ON COLUMN user_account.d_version IS 'デ連バージョン';

