CREATE TABLE user_account_work (
    user_code NUMERIC(38,0) NOT NULL,
    shop_code VA<PERSON>HAR(16) NOT NULL,
    user_login_id VARCHAR(20) NOT NULL,
    password VARCHAR(128) NOT NULL,
    user_name VARCHAR(20) NOT NULL,
    email VARCHAR(256),
    login_error_count NUMERIC(10,0) NOT NULL,
    login_locked_flg NUMERIC(1,0) NOT NULL,
    login_datetime TIMESTAMP(0),
    memo VARCHAR(200),
    password_last_updated_datetime TIMESTAMP(0),
    auth_secret_key VARCHAR(128),
    role_id VARCHAR(8) NOT NULL,
    login_token VARCHAR(100),
    orm_rowid NUMERIC(38,0) NOT NULL,
    created_user VARCHAR(100) NOT NULL,
    created_datetime TIMESTAMP(3) NOT NULL,
    updated_user VARCHAR(100) NOT NULL,
    updated_datetime TIMESTAMP(3) NOT NULL,
    PRIMARY KEY (user_code)
);
COMMENT ON TABLE user_account_work IS '管理ユーザワーク';
COMMENT ON COLUMN user_account_work.user_code IS 'ユーザコード';
COMMENT ON COLUMN user_account_work.shop_code IS 'ショップコード';
COMMENT ON COLUMN user_account_work.user_login_id IS 'ユーザログインID';
COMMENT ON COLUMN user_account_work.password IS 'パスワード';
COMMENT ON COLUMN user_account_work.user_name IS 'ユーザ名';
COMMENT ON COLUMN user_account_work.email IS 'メールアドレス';
COMMENT ON COLUMN user_account_work.login_error_count IS 'ログイン失敗回数';
COMMENT ON COLUMN user_account_work.login_locked_flg IS 'ログインロックフラグ';
COMMENT ON COLUMN user_account_work.login_datetime IS 'ログイン日時';
COMMENT ON COLUMN user_account_work.memo IS 'メモ';
COMMENT ON COLUMN user_account_work.password_last_updated_datetime IS 'パスワード最終更新日時';
COMMENT ON COLUMN user_account_work.auth_secret_key IS '認証シークレットキー';
COMMENT ON COLUMN user_account_work.role_id IS 'ロールID';
COMMENT ON COLUMN user_account_work.login_token IS 'ログイントークン';
COMMENT ON COLUMN user_account_work.orm_rowid IS 'データ行ID';
COMMENT ON COLUMN user_account_work.created_user IS '作成ユーザ';
COMMENT ON COLUMN user_account_work.created_datetime IS '作成日時';
COMMENT ON COLUMN user_account_work.updated_user IS '更新ユーザ';
COMMENT ON COLUMN user_account_work.updated_datetime IS '更新日時';

