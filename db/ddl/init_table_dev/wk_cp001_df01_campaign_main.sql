CREATE TABLE wk_cp001_df01_campaign_main (
  campaign_instructions_code VARCHAR(16) NOT NULL
  , campaign_instructions_name VARCHAR(50) NOT NULL
  , enabled_flag VARCHAR(5) NOT NULL
  , campaign_start_date VARCHAR(29) NOT NULL
  , campaign_end_date VARCHAR(29) NOT NULL
  , customer_groups_group_id VARCHAR(25) NOT NULL
  , campaign_applied_scope VARCHAR(2) NOT NULL
  , split_num NUMERIC(2)
  , CONSTRAINT wk_cp001_df01_campaign_main_PKC PRIMARY KEY (campaign_instructions_code)
) ;

COMMENT ON TABLE wk_cp001_df01_campaign_main IS 'キャンペーンプロモーションのキャンペーン用ワークテーブル(メイン)';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.enabled_flag IS '有効フラグ';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.campaign_start_date IS 'キャンペーン適用開始日';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.campaign_end_date IS 'キャンペーン適用終了日';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.customer_groups_group_id IS '顧客グループID';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.campaign_applied_scope IS 'キャンペーン適用範囲';
COMMENT ON COLUMN wk_cp001_df01_campaign_main.split_num is '分割単位';