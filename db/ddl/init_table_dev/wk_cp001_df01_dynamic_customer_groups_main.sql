CREATE TABLE wk_cp001_df01_dynamic_customer_groups_main (
  campaign_instructions_code VARCHAR(16) NOT NULL
  , group_id VARCHAR(25) NOT NULL
  , campaign_instructions_name VARCHAR(50) NOT NULL
  , split_num NUMERIC(2)
  , CONSTRAINT wk_cp001_df01_dynamic_customer_groups_main_PKC PRIMARY KEY (campaign_instructions_code)
) ;

COMMENT ON TABLE wk_cp001_df01_dynamic_customer_groups_main IS '動的顧客グループ用ワークテーブル(メイン)';
COMMENT ON COLUMN wk_cp001_df01_dynamic_customer_groups_main.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_dynamic_customer_groups_main.group_id IS '顧客グループID';
COMMENT ON COLUMN wk_cp001_df01_dynamic_customer_groups_main.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN wk_cp001_df01_dynamic_customer_groups_main.split_num is '分割単位';