CREATE TABLE wk_cp001_df01_pricebook_main (
  campaign_instructions_code VARCHAR(16) NOT NULL
  , pricebook_id VARCHAR(37) NOT NULL
  , campaign_instructions_name VARCHAR(50) NOT NULL
  , campaign_start_date VARCHAR(29) NOT NULL
  , campaign_end_date VARCHAR(29) NOT NULL
  , single_product_flg VARCHAR(1) NOT NULL
  , split_num NUMERIC(2)
  , CONSTRAINT wk_cp001_df01_pricebook_main_PKC PRIMARY KEY (campaign_instructions_code)
) ;

COMMENT ON TABLE wk_cp001_df01_pricebook_main IS 'キャンペーン価格表用ワークテーブル(メイン)';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.pricebook_id IS '価格表ID';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.campaign_start_date IS 'キャンペーン適用開始日';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.campaign_end_date IS 'キャンペーン適用終了日';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.single_product_flg IS '単一商品フラグ';
COMMENT ON COLUMN wk_cp001_df01_pricebook_main.split_num is '分割単位';