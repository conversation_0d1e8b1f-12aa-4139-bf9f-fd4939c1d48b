CREATE TABLE wk_cp001_df01_product_campaign_flag_list (
  campaign_instructions_code VARCHAR(16) NOT NULL
  , preferential_product_flg VARCHAR(1)
  , single_product_flg VARCHAR(1)
  , CONSTRAINT wk_cp001_df01_product_campaign_flag_list_PKC PRIMARY KEY (campaign_instructions_code)
) ;

COMMENT ON TABLE wk_cp001_df01_product_campaign_flag_list IS 'キャンペーンの価格表出力のための属性を判定するテーブル';
COMMENT ON COLUMN wk_cp001_df01_product_campaign_flag_list.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_product_campaign_flag_list.preferential_product_flg IS '優待商品フラグ';
COMMENT ON COLUMN wk_cp001_df01_product_campaign_flag_list.single_product_flg IS '単一商品フラグ';