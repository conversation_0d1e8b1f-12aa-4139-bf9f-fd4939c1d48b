CREATE TABLE wk_cp001_df01_static_customer_groups_campaign_main (
  group_id VARCHAR(25) NOT NULL
  , campaign_instructions_name VARCHAR(50) NOT NULL
  , split_num NUMERIC(2)
  , CONSTRAINT wk_cp001_df01_static_customer_groups_campaign_main_PKC PRIMARY KEY (group_id)
) ;

COMMENT ON TABLE wk_cp001_df01_static_customer_groups_campaign_main IS '静的顧客グループのキャンペーン基本情報用ワークテーブル(メイン)';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_campaign_main.group_id IS '顧客グループID';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_campaign_main.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_campaign_main.split_num is '分割単位';