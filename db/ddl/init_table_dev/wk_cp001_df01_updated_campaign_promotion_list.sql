CREATE TABLE wk_cp001_df01_updated_campaign_promotion_list (
  campaign_instructions_code VARCHAR(16) NOT NULL
  , campaign_price_flg VARCHAR(1) NOT NULL
  , static_customer_flg VARCHAR(1) NOT NULL
  , dynamic_customer_flg VARCHAR(1) NOT NULL
  , CONSTRAINT wk_cp001_df01_updated_campaign_promotion_list_PKC PRIMARY KEY (campaign_instructions_code)
) ;

COMMENT ON TABLE wk_cp001_df01_updated_campaign_promotion_list IS '価格表、顧客グループに出力するキャンペーンかどうか判定するテーブル';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.campaign_price_flg IS '価格表フラグ';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.static_customer_flg IS '静的顧客フラグ';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.dynamic_customer_flg IS '動的顧客フラグ';