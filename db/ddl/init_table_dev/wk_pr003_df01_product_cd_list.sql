CREATE TABLE wk_pr003_df01_product_cd_list (
  MAIL_ORDER_PRODUCT_CD VARCHAR(16) NOT NULL,
  DATA_DIV VARCHAR(2) NULL,
  PRIMARY KEY (MAIL_ORDER_PRODUCT_CD)
);
COMMENT ON TABLE wk_pr003_df01_product_cd_list IS 'PR003_DF01のproduct_id抽出用ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_product_cd_list.MAIL_ORDER_PRODUCT_CD IS 'product_id';
COMMENT ON COLUMN wk_pr003_df01_product_cd_list.DATA_DIV IS '01:商品更新 02:価格更新 03:セット商品更新 04:定期便更新';