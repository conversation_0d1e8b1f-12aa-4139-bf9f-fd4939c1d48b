CREATE TABLE wk_pr003_df01_product_linkage_work (
    MDM_INTEGRATION_MANAGEMENT_CD INT8 NULL,
    MAIL_ORDER_PRODUCT_CD VARCHAR(16) NOT NULL,
    WEB_PRODUCT_NAME VARCHAR(200) NULL,
    tax_class_id INT4 NULL,
    REPRESENTATIVE_PRODUCT_CD VARCHAR(10) NULL,
    REGISTRATION_NAME VARCHAR(200) NULL,
    CONTENTS VARCHAR(6) NULL,
    CORE_DEPARTMENT VARCHAR(2) NULL,
    DEP VARCHAR(6) NULL,
    JAN VARCHAR(13) NULL,
    PRODUCT_SEGMENT VARCHAR(5) NULL,
    BUSINESS_SEGMENT VARCHAR(5) NULL,
    PRODUCT_CAT VARCHAR(5) NULL,
    LGROUP VARCHAR(2) NULL,
    MGROUP VARCHAR(2) NULL,
    SGROUP VARCHAR(2) NULL,
    DGROUP VARCHAR(2) NULL,
    PRODUCT_TYPE VARCHAR(2) NULL,
    WEB VARCHAR(1) NULL,
    ORDER_PER_ORDER_MAX INT4 NULL,
    WEIGHT NUMERIC(7, 2) NULL,
    COLOR_NAME VARCHAR(40) NULL,
    COLOR_CD VARCHAR(3) NULL,
    SIZE_NAME VARCHAR(40) NULL,
    SIZE_CD VARCHAR(3) NULL,
    MAIL_DELIVERY_FLG VARCHAR(1) NULL,
    NEKOPOSU_VOLUME_RATE INT4 NULL,
    OUTSIDE_HOME_RECEIVE_SERVICE_FLG VARCHAR(1) NULL,
    OUTSIDE_HOME_VOLUME_RATE INT4 NULL,
    COMPANY_SALES_BUY_FLG VARCHAR(1) NULL,
    SET_COMPOSITION_FLG VARCHAR(1) NULL,
    BEFORE_RENEWAL_PRODUCT_NO VARCHAR(200) NULL,
    SHAPE_NAME VARCHAR(40) NULL,
    SHAPE_CD VARCHAR(3) NULL,
    SEASON VARCHAR(15) NULL,
    USE_POINT_CNT INT4 NULL,
    SALES_CHANNEL_1_SALE_START_DATE TIMESTAMP NULL,
    SALES_CHANNEL_1_SALE_END_DATE TIMESTAMP NULL,
    PRODUCT_SERIES VARCHAR(5) NULL,
    SET_PRODUCT_FLG VARCHAR(1) NULL,
    PREFERENTIAL_PRODUCT_FLG VARCHAR(1) NULL,
    txInventoryProductID VARCHAR(10) NULL,
    BUTTOBI_SUBSC_BUNDLE_YN VARCHAR(1) NULL,
    CORE_PRODUCT_NAME VARCHAR(200) NULL,
    DATA_DIV VARCHAR(2) NULL,
    SPLIT_NUM NUMERIC(2),
    PRIMARY KEY (MAIL_ORDER_PRODUCT_CD)
);
COMMENT ON TABLE wk_pr003_df01_product_linkage_work IS 'PR003_DF01の商品情報を格納するワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mdm_integration_management_cd IS 'MDM統合管理コード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mail_order_product_cd IS '通販商品番号';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.web_product_name IS 'ＷＥＢ用商品名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.tax_class_id IS '税区分ID';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.representative_product_cd IS '代表商品コード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.registration_name IS '登録名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.contents IS 'コンテンツ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.core_department IS '基幹部門';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.dep IS '担当部署';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.jan IS 'JAN';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_segment IS '商品セグメント';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.business_segment IS '事業セグメント';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_cat IS '商品分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.lgroup IS '大分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mgroup IS '中分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.sgroup IS '小分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.dgroup IS '細分類';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_type IS '商品種別';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.web IS 'WEBサイト';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.order_per_order_max IS '注文毎注文上限数';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.weight IS '重さ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.color_name IS 'カラー名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.color_cd IS 'カラーコード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.size_name IS 'サイズ名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.size_cd IS 'サイズコード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.mail_delivery_flg IS 'メール便フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.nekoposu_volume_rate IS 'ネコポス体積率';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.outside_home_receive_service_flg IS '自宅外受取フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.outside_home_volume_rate IS '自宅外受取体積率';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.company_sales_buy_flg IS '社販購入可';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.set_composition_flg IS 'セット構成品フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.before_renewal_product_no IS 'リニューアル前商品番号';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.shape_name IS '形状名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.shape_cd IS '形状コード';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.season IS 'シーズン';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.use_point_cnt IS '利用ポイント数';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.sales_channel_1_sale_start_date IS '販路１販売開始日時';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.sales_channel_1_sale_end_date IS '販路１販売終了日時';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.product_series IS '商品シリーズ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.set_product_flg IS 'セット商品フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.preferential_product_flg IS '優待商品フラグ';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.txinventoryproductid IS '在庫商品ID';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.buttobi_subsc_bundle_yn IS 'ぶっとび定期便同梱可否';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.core_product_name IS '基幹商品名';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.data_div IS '01:抽出条件01 02:抽出条件02 03:抽出条件03 04:抽出条件04';
COMMENT ON COLUMN wk_pr003_df01_product_linkage_work.split_num IS '分割単位';