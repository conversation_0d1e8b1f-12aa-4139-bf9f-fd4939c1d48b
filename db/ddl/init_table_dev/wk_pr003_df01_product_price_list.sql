CREATE TABLE wk_pr003_df01_product_price_list (
  product_id VARCHAR(16)
  , product_div VARCHAR(2)
  , amount INTEGER
  , split_num NUMERIC(2)
  , CONSTRAINT wk_pr003_df01_product_price_list_PKC PRIMARY KEY (product_id)
) ;

COMMENT ON TABLE wk_pr003_df01_product_price_list IS 'PR003_DF01の価格情報を格納するワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.product_id IS 'product_id';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.product_div IS '01:通常 02:セット 03:定期便';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.amount IS 'amount';
COMMENT ON COLUMN wk_pr003_df01_product_price_list.split_num IS '分割単位';