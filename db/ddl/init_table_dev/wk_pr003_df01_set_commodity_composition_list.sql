CREATE TABLE wk_pr003_df01_set_commodity_composition_list (
  product_id VARCHAR(16) NOT NULL
  , child_commodity_code VARCHAR(16) NOT NULL
  , composition_quantity NUMERIC(2)
  , tax_exc INTEGER
  , CONSTRAINT wk_pr003_df01_set_commodity_composition_list_PKC PRIMARY KEY (product_id,child_commodity_code)
) ;

COMMENT ON TABLE wk_pr003_df01_set_commodity_composition_list IS 'PR003_DF01(価格表)のセット商品用ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.product_id IS 'product_id';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.child_commodity_code IS 'child_commodity_code';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.composition_quantity IS 'composition_quantity';
COMMENT ON COLUMN wk_pr003_df01_set_commodity_composition_list.tax_exc IS 'tax_exc';