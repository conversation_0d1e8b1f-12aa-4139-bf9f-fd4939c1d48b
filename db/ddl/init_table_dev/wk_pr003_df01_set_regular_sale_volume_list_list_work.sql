CREATE TABLE wk_pr003_df01_set_regular_sale_volume_list_list_work (
  MAIL_ORDER_PRODUCT_CD VARCHAR(16) NOT NULL,
  WEIGHT NUMERIC(7, 2) NULL,
  NEKOPOSU_VOLUME_RATE INT4 NULL,
  OUTSIDE_HOME_VOLUME_RATE INT4 NULL,
  PRIMARY KEY (MAIL_ORDER_PRODUCT_CD)
);
COMMENT ON TABLE wk_pr003_df01_set_regular_sale_volume_list_list_work IS 'PR003_DF01の定期便の重さ、ネコポス体積率、自宅外受取体積率計算ワークテーブル';
COMMENT ON COLUMN wk_pr003_df01_set_regular_sale_volume_list_list_work.MAIL_ORDER_PRODUCT_CD IS 'product_id';
COMMENT ON COLUMN wk_pr003_df01_set_regular_sale_volume_list_list_work.WEIGHT IS 'txWeight';
COMMENT ON COLUMN wk_pr003_df01_set_regular_sale_volume_list_list_work.NEKOPOSU_VOLUME_RATE IS 'txVolume';
COMMENT ON COLUMN wk_pr003_df01_set_regular_sale_volume_list_list_work.OUTSIDE_HOME_VOLUME_RATE IS 'txEntrustVolume';