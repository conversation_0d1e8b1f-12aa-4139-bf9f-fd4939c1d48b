CREATE TABLE wk_sl002_ff01_storeid (
  store_id VARCHAR(16) NOT NULL
  , allocation_timestamp VARCHAR(29) NOT NULL
  , split_num NUMERIC(2)
  , CONSTRAINT wk_sl002_ff01_storeid_PKC PRIMARY KEY (store_id)
) ;

COMMENT ON TABLE wk_sl002_ff01_storeid IS 'SL002_FF01(店舗在庫(差分))用ワークテーブル';
COMMENT ON COLUMN wk_sl002_ff01_storeid.store_id IS '店舗コード';
COMMENT ON COLUMN wk_sl002_ff01_storeid.allocation_timestamp IS '連携時の時刻';
COMMENT ON COLUMN wk_sl002_ff01_storeid.split_num is '分割単位';