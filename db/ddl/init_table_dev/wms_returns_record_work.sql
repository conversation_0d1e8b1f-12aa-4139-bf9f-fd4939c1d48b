CREATE TABLE wms_returns_record_work (
    KANRI_NO VARCHAR(13) NOT NULL,
    NYUKO_YMD VARCHAR(10) NOT NULL,
    JUCHU_NO VARCHAR(15) NOT NULL,
    HIN<PERSON>N_CD VARCHAR(25) NOT NULL,
    HENPIN_QT NUMERIC(15) NOT NULL,
    LETTER_FLG VARCHAR(1) NOT NULL,
    HENPIN_REASON_KBN VARCHAR(2) NOT NULL,
    RYOHIN_KBN VARCHAR(2) NOT NULL,
    HENPIN_BIKO VARCHAR(50)
);
COMMENT ON TABLE wms_returns_record_work IS 'WMS返品実績ワーク';
COMMENT ON COLUMN wms_returns_record_work.KANRI_NO IS '管理番号';
COMMENT ON COLUMN wms_returns_record_work.NYUKO_YMD IS '入庫日';
COMMENT ON COLUMN wms_returns_record_work.JUCHU_NO IS '受注番号';
COMMENT ON COLUMN wms_returns_record_work.HINBAN_CD IS '品番';
COMMENT ON COLUMN wms_returns_record_work.HENPIN_QT IS '数量';
COMMENT ON COLUMN wms_returns_record_work.LETTER_FLG IS '手紙有無';
COMMENT ON COLUMN wms_returns_record_work.HENPIN_REASON_KBN IS '返品理由';
COMMENT ON COLUMN wms_returns_record_work.RYOHIN_KBN IS '良品区分';
COMMENT ON COLUMN wms_returns_record_work.HENPIN_BIKO IS '備考欄';

