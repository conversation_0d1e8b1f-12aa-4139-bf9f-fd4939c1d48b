#!/bin/bash

# <PERSON>ript to run multiple SQL files on a PostgreSQL database
# Usage: ./run_sql_scripts.sh <db_name> <username> <password> <sql_folder>

# Check if required parameters are provided
if [ $# -ne 4 ]; then
    echo "Usage: $0 <db_name> <username> <password> <sql_folder>"
    exit 1
fi

DB_NAME=$1
USERNAME=$2
PASSWORD=$3
SQL_FOLDER=$4

# Check if the SQL folder exists
if [ ! -d "$SQL_FOLDER" ]; then
    echo "Error: SQL folder '$SQL_FOLDER' does not exist."
    exit 1
fi

# Export PostgreSQL password to avoid password prompt
export PGPASSWORD="$PASSWORD"

# Create a log file for failed SQL scripts
LOG_FILE="failed_sql_scripts.log"
> "$LOG_FILE"  # Clear the log file if it exists

# Process each SQL file in the folder
echo "Starting to process SQL files from '$SQL_FOLDER'..."
FAILED_COUNT=0

for SQL_FILE in "$SQL_FOLDER"/*.sql; do
    if [ -f "$SQL_FILE" ]; then
        echo "Processing: $SQL_FILE"
        
        # Run the SQL file
        psql -h localhost -p 15432 -U "$USERNAME" -d "$DB_NAME" -f "$SQL_FILE" 2>&1
        
        # Check if there was an error
        if [ $? -ne 0 ]; then
            echo "Error executing: $SQL_FILE" | tee -a "$LOG_FILE"
            echo "----------------------------------------" >> "$LOG_FILE"
            FAILED_COUNT=$((FAILED_COUNT + 1))
        else
            echo "Successfully executed: $SQL_FILE"
        fi
        
        echo "----------------------------------------"
    fi
done

# Unset the password variable for security
unset PGPASSWORD

# Display summary
echo ""
echo "Script execution completed."
echo "Total failed SQL scripts: $FAILED_COUNT"

if [ $FAILED_COUNT -gt 0 ]; then
    echo "Failed SQL scripts are listed in: $LOG_FILE"
    echo "Failed SQL files:"
    cat "$LOG_FILE"
else
    echo "All SQL scripts executed successfully."
fi

exit 0