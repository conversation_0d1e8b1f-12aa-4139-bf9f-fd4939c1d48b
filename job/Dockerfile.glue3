FROM amazonlinux:2023

# プロキシ設定（ビルド時に設定される場合）
ENV http_proxy=http://tkyproxy-std.intra.tis.co.jp:8080
ENV HTTP_PROXY=http://tkyproxy-std.intra.tis.co.jp:8080
ENV https_proxy=http://tkyproxy-std.intra.tis.co.jp:8080
ENV HTTPS_PROXY=http://tkyproxy-std.intra.tis.co.jp:8080
ENV no_proxy=127.0.0.1,localhost,s2105c10703-t1
ENV NO_PROXY=127.0.0.1,localhost,s2105c10703-t1
RUN mkdir -p /etc/dnf && echo "proxy=http://tkyproxy-std.intra.tis.co.jp:8080" >> /etc/dnf/dnf.conf
# Python3とビルドツールのインストール
RUN dnf install -y python3 python3-devel python3-pip gcc gcc-c++     make libffi-devel openssl-devel git findutils zip

# 作業ディレクトリ設定
WORKDIR /app
