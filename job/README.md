# Job プロジェクトのセットアップ

このドキュメントは、Job プロジェクト環境のセットアップ手順を提供する。

## 1. 前提条件

- Python 3.9
- pip 21.0以上

## 2. 環境構築

### 2.1. uvのインストール

`uv` をインストールする：

```sh
pip install uv
```

### 2.2. 仮想環境の作成と有効化

1. tis-dlpf-appディレクトリに移動：
```sh
cd /path/to/tis-dlpf-app
```

2. 仮想環境を作成：
```sh
uv venv job/.venv
```

3. 仮想環境を有効化：

```sh
source job/.venv/bin/activate
```

### 2.3. 依存関係のインストール

仮想環境を有効化した状態で、依存関係をインストール：

```sh
# システム依存のコンパイラとライブラリ（必要に応じて）
sudo apt-get update
sudo apt install gcc clang python3-dev libpq-dev

# プロジェクト依存関係のインストール（開発用パッケージを含む）
cd job
uv sync --all-groups --reinstall
```

## 3. 開発環境の確認

以下のコマンドで環境が正しく設定されているか確認：

```sh
python --version  # Python 3.9であることを確認
uv --version     # uvのバージョンを確認
```

## 4. ユニットテストの実行

ユニットテストの実行手順については、`job/README_ut.md` を参照。

## 5. AWS Glue用パッケージの作成

AWS Glue向けのWHLファイルおよび依存パッケージの作成手順については、別ドキュメントを参照してください：

👉 [AWS Glue用パッケージ作成手順](README_wheel.md)

## 6. 開発ツールの設定

### 6.1. コードフォーマットとリント

- black: コードフォーマッター
- flake8: コードリンター

設定ファイル：
- `.vscode/settings.json`: VSCode設定

### 5.2. コマンド例

```sh
# コードフォーマット
VsCodeにてソースファイル保存時に自動的適用される

# コードリント
VsCodeの「PROBLEMS」タブ（問題タブ）に問題一覧が自動的表示されて、
なくなるまで全て対応してください。
```

## 6. トラブルシューティング

1. uvのインストールエラー
   - Pythonバージョンの確認
   - pipの更新: `pip install --upgrade pip`

2. 依存関係のインストールエラー
   - 仮想環境が有効化されているか確認
   - `uv pip sync --upgrade` で強制更新

3. 環境の再構築
   ```sh
   rm -rf job/.venv
   uv venv job/.venv
   ```


