# 単体テストの実施手順書

## 目次
1. テスト環境のセットアップ
   1. Python仮想環境の構築
   2. 依存パッケージのインストール
   3. SQLファイル環境の構築
2. テストの実行
   1. テストの実行方法
   2. テストの種類と指定方法
3. テスト結果の分析
   1. カバレッジレポートの作成
   2. レポートの確認方法
4. 開発ツールの利用
   1. Python Test Explorerの設定
   2. トラブルシューティング

## 1. テスト環境のセットアップ

### 1.1. Python仮想環境の構築

単体テストは専用の仮想環境（.venv）内で実行します。

```sh
# 仮想環境の有効化
source job/.venv/bin/activate

# 確認方法：プロンプトの先頭に(.venv)が表示される
(.venv) user@host:~/workspaces/tis-dlpf-app/job$
```

### 1.2. 依存パッケージのインストール

テストに必要なパッケージをインストールします。

#### 1.2.1. 基本的なパッケージのインストール

```sh
# テスト用パッケージのインストール
uv sync --all-groups --reinstall
```

#### 1.2.2. 依存関係の追加（*初期環境構築時実行不要*）

新しいパッケージを追加する場合は、以下のコマンドを使用します：

# 本番環境用パッケージの追加
バージョン固定したいので、特定バージョンの指定が必須
```sh
uv add --optional-dependencies runtime "paramiko==3.5.1"
```
上記のコマンド実行すると、
- pyproject.tomlの[project.optional-dependencies]runtimeセッションに追加される
- ローカルのvenv環境に依存パッケージのダウンロードして自動的に依存関係の解決を実施される

# 開発環境用パッケージの追加
```sh
uv add --dev pytest pytest-cov
```

`uv add`を使用すると、自動的に:
- pyproject.tomlに依存関係が追加
- パッケージのインストール
- 依存関係の解決

が実行されます。手動でpyproject.tomlを編集する必要がなくなり、より効率的に依存関係を管理できます。

### 1.3. SQLファイル環境の構築

テスト実行時のSQLファイル参照ルール：

1. 本番用SQLファイル
   - 配置場所：`job/source/sql/*.sql`
   - テスト参照：`job/test/resources/sql`にソフトリンクを作成
   - 例：`sql_common_select_001.sql`, `sql_common_update_001.sql`など

2. テスト専用SQLファイル
   - 配置場所：`job/test/resources/sql`に直接配置
   - 例：`test.sql`など
   - ソフトリンク不要

セットアップ方法：
```sh
# SQLファイルのソフトリンク作成
./job/test/resources/sql/make_sqls_soft_link.bash
```

スクリプトの実行により：
- source/sqlの各SQLファイルへのソフトリンクを作成
- 既存のテスト用SQLファイルは保持
- 古いソフトリンクは自動的に更新

注意事項：
- 新しい本番用SQLファイルを追加した場合は、必ずスクリプトを実行してください
- テスト専用のSQLファイルは直接編集可能です

## 2. テストの実行

### 2.1. テストの実行方法

```sh
# 全テストの実行
pytest

# 特定のテストファイルの実行
pytest test/test_module.py

# 特定のテストクラス/メソッドの実行
pytest test/test_module.py::TestClass::test_method
```

### 2.2. テストの種類と指定方法

1. 実行範囲の指定
```sh
# 失敗したテストのみ再実行
pytest --lf

# 特定のマーカーのみ実行
pytest -m marker_name
```

2. 出力レベルの指定
```sh
# 詳細な出力
pytest -v

# より詳細な出力（変数の値なども表示）
pytest -vv
```

## 3. テスト結果の分析

### 3.1. カバレッジレポートの作成

```bash
# テストの実行とカバレッジデータの収集
pytest

# HTMLレポートの生成
coverage html
```

### 3.2. レポートの確認方法

1. HTMLレポートの場所：`coverage_html_report/index.html`
2. 確認できる情報：
   - 全体のカバレッジ率
   - ファイルごとのカバレッジ率
   - カバーされていない行の詳細
   - ブランチカバレッジの情報

3. zipファイルの作成：
```bash
cd tool
./generate_coverage_report.bash
# => job/tool/coverage_report.zipが作成されます
```

## 4. 開発ツールの利用

### 4.1. Python Test Explorerの設定

1. インストール：
   - VS Codeの拡張機能「Python Test Explorer」をインストール

2. 基本的な使用方法：
   - テストのツリー表示
   - 個別テストの実行
   - テスト結果の可視化

### 4.2. トラブルシューティング

1. よくある問題：
   - 仮想環境の確認：`(.venv)`が表示されているか
   - テストファイル名：`test_`で始まっているか
   - PYTHONPATHの設定：正しいパスが設定されているか

2. エラー解決のヒント：
   - インポートエラー：依存関係の確認
   - テストの発見エラー：ファイル名とパスの確認
   - 実行エラー：環境変数とパッケージの確認
