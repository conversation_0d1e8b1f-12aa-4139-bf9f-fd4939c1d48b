# AWS Glue用パッケージ作成手順

このドキュメントでは、AWS Glue向けのWheelファイル（Pythonホイール）と依存パッケージの作成・デプロイ手順について説明します。

## 概要

AWS Glueジョブ用のパッケージング方法として、このプロジェクトでは以下のアプローチを採用しています：

1. **メインパッケージ（WHL）**: プロジェクトのメインコードをPythonホイール（.whl）ファイルにパッケージ化
   - 依存関係処理モジュール（glue_deps_handler.py）も含まれています
2. **依存ライブラリ（deps.zip）**: サードパーティの依存ライブラリを単一のZIPファイルにパッケージ化
3. **Glueジョブメインスクリプト**: 各Glueジョブの実行ファイル（glue_job_*.py）をS3にデプロイ

## パッケージ作成・デプロイ手順

### 0. 環境の前提条件

パッケージ作成・デプロイスクリプトを実行するには、以下が必要です：

- **Docker**: ビルドおよびパッケージング処理にDockerを使用します
  - Dockerがインストールされていない場合は、「ローカル開発環境構築手順_API&バッチ.xlsx」の「13.Dockerのインストール手順」シートを参照してください
  - WSL環境でのDockerインストール方法については、上記ドキュメントに記載のシートを参照してください

### 1. 環境の準備

まず、環境を整えるために必要なパッケージをインストールし、依存パッケージを同期します：

```bash
# job ディレクトリに移動
cd job

# Python buildパッケージをインストール（WHLファイル作成に必要）
uv pip install build

# 依存パッケージを同期（不具合防止のため重要）
uv sync --all-groups --reinstall
```

### 2. パッケージ作成・デプロイ

パッケージ作成・デプロイ前に、必ず適切なブランチ（開発環境ならdevelop、検証環境ならrelease）をチェックアウトし、リモートリポジトリとの差分がないことを確認してください。

メインパッケージ（WHL）、依存ライブラリ（deps.zip）およびGlueジョブメインスクリプト（glue_job_*.py）を自動的に作成してS3にアップロードするには、以下のスクリプトを使用します：

```bash
# job ディレクトリに移動
cd job

# 開発環境用
./build_and_deploy_dev.sh

# または検証環境用
./build_and_deploy_stg.sh
```

これらのスクリプトは内部で共通の `build_and_deploy.sh` を呼び出し、以下の処理を行います：

1. **アプリケーションWHLの作成**: プロジェクトコードをWheelファイルにビルド
2. **deps.zipの作成**: 必要な依存ライブラリを単一のZIPファイルにまとめる（manylinux2014互換）
   - **効率化:** `pyproject.toml` に変更がない場合、このステップは自動的にスキップされます（GitHub Actions実行時）。ローカル実行時は `--skip-deps-build` オプションでスキップ可能です。
3. **S3へのアップロード**: 以下のファイルをS3にアップロード（ユーザー確認あり）
   - WHLファイル（アプリケーションコード）
   - deps.zip（依存ライブラリ、ビルドされた場合のみ）
   - glue_job_*.py（各Glueジョブのメイン起動スクリプト）

**実行環境による挙動の違い:**

- **ローカル環境 (WSLなど):** 環境変数 (`http_proxy`, `https_proxy`, `no_proxy`) で設定されたプロキシ情報がDockerビルド・実行時に使用されます。
- **GitHub Actions環境:** プロキシ設定は自動的にスキップされます。

実行時に各ファイルのアップロードを個別に選択できるため、頻繁に更新するアプリケーションコードのみをアップロードし、依存パッケージ（deps.zip）は必要に応じてアップロードすることができます。

### 3. AWS Glueでの使用方法

CloudFormationでGlueジョブを定義する際、以下のパラメータを設定します：

開発環境の例）
```yaml
DefaultArguments:
  "--extra-py-files": "s3://aws-glue-assets-886436956581-ap-northeast-1/scripts/glue_job-0.1.0-py3-none-any.whl,s3://aws-glue-assets-886436956581-ap-northeast-1/scripts/deps.zip"
```

各Glueジョブスクリプトには、先頭に以下のコードを追加する必要があります：

```python
# 依存関係のセットアップ（スクリプト先頭で実行）
from source.glue_deps_handler import setup_glue_dependencies
setup_glue_dependencies()

# 以降、通常通りモジュールをインポート
import boto3
# ...残りのコード
```

注意点：
- build_and_deploy.sh実行後に表示される`--extra-py-files`パラメータ文字列をそのまま使用できます
- 各ジョブスクリプトの先頭で依存関係セットアップコードを追加することが重要です
- メインスクリプト（glue_job_*.py）は自動的にS3の`/scripts/source/`ディレクトリにデプロイされます

### 4. deps.zip方式のメリット

旧方式（個別WHLファイル）と比較した、deps.zip方式の主なメリットは以下の通りです：

1. **パラメータの簡素化**: 全依存ライブラリを単一のパスで指定できる
2. **バイナリ互換性の向上**: manylinux2014形式を使用しGlue環境との互換性を確保
3. **データファイルアクセスの改善**: botocoreなどのデータファイルに依存するパッケージが正常に動作
4. **実行環境の安定化**: deps.zipを展開するため、ZIPファイル内部へのアクセス問題を解消

## 今後の予定

- CI/CDパイプラインとの統合
- 本番環境用対応

Todo: 本番環境への対応方法については、今後検討を進める予定です。
