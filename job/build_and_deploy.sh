#!/bin/bash

# AWS Glue 向けパッケージとdeps.zipをビルドして S3 にアップロードするスクリプト
# Docker環境を使用して依存ライブラリをダウンロードし、単一のZIPファイルにパッケージ化

set -e  # エラー時に実行を停止

# デフォルト値を設定せず、引数で指定を必須にする
BUCKET=""
PREFIX=""
CLEAN=false
SKIP_DEPS_BUILD=false
NON_INTERACTIVE=false
DEPLOY_DEPS=true
DEPLOY_SOURCE=true
DEPLOY_SCRIPTS=true
DRY_RUN=false

# CodeBuild環境の検出（log_info関数はまだ定義されていないため、echoを使用）
if [ -n "$CODEBUILD_BUILD_ID" ]; then
  echo "CodeBuild環境を検出しました。非対話モードを自動的に有効化します。"
  NON_INTERACTIVE=true
fi

# 現在のユーザーIDとグループIDを取得
HOST_UID=$(id -u)
HOST_GID=$(id -g)

# 色付きログ出力用の設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# エラーハンドリング関数 - 直ちに終了する
# 使用方法: error_exit "エラーメッセージ"
error_exit() {
    log_error "$1"
    echo "ビルド・デプロイ処理を中止します"
    exit 1
}

# Dockerファイルの内容
create_dockerfile() {
    local dockerfile="$1"
    local http_proxy="$2"
    local https_proxy="$3"
    local no_proxy="$4"

    cat > "$dockerfile" << EOF
FROM amazonlinux:2023

# プロキシ設定（ビルド時に設定される場合）
EOF

    # CodeBuild環境または非対話モードの場合はプロキシ設定をスキップ
    if [ -z "$CODEBUILD_BUILD_ID" ] && [ "$NON_INTERACTIVE" = "false" ]; then
        if [ -n "$http_proxy" ]; then
            cat >> "$dockerfile" << EOF
ENV http_proxy=$http_proxy
ENV HTTP_PROXY=$http_proxy
EOF
        fi

        if [ -n "$https_proxy" ]; then
            cat >> "$dockerfile" << EOF
ENV https_proxy=$https_proxy
ENV HTTPS_PROXY=$https_proxy
EOF
        fi

        if [ -n "$no_proxy" ]; then
            cat >> "$dockerfile" << EOF
ENV no_proxy=$no_proxy
ENV NO_PROXY=$no_proxy
EOF
        fi

        # dnfの設定
        if [ -n "$http_proxy" ]; then
            cat >> "$dockerfile" << EOF
RUN mkdir -p /etc/dnf && echo "proxy=$http_proxy" >> /etc/dnf/dnf.conf
EOF
        fi
    else
        log_info "CodeBuild環境または非対話モードのため、Dockerfileへのプロキシ設定をスキップします。"
    fi

    # 残りの共通Dockerfileの内容
    cat >> "$dockerfile" << EOF
# Python3とビルドツールのインストール
RUN dnf install -y python3 python3-devel python3-pip gcc gcc-c++ \
    make libffi-devel openssl-devel git findutils zip

# 作業ディレクトリ設定
WORKDIR /app
EOF
}

# Docker環境チェックとビルド関数
setup_docker_env() {
    # Dockerコマンドの存在確認
    if ! command -v docker &> /dev/null; then
        log_error "dockerコマンドが見つかりません。このスクリプトの実行にはDockerが必要です。"
        log_error "PATH環境変数: $PATH"
        log_error "インストールされているコマンド: $(which docker 2>&1)"
        error_exit "Docker環境のセットアップに失敗しました。"
    fi

    log_info "Dockerコマンドが見つかりました: $(which docker)"

    # Docker Daemonが動作しているか確認
    log_info "Docker Daemonの動作を確認しています..."
    if ! docker info &> /dev/null; then
        log_error "Docker Daemonが動作していません。Docker serviceが起動しているか確認してください。"
        log_error "Docker Daemonの状態: $(systemctl status docker 2>&1 || echo 'systemctlコマンドが利用できません')"
        log_error "Docker Daemonの起動を試みます..."

        # CodeBuild環境では特別な対応
        if [ -n "$CODEBUILD_BUILD_ID" ]; then
            log_info "CodeBuild環境を検出しました。Docker Daemonの起動を試みます..."
            nohup dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &> /tmp/dockerd.log &
            log_info "Docker Daemonの起動を待機しています..."
            sleep 20
            if ! docker info &> /dev/null; then
                log_error "Docker Daemonの起動に失敗しました。ログを確認してください:"
                log_error "$(cat /tmp/dockerd.log 2>&1 || echo 'ログファイルが見つかりません')"
                error_exit "Docker環境のセットアップに失敗しました。"
            fi
            log_info "Docker Daemonが正常に起動しました。"
        else
            error_exit "Docker環境のセットアップに失敗しました。"
        fi
    fi

    log_info "Docker環境をセットアップしています..."

    # プロキシ設定を環境から取得（存在する場合）
    local http_proxy_val="$http_proxy"
    local https_proxy_val="$https_proxy"
    local no_proxy_val="$no_proxy"

    # プロキシ情報を表示
    if [ -n "$http_proxy_val" ]; then
        log_info "HTTP プロキシ設定: $http_proxy_val"
    fi
    if [ -n "$https_proxy_val" ]; then
        log_info "HTTPS プロキシ設定: $https_proxy_val"
    fi
    if [ -n "$no_proxy_val" ]; then
        log_info "プロキシ除外設定: $no_proxy_val"
    fi

    # Dockerfileの生成
    local dockerfile="Dockerfile.glue3"
    create_dockerfile "$dockerfile" "$http_proxy_val" "$https_proxy_val" "$no_proxy_val"

    # 常に新しいDockerイメージをビルド
    log_info "Dockerイメージをビルドしています..."

    # 既存のイメージを削除
    if docker image inspect glue3-builder &> /dev/null; then
        log_info "既存のglue3-builderイメージを削除します..."
        docker rmi -f glue3-builder &> /dev/null || true
    fi

    # docker buildコマンドを実行
    local build_cmd="docker build"

    # CodeBuild環境または非対話モードの場合はプロキシ設定を--build-argとして追加しない
    if [ -z "$CODEBUILD_BUILD_ID" ] && [ "$NON_INTERACTIVE" = "false" ]; then
        if [ -n "$http_proxy_val" ]; then
            build_cmd="$build_cmd --build-arg http_proxy=$http_proxy_val"
            build_cmd="$build_cmd --build-arg HTTP_PROXY=$http_proxy_val"
        fi
        if [ -n "$https_proxy_val" ]; then
            build_cmd="$build_cmd --build-arg https_proxy=$https_proxy_val"
            build_cmd="$build_cmd --build-arg HTTPS_PROXY=$https_proxy_val"
        fi
        if [ -n "$no_proxy_val" ]; then
            build_cmd="$build_cmd --build-arg no_proxy=$no_proxy_val"
            build_cmd="$build_cmd --build-arg NO_PROXY=$no_proxy_val"
        fi
    else
        log_info "CodeBuild環境または非対話モードのため、docker build時のプロキシ関連build-argをスキップします。"
    fi

    # イメージ名とファイル名を追加
    build_cmd="$build_cmd -t glue3-builder -f $dockerfile ."

    log_info "実行するビルドコマンド: $build_cmd"

    # ビルドコマンドを実行
    if ! eval "$build_cmd"; then
        error_exit "Dockerイメージのビルドに失敗しました。"
    fi
}

# pyproject.tomlから依存関係を抽出
extract_dependencies() {
    local workdir="$1"
    local tempdir="$2"
    local pyproject_file="${workdir}/pyproject.toml"
    local dep_file="${tempdir}/dependencies.txt"

    log_info "pyproject.tomlから依存関係を抽出しています..."

    if [ ! -f "${pyproject_file}" ]; then
        error_exit "pyproject.tomlが見つかりません: ${pyproject_file}"
    fi

    # tomliモジュールの確認
    python3 -c "import tomli" 2>/dev/null || {
        log_warn "tomliモジュールがインストールされていません。インストールを試みます..."
        python3 -m pip install tomli
    }

    # 依存関係を抽出
    log_info "extract_deps.pyを実行: ${workdir}/extract_deps.py ${pyproject_file} > ${dep_file}"
    if ! python3 "${workdir}/extract_deps.py" "${pyproject_file}" > "${dep_file}"; then
        error_exit "依存関係の抽出に失敗しました。extract_deps.pyのエラーを確認してください。"
    fi

    if [ ! -s "${dep_file}" ]; then
        error_exit "依存関係が抽出できませんでした。pyproject.tomlを確認してください。"
    fi

    log_info "依存関係ファイルを作成しました: ${dep_file}"
    log_info "抽出された依存関係数: $(wc -l < ${dep_file})"
    log_info "以下の依存関係を抽出しました（先頭10件）:"
    head -n 10 "${dep_file}" | while read -r line; do
        log_info "  - $line"
    done
}

# 安全な一時ディレクトリのクリーンアップ
safe_cleanup_temp() {
    local temp_dir="$1"

    if [ -d "${temp_dir}" ]; then
        log_info "一時ディレクトリ ${temp_dir} をクリーンアップしています..."
        rm -rf "${temp_dir}" 2>/dev/null || {
            log_warn "一部のファイルを削除できませんでした。手動で削除が必要な場合があります。"
            log_warn "  rm -rf ${temp_dir}"
        }
    fi
}

# Docker環境を使用した依存ライブラリのダウンロードとZIP化
build_deps_zip() {
    local workdir="$1"
    local tempdir="$2"
    local distdir="$3"
    local deps_zip="${distdir}/deps.zip"

    # 既存の一時ディレクトリが存在する場合はクリーンアップ
    safe_cleanup_temp "${tempdir}/packages"
    mkdir -p "${tempdir}/packages"

    # 依存関係ファイルが存在するか確認
    if [ ! -f "${tempdir}/dependencies.txt" ]; then
        error_exit "依存関係ファイルが見つかりません: ${tempdir}/dependencies.txt"
    fi

    log_info "Docker環境を使用して依存関係をダウンロードし、ZIPファイルを作成しています..."
    log_info "ホストのUID:GID = ${HOST_UID}:${HOST_GID}"

    # プロキシ設定を環境から取得
    local docker_run_opts="--rm"

    # CodeBuild環境または非対話モードの場合はプロキシ設定を追加しない
    if [ -z "$CODEBUILD_BUILD_ID" ] && [ "$NON_INTERACTIVE" = "false" ]; then
        if [ -n "$http_proxy" ]; then
            docker_run_opts="$docker_run_opts -e http_proxy=$http_proxy"
            docker_run_opts="$docker_run_opts -e HTTP_PROXY=$http_proxy"
        fi
        if [ -n "$https_proxy" ]; then
            docker_run_opts="$docker_run_opts -e https_proxy=$https_proxy"
            docker_run_opts="$docker_run_opts -e HTTPS_PROXY=$https_proxy"
        fi
        if [ -n "$no_proxy" ]; then
            docker_run_opts="$docker_run_opts -e no_proxy=$no_proxy"
            docker_run_opts="$docker_run_opts -e NO_PROXY=$no_proxy"
        fi
    else
        log_info "CodeBuild環境または非対話モードのため、docker run時のプロキシ関連環境変数をスキップします。"
    fi

    # マウントとボリュームの設定
    docker_run_opts="$docker_run_opts -v ${workdir}:/app"

    # 実行するコマンドの構築
    local cmd='
      set -e

      # 環境情報の表示
      echo "### 実行環境情報 ###"
      echo "ユーザー: $(id -u):$(id -g)"
      echo "カレントディレクトリ: $(pwd)"
      echo "OS情報: $(cat /etc/os-release)"
      echo "Pythonバージョン: $(python3 --version)"

      # ディレクトリの作成と事前クリーンアップ
      echo "### ディレクトリ準備 ###"
      mkdir -p /app/.deps_tmp/packages

      # 既存のパッケージディレクトリをクリーンアップ
      if [ -d "/app/.deps_tmp/packages" ] && [ "$(ls -A /app/.deps_tmp/packages)" ]; then
          echo "既存のパッケージを削除しています..."
          rm -rf /app/.deps_tmp/packages/*
      fi

      # 依存関係ファイルの確認
      echo "### 依存関係ファイルの確認 ###"
      if [ ! -f "/app/.deps_tmp/dependencies.txt" ]; then
          echo "エラー: 依存関係ファイルが見つかりません: /app/.deps_tmp/dependencies.txt"
          ls -la /app/.deps_tmp/
          exit 1
      fi

      echo "依存関係ファイルの内容:"
      cat /app/.deps_tmp/dependencies.txt

      # 依存関係を一括でインストール
      echo "### パッケージの一括インストール開始 ###"
      python3 -m pip install -t /app/.deps_tmp/packages \
        --no-cache-dir \
        --only-binary=:all: \
        --platform manylinux2014_x86_64 \
        -r /app/.deps_tmp/dependencies.txt || { echo "エラー: 依存パッケージのインストールに失敗しました"; exit 1; }

      # ZIPファイルを作成（/tmpに一時ファイルとして作成）- 詳細ログを抑制
      echo "### ZIPファイル作成 ###"
      cd /app/.deps_tmp/packages
      echo "依存パッケージをZIP化しています..."
      zip -qr /tmp/deps.zip ./*
      echo "ZIP化完了"

      # ディレクトリの作成を確認
      echo "### 出力ディレクトリの確認 ###"
      mkdir -p /app/dist
      ls -la /app/dist

      # 一時ファイルからホストのパスにコピー
      echo "### ZIPファイルをコピー ###"
      cp /tmp/deps.zip /app/dist/

      # ファイル所有権をホストユーザーに変更
      echo "### ファイル所有権の変更 ###"
      chown -R '"${HOST_UID}:${HOST_GID}"' /app/.deps_tmp
      chown -R '"${HOST_UID}:${HOST_GID}"' /app/dist

      # 確認
      echo "### 処理完了 ###"
      ls -la /app/dist/
    '

    # 実行するdocker runコマンドの表示
    local docker_run_cmd="docker run $docker_run_opts glue3-builder bash -c '$cmd'"
    log_info "実行するコマンド: $docker_run_cmd"

    # Docker runコマンドを実行
    if ! eval "$docker_run_cmd"; then
        error_exit "Docker環境での依存関係のダウンロードとZIP化に失敗しました。"
    fi

    # ZIPファイルが存在するか確認
    if [ ! -f "${deps_zip}" ]; then
        error_exit "deps.zipファイルの作成に失敗しました。"
    fi

    log_info "deps.zipのサイズ: $(du -h ${deps_zip} | cut -f1)"

    # 処理完了後の一時ディレクトリのクリーンアップ
    safe_cleanup_temp "${tempdir}"
}

# 呼び出し元スクリプトを取得
get_caller_script() {
    local parent_pid=$PPID
    ps -o args= -p $parent_pid | grep -o '[^/]*\.sh' || echo ""
}

# スクリプト名と呼び出し元を取得
CURRENT_SCRIPT="$(basename "$0")"
CALLER_SCRIPT=$(get_caller_script)

# 使い方を表示
usage() {
    echo "使用方法: $0 --bucket BUCKET --prefix PREFIX [オプション]"
    echo ""
    echo "オプション:"
    echo "  -b, --bucket BUCKET  S3 バケット名（必須）"
    echo "  -p, --prefix PREFIX  S3 プレフィックス（必須）"
    echo "  -c, --clean          ビルド前にキャッシュと一時ファイルを削除"
    echo "  -h, --help           このヘルプメッセージを表示"
    echo "  --skip-deps-build    deps.zipのビルドをスキップ"
    echo "  --non-interactive    非対話モード（確認プロンプトをスキップ）"
    echo "  --deploy-deps        依存関係をデプロイするかどうか（true/false）"
    echo "  --deploy-source      ソースコードをデプロイするかどうか（true/false）"
    echo "  --deploy-scripts     ジョブスクリプトをデプロイするかどうか（true/false）"
    echo "  --dry-run            実際のデプロイを行わず内容確認（S3アップロードをスキップ）"
    echo ""
    echo "例: $0 --bucket my-bucket --prefix scripts"
    echo "例: $0 --bucket my-bucket --non-interactive --deploy-deps true --deploy-source true --deploy-scripts false"
    echo "例: $0 --bucket my-bucket --dry-run --non-interactive"
    echo ""
    echo "※環境別のラッパースクリプトの使用を推奨："
    echo "  開発環境: ./build_and_deploy_dev.sh"
    echo "  検証環境: ./build_and_deploy_stg.sh"
    exit 1
}

# 引数の解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--bucket)
            BUCKET="$2"
            shift 2
            ;;
        -p|--prefix)
            PREFIX="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        --skip-deps-build)
            SKIP_DEPS_BUILD=true
            shift
            ;;
        --non-interactive)
            NON_INTERACTIVE=true
            shift
            ;;
        --deploy-deps)
            DEPLOY_DEPS="$2"
            shift 2
            ;;
        --deploy-source)
            DEPLOY_SOURCE="$2"
            shift 2
            ;;
        --deploy-scripts)
            DEPLOY_SCRIPTS="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            echo "エラー: 不明なオプション「$1」"
            usage
            ;;
    esac
done

# 必須パラメータのチェック
if [ -z "$BUCKET" ]; then
    echo "エラー: バケット名(--bucket)は必須パラメータです"
    usage
fi

if [ -z "$PREFIX" ]; then
    echo "エラー: プレフィックス(--prefix)は必須パラメータです"
    usage
fi

# 呼び出し元の判断
if [[ "$CURRENT_SCRIPT" == "build_and_deploy.sh" && ! "$CALLER_SCRIPT" =~ ^(build_and_deploy_dev|build_and_deploy_stg)\.sh$ ]]; then
    echo "注意: 直接 $CURRENT_SCRIPT を実行しています。"
    echo "環境別のラッパースクリプトの使用を推奨します："
    echo "  開発環境: ./build_and_deploy_dev.sh"
    echo "  検証環境: ./build_and_deploy_stg.sh"
    echo ""
    echo "続行するには任意のキーを押してください。中止する場合は Ctrl+C を押してください..."
    read -n 1 -s
    echo "続行します..."
fi

# プロジェクトルートに移動
cd "$(dirname "$0")"

# ワークディレクトリとパスの設定
WORK_DIR="$(pwd)"
TEMP_DIR="${WORK_DIR}/.deps_tmp"
DIST_DIR="${WORK_DIR}/dist"

echo "======== Glue ジョブパッケージビルド ========"
echo "S3 バケット: $BUCKET"
echo "S3 プレフィックス: $PREFIX"

# 環境の判別
if [[ "$BUCKET" == *"886436956581"* ]]; then
    ENV_NAME="開発環境(dev)"
    EXPECTED_BRANCH="develop"
elif [[ "$BUCKET" == *"869935081854"* ]]; then
    ENV_NAME="検証環境(stg)"
    EXPECTED_BRANCH="release"
else
    ENV_NAME="不明な環境"
    EXPECTED_BRANCH="unknown"
fi

echo "======== デプロイ前の最終確認 ========="
echo "対象環境: $ENV_NAME"

# gitコマンドが利用可能か確認
if ! command -v git &> /dev/null; then
    echo "警告: gitコマンドが見つかりません。ブランチ確認をスキップします。"
else
    # 現在のブランチを取得
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

    # 期待されるブランチかどうか確認
    if [ "$CURRENT_BRANCH" != "$EXPECTED_BRANCH" ]; then
        echo "警告: 現在のブランチは '$CURRENT_BRANCH' ですが、$ENV_NAME では '$EXPECTED_BRANCH' ブランチが推奨されています。"
        echo "異なるブランチからのデプロイは意図しない結果を招く可能性があります。"
    else
        echo "ブランチ確認: OK（$EXPECTED_BRANCH ブランチを使用中）"

        # リモートとの差分を確認（CodeBuild環境では失敗する可能性があるため、エラーハンドリングを強化）
        echo "リモートリポジトリとの差分を確認しています..."

        # CodeBuild環境ではリモート確認をスキップ
        if [ -n "$CODEBUILD_BUILD_ID" ]; then
            echo "CodeBuild環境を検出しました。リモート差分確認をスキップします。"
        else
            # git fetchを実行し、エラーをキャッチ
            if ! git fetch origin "$EXPECTED_BRANCH" &> /dev/null; then
                echo "警告: リモートブランチの取得に失敗しました。リモート差分確認をスキップします。"
            else
                # git rev-listを実行し、エラーをキャッチ
                DIFF_COUNT_OUTPUT=$(git rev-list --count HEAD...origin/"$EXPECTED_BRANCH" 2>&1)
                if [[ $? -ne 0 ]]; then
                    echo "警告: リモート差分の計算に失敗しました: $DIFF_COUNT_OUTPUT"
                    echo "リモート差分確認をスキップします。"
                else
                    DIFF_COUNT=$DIFF_COUNT_OUTPUT
                    if [ "$DIFF_COUNT" -ne 0 ]; then
                        echo "警告: ローカルの $EXPECTED_BRANCH ブランチはリモートと同期されていません"
                        echo "リモートとの差分: $DIFF_COUNT コミット"
                        echo "デプロイ前に 'git pull origin $EXPECTED_BRANCH' の実行を推奨します"
                    else
                        echo "リモート同期確認: OK（リモートと差分なし）"
                    fi
                fi
            fi
        fi
    fi

    # 変更されていないファイルの確認
    UNSTAGED_CHANGES=$(git status --porcelain | wc -l)
    if [ "$UNSTAGED_CHANGES" -ne 0 ]; then
        echo "警告: コミットされていない変更があります"
        git status --short
        echo ""
    else
        echo "変更確認: OK（未コミットの変更なし）"
    fi
fi

# ユーザーに最終確認
echo ""
echo "--------------------------------------"
echo "重要: デプロイ操作は以下の内容で進められます"
echo "- 対象環境: $ENV_NAME"
echo "- S3バケット: $BUCKET"
echo "- S3プレフィックス: $PREFIX"
echo "- ビルド方式: deps.zip（全依存関係を単一ZIPにパッケージ化）"
echo "- ビルド環境: Docker環境（AmazonLinux 2023）"
echo "- バイナリ互換性: manylinux2014形式（AWS Glue環境との互換性向上）"
echo "- 非対話モード: $NON_INTERACTIVE"
echo "- ドライランモード: $DRY_RUN"

if [ "$DRY_RUN" = "true" ]; then
    log_info "ドライランモードが有効です。S3へのアップロードはスキップされます。"
fi

if [ "$NON_INTERACTIVE" = "true" ]; then
    log_info "非対話モードが有効なため、確認をスキップします"
else
    echo ""
    echo "続行してもよろしいですか？ [y/N]"
    read -r confirmation

    if [[ ! "$confirmation" =~ ^[Yy]$ ]]; then
        echo "デプロイ操作がキャンセルされました"
        exit 0
    fi
fi

# クリーンビルドの場合、キャッシュと一時ファイルを削除
if [ "$CLEAN" = true ]; then
    log_info "クリーンビルドを実行します..."
    rm -rf dist build deps .eggs *.egg-info ${TEMP_DIR}
    find . -name __pycache__ -type d -exec rm -rf {} + 2>/dev/null || true
fi

# ディレクトリ作成
mkdir -p ${DIST_DIR}
mkdir -p ${TEMP_DIR}

# 1. WHLファイルのビルド（依存関係のメタデータなし）
log_info "WHLファイルをビルドしています..."

# pyproject.tomlのバックアップを作成
log_info "pyproject.tomlのバックアップを作成しています..."
cp pyproject.toml pyproject.backup.toml

# dependencies節を空にする（ファイル内の特定部分だけを修正）
log_info "一時的にpyproject.tomlのdependencies節を空にしています..."
sed -i '/dependencies = \[/,/\]/c\dependencies = []' pyproject.toml

# WHLファイルをビルド
log_info "WHLファイルをビルドしています..."
if ! python -m build --wheel 2>/dev/null; then
    log_error "python-buildモジュールが見つかりません。必要なパッケージをインストールしています..."
    pip install build wheel setuptools
    if ! python -m build --wheel; then
        log_error "WHLファイルのビルドに失敗しました。"
        log_error "以下のコマンドを実行してPythonビルドツールをインストールしてください："
        log_error "pip install build wheel setuptools"
        # 元のpyproject.tomlに戻す
        log_info "元のpyproject.tomlに戻しています..."
        mv pyproject.backup.toml pyproject.toml
        error_exit "WHLファイルのビルドに失敗しました。"
    fi
fi

# 元のpyproject.tomlに戻す
log_info "元のpyproject.tomlに戻しています..."
mv pyproject.backup.toml pyproject.toml

# パッケージ情報の取得
WHL_FILE=$(find dist -name "glue_job*.whl" | sort -V | tail -n 1)
if [ -z "$WHL_FILE" ]; then
    error_exit "WHLファイルが見つかりません。ビルドに失敗した可能性があります。"
fi

log_info "WHLファイル: $WHL_FILE"

# 2. 依存ライブラリをdeps.zipとしてパッケージ化
log_info "依存ライブラリをdeps.zipにパッケージ化しています..."

# Docker環境のセットアップとZIPビルド
if [ "$SKIP_DEPS_BUILD" = false ]; then
    # DRY-RUNモードでも実際のビルドプロセスを実行（S3アップロードのみスキップ）
    log_info "deps.zipをビルドします..."

    # 依存関係の抽出
    extract_dependencies "${WORK_DIR}" "${TEMP_DIR}"

    # Docker環境のセットアップを試みる
    DOCKER_AVAILABLE=true
    setup_docker_env_result=0
    setup_docker_env || setup_docker_env_result=$?

    if [ $setup_docker_env_result -ne 0 ]; then
        log_warn "Docker環境のセットアップに失敗しました。代替手段を使用します..."
        DOCKER_AVAILABLE=false
    fi

    if [ "$DOCKER_AVAILABLE" = "true" ]; then
        # Docker環境を使用してZIPビルド
        log_info "Docker環境を使用してdeps.zipをビルドします..."
        build_deps_zip "${WORK_DIR}" "${TEMP_DIR}" "${DIST_DIR}"
    else
        # Docker環境が利用できない場合は、直接pipを使用
        log_info "Docker環境が利用できないため、直接pipを使用してdeps.zipをビルドします..."
        mkdir -p "${DIST_DIR}"
        mkdir -p "${TEMP_DIR}/packages"

        # 依存関係ファイルが存在するか確認
        if [ ! -f "${TEMP_DIR}/dependencies.txt" ]; then
            error_exit "依存関係ファイルが見つかりません: ${TEMP_DIR}/dependencies.txt"
        fi

        # 依存関係を直接インストール
        log_info "依存関係を直接インストールしています..."
        python3 -m pip install -t "${TEMP_DIR}/packages" --no-cache-dir -r "${TEMP_DIR}/dependencies.txt" || {
            error_exit "依存パッケージのインストールに失敗しました"
        }

        # ZIPファイルを作成
        log_info "deps.zipを作成しています..."
        (cd "${TEMP_DIR}/packages" && zip -r "${DIST_DIR}/deps.zip" .) || {
            error_exit "deps.zipの作成に失敗しました"
        }

        log_info "deps.zipを作成しました: ${DIST_DIR}/deps.zip"
    fi
else
    log_info "--skip-deps-buildが指定されたため、deps.zipのビルドをスキップします。"
    # スキップする場合でもdistディレクトリは作成しておく
    mkdir -p ${DIST_DIR}

    # deps.zipが存在するか確認し、存在しない場合はS3からダウンロード
    DEPS_ZIP="${DIST_DIR}/deps.zip"
    if [ ! -f "${DEPS_ZIP}" ]; then
        log_info "deps.zipが見つかりません。S3からダウンロードを試みます..."
        if [ "$DRY_RUN" = "true" ]; then
            log_info "【DRY-RUN】S3からのダウンロードをシミュレートします"
            echo "DRY-RUN mode - dummy deps.zip" > "${DEPS_ZIP}"
        else
            # S3からdeps.zipをダウンロード
            if ! aws s3 cp "s3://$BUCKET/$PREFIX/deps.zip" "${DEPS_ZIP}" --quiet; then
                log_warn "S3からdeps.zipのダウンロードに失敗しました"
                log_warn "依存関係に変更がある場合は--skip-deps-buildオプションを削除してください"
                error_exit "deps.zipが見つかりません。ビルドに失敗した可能性があります。"
            fi
            log_info "S3からdeps.zipをダウンロードしました"
        fi
    fi
fi

# deps.zipの存在確認
DEPS_ZIP="${DIST_DIR}/deps.zip"
if [ ! -f "${DEPS_ZIP}" ]; then
    error_exit "deps.zipが見つかりません。ビルドに失敗した可能性があります。"
fi

log_info "deps.zipファイル: ${DEPS_ZIP} ($(du -h ${DEPS_ZIP} | cut -f1))"

# 3. S3へのアップロード

# WHL ファイル
WHL_BASENAME=$(basename "$WHL_FILE")
echo ""
echo "--------------------------------------"
echo "アプリケーションWHLファイルをS3にアップロードしますか？"
echo "ターゲット: s3://$BUCKET/$PREFIX/$WHL_BASENAME"
echo "※このファイルは頻繁に更新されます"

upload_whl=true
if [ "$NON_INTERACTIVE" = "true" ]; then
    if [ "$DEPLOY_SOURCE" = "true" ]; then
        log_info "非対話モード: WHLファイルをアップロードします"
    else
        log_info "非対話モード: WHLファイルのアップロードをスキップします"
        upload_whl=false
    fi
else
    echo "アップロードしますか？ [Y/n]"
    read -r upload_whl_confirmation
    if [[ "$upload_whl_confirmation" =~ ^[Nn]$ ]]; then
        upload_whl=false
    fi
fi

if [ "$upload_whl" = "true" ]; then
    if [ "$DRY_RUN" = "true" ]; then
        log_info "【ドライラン】WHLファイルをアップロードします: s3://$BUCKET/$PREFIX/$WHL_BASENAME"
    else
        aws s3 cp "$WHL_FILE" "s3://$BUCKET/$PREFIX/$WHL_BASENAME" --acl bucket-owner-full-control || {
            error_exit "WHLファイルのアップロードに失敗しました"
        }
        log_info "WHL アップロード済み: s3://$BUCKET/$PREFIX/$WHL_BASENAME"
    fi
else
    log_info "WHLファイルのアップロードはスキップされました"
fi

# deps.zip ファイル (ビルドがスキップされなかった場合のみアップロード)
if [ "$SKIP_DEPS_BUILD" = false ]; then
    DEPS_ZIP_BASENAME=$(basename "$DEPS_ZIP")
    echo ""
    echo "--------------------------------------"
    echo "deps.zipファイルをS3にアップロードしますか？"
    echo "ターゲット: s3://$BUCKET/$PREFIX/$DEPS_ZIP_BASENAME"
    echo "※このファイルはライブラリの依存関係が変更されたときのみ更新が必要です"

    upload_deps=true
    if [ "$NON_INTERACTIVE" = "true" ]; then
        if [ "$DEPLOY_DEPS" = "true" ]; then
            log_info "非対話モード: deps.zipファイルをアップロードします"
        else
            log_info "非対話モード: deps.zipファイルのアップロードをスキップします"
            upload_deps=false
        fi
    else
        echo "アップロードしますか？ [Y/n]"
        read -r upload_deps_confirmation
        if [[ "$upload_deps_confirmation" =~ ^[Nn]$ ]]; then
            upload_deps=false
        fi
    fi

    if [ "$upload_deps" = "true" ]; then
        # deps.zipが存在するか最終確認
        if [ ! -f "${DEPS_ZIP}" ]; then
            error_exit "deps.zipが見つかりません。アップロードできません。"
        fi
        if [ "$DRY_RUN" = "true" ]; then
            log_info "【ドライラン】deps.zipファイルをアップロードします: s3://$BUCKET/$PREFIX/$DEPS_ZIP_BASENAME"
        else
            aws s3 cp "$DEPS_ZIP" "s3://$BUCKET/$PREFIX/$DEPS_ZIP_BASENAME" --acl bucket-owner-full-control || {
                error_exit "deps.zipファイルのアップロードに失敗しました"
            }
            log_info "deps.zip アップロード済み: s3://$BUCKET/$PREFIX/$DEPS_ZIP_BASENAME"
        fi
    else
        log_info "deps.zipファイルのアップロードはスキップされました"
    fi
else
    log_info "deps.zipのビルドがスキップされたため、アップロードもスキップします。"
fi

# glue_deps_handler.pyはWHLファイル内に含まれているため、別途アップロードは不要

# 4. Glueジョブメインスクリプトのデプロイ
echo ""
echo "--------------------------------------"
echo "Glueジョブメインスクリプト(glue_job_*.py)をS3にアップロードしますか？"
echo "ターゲット: s3://$BUCKET/$PREFIX/source/"
echo "※各GLUEジョブのメイン起動スクリプトをデプロイします"

upload_scripts=true
if [ "$NON_INTERACTIVE" = "true" ]; then
    if [ "$DEPLOY_SCRIPTS" = "true" ]; then
        log_info "非対話モード: Glueジョブメインスクリプトをアップロードします"
    else
        log_info "非対話モード: Glueジョブメインスクリプトのアップロードをスキップします"
        upload_scripts=false
    fi
else
    echo "アップロードしますか？ [Y/n]"
    read -r upload_scripts_confirmation
    if [[ "$upload_scripts_confirmation" =~ ^[Nn]$ ]]; then
        upload_scripts=false
    fi
fi

if [ "$upload_scripts" = "true" ]; then
    if [ "$DRY_RUN" = "true" ]; then
        log_info "【ドライラン】Glueジョブメインスクリプトをアップロードします: s3://$BUCKET/$PREFIX/source/glue_job_*.py"
    else
        aws s3 cp source/ "s3://$BUCKET/$PREFIX/source/" --recursive --exclude "*" --include "glue_job_*.py" --acl bucket-owner-full-control || {
            error_exit "Glueジョブメインスクリプトのアップロードに失敗しました"
        }
        log_info "Glueジョブメインスクリプト アップロード済み: s3://$BUCKET/$PREFIX/source/glue_job_*.py"
    fi
else
    log_info "Glueジョブメインスクリプトのアップロードはスキップされました"
fi

# 5. CloudFormation用パラメータ文字列の生成
echo "--------------------------------------"
log_info "AWS Glue用パラメータ文字列を生成しています..."

# メインWHLファイルとdeps.zipのS3パス（--extra-py-files用）
# deps.zipのパスはビルドされた場合のみ含める
if [ "$SKIP_DEPS_BUILD" = false ]; then
    EXTRA_PY_FILES="s3://$BUCKET/$PREFIX/$WHL_BASENAME,s3://$BUCKET/$PREFIX/$DEPS_ZIP_BASENAME"
else
    EXTRA_PY_FILES="s3://$BUCKET/$PREFIX/$WHL_BASENAME"
    log_info "deps.zipはビルドされなかったため、--extra-py-filesには含めません。"
fi

echo "======== ビルド・デプロイ完了 ========"
echo ""
echo "CloudFormation テンプレートで以下の設定を使用してください:"
echo ""
echo "DefaultArguments:"
echo "  \"--extra-py-files\": \"$EXTRA_PY_FILES\""
echo ""
echo "※注意: deps.zip方式では、各Glueジョブスクリプトの先頭で以下のコードを追加してください:"
echo ""
echo "# 依存関係のセットアップ（スクリプト先頭で実行）"
echo "from source.glue_deps_handler import setup_glue_dependencies"
echo "setup_glue_dependencies()"
echo ""
echo "# 以降、通常通りモジュールをインポート"
echo "import boto3"
echo "..."
echo ""

echo "======== 次のステップ ========"
echo "1. 必要に応じてGitリポジトリにファイルを格納:"
echo "   - ビルド済みのWHLファイル: $WHL_FILE"
echo "   - 依存ライブラリdeps.zip: $DEPS_ZIP"
echo ""
echo "2. ファイル格納後、プルリクエストを作成してください:"
echo "   - 開発環境の場合: developブランチへのPR"
echo "   - 検証環境の場合: releaseブランチへのPR"
echo ""
echo "これらのファイルを格納することで、他のチームメンバーが"
echo "デプロイに使用したファイルバージョンを確認・再利用できます。"
echo "==============================================="
