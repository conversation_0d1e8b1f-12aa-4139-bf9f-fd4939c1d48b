#!/bin/bash

# 検証環境(stg)向けのビルド・デプロイラッパースクリプト
# 内部で共通の build_and_deploy.sh を呼び出します

# このスクリプトのディレクトリに移動
cd "$(dirname "$0")"

# 検証環境のS3バケット設定
BUCKET="aws-glue-assets-869935081854-ap-northeast-1"
PREFIX="scripts"

echo "======== 検証環境(stg)向けデプロイを実行します ========"
echo "S3 バケット: $BUCKET"

# 引数をそのまま転送し、バケット名とプレフィックスを明示的に設定
./build_and_deploy.sh --bucket "$BUCKET" --prefix "$PREFIX" "$@"

# 終了コードを保持
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo "========検証環境(stg)へのデプロイが完了しました========"
else
    echo "========検証環境(stg)へのデプロイが失敗しました========"
fi

exit $exit_code
