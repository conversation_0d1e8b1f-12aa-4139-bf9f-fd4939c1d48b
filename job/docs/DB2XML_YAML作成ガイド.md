# DB2XML YAML作成ガイド
- 2025/02/24: クエリ階層化ルールを明確化
  * queryとsub_queryの使い分けを追加
  * SubTree内でのクエリ使用規則を明確化
  * 親レコードの自動参照に関する説明を拡充
- 2025/02/21: SubTreeとQueryの関係に関する説明を追加
  * SubTreeとQueryの基本原則を追加
  * Query選定のルールを明確化
  * トラブルシューティング情報を拡充

## 1. はじめに

### 1.1 本ガイドの目的
このガイドは、DB2XMLコンバーターのYAMLファイルを作成するための手順と注意点をまとめたものです。

### 1.2 作成の流れ
1. XMLインターフェース定義の理解
2. データベース構造の確認
3. YAMLファイルの作成
4. テストと検証

## 2. 基本概念と構造

### 2.1 SubTreeとQueryの関係
XMLの階層構造とクエリの関係について、以下の重要な原則があります：

1. MainQuery (query)
   - SubTreeのルートでのみ使用可能
   - データの主要な取得元として機能
   - 子要素への基準データを提供

2. SubQuery (sub_query)
   - 子要素でのみ使用可能
   - 親要素のデータを参照して実行
   - 親のすべてのフィールドを自動的にパラメータとして利用可能

### 2.2 クエリ階層化の基本
```yaml
mappings:
  - target_element: "root"
    child_elements:
      # MainQuery: 親要素のデータ取得
      - target_element: "parent"
        query: "parent_query"
        attributes:
          id: "{parent_id}"
        child_elements:
          # SubQuery: 子要素のデータ取得
          - target_element: "child"
            sub_query: "child_query"  # 親のparent_idを自動参照
            attributes:
              id: "{child_id}"
```

### 2.3 固定値要素の定義
DB依存の有無によって要素の生成方法が異なります。

1. DB非依存要素（常に生成）
```yaml
mappings:
  - target_element: "root"
    child_elements:
      # 空要素の生成
      - target_element: "campaign-scope"  # DB非依存の空要素

      # 固定値を持つ要素
      - target_element: "flags"
        child_elements:
          - target_element: "enabled"
            value: "true"
          - target_element: "archived"
            value: "false"
```

2. DB依存要素（データの有無で判断）
```yaml
mappings:
  - target_element: "root"
    child_elements:
      # クエリ結果に基づく要素
      - target_element: "products"
        query: "product_query"
        child_elements:
          - target_element: "name"
            source_column: "product_name"
```

## 3. 実装パターンと例

### 3.1 複数MainQueryとSub-queryの実装

#### 3.1.1 基本構造
```yaml
etl:
  # MainQuery群の定義
  query1: |
    SELECT * FROM table1
    WHERE updated_datetime > :sync_timestamp
      AND updated_datetime <= :diff_base_timestamp

  query2: |
    SELECT * FROM table2
    WHERE updated_datetime > :sync_timestamp
      AND updated_datetime <= :diff_base_timestamp

  # SubQuery群の定義
  sub_query1: |
    SELECT * FROM sub_table1
    WHERE parent_id = :parent_id

  sub_query2: |
    SELECT * FROM sub_table2
    WHERE parent_id = :parent_id
```

#### 3.1.2 マッピング構造
```yaml
mappings:
  - target_element: "root"
    child_elements:
      # MainQuery 1の処理
      - target_element: "section1"
        query: "query1"
        child_elements:
          - target_element: "sub_section1"
            sub_query: "sub_query1"

      # MainQuery 2の処理
      - target_element: "section2"
        query: "query2"
        child_elements:
          - target_element: "sub_section2"
            sub_query: "sub_query2"
```

### 3.2 キャンペーン・プロモーションの実装例
複数のMainQueryと、それぞれに対応する複数のSub-queryを使用した実装例です。

```yaml
etl:
  parameters:
    sync_timestamp:
      type: datetime
      required: true
    diff_base_timestamp:
      type: datetime
      required: true

  # MainQuery 1: キャンペーン基本情報
  campaign_query: |
    SELECT
      ci.campaign_instructions_code as campaign_code,
      ci.campaign_instructions_name as campaign_name,
      ci.campaign_start_date,
      ci.campaign_end_date,
      ci.baitai_code as media_code
    FROM campaign_instructions ci
    WHERE ci.updated_datetime > :sync_timestamp
      AND ci.updated_datetime <= :diff_base_timestamp

  # Sub-query 1-1: プロモーション情報
  promotion_query: |
    SELECT
      promotion_id,
      promotion_type,
      commodity_code,
      discount_rate,
      discount_amount
    FROM campaign_promotion
    WHERE campaign_instructions_code = :campaign_code

  # Sub-query 1-2: 特典情報
  benefit_query: |
    SELECT
      benefit_id,
      benefit_type,
      point_rate
    FROM campaign_benefits
    WHERE campaign_instructions_code = :campaign_code

  # MainQuery 2: 商品基本情報
  product_query: |
    SELECT
      p.commodity_code as product_code,
      p.commodity_name as product_name,
      p.category_code
    FROM products p
    WHERE p.updated_datetime > :sync_timestamp
      AND p.updated_datetime <= :diff_base_timestamp

  # Sub-query 2-1: 商品バリエーション
  variation_query: |
    SELECT
      variation_code,
      size_code,
      color_code
    FROM product_variations
    WHERE commodity_code = :product_code

  # MainQuery 3: 顧客グループ情報
  customer_group_query: |
    SELECT
      group_code,
      group_name,
      priority_level
    FROM customer_groups
    WHERE updated_datetime > :sync_timestamp
      AND updated_datetime <= :diff_base_timestamp

  # Sub-query 3-1: グループ条件
  group_condition_query: |
    SELECT
      condition_type,
      condition_value
    FROM group_conditions
    WHERE group_code = :group_code

  # マッピング定義
  mappings:
    - target_element: "promotions"
      child_elements:
        # MainQuery 1: キャンペーン情報のツリー
        - target_element: "campaign"
          query: "campaign_query"
          attributes:
            id: "{campaign_code}"
          child_elements:
            # 基本情報
            - target_element: "name"
              source_column: "campaign_name"
            - target_element: "period"
              child_elements:
                - target_element: "start-date"
                  source_column: "campaign_start_date"
                  type: "datetime"
                - target_element: "end-date"
                  source_column: "campaign_end_date"
                  type: "datetime"

            # 固定値要素群
            - target_element: "campaign-scope"
            - target_element: "flags"
              child_elements:
                - target_element: "enabled"
                  value: "true"
                - target_element: "archived"
                  value: "false"

            # Sub-query 1-1: プロモーション情報
            - target_element: "promotions"
              child_elements:
                - target_element: "promotion"
                  sub_query: "promotion_query"
                  attributes:
                    id: "{promotion_id}"
                    type: "{promotion_type}"
                  child_elements:
                    - target_element: "target-product"
                      source_column: "commodity_code"
                    - target_element: "discount"
                      child_elements:
                        - target_element: "rate"
                          source_column: "discount_rate"
                          condition: "discount_rate is not null"
                          type: "number"
                        - target_element: "amount"
                          source_column: "discount_amount"
                          condition: "discount_amount is not null"
                          type: "number"

            # Sub-query 1-2: 特典情報
            - target_element: "benefits"
              child_elements:
                - target_element: "benefit"
                  sub_query: "benefit_query"
                  attributes:
                    id: "{benefit_id}"
                    type: "{benefit_type}"
                  child_elements:
                    - target_element: "point-rate"
                      source_column: "point_rate"
                      type: "number"

        # MainQuery 2: 商品情報のツリー
        - target_element: "products"
          query: "product_query"
          child_elements:
            - target_element: "product"
              attributes:
                code: "{product_code}"
                category: "{category_code}"
              child_elements:
                - target_element: "name"
                  source_column: "product_name"

                # Sub-query 2-1: バリエーション情報
                - target_element: "variations"
                  child_elements:
                    - target_element: "variation"
                      sub_query: "variation_query"
                      attributes:
                        code: "{variation_code}"
                      child_elements:
                        - target_element: "size"
                          source_column: "size_code"
                        - target_element: "color"
                          source_column: "color_code"

        # MainQuery 3: 顧客グループ情報のツリー
        - target_element: "customer-groups"
          query: "customer_group_query"
          child_elements:
            - target_element: "group"
              attributes:
                code: "{group_code}"
                priority: "{priority_level}"
              child_elements:
                - target_element: "name"
                  source_column: "group_name"

                # Sub-query 3-1: グループ条件情報
                - target_element: "conditions"
                  child_elements:
                    - target_element: "condition"
                      sub_query: "group_condition_query"
                      attributes:
                        type: "{condition_type}"
                      child_elements:
                        - target_element: "value"
                          source_column: "condition_value"
```
#### 生成されるXMLの例
```xml
<?xml version="1.0" encoding="UTF-8"?>
<promotions>
  <!-- キャンペーン情報 -->
  <campaign id="CAMP001">
    <name>春の大感謝祭</name>
    <period>
      <start-date>2025-03-01T00:00:00</start-date>
      <end-date>2025-03-31T23:59:59</end-date>
    </period>
    <!-- 固定値要素 -->
    <campaign-scope/>
    <flags>
      <enabled>true</enabled>
      <archived>false</archived>
    </flags>
    <!-- プロモーション情報（Sub-query 1-1の結果） -->
    <promotions>
      <promotion id="PROM001" type="discount">
        <target-product>PROD001</target-product>
        <discount>
          <rate>10.00</rate>
        </discount>
      </promotion>
    </promotions>
    <!-- 特典情報（Sub-query 1-2の結果） -->
    <benefits>
      <benefit id="BNF001" type="point">
        <point-rate>5.00</point-rate>
      </benefit>
    </benefits>
  </campaign>

  <!-- 商品情報（MainQuery 2の結果） -->
  <products>
    <product code="PROD001" category="CAT001">
      <name>テスト商品</name>
      <!-- バリエーション情報（Sub-query 2-1の結果） -->
      <variations>
        <variation code="VAR001">
          <size>M</size>
          <color>RED</color>
        </variation>
      </variations>
      <prices>
        <price type="normal">
          <amount>1000</amount>
          <valid-period>
            <from>2025-03-01T00:00:00</from>
            <to>2025-03-31T23:59:59</to>
          </valid-period>
        </price>
      </prices>
    </product>
  </products>

  <!-- 顧客グループ情報（MainQuery 3の結果） -->
  <customer-groups>
    <group code="GRP001" priority="1">
      <name>プレミアム会員</name>
      <!-- グループ条件情報（Sub-query 3-1の結果） -->
      <conditions>
        <condition type="purchase_amount">
          <value>100000</value>
        </condition>
      </conditions>
    </group>
  </customer-groups>
</promotions>
```

この出力例から以下の点が確認できます：

1. MainQueryの結果
   - campaign要素（MainQuery 1）
   - products要素（MainQuery 2）
   - customer-groups要素（MainQuery 3）

2. Sub-queryの結果
   - promotions, benefits（Sub-query 1-1, 1-2）
   - variations（Sub-query 2-1）
   - conditions（Sub-query 3-1）

3. 固定値要素
   - campaign-scope（空要素）
   - flags/enabled, flags/archived（固定値）

## 4. エラー対応とトラブルシューティング

### 4.1 よくあるエラーと対処法

1. クエリ関連のエラー
```text
エラー: "親パラメータが未定義です"
原因: SubQueryで参照する親フィールドが取得されていない
対処:
- MainQueryのSELECT句を確認
- フィールド名の一致を確認
- エイリアスの使用を検討
```

2. データ型エラー
```text
エラー: "数値変換エラー"
原因: データ型の不一致
対処:
- typeプロパティの指定を確認
- NULL値の処理方法を指定
- 変換ルールの追加
```

### 4.2 デバッグ方法

1. クエリのテスト実行
```sql
-- MainQueryのテスト
SELECT * FROM campaign_instructions
WHERE updated_datetime > '2025-02-24 00:00:00'
  AND updated_datetime <= '2025-02-24 12:00:00';

-- SubQueryのテスト（親パラメータを指定）
SELECT * FROM campaign_promotion
WHERE campaign_instructions_code = 'TEST001';
```

2. ログの確認項目
```yaml
# DEBUGログで確認すべき情報
- クエリの実行パラメータ値
- 取得レコード数
- XML生成過程
- エラーの詳細と発生箇所
```

## 5. パフォーマンス最適化

### 5.1 クエリの最適化
1. インデックスの活用
```sql
CREATE INDEX idx_campaign_updated_dt
ON campaign_instructions(updated_datetime);

CREATE INDEX idx_promotion_campaign_code
ON campaign_promotion(campaign_instructions_code);
```

2. 結合の最適化
```sql
SELECT /*+ INDEX(ci idx_campaign_updated_dt) */
  ci.campaign_instructions_code,
  cp.promotion_id
FROM campaign_instructions ci
JOIN campaign_promotion cp
  ON ci.campaign_instructions_code = cp.campaign_instructions_code
WHERE ci.updated_datetime > :sync_timestamp
  AND ci.updated_datetime <= :diff_base_timestamp;
```

### 5.2 メモリ使用量の制御
```yaml
database:
  # バッチサイズの設定
  batch_size: 1000

  # タイムアウトの設定
  timeout: 3600

  # メモリ制限
  max_memory: 1024M
```

## 6. 更新履歴

- 2025/02/24: クエリ階層化ルールを明確化
  * queryとsub_queryの使い分けを追加
  * SubTree内でのクエリ使用規則を明確化
  * 親レコードの自動参照に関する説明を拡充
  * 複数MainQueryの実装例を追加

- 2025/02/21: 初版作成
  * 基本構造の定義
  * 実装例の追加
  * トラブルシューティング情報の追加

## 7. 参考資料

- [設計ガイド](../docs/設計ガイド.md)
- [テストケース](../test/test_db2xml_converter.py)
- [実装例](../test/resources/config/converter/db2xml/)
