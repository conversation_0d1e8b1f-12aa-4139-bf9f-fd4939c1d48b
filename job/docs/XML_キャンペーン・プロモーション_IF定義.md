# XML定義一覧表

## キャンペーン・プロモーション IF定義

### 前提条件
- /要素 @属性の型がHTMLの場合は、アンパサンド（&）、右大不等号（<）などの特殊文字をWEBDBで文字実体参照（アンパサンド（&amp;）、右大不等号（&lt;）など）にエンコードしWEBDB→SFCCへ連携する
- /要素 @属性の型がDate + Timeのフォーマットは、YYYY-MM-DDThh:mm:ss.sTZD
  - 協定世界時(UTC)形式例：2021-04-01T09:09:43.000Z
  - 日本標準時(JST)形式例：2021-04-01T18:09:43.000+09:00

### キャンペーン定義

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|------|--------------|-----|----------|--------|----------|------|
| /promotions | - | - | - | - | - | - | - | - |
| /promotions/campaign | - | N | - | - | - | - | - | - |
| /promotions/campaign/@campaign-id | キャンペーンID | - | ※ | String | - | キャンペーン設定.キャンペーン設定コード | キャンペーン設定コードを設定する | - |
| /promotions/campaign/description | 説明 | - | ○ | String | - | キャンペーン設定.キャンペーン設定名称 | キャンペーン名称を設定する | - |
| /promotions/campaign/enabled-flag | 有効フラグ | - | - | boolean | - | キャンペーン設定.削除フラグ | trueで設定。ただし、削除フラグがONの場合、falseとする | 有効な場合true、無効な場合false |
| /promotions/campaign/campaign-scope | - | - | - | - | - | - | 設定不要 | - |
| /promotions/campaign/campaign-scope/applicable-online | - | - | - | - | - | - | <applicable-online/>タグを固定で設定 | - |
| /promotions/campaign/start-date | キャンペーン開始日 | - | - | Date + Time | - | キャンペーン設定.キャンペーン適用開始日 | キャンペーン設定.キャンペーン適用開始日 | 時限設定をする場合に設定する |
| /promotions/campaign/end-date | キャンペーン終了日 | - | - | Date + Time | - | キャンペーン設定.キャンペーン適用終了日 | キャンペーン設定.キャンペーン適用終了日 | 時限設定をする場合に設定する |
| /promotions/campaign/customer-groups | キャンペーンの限定子の顧客グループ | - | - | - | - | - | - | 顧客グループでのみ制御を行う想定のため必須 |
| /promotions/campaign/customer-groups/@match-mode | all または any | - | - | String | - | - | anyを設定 | 「すべて」又は「どれか１つ」を設定 |
| /promotions/campaign/customer-groups/customer-group | キャンペーン対象の顧客グループ | N | - | - | - | - | - | 同一のキャンペーンで顧客グループが存在する場合、顧客グループIDを設定 |
| /promotions/campaign/customer-groups/customer-group/@group-id | 顧客グループのID | - | - | String | - | キャンペーン設定.キャンペーン設定コード | customer.profile.custom.txRank | 顧客プロファイル属性を指定する |
| /promotions/campaign/custom-attributes | カスタム属性 | - | - | - | - | - | - | - |
| /promotions/campaign/custom-attributes/custom-attribute | キャンペーン適用範囲 | - | - | - | - | キャンペーン設定.キャンペーン適用範囲 | - | OMS側のキャンペーン適用範囲\n01:単商品明細単位\n02:注文全体単位 |
| /promotions/campaign/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txCampaignScopeApplication" を設定 | - |

### プロモーション定義

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|------|--------------|-----|----------|--------|----------|------|
| /promotions/promotion | - | N | - | - | - | - | - | - |
| /promotions/promotion/@promotion-id | プロモーションID | - | ※ | String | - | キャンペーン設定.キャンペーン設定コード、キャンペーン設定プロモーション.プロモーション番号 | キャンペーン設定.キャンペーン設定コード + "_" + キャンペーン設定プロモーション.プロモーション番号 | - |
| /promotions/promotion/enabled-flag | 有効 | - | - | boolean | - | - | trueで設定 | 有効な場合true、無効な場合false |
| /promotions/promotion/archived-flag | アーカイブ済み | - | - | boolean | - | - | falseで設定 | false を固定で設定 |
| /promotions/promotion/searchable-flag | 検索可能 | - | - | boolean | - | - | trueで設定 | XMLサンプルに合わせて「true」に修正 |
| /promotions/promotion/refinable-flag | - | - | - | boolean | - | - | falseで設定 | false を固定で設定 |
| /promotions/promotion/prevent-requalifying-flag | - | - | - | boolean | - | - | falseで設定 | false を固定で設定 |
| /promotions/promotion/prorate-across-eligible-items-flag | - | - | - | boolean | - | - | falseで設定 | false を固定で設定 |
| /promotions/promotion/exclusivity | 除外 | - | - | String | - | - | noを設定 | no(ほかのプロモーションと組み合わせ可能)、class または global（組み合わせ不可） |
| /promotions/promotion/name | 名前 | - | - | String | - | キャンペーン設定.キャンペーン設定名称 | キャンペーン名称を設定する | - |
| /promotions/promotion/callout-msg | コールアウトメッセージ | - | - | HTML | - | - | - | 商品詳細画面でキャンペーンメッセージを表示するために使用 |
| /promotions/promotion/callout-msg/@xml:lang | 言語設定 | - | - | String | - | - | "ja-JP"を設定 | 日本語を指定 |

### カスタム属性

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|------|--------------|-----|----------|--------|----------|------|
| /promotions/promotion/custom-attributes/custom-attribute | キャンペーン終了日（画面表示用） | - | - | - | - | キャンペーン設定.キャンペーン適用終了日 | キャンペーン設定.キャンペーン適用終了日 | 商品詳細画面、一覧画面で「〇月〇日まで」の情報を表示するために使用する想定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txCampaignEndDateForScreenDisplay" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 条件分類 | - | - | - | - | キャンペーン設定条件.条件分類 | キャンペーン設定条件.条件分類 | - |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txConditionClassification" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 媒体コード | - | - | - | - | キャンペーン設定.媒体コード | キャンペーン設定.媒体コード | - |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txMediaCode" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 優待プロモーションフラグ | - | - | - | - | 商品連携マスタ.優待商品フラグ | - | 顧客が優待対象かどうかを判定するために使用する項目 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txPreferentialPromotionFlag" を設定 | キャンペーン設定商品リストに含まれる商品IDがMDMの商品マスタの優待商品フラグがONの場合、trueとする。 |
| /promotions/promotion/custom-attributes/custom-attribute | 優先順位 | - | - | - | - | キャンペーン設定.キャンペーン優先順位 | キャンペーン設定.キャンペーン優先順位 | - |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txPriority" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 商品コード_リスト_条件種別1 | - | - | - | - | キャンペーン設定条件.条件種別1 | - | キャンペーン設定条件.条件種別1 = 購入商品 かつ キャンペーン設定条件.条件種別2 = 商品コード または商品リストの場合、キャンペーン設定条件.条件種別1を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txProductCodeListConditionType1" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 商品コード_リスト_条件種別2 | - | - | - | - | キャンペーン設定条件.条件種別2 | - | キャンペーン設定条件.条件種別1 = 購入商品 かつ キャンペーン設定条件.条件種別2 = 商品コード または商品リストの場合、キャンペーン設定条件.条件種別2を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txProductCodeListConditionType2" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 販売経路_条件種別1 | - | - | - | - | キャンペーン設定条件.条件種別1 | - | キャンペーン設定条件.条件種別1 = 受注 かつ キャンペーン設定条件.条件種別2 = 販売経路の場合、キャンペーン設定条件.条件種別1を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txSalesChannelConditionType1" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 販売経路_条件種別2 | - | - | - | - | キャンペーン設定条件.条件種別2 | - | キャンペーン設定条件.条件種別1 = 受注 かつ キャンペーン設定条件.条件種別2 = 販売経路の場合、キャンペーン設定条件.条件種別2を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txSalesChannelConditionType2" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 販売経路_条件内容 | - | - | - | - | キャンペーン設定条件.条件内容 | - | キャンペーン設定条件.条件種別1 = 受注 かつ キャンペーン設定条件.条件種別2 = 販売経路の場合、キャンペーン設定条件.条件内容を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txSalesChannelContentsConditions" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 初回購入_条件種別1 | - | - | - | - | キャンペーン設定条件.条件種別1 | - | キャンペーン設定条件.条件種別1 = 受注 かつ キャンペーン設定条件.条件種別2 = 初回購入の場合、キャンペーン設定条件.条件種別1を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txFirstPurchaseConditionType1" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 初回購入_条件種別2 | - | - | - | - | キャンペーン設定条件.条件種別2 | - | キャンペーン設定条件.条件種別1 = 受注 かつ キャンペーン設定条件.条件種別2 = 初回購入の場合、キャンペーン設定条件.条件種別2を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txFirstPurchaseConditionType2" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 初回購入_条件内容 | - | - | - | - | キャンペーン設定条件.条件内容 | - | キャンペーン設定条件.条件種別1 = 受注 かつ キャンペーン設定条件.条件種別2 = 初回購入の場合、キャンペーン設定条件.条件内容を設定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txFirstPurchaseConditionContent" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | OFF率（画面表示用） | - | - | - | - | キャンペーン設定プロモーション.割引率 | キャンペーン設定プロモーション.割引率 | 商品詳細や一覧画面で○○％OFFの表示に使用想定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txDiscountRateForScreenDisplay" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 上限数量（1注文） | - | - | - | - | キャンペーン設定.注文毎注文上限数 | キャンペーン設定.注文毎注文上限数 | １注文で〇〇個以上購入不可のようなキャンペーンの上限数 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txOrderLimitPerOrder" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | 上限数量（同一CP累積） | - | - | - | - | キャンペーン設定.キャンペーン累積受注上限数 | キャンペーン設定.キャンペーン累積受注上限数 | 累計注文数〇〇個以上購入不可のようなキャンペーンの上限数 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txCumulativeOrderLimit" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | キャンペーン条件連番 | - | - | - | - | キャンペーン設定条件.キャンペーン条件連番 | キャンペーン設定条件.キャンペーン条件連番 | - |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txCampaignConditionSeqNo" を設定 | - |
| /promotions/promotion/custom-attributes/custom-attribute | プロモーション種別 | - | - | - | - | キャンペーン設定プロモーション.プロモーション種別 | キャンペーン設定プロモーション.プロモーション種別 | OMS側のプロモーション種別を設定\n01:プレゼント\n02:値引\n03:割引\n04:送料指定 |
| /promotions/promotion/custom-attributes/custom-attribute/@attribute-id | - | - | - | String | - | - | "txPromotionType" を設定 | - |

### 値引の場合の商品プロモーションのルール

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|------|--------------|-----|----------|--------|----------|------|
| /promotions/promotion/product-promotion-rule | 値引の場合の商品プロモーションのルール | - | - | - | - | - | - | BMの「対象商品を含まない」、商品ID指定で対象商品を設定する想定 |
| /promotions/promotion/product-promotion-rule/discounted-products/included-products/condition-group/product-id-condition/@operator | - | - | - | String | - | - | 「is equal」の文字列 | XMLサンプルに合わせて「is-equal」から「is equal」に修正 |
| /promotions/promotion/product-promotion-rule/discounted-products/included-products/condition-group/product-id-condition/product-id | - | - | - | String | - | キャンペーン設定プロモーション.商品コード | キャンペーン設定プロモーション.商品コードを設定する | - |
| /promotions/promotion/product-promotion-rule/simple-discount/amount | - | - | - | Number | - | キャンペーン設定プロモーション.値引額 | キャンペーン設定プロモーション.値引額 | - |

### 割引の場合の商品プロモーションのルール

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|------|--------------|-----|----------|--------|----------|------|
| /promotions/promotion/product-promotion-rule | 割引の場合の商品プロモーションのルール | - | - | - | - | - | - | - |
| /promotions/promotion/product-promotion-rule/discounted-products/included-products/condition-group/product-id-condition/@operator | - | - | - | String | - | - | 「is equal」の文字列 | XMLサンプルに合わせて「is-equal」から「is equal」に修正 |
| /promotions/promotion/product-promotion-rule/discounted-products/included-products/condition-group/product-id-condition/product-id | - | N | - | String | - | キャンペーン設定プロモーション.商品コード | キャンペーン設定プロモーション.商品コードを設定する | 商品リストの場合、複数のproduct-idタグが生成される |
| /promotions/promotion/product-promotion-rule/simple-discount/percentage | - | - | - | Number | - | キャンペーン設定プロモーション.割引率 | キャンペーン設定プロモーション.割引率 | - |

### 価格指定の場合の商品プロモーションのルール

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|------|--------------|-----|----------|--------|----------|------|
| /promotions/promotion/product-promotion-rule | 価格指定の場合の商品プロモーションのルール | - | - | - | - | - | - | - |
| /promotions/promotion/product-promotion-rule/discounted-products/included-products/condition-group/product-id-condition/@operator | - | - | - | String | - | - | 「is equal」の文字列 | XMLサンプルに合わせて「is-equal」から「is equal」に修正 |
| /promotions/promotion/product-promotion-rule/discounted-products/included-products/condition-group/product-id-condition/product-id | - | - | - | String | - | キャンペーン設定条件.条件内容 or キャンペーン設定商品.商品コード | 条件に応じて設定 | キャンペーン設定条件.条件種別1 = 購入商品 かつ キャンペーン設定条件.条件種別2 = 商品コードの場合、キャンペーン設定条件.条件内容を設定。複数レコードが存在する場合、product-idタグにて複数商品を指定する |
| /promotions/promotion/product-promotion-rule/simple-discount/price-book-price/pricebook-id | - | - | - | String | - | キャンペーン設定プロモーション.キャンペーン設定コード | txCampaignPricebookId + キャンペーン設定プロモーション.キャンペーン設定コード | - |

### プロモーション割り当て定義

| 要素パス | 説明 | 1orN | 値保持フラグ | 型 | データ例 | 取得元 | 加工内容 | 記事 |
|----------|------|--------------|-----|----------|--------|----------|------|------|
| /promotions/promotion-campaign-assignment | プロモーションの割り当て | N | - | - | - | - | - | - |
| /promotions/promotion-campaign-assignment/@promotion-id | プロモーションのID | - | ※ | String | - | キャンペーン設定プロモーション.キャンペーン設定コード、キャンペーン設定プロモーション.プロモーション番号 | キャンペーン設定.キャンペーン設定コード + "_" + キャンペーン設定プロモーション.プロモーション番号を設定する | 1～256文字 |
| /promotions/promotion-campaign-assignment/@campaign-id | キャンペーンのID | - | ※ | String | - | キャンペーン設定.キャンペーン設定コード | キャンペーン設定.キャンペーン設定コード | 1～256文字 |
| /promotions/promotion-campaign-assignment/qualifiers | キャンペーンの適用条件（限定子） | - | - | - | - | - | - | - |
| /promotions/promotion-campaign-assignment/qualifiers/@match-mode | - | - | - | String | - | - | anyを設定 | 「all」（すべて）又は「any」（どれか１つ）を設定 |
| /promotions/promotion-campaign-assignment/qualifiers/customer-groups | - | - | - | - | - | - | <customer-groups/>のタグを固定で設定 | - |
| /promotions/promotion-campaign-assignment/qualifiers/source-codes | - | - | - | - | - | - | <source-codes/>のタグを固定で設定 | - |
| /promotions/promotion-campaign-assignment/qualifiers/coupons | - | - | - | - | - | - | <coupons/>のタグを固定で設定 | - |
| /promotions/promotion-campaign-assignment/rank | ランク | - | - | Number | - | - | 設定不要 | 10～100\nランクが指定されているプロモーションは、ランクが指定されていないプロモーションよりも前に計算されます\nランクが指定されているプロモーションが2つある場合、ランクが低い方のプロモーションが先に計算されます |


## データ抽出SQLの業務仕様

### 1. データ抽出の基本方針
- ベーステーブル：campaign_instructions（キャンペーン設定マスタ）
- 差分抽出：指定された期間内の更新を対象
- 関連データの変更も含めて抽出

### 2. 差分抽出の対象範囲
以下のテーブルのいずれかが更新された場合、該当するキャンペーンを抽出対象とする：
- campaign_instructions（キャンペーン設定）
- campaign_customer（キャンペーン設定顧客）
- campaign_promotion（キャンペーン設定プロモーション）
- campaign_instructions_commodity（キャンペーン設定商品）
- campaign_combi_limit（キャンペーン併用不可）

### 3. キャンペーン条件の判定
- 受注条件：キャンペーン設定条件マスタの条件種別1が「受注」（コード値: '3'）
- ネット条件：キャンペーン設定条件マスタの条件種別2が「ネット/販売経路」（コード値: '304'）
- 上記の両方を満たすキャンペーンのみを対象とする

### 4. プロモーションの抽出条件
以下のいずれかを満たすプロモーションを抽出対象とする：

1. プレゼントプロモーション（種別：01）
   - 条件なし（全て抽出）

2. 上限数量設定あり
   - 上限数量(1注文)が設定されているもの
   - 上限数量(同一CP累積)が設定されているもの

3. その他のプロモーション（種別：02:値引, 03:割引）
   - 商品コード(201)または商品リスト(205)の条件があること
   - 以下の条件を含まないこと：
     * 会員(1)ランク(101)による条件
     * 購入商品(2)かつ部門(202)による条件
     * 購入商品(2)かつ大分類(203)による条件
     * 購入商品(2)かつ商品シリーズ(204)による条件
     * 購入商品(2)かつ基準数量(206)による条件
     * 購入商品(2)かつ基準種類(207)による条件

### 5. 出力データの更新制御
- sync_timestamp：前回同期時刻
- diff_base_timestamp：今回同期時刻
- この2つのタイムスタンプ間の更新を対象とする
