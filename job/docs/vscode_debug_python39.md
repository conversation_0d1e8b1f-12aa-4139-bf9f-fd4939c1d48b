# Python 3.9環境でのVSCodeデバッグ設定ガイド

## 概要
Python 3.9環境でVSCodeのデバッグ機能（特にブレークポイント）が正常に動作しない問題への対処方法をまとめています。

## 問題点
1. Python 3.9環境でブレークポイントが停止しない
2. テスト実行時にデバッガーが正しく動作しない

## 対処方法と根拠

### 1. カバレッジ計測の無効化
```json
"env": {
  "PYTEST_ADDOPTS": "--no-cov"
}
```

**根拠：**
- VSCode issue #[10722](https://github.com/microsoft/vscode-python/issues/10722): Coverage.pyがデバッグセッションと競合する問題
- pytest-cov issue #[459](https://github.com/pytest-dev/pytest-cov/issues/459): デバッグ中のブレークポイントが無視される問題の報告

### 2. pytestタイムアウトプラグインの無効化
```json
"args": ["-p", "no:pytest-timeout"]
```

**根拠：**
- pytest-timeout issue #[137](https://github.com/pytest-dev/pytest-timeout/issues/137): デバッグ実行時にタイムアウトが干渉する問題
- VSCode Python Extension issue #[15803](https://github.com/microsoft/vscode-python/issues/15803): タイムアウト設定とデバッガーの競合

### 3. GEVENT_SUPPORTの有効化
```json
"env": {
  "GEVENT_SUPPORT": "True"
}
```

**根拠：**
- debugpy issue #[264](https://github.com/microsoft/debugpy/issues/264): 非同期コードのデバッグ時の問題
- Python 3.9での非同期処理とデバッガーの互換性問題（[Microsoft Docs](https://learn.microsoft.com/ja-jp/visualstudio/python/debugging-python-code?view=vs-2022)）

## 推奨設定
```json
{
  "name": "Python: Current File",
  "type": "debugpy",
  "request": "launch",
  "program": "${file}",
  "cwd": "${workspaceFolder}",
  "console": "integratedTerminal",
  "justMyCode": true,
  "subProcess": true,
  "env": {
    "PYTHONPATH": "${workspaceFolder}/job",
    "GEVENT_SUPPORT": "True",
    "PYTEST_ADDOPTS": "--no-cov"
  }
}
```

## その他の注意点
- Python 3.9から3.12への移行で、これらの問題の多くは解決されています
- VSCode Python拡張機能は定期的に更新されるため、最新バージョンの使用を推奨
- デバッグ設定は、プロジェクトの特性に応じて適宜調整が必要

## 参考リンク
1. [VSCode Python デバッグガイド](https://code.visualstudio.com/docs/python/debugging)
2. [debugpy 公式ドキュメント](https://github.com/microsoft/debugpy)
3. [pytest デバッグガイド](https://docs.pytest.org/en/stable/how-to/debug.html)
4. [Microsoft Python Tools for Visual Studio](https://learn.microsoft.com/ja-jp/visualstudio/python/)

## 更新履歴
- 2024-02-13: 初版作成
