# キャンペーン関連テーブルER図

## テーブル対応表

| 物理名 | 論理名 |
|--------|--------|
| campaign_instructions | キャンペーン設定 |
| campaign_order_group | キャンペーン設定条件グループ |
| campaign_order | キャンペーン設定条件 |
| campaign_promotion | キャンペーン設定プロモーション |
| campaign_instructions_commodity | キャンペーン設定商品 |
| campaign_customer | キャンペーン設定顧客 |
| campaign_combi_limit | キャンペーン併用不可 |
| shop_mst | ショップマスタ |

```mermaid
erDiagram
    "キャンペーン設定 (campaign_instructions)" ||--o{ "キャンペーン設定条件グループ (campaign_order_group)" : "条件グループ設定"
    "キャンペーン設定 (campaign_instructions)" ||--o{ "キャンペーン設定プロモーション (campaign_promotion)" : "プロモーション設定"
    "キャンペーン設定 (campaign_instructions)" ||--o{ "キャンペーン設定商品 (campaign_instructions_commodity)" : "商品設定"
    "キャンペーン設定 (campaign_instructions)" ||--o{ "キャンペーン設定顧客 (campaign_customer)" : "顧客設定"
    "キャンペーン設定 (campaign_instructions)" ||--o{ "キャンペーン併用不可 (campaign_combi_limit)" : "併用不可設定"
    "キャンペーン設定条件グループ (campaign_order_group)" ||--o{ "キャンペーン設定条件 (campaign_order)" : "条件グループ化"
    "キャンペーン設定商品 (campaign_instructions_commodity)" }o--|| "ショップマスタ (shop_mst)" : "ショップ参照"

    "キャンペーン設定 (campaign_instructions)" {
        varchar campaign_instructions_code PK "キャンペーン設定コード"
        varchar campaign_instructions_name "キャンペーン設定名称"
        varchar campaign_type "キャンペーン種別"
        int delete_flg "削除フラグ"
        int campaign_priority "キャンペーン優先順位"
        varchar campaign_applied_scope "キャンペーン適用範囲"
        varchar campaign_use_limit "キャンペーン利用制限"
        int oneshot_order_limit "注文毎注文上限数"
        int campaign_quantity_limit "キャンペーン累積受注上限数"
        timestamp campaign_start_date "キャンペーン適用開始日"
        timestamp campaign_end_date "キャンペーン適用終了日"
        int present_use_flg "選択式プレゼント利用フラグ"
        int campaign_customer_flg "キャンペーン会員指定フラグ"
        int campaign_combi_limit_flg "キャンペーン併用制限フラグ"
        int permanent_campaign_flg "常設キャンペーンフラグ"
        varchar baitai_code "媒体コード"
        varchar campaign_description "キャンペーン内容"
    }
    
    "キャンペーン設定条件グループ (campaign_order_group)" {
        varchar campaign_instructions_code PK,FK "キャンペーン設定コード"
        int campaign_group_no PK "キャンペーン設定グループ番号"
        varchar campaign_joken_disp "キャンペーン条件（一覧表示用）"
        varchar exclude_joken_disp "キャンペーン除外条件（一覧表示用）"
    }
    
    "キャンペーン設定条件 (campaign_order)" {
        varchar campaign_instructions_code PK,FK "キャンペーン設定コード"
        int campaign_group_no PK,FK "キャンペーン設定グループ番号"
        varchar joken_type PK "条件分類"
        int campaign_joken_no PK "キャンペーン設定条件番号"
        varchar joken_kind1 "条件種別１"
        varchar joken_kind2 "条件種別２"
        varchar joken "条件内容"
        int joken_min "条件内容下限"
        int joken_max "条件内容上限"
        int regular_kaiji "定期回次"
        int joken_month_num "期限月数"
        varchar commodity_name "商品名称"
    }
    
    "キャンペーン設定プロモーション (campaign_promotion)" {
        varchar campaign_instructions_code PK,FK "キャンペーン設定コード"
        int promotion_no PK "プロモーション番号"
        varchar promotion_type "プロモーション種別"
        varchar shop_code "ショップコード"
        varchar commodity_code "商品コード"
        varchar commodity_name "商品名称"
        int present_qt "プレゼント数量"
        int discount_rate "値引率"
        int discount_amount "値引額"
        int discount_retail_price "値引後販売価格"
        int shipping_charge "送料"
    }
    
    "キャンペーン設定商品 (campaign_instructions_commodity)" {
        varchar campaign_instructions_code PK,FK "キャンペーン設定コード"
        varchar shop_code PK,FK "ショップコード"
        varchar commodity_code PK "商品コード"
        varchar joken_type "条件分類"
    }
    
    "キャンペーン設定顧客 (campaign_customer)" {
        varchar campaign_instructions_code PK,FK "キャンペーン設定コード"
        varchar customer_code PK "顧客コード"
        varchar joken_type "条件分類"
    }
    
    "キャンペーン併用不可 (campaign_combi_limit)" {
        varchar campaign_instructions_code PK,FK "キャンペーン設定コード"
        varchar campaign_combi_limit_code PK "併用不可キャンペーン設定コード"
    }
    
    "ショップマスタ (shop_mst)" {
        varchar shop_cd PK "店舗コード"
        varchar shop_name_full "店舗名全角"
        varchar zip_cd "郵便番号"
        varchar prefecture_cd "都道府県コード"
        varchar address1 "住所1"
        varchar address2 "住所2"
        varchar tel_num "電話番号"
        timestamp begin_date "開店日"
        timestamp end_date "閉店日"
    }
```
