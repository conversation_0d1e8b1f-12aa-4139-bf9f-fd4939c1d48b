#!/usr/bin/env python3
"""
pyproject.tomlから依存関係を抽出するスクリプト
"""
import sys
import tomli

def extract_dependencies(pyproject_path):
    """pyproject.tomlから依存関係リストを抽出する"""
    try:
        with open(pyproject_path, "rb") as f:
            pyproject_data = tomli.load(f)
        
        # poetry形式の依存関係を探す
        if "tool" in pyproject_data and "poetry" in pyproject_data["tool"]:
            dependencies = pyproject_data["tool"]["poetry"].get("dependencies", {})
            # python自体は依存関係から除外
            if "python" in dependencies:
                del dependencies["python"]
            
            # 依存関係をpip installの形式に変換
            formatted_deps = []
            for package, constraint in dependencies.items():
                if isinstance(constraint, str):
                    formatted_deps.append(f"{package}=={constraint}")
                elif isinstance(constraint, dict) and "version" in constraint:
                    formatted_deps.append(f"{package}=={constraint['version']}")
                else:
                    formatted_deps.append(package)
            
            return formatted_deps
        
        # PDM形式の依存関係を探す
        elif "project" in pyproject_data and "dependencies" in pyproject_data["project"]:
            dependencies = pyproject_data["project"]["dependencies"]
            
            # 依存関係をpip installの形式に変換
            formatted_deps = []
            for dep in dependencies:
                if " @ " in dep:  # URLからのインストールはそのまま使用
                    formatted_deps.append(dep)
                elif "==" in dep:  # バージョン指定があればそのまま使用
                    formatted_deps.append(dep)
                else:
                    # バージョン指定がなければ、パッケージ名だけ追加
                    formatted_deps.append(dep.split(";")[0].strip())
            
            return formatted_deps
        
        # 従来のsetup.pyスタイルまたはその他の形式
        elif "build-system" in pyproject_data and "requires" in pyproject_data["build-system"]:
            return pyproject_data["build-system"]["requires"]
        
        else:
            print("依存関係情報が見つかりませんでした。対応していない形式です。", file=sys.stderr)
            return []
            
    except Exception as e:
        print(f"エラー: {str(e)}", file=sys.stderr)
        return []

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(f"使用法: {sys.argv[0]} <pyproject.toml>", file=sys.stderr)
        sys.exit(1)
    
    dependencies = extract_dependencies(sys.argv[1])
    
    # スペースを含む場合はクォートで囲む
    for dep in dependencies:
        if " " in dep:
            print(f'"{dep}"')
        else:
            print(dep)
