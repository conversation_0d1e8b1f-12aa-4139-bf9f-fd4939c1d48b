[project]
name = "glue-job"
version = "0.1.0"
description = "Glue ジョブのモジュール"
authors = [{ name = "dlpf", email = "<EMAIL>" }]
requires-python = "==3.9.*"
dependencies = [
    "paramiko>=3.5.1",
    "office365-rest-python-client>=2.5.14",
    "urllib3<2.0.0",
    "psycopg2-binary<2.9.0",
    "boto3>=1.26.0,<2.0.0",
    "pandas==2.2.3",
    "sqlalchemy==2.0.36",
    "requests==2.32.3",
    "requests_oauthlib==2.0.0",
    "fsspec==2024.6.1",
    "s3fs==2024.6.1"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["source"]

[tool.hatch.build]
include = ["source/**/*.py", "pyproject.toml"]

[tool.pytest.ini_options]
addopts = "--cov --cov-config=.coveragerc"
testpaths = ["test"]
python_files = ["test_*.py"]

[dependency-groups]
dev = [
    "black>=24.10.0",
    "cfn-lint>=1.22.2",
    "moto[all,secretsmanager,ssm]==4.2.0",
    "pylint>=3.3.3",
    "pytest>=8.3.4",
    "pytest-cov>=6.0.0",
    "uv>=0.1.25",                          # uvパッケージマネージャーを追加
    "tomli>=2.0.1",                        # Python 3.9以前でTOMLファイルを読み込むために必要
    "build>=1.2.2.post1",                  # ホイール作成用
    "wheel>=0.42.0",                       # ホイール作成用
    "setuptools>=69.0.0",                  # setup.pyと連携するために必要
]

[tool.hatch.envs.default]
dependencies = ["pytest-cov"]