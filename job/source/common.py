import os
import json
import boto3
import logging
import time
import sys
import re
from typing import Any, Callable, Dict
from botocore.config import Config
from botocore.exceptions import ClientError

from source.glue_logger import GlueLogger


def initialize_env() -> None:
    """
    Parameter Store から環境変数設定を取得し、
    実行プロセスの環境変数として設定する

    Parameter Store:
    - パラメータ名: /glue/job/environment-config
    - 値: JSON形式の環境変数設定
    {
        "process": {
            "TZ": "Asia/Tokyo"
        },
        "aws": {
            "S3_BUCKET_NAME": "my-bucket-name",
            "REGION_NAME": "ap-northeast-1",
            "S3_RETRY_LIMIT": 3,
            "S3_RETRY_MODE": "standard"
        }
    }

    Raises:
        Exception: Parameter Store からの取得に失敗した場合
        json.JSONDecodeError: JSONパースに失敗した場合
    """
    try:
        # リージョンの明示的な設定
        region_name = os.environ.get("AWS_DEFAULT_REGION", "ap-northeast-1")

        # Parameter Store クライアントの初期化（リージョン指定）
        ssm_client = boto3.client("ssm", region_name=region_name)

        # パラメータを取得
        response = ssm_client.get_parameter(
            Name="/glue/job/environment-config", WithDecryption=True
        )

        # JSON形式の値をパース
        env_settings = json.loads(response["Parameter"]["Value"])

        # プロセス全体の環境変数を設定
        if "process" in env_settings:
            old_tz = os.environ.get("TZ")

            for key, value in env_settings["process"].items():
                os.environ[key] = str(value)
                logging.info(f"Set process environment variable: {key}")

            # TZが変更された場合、time.tzset()を呼び出し
            new_tz = os.environ.get("TZ")
            if old_tz != new_tz and hasattr(time, "tzset"):
                time.tzset()
                logging.info("Updated timezone settings")

        # AWS関連の環境変数を設定
        if "aws" in env_settings:
            for key, value in env_settings["aws"].items():
                os.environ[key] = str(value)
                logging.info(f"Set AWS environment variable: {key}")

        # デフォルトのAWSリージョンを設定（Tokyoリージョン）
        if "AWS_DEFAULT_REGION" not in os.environ:
            os.environ["AWS_DEFAULT_REGION"] = "ap-northeast-1"
            logging.info(f"Set default AWS region: {os.environ['AWS_DEFAULT_REGION']}")

    except json.JSONDecodeError as e:
        logging.error(f"Failed to parse environment settings JSON: {str(e)}")
        raise
    except Exception as e:
        logging.error(f"Failed to initialize environment variables: {str(e)}")
        raise


def get_job_params() -> Dict[str, Any]:
    """
    ジョブパラメータをプロセスの入力パラメータから取得する

    Returns:
        Dict[str, Any]: パラメータの辞書

    Raises:
        ValueError: パラメータの形式が不正な場合
    """
    params = {}
    args = sys.argv[1:]
    try:
        for i in range(0, len(args), 2):
            key = args[i].lstrip("-")  # '--'または'-'を削除
            value = args[i + 1]
            params[key] = value
    except Exception as e:
        raise ValueError(f"Invalid parameter format: {str(e)}")

    return params


def retry_function(
    func: Callable,
    logger: GlueLogger,
    process_name: str,
    info_msg_id: str,
    error_msg_id: str,
    retry_limit: int = 3,
    retry_interval: float = 1.0,
    args: tuple = (),
) -> Any:
    """
    指定された関数をリトライ処理付きで実行する

    Args:
        func (Callable): 実行対象の関数
        logger (GlueLogger): ロガーインスタンス
        process_name (str): 処理名
        info_msg_id (str): リトライ時に出力するINFOログのメッセージID
        error_msg_id (str): 最終失敗時に出力するERRORログのメッセージID
        retry_limit (int): リトライ回数の上限
        retry_interval (float): リトライ間隔（秒）
        args (tuple): 関数に渡す引数

    Returns:
        Any: 関数の実行結果

    Raises:
        Exception: 全てのリトライが失敗した場合
    """
    for attempt in range(retry_limit):
        try:
            return func(*args)
        except Exception:
            if attempt == retry_limit - 1:
                # stack_trace = traceback.format_exc()
                logger.loggerByMsgId(error_msg_id, (process_name))
                raise

            logger.loggerByMsgId(info_msg_id, (process_name))
            time.sleep(retry_interval)


def create_s3_client(
    region_name: str = None, max_attempts: int = 3, mode: str = "standard"
) -> boto3.client:
    """
    指定された設定でS3クライアントを作成し、返却する。

    Args:
        region_name (str, optional): 使用するAWSリージョン。デフォルトはNone（デフォルトリージョンを使用）。
        max_attempts (int, optional): 失敗したリクエストの最大再試行回数。デフォルトは3。
        mode (str, optional): 再試行モード（'legacy'、'standard'、または'adaptive'）。デフォルトは'standard'。

    Returns:
        boto3.client: 設定されたS3クライアント

    Raises:
        ValueError: 無効なモードが指定された場合
    """
    if mode not in ["legacy", "standard", "adaptive"]:
        raise ValueError(
            "無効なモードです。'legacy'、'standard'、または'adaptive'を指定してください。"
        )

    config = Config(retries={"max_attempts": max_attempts, "mode": mode})

    return boto3.client("s3", region_name=region_name, config=config)


def get_s3_client():
    region_name = os.environ.get("REGION_NAME", "ap-northeast-1")
    retry_limit = int(os.environ.get("S3_RETRY_LIMIT", 3))
    retry_mode = os.environ.get("S3_RETRY_MODE", "standard")
    return create_s3_client(
        region_name=region_name, max_attempts=retry_limit, mode=retry_mode
    )


def get_s3_file(bucket: str, key: str, local_path: str) -> None:
    """
    S3からファイルを取得し、ローカルに保存する

    Args:
        bucket (str): S3バケット名
        key (str): S3オブジェクトキー
        local_path (str): ローカルの保存先パス

    Raises:
        ClientError: S3操作に失敗した場合
    """
    s3_client = get_s3_client()
    try:
        s3_client.download_file(bucket, key, local_path)
    except ClientError as e:
        logging.error(f"Failed to download file from S3: {str(e)}")
        raise


def put_s3_file(local_path: str, bucket: str, key: str) -> None:
    """
    ローカルファイルをS3に配置する

    Args:
        local_path (str): アップロードするローカルファイルのパス
        bucket (str): S3バケット名
        key (str): S3オブジェクトキー

    Raises:
        ClientError: S3操作に失敗した場合
    """
    s3_client = get_s3_client()
    try:
        s3_client.upload_file(local_path, bucket, key)
    except ClientError as e:
        logging.error(f"Failed to upload file to S3: {str(e)}")
        raise


def backup_input_file(bucket: str, source_key: str, backup_key: str) -> None:
    """
    インプットファイルをバックアップする

    Args:
        bucket (str): S3バケット名
        source_key (str): 元のS3オブジェクトキー
        backup_key (str): バックアップ先のS3オブジェクトキー

    Raises:
        ClientError: S3操作に失敗した場合
    """
    s3_client = get_s3_client()
    try:
        s3_client.copy_object(
            CopySource={"Bucket": bucket, "Key": source_key},
            Bucket=bucket,
            Key=backup_key,
        )
    except ClientError as e:
        logging.error(f"Failed to backup input file: {str(e)}")
        raise


def delete_input_file(bucket: str, key: str) -> None:
    """
    インプットファイルを削除する

    Args:
        bucket (str): S3バケット名
        key (str): 削除するS3オブジェクトキー

    Raises:
        ClientError: S3操作に失敗した場合
    """
    s3_client = get_s3_client()
    try:
        s3_client.delete_object(Bucket=bucket, Key=key)
    except ClientError as e:
        logging.error(f"Failed to delete input file: {str(e)}")
        raise

def find_s3_prefix(aws_s3_dir: str, bucket_name: str) -> tuple[str, str]:
    """
    指定されたS3ディレクトリ（プレフィックス）に基づき、日付サフィックス付きのプレフィックスを検索します。
    例: 'a/b/' -> ('a/b_20240321/', '20240321') or ('a/b/', '')

    Args:
        aws_s3_dir (str): S3バケット内のディレクトリ（プレフィックス）。バケット名やファイル名は含まれません。
        bucket_name (str): 対象のS3バケットの名前。
    Returns:
        tuple[str, str]: (プレフィックス, 日付文字列)のタプル。日付サフィックスがない場合は空文字を返します。
    Raises:
        ValueError: プレフィックスが見つからない場合。
    """
    # Remove trailing slash if exists
    aws_s3_dir_prefix = aws_s3_dir.rstrip('/')

    # Initialize S3 client
    s3_client = get_s3_client()

    # List all objects that match the prefix aws_s3_dir
    response = s3_client.list_objects_v2(
        Bucket=bucket_name,
        Prefix=aws_s3_dir_prefix,
        Delimiter='/'
    )

    # Extract the prefix matches from the response
    if 'CommonPrefixes' not in response or not response['CommonPrefixes']:
        raise ValueError(f"指定されたディレクトリに一致するプレフィックスが見つかりません: {aws_s3_dir}")

    prefixes = [prefix['Prefix'] for prefix in response['CommonPrefixes']]

    # Sort the prefixes in ascending order
    sorted_prefixes = sorted(prefixes)

    # Get the first prefix
    selected_prefix = sorted_prefixes[0]

    # Try to extract date suffix from the prefix (assuming format like 'a/b_20240321/')
    prefix_parts = selected_prefix.rstrip('/').split('_')
    if len(prefix_parts) > 1:
        date_str = prefix_parts[-1]
        if date_str.isdigit() and len(date_str) == 8:
            return selected_prefix, date_str
    
    # If no date suffix found, return the prefix with empty date string
    return selected_prefix, ""

def find_s3_file(aws_s3_dir, file_name, bucket_name: str) -> str:
    """
    指定されたS3のパス、ファイル名に基づき、もっとも古い日付付きのファイル名を検索します。
    例: 'filename*.ext' -> 'filename20240321.ext'

    Args:
        aws_s3_dir (str): S3バケット内のディレクトリ（プレフィックス）。バケット名やファイル名は含まれません。
        file_name (str): S3バケット内のファイル名(ファイル名には*を含む）
        bucket_name (str): 対象のS3バケットの名前。
    Returns:
        str: 指定されたファイル名に合致する、日付が最も古いファイルの名前。
    Raises:
        ValueError: 指定されたファイル名に合致するファイルが見つからない場合。
    """

    prefix, _ = find_s3_prefix(aws_s3_dir, bucket_name)

    # Initialize S3 client
    s3_client = get_s3_client()

    # List all objects that match the prefix aws_s3_dir
    response = s3_client.list_objects_v2(
        Bucket=bucket_name,
        Prefix=prefix,
        Delimiter='/'
    )

    # 指定したディレクトリ直下のファイルを取得
    files = [
        obj['Key'] for obj in response.get('Contents', [])
        if obj['Key'].startswith(prefix) and '/' not in obj['Key'][len(prefix):]
    ]

    # ファイル名だけを抽出
    file_names = [os.path.basename(file) for file in files]
    
    # 取得するファイルパターンの決定
    escape_file_name = file_name.replace(".", "\.")  # "."をエスケープするための処理
    file_pattern = re.compile(fr'{escape_file_name.replace("*", ".*")}', re.IGNORECASE)  # "*"を".*"に変換してパターン化

    targets = []
    for file in file_names:
        if file_pattern.match(file):
            targets.append(file)
    else:
        if not targets:
            raise ValueError(f"指定されたファイル名に一致するプレフィックスが見つかりません: {file_name}")
    return sorted(targets)[0]

def move_file_to_error_dir(input_dir: str, bucket_name: str) -> None:
    """
    バックアップフラグに基づいて、入力ディレクトリからエラーディレクトリにファイルを移動します。

    Args:
        input_dir (str): 入力ディレクトリ。
        bucket_name (str): S3バケットの名前。

    Raises:
        ClientError: S3操作にエラーが発生した場合。
    """

    # Extract the file name and path parts
    file_name = input_dir.split('/')[-1]
    path_parts = input_dir.split('/')[1:-1]

    # Construct the error directory
    error_dir = f"error/{'/'.join(path_parts)}/{file_name}"

    # Move the file using the S3 SDK
    s3_client = get_s3_client()
    try:
        s3_client.copy_object(
            CopySource={'Bucket': bucket_name, 'Key': input_dir},
            Bucket=bucket_name,
            Key=error_dir
        )
        s3_client.delete_object(Bucket=bucket_name, Key=input_dir)
    except ClientError as e:
        print(f"Failed to move file to error directory: {str(e)}")
        raise


def str_to_bool(flag_str: str) -> bool:
    """
    フラグ文字列が"True"（大文字小文字含む）の場合、True
    それ以外はFalseを返します。

    Args:
        flag_str (str): フラグ文字列（"True"（大文字小文字含む）かそれ以外を想定）
    """
    if flag_str.upper() == 'TRUE':
        return True
    else:
        return False
    
class ExternalConnection:

    def __init__(self):
        """
        初期化処理
        Args:なし
        """
        #リトライ回数を設定
        self.max_retries = 4
 
    # FTP関連メソッド
    def ftp_login(self, ftp, username, password):
        """
        FTP接続にログインする（リトライ機能付き）。

        Args:
            ftp (FTP): FTPオブジェクト
            username (str): ユーザー名
            password (str): パスワード
        
        Raises:
            BaseException: FTP接続でログインに失敗した場合。
        """    
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                ftp.login(user=username, passwd=password)
                break  
            except BaseException as e:
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to log in to the FTP connection: {str(e)}")
                    raise 
    
    def ftp_nlst(self, ftp):
        """
        FTPサーバーからファイルリストを取得する（リトライ機能付き） 。

        Args:
            ftp (FTP): FTPオブジェクト
        
        Returns:
            files: ファイル名のリスト
        
        Raises:
            BaseException: FTP接続でファイルリストの取得に失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                files = ftp.nlst()
                return files
            except ClientError as e:
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to retrieve the file list from the FTP server: {str(e)}")
                    raise

    def ftp_retrbinary(self, ftp, file, temp_buffer):
        """
        FTPサーバーからバイナリファイルをダウンロードする（リトライ機能付き）。

        Args:
            ftp (FTP): FTPオブジェクト
            file (str): ダウンロードするファイルの名前
            temp_buffer (BytesIO or file-like object): ダウンロードしたデータを書き込むためのバッファ
        
        Raises:
            BaseException: FTP接続でバイナリファイルのダウンロードに失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                ftp.retrbinary(f'RETR {file}', temp_buffer.write)
                break
            except BaseException as e:
                if retry_num  < self.max_retries:
                    retry_num += 1
                    time.sleep(1)
                else:
                    logging.error(f"Failed to download the binary file from the FTP server: {str(e)}")
                    raise
    
    def ftp_storbinary(self, ftp, remotePath, s3_file_data):
        """
        FTPサーバーにファイルをアップロードする（リトライ機能付き）。

        Args:
            ftp (FTP): FTPオブジェクト
            remotePath (str): サーバー上の保存先パス
            s3_file_data (dict): アップロードするファイルデータオブジェクト
        
        Raises:
            BaseException: FTP接続でファイルのアップロードに失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                ftp.storbinary(f"STOR {remotePath}", s3_file_data['Body'])
                break  
            except BaseException as e: 
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1)
                else:
                    logging.error(f"Failed to upload the file to FTP server: {str(e)}")
                    raise
    
    def ftp_delete(self, ftp, file):
        """
        FTPサーバーからファイルを削除する（リトライ機能付き）。

        Args:
            ftp (FTP): FTPオブジェクト
            file (str): 削除するファイルの名前
        
        Raises:
            BaseException: FTP接続でファイルの削除に失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                ftp.delete(file)
                break
            except BaseException as e:
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to delete the file from the FTP server: {str(e)}")
                    raise

    # SFTP関連メソッド
    def connect(self, client, hostname, username, password = None, pkey = None):
        """
        ユーザー名とパスワードもしくは秘密鍵を使用してSSH接続を確立する（リトライ機能付き）。
        username は必須項目で、password と pkey は任意項目。

        Args:
            client (SSHClient): ParamikoのSSHクライアントオブジェクト
            hostname (str): 接続するサーバーのホスト名またはIPアドレス
            username (str): ユーザー名
            password (str): パスワード
            pkey (PKey): 秘密鍵
        
        Raises:
            BaseException: SSH接続に失敗した場合もしくはパスワードと秘密鍵の両方が設定された場合。
        """
        if password and pkey:
            raise BaseException("Password and pkey cannot both be set. Choose one authentication method.")
         
        retry_num = 0
        if password:
            while retry_num <= self.max_retries:
                try:
                    client.connect(hostname=hostname, username=username, password=password)
                    break
                except BaseException as e: 
                    if retry_num < self.max_retries:
                        retry_num += 1
                        time.sleep(1) 
                    else:
                        logging.error(f"Failed to establish SSH connection: {str(e)}")
                        raise
        else:
            while retry_num <= self.max_retries:
                try:
                    client.connect(hostname=hostname, username=username, pkey=pkey)
                    break
                except BaseException as e: 
                    if retry_num < self.max_retries:
                        retry_num += 1
                        time.sleep(1) 
                    else:
                        logging.error(f"Failed to establish SSH connection: {str(e)}")
                        raise

    def open_sftp(self, client):
        """
        SSH接続を利用してSFTPセッションを開始する（リトライ機能付き）。

        Args:
            client (SSHClient): ParamikoのSSHクライアントオブジェクト

        Raises:
            BaseException: SSHセッションの開始に失敗した場合。
        """ 
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                sftp = client.open_sftp()
                return sftp
            except BaseException as e: 
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to initiate SSH session: {str(e)}")
                    raise

    def transport_connect(self, transport, username, password = None, pkey = None):
        """
        ユーザー名とパスワードもしくは秘密鍵を使用してSFTPサーバーに接続する（リトライ機能付き） 。
        username は必須項目で、password と pkey は任意項目。

        Args:
            transport (Transport): ParamikoのTransportオブジェクト
            username (str): ユーザー名
            password (str): パスワード
            pkey (PKey): 秘密鍵

        Raises:
            BaseException: SFTPサーバーの接続に失敗した場合もしくはパスワードと秘密鍵の両方が設定された場合。
        """
        if password and pkey:
            raise BaseException("Password and pkey cannot both be set. Choose one authentication method.")
    
        retry_num = 0
        if password:
            while retry_num <= self.max_retries:
                try:
                    transport.connect(username=username, password=password)
                    break
                except BaseException as e:
                    if retry_num < self.max_retries:
                        retry_num += 1
                        time.sleep(1) 
                    else:
                        logging.error(f"Failed to establish SFTP connection using username and password: {str(e)}")
                        raise
        
        else:
            while retry_num <= self.max_retries:
                try:
                    transport.connect(username=username, pkey=pkey)
                    break
                except BaseException as e:
                    if retry_num < self.max_retries:
                        retry_num += 1
                        time.sleep(1) 
                    else:
                        logging.error(f"Failed to establish SFTP connection using username and pkey: {str(e)}")
                        raise
    
    def sftp_listdir(self, sftp):
        """
        SFTPサーバー上で現在のディレクトリのファイルリストを取得する（リトライ機能付き） 。

        Args:
            sftp (SFTPClient): Paramiko SFTPClientオブジェクト

        Raises:
            BaseException: SFTP接続でファイルリストの取得に失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                files = sftp.listdir()
                return files
            except BaseException as e:
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to retrieve the file list from the SFTP server: {str(e)}")
                    raise

    def sftp_getfo(self, sftp, file, temp_buffer):
        """
        SFTPサーバー上で指定されたファイルを取得し、ファイルオブジェクト（temp_buffer）に書き込む（リトライ機能付き） 。

        Args:
            sftp (SFTPClient): Paramiko SFTPClientオブジェクト
            file (str): ダウンロードするファイルの名前
            temp_buffer (BytesIO or file-like object): ダウンロードしたデータを書き込むためのバッファ

        Raises:
            BaseException: SFTP接続でファイルリストの取得に失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                sftp.getfo(file, temp_buffer)
                return temp_buffer
            except BaseException as e:
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to retrieve the file from the SFTP server: {str(e)}")
                    raise

    def sftp_putfo(self, sftp, s3_file_data, output_full_path):
        """
        SFTPサーバーにファイルをアップロードする（リトライ機能付き）。

        Args:
            sftp (SFTPClient): Paramiko SFTPClientオブジェクト
            s3_file_data (dict): アップロードするファイルデータオブジェクト
            output_full_path (str): サーバー上の保存先パス
        
        Raises:
            BaseException: SFTP接続でファイルのアップロードに失敗した場合。
        """ 
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                sftp.putfo(s3_file_data, output_full_path)
                break
            except BaseException as e: 
                if retry_num < self.max_retries:
                    retry_num += 1
                    time.sleep(1) 
                else:
                    logging.error(f"Failed to upload the file to SFTP server: {str(e)}")
                    raise
    
    def sftp_remove(self, sftp, file):
        """
        SFTPサーバーからファイルを削除する（リトライ機能付き）。

        Args:
            sftp (SFTPClient): Paramiko SFTPClientオブジェクト
            file (str): 削除するファイルの名前
        
        Raises:
             BaseException: SFTP接続でファイルの削除に失敗した場合。
        """
        retry_num = 0
        while retry_num <= self.max_retries:
            try:
                sftp.remove(file)
                break
            except BaseException as e: 
                if retry_num < self.max_retries:
                    rretry_num += 1
                    time.sleep(1)
                else:
                    logging.error(f"Failed to remove the file from the SFTP server: {str(e)}")
                    raise
