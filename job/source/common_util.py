import codecs
from configparser import ConfigParser
import os
from pathlib import Path
from typing import Any, Dict

import yaml


def get_resource_path(resource_type: str, file_name: str) -> Path:
    """リソースファイルのパスを解決する

    Args:
        resource_type: リソースタイプ（'config' or 'sql'）
        file_name: ファイル名

    Returns:
        Path: リソースファイルのパス
    """
    base_path = Path(os.path.dirname(__file__))
    return base_path / resource_type / file_name


def load_log_config(file_name: str) -> ConfigParser:
    """INI形式のログ設定ファイルを読み込む

    Args:
        file_name: 設定ファイル名

    Returns:
        ConfigParser: 設定内容
    """
    config = ConfigParser()
    config_path = get_resource_path("config", file_name)
    config.read(config_path, encoding="utf-8")
    return config


def load_yaml_config(file_name: str) -> Dict[str, Any]:
    """YAML設定ファイルを読み込む

    Args:
        file_name: YAMLファイル名

    Returns:
        Dict[str, Any]: 設定内容
    """
    config_path = get_resource_path("config", file_name)
    with open(config_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)


def load_sql_config(file_name: str) -> str:
    """SQLファイルを読み込む

    Args:
        file_name: SQLファイル名

    Returns:
        str: SQLクエリ
    """
    sql_path = get_resource_path("sql", file_name)
    with open(sql_path, "r", encoding="utf-8") as f:
        return f.read()


def normalize_encoding(encoding: str) -> str:
    """
    文字コードを正規化する共通処理
    Args:
        encoding: 入力文字コード
    Returns:
        str: 正規化された文字コード
    """
    if not encoding:
        return encoding

    try:
        # codecsで正規化
        codec_info = codecs.lookup(encoding)
        normalized = codec_info.name

        # 正規化ルール
        if normalized in ["shift_jis", "ms932", "sjis", "cp932"]:
            return "ms932"  # 内部的にはms932を使用
        elif normalized == "utf-8":
            return "utf_8"  # アンダースコア形式に統一

        return normalized

    except LookupError:
        return encoding
