common:
  encoding: utf-8
  format: csv2tsv
input:
  csv_options:
    delimiter: ","
    has_header: false
    line_ending: "\r\n"
output:
  tsv_options:
    delimiter: "\t"
    has_header: true
    line_ending: "\n"
header_definition:
  columns:
    - name: "受付番号"
    - name: "顧客番号"
    - name: "レコード番号"
    - name: "顧客名"
    - name: "郵便番号"
    - name: "顧客住所１"
    - name: "顧客住所２"
    - name: "顧客住所３"
    - name: "電話番号"
    - name: "都道府県コード"
    - name: "顧客フラグ"
    - name: "注文日"
    - name: "支払区分"
    - name: "合計金額"
    - name: "配達先顧客名"
    - name: "配達先郵便番号"
    - name: "配達先住所１"
    - name: "配達先住所２"
    - name: "配達先住所３"
    - name: "配達先電話番号"
    - name: "ギフトフラグ"
    - name: "配達希望日"
    - name: "夜間可否"
    - name: "化粧品金額"
    - name: "健康食品金額"
    - name: "インナーウェア金額"
    - name: "更新日時"
    - name: "伝票出力日"
    - name: "バーコード"
    - name: "業者フラグ"
    - name: "ステータスフラグ"
    - name: "出庫伝票番号"
    - name: "注文番号"
    - name: "病院名"
    - name: "出荷日"
    - name: "出荷予定日"
    - name: "包装数"
    - name: "着店コード"
    - name: "定期フラグ"
    - name: "配達BOX区分"
    - name: "航空輸送可否"
    - name: "消費税"
    - name: "コンビニ受取可否"
    - name: "PUDO利用可否"
    - name: "入庫予定可否"
    - name: "過受注可否"
    - name: "予約受注可否"
    - name: "ギフトラッピング可否"
    - name: "注文監視可否"
    - name: "不足可否"
    - name: "航空便不可可否"
    - name: "さとふる可否"
    - name: "特別社販可否"
    - name: "らくがき板可否"
    - name: "複数サンプル可否"
    - name: "伝票サイズコード"
    - name: "倉庫コード"
    - name: "取扱店ＣＤ"
    - name: "取込可否"
    - name: "取込日"
