common:
  encoding: utf-8
  format: csv2tsv
input:
  csv_options:
    delimiter: ","
    has_header: false
    line_ending: "\r\n"
output:
  tsv_options:
    delimiter: "\t"
    has_header: true
    line_ending: "\n"
header_definition:
  columns:
    - name: "受付番号"
    - name: "連番"
    - name: "商品コード"
    - name: "数量"
    - name: "伝票出力日"
    - name: "バーコード"
    - name: "業者フラグ"
    - name: "送り状ブランチNO."
    - name: "部門区分"
    - name: "商品金額"
    - name: "注文区分"
