common:
  encoding: cp932
  format: csv2tsv
input:
  csv_options:
    delimiter: ","
    has_header: false
output:
  tsv_options:
    delimiter: "\t"
    has_header: true
header_definition:
  columns:
    - name: "ロジマネ入出庫区分"
    - name: "伝票日付"
    - name: "伝票番号"
    - name: "倉庫管理番号"
    - name: "数量"
    - name: "入出庫区分"
    - name: "センターコード"
    - name: "在庫区分"
    - name: "確保倉庫（Commons対象店舗）"
    - name: "センターコード（相手）"
    - name: "在庫区分（相手）"
    - name: "確保倉庫（相手）"
    - name: "入出荷先CD"
    - name: "入出荷先名称"
    - name: "事由コード"
    - name: "事由コード名称"
    - name: "事由コード内訳"
