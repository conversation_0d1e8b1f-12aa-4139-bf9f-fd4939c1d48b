common:
  format: "db2fixed"
  encoding: "shift-jis" # 固定長ファイルはshift-jis固定

output:
  encoding: shift-jis # 文字エンコーディング（固定長ファイルなので、shift-jisに固定）
  format: fixed-length # 出力形式（固定値: fixed-length）
  line_ending: "\r\n" # 改行コード（任意）

fixed_length:
  total_length: 3840 # フィールド長の合計
  fields:
    - name: "input_no" #入力番号
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "input_sys_type" #入力システム区分
      length: 3
      type: "string"
      align: "left"
      padding_char: " "

    - name: "corp_cd" #会社コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "vote_employee_cd" #起票社員コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "vote_dept_cd" #起票部門コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "approval_employee_cd" #承認社員コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "approval_date" #承認日付
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "approval_status_type" #承認状態区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "journal_type" #仕訳種別区分
      length: 2
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_date" #伝票日付
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_no" #伝票番号
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_ope_ban_type" #伝票操作禁止区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "journal_reference_type" #仕訳基準種別区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "input_unit_no" #入力単位番号
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "xml_db_seq_key" #XML-DB連番キー
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "transfer_link_key" #振替リンクキー
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve1" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "fixes_reason_code" #修正事由コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "business_code" #業務コード
      length: 3
      type: "string"
      align: "left"
      padding_char: " "

    - name: "form_code" #形態コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "order_top_char" #オーダ 冠記号
      length: 2
      type: "string"
      align: "left"
      padding_char: " "

    - name: "order_item" #オーダ 費目
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "order_other" #オーダ その他
      length: 18
      type: "string"
      align: "left"
      padding_char: " "

    - name: "wh_code" #倉庫コード
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "join_segment_code_1" #連結セグメントコード1
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "join_segment_code_2" #連結セグメントコード2
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "join_segment_code_3" #連結セグメントコード3
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_corp_cd" #相手会社コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_corp_join_segment_code_1" #相手会社連結セグメントコード1
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_corp_join_segment_code_2" #相手会社連結セグメントコード2
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_corp_join_segment_code_3" #相手会社連結セグメントコード3
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_user_open_date_1" #伝票ユーザ開放日付1(遡及年月日)
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_user_open_code_1" #伝票ユーザ開放コード1(注文番号/社給番号/伝票番号)
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_user_open_code_2" #伝票ユーザ開放コード2(請求支払日付・回収予定日付)
      length: 16
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve2" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_remarks" #伝票備考
      length: 192
      type: "string"
      align: "left"
      padding_char: " "

    - name: "approval_remarks" #承認備考
      length: 192
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_user_open_area" #伝票ユーザ開放域
      length: 192
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_user_open_area_2" #伝票ユーザ開放域2(付加情報3)
      length: 60
      type: "string"
      align: "left"
      padding_char: " "

    - name: "line_num" #行番号
      length: 9
      type: "string"
      align: "left"
      padding_char: " "

    - name: "slip_detail_lending_type" #伝票明細貸借区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "account_code" #勘定科目コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "accounting_dept_code" #会計部門コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "details_type" #細目識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "details_code" #細目コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "items_type" #内訳識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "items_code" #内訳コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_1" #集計拡張コード1識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_1_type" #集計拡張コード1
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_2" #集計拡張コード2識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_2_type" #集計拡張コード2
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_3" #集計拡張コード3識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_3_type" #集計拡張コード3
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_4" #集計拡張コード4識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_4_type" #集計拡張コード4
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_5" #集計拡張コード5識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "count_ext_code_5_type" #集計拡張コード5
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_1" #検索拡張コード1識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_1_type" #検索拡張コード1
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_2" #検索拡張コード2識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_2_type" #検索拡張コード2
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_3" #検索拡張コード3識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_3_type" #検索拡張コード3
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_4" #検索拡張コード4識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_4_type" #検索拡張コード4
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_5" #検索拡張コード5識別区分
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "search_ext_code_5_type" #検索拡張コード5
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "business_partner_code" #取引先コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "segment_code" #セグメントコード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "cost_burden_center_code" #負担元コストセンタコード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_cash_code" #請求支払先コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "business_segment_code" #事業セグメントコード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "region_segment_code" #地域セグメントコード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "customer_segment_code" #顧客セグメントコード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_segment_code_1" #ユーザ開放セグメントコード1
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_segment_code_2" #ユーザ開放セグメントコード2
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "match_key" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tran_currency_code" #取引通貨コード
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tran_currency_exchange_rate_type" #取引通貨為替レート識別区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tran_currency_rate" #取引通貨レート
      length: 13
      type: "string"
      align: "left"
      padding_char: " "

    - name: "view_currency_exchange_rate_type_1" #表示通貨為替レート識別区分1
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "view_currency_rate_1" #表示通貨レート1
      length: 13
      type: "string"
      align: "left"
      padding_char: " "

    - name: "view_currency_exchange_rate_type_2" #表示通貨為替レート識別区分2
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "view_currency_rate_2" #表示通貨レート2
      length: 13
      type: "string"
      align: "left"
      padding_char: " "

    - name: "view_currency_exchange_rate_type_3" #表示通貨為替レート識別区分3
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "view_currency_rate_3" #表示通貨レート3
      length: 13
      type: "string"
      align: "left"
      padding_char: " "

    - name: "funding_code" #資金コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tax_type_code" #消費税区分コード
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve3" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tax_rate_type" #消費税率区分
      length: 6
      type: "string"
      align: "left"
      padding_char: " "

    - name: "function_currency_amout" #機能通貨発生金額
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tran_currency_amout" #取引通貨発生金額
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "reference_tax" #参考消費税金額
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_num_1" #ユーザ開放数値1
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tax_type" #課税区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "history_property_code" #履歴物件コード
      length: 36
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_account_code" #相手勘定科目コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve4" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve5" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve6" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve7" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve8" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve9" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve10" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve11" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve12" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve13" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve14" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve15" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve16" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve17" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve18" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve19" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve20" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve21" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve22" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve23" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve24" #システムリザーブ
      length: 6
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve25" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve26" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve27" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve28" #システムリザーブ
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "quantity" #数量
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "unit_cd" #単位コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "quantity_sub" #数量(副)
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "unit_cd_sub" #単位コード(副)
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "function_currency_price" #機能通貨単価
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "tran_currency_price" #取引通貨単価
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "ext_num_1" #拡張数値1
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "ext_num_2" #拡張数値2
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "ext_num_3" #拡張数値3
      length: 20
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_date_1" #ユーザ開放日付1(主オーダ発行日)
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_1" #ユーザ開放コード1(債権債務発生社員コード)
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_2" #ユーザ開放コード2(仕損コード)
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_3" #ユーザ開放コード3(支払区分コード)
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_4" #ユーザ開放コード4(オーダ明細番号)
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_5" #ユーザ開放コード5(代表機種コード)
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_6" #ユーザ開放コード6(払出元コード)
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_7" #ユーザ開放コード7(発生元品名コード)
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_1" #ユーザ開放域1(発生元品名(漢字))
      length: 72
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve29" #システムリザーブ
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve30" #システムリザーブ
      length: 72
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_2" #ユーザ開放域2(付加情報1)
      length: 36
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_3" #ユーザ開放域3(付加情報2)
      length: 36
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_code_8" #ユーザ開放コード8(取引時間)
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_5" #ユーザ開放域5(タイムゾーン)
      length: 24
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_6" #ユーザ開放域6(資金収支プロジェクトコード)
      length: 36
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_7" #ユーザ開放域7(空き1)
      length: 60
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_area_8" #ユーザ開放域8(空き2)
      length: 72
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve31" #システムリザーブ
      length: 2
      type: "string"
      align: "left"
      padding_char: " "

    - name: "user_open_date_2" #ユーザ開放日付2(決算年月)
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "text_description_bill_remarks" #文字摘要／手形備考
      length: 192
      type: "string"
      align: "left"
      padding_char: " "

    - name: "detail_user_open_area" #明細ユーザ開放域
      length: 192
      type: "string"
      align: "left"
      padding_char: " "

    - name: "detail_user_open_area_2" #明細ユーザ開放域2
      length: 384
      type: "string"
      align: "left"
      padding_char: " "

    - name: "individual_application_key" #個別消込キー
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "recovery_payment_dept_code" #回収支払部門コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "contract_no" #契約番号
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "invoice_no" #インボイス番号
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "recovery_payment_schedule_date" #回収支払予定日付
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_cash_closing_date" #請求支払締め日付
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "upd_sub_sys_type" #更新サブシステム区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "property_control_number" #物件管理番号
      length: 36
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_no" #手形番号
      length: 36
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_kind_type" #手形種類区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_type" #手形区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "transition_type" #推移区分
      length: 2
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_cash_settlement_date" #手形期日/期日現金決済日
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_split_type_sys_reserve" #手形分割区分(システムリザーブ)
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "effort_payment_advice_date" #取組日付/支払通知日付
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "cash_schedule_date" #現金化予定日付
      length: 8
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_site" #手形サイト
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve32" #システムリザーブ
      length: 4
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve33" #システムリザーブ
      length: 15
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bank_account_holder" #振出人名称/自社銀行口座名義人/相手銀行口座名義人
      length: 192
      type: "string"
      align: "left"
      padding_char: " "

    - name: "payment_place_counter_bank_code" #支払場所銀行コード/相手銀行コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "payment_place" #支払場所
      length: 96
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_effort_company_bank_code" #手形取組銀行コード/自社銀行コード
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "bill_discount_fee" #手形割引手数料
      length: 13
      type: "string"
      align: "left"
      padding_char: " "

    - name: "telegraph_document_transfer_type" #電信文書振込区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "fee_burden_type" #手数料負担区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "fb_transfer_process_type" #FB振込処理区分
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "company_bank_account_type" #自社銀行口座種別
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "company_bank_account_no" #自社銀行口座番号
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_bank_account_type" #相手銀行口座種別
      length: 1
      type: "string"
      align: "left"
      padding_char: " "

    - name: "counter_bank_account_no" #相手銀行口座番号
      length: 12
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve34" #システムリザーブ
      length: 22
      type: "string"
      align: "left"
      padding_char: " "

    - name: "sys_reserve35" #システムリザーブ
      length: 11
      type: "string"
      align: "left"
      padding_char: " "

etl:
  main_query: |
    SELECT
      input_no,
      input_sys_type,
      corp_cd,
      vote_employee_cd,
      vote_dept_cd,
      approval_employee_cd,
      approval_date,
      approval_status_type,
      journal_type,
      slip_date,
      slip_no,
      slip_ope_ban_type,
      journal_reference_type,
      input_unit_no,
      xml_db_seq_key,
      transfer_link_key,
      sys_reserve1,
      fixes_reason_code,
      business_code,
      form_code,
      order_top_char,
      order_item,
      order_other,
      wh_code,
      join_segment_code_1,
      join_segment_code_2,
      join_segment_code_3,
      counter_corp_cd,
      counter_corp_join_segment_code_1,
      counter_corp_join_segment_code_2,
      counter_corp_join_segment_code_3,
      slip_user_open_date_1,
      slip_user_open_code_1,
      slip_user_open_code_2,
      sys_reserve2,
      slip_remarks,
      approval_remarks,
      slip_user_open_area,
      slip_user_open_area_2,
      line_num,
      slip_detail_lending_type,
      account_code,
      accounting_dept_code,
      details_type,
      details_code,
      items_type,
      items_code,
      count_ext_code_1,
      count_ext_code_1_type,
      count_ext_code_2,
      count_ext_code_2_type,
      count_ext_code_3,
      count_ext_code_3_type,
      count_ext_code_4,
      count_ext_code_4_type,
      count_ext_code_5,
      count_ext_code_5_type,
      search_ext_code_1,
      search_ext_code_1_type,
      search_ext_code_2,
      search_ext_code_2_type,
      search_ext_code_3,
      search_ext_code_3_type,
      search_ext_code_4,
      search_ext_code_4_type,
      search_ext_code_5,
      search_ext_code_5_type,
      business_partner_code,
      segment_code,
      cost_burden_center_code,
      bill_cash_code,
      business_segment_code,
      region_segment_code,
      customer_segment_code,
      user_open_segment_code_1,
      user_open_segment_code_2,
      match_key,
      tran_currency_code,
      tran_currency_exchange_rate_type,
      tran_currency_rate,
      view_currency_exchange_rate_type_1,
      view_currency_rate_1,
      view_currency_exchange_rate_type_2,
      view_currency_rate_2,
      view_currency_exchange_rate_type_3,
      view_currency_rate_3,
      funding_code,
      tax_type_code,
      sys_reserve3,
      tax_rate_type,
      function_currency_amout,
      tran_currency_amout,
      reference_tax,
      user_open_num_1,
      tax_type,
      history_property_code,
      counter_account_code,
      sys_reserve4,
      sys_reserve5,
      sys_reserve6,
      sys_reserve7,
      sys_reserve8,
      sys_reserve9,
      sys_reserve10,
      sys_reserve11,
      sys_reserve12,
      sys_reserve13,
      sys_reserve14,
      sys_reserve15,
      sys_reserve16,
      sys_reserve17,
      sys_reserve18,
      sys_reserve19,
      sys_reserve20,
      sys_reserve21,
      sys_reserve22,
      sys_reserve23,
      sys_reserve24,
      sys_reserve25,
      sys_reserve26,
      sys_reserve27,
      sys_reserve28,
      quantity,
      unit_cd,
      quantity_sub,
      unit_cd_sub,
      function_currency_price,
      tran_currency_price,
      ext_num_1,
      ext_num_2,
      ext_num_3,
      user_open_date_1,
      user_open_code_1,
      user_open_code_2,
      user_open_code_3,
      user_open_code_4,
      user_open_code_5,
      user_open_code_6,
      user_open_code_7,
      user_open_area_1,
      sys_reserve29,
      sys_reserve30,
      user_open_area_2,
      user_open_area_3,
      user_open_code_8,
      user_open_area_5,
      user_open_area_6,
      user_open_area_7,
      user_open_area_8,
      sys_reserve31,
      user_open_date_2,
      text_description_bill_remarks,
      detail_user_open_area,
      detail_user_open_area_2,
      individual_application_key,
      recovery_payment_dept_code,
      contract_no,
      invoice_no,
      recovery_payment_schedule_date,
      bill_cash_closing_date,
      upd_sub_sys_type,
      property_control_number,
      bill_no,
      bill_kind_type,
      bill_type,
      transition_type,
      bill_cash_settlement_date,
      bill_split_type_sys_reserve,
      effort_payment_advice_date,
      cash_schedule_date,
      bill_site,
      sys_reserve32,
      sys_reserve33,
      bank_account_holder,
      payment_place_counter_bank_code,
      payment_place,
      bill_effort_company_bank_code,
      bill_discount_fee,
      telegraph_document_transfer_type,
      fee_burden_type,
      fb_transfer_process_type,
      company_bank_account_type,
      company_bank_account_no,
      counter_bank_account_type,
      counter_bank_account_no,
      sys_reserve34,
      sys_reserve35
    FROM accounting_data_store_sales

  mappings:
    - source_column: "input_no"
      target_field: "input_no"
      type: "string"
    - source_column: "input_sys_type"
      target_field: "input_sys_type"
      type: "string"
    - source_column: "corp_cd"
      target_field: "corp_cd"
      type: "string"
    - source_column: "vote_employee_cd"
      target_field: "vote_employee_cd"
      type: "string"
    - source_column: "vote_dept_cd"
      target_field: "vote_dept_cd"
      type: "string"
    - source_column: "approval_employee_cd"
      target_field: "approval_employee_cd"
      type: "string"
    - source_column: "approval_date"
      target_field: "approval_date"
      type: "string"
    - source_column: "approval_status_type"
      target_field: "approval_status_type"
      type: "string"
    - source_column: "journal_type"
      target_field: "journal_type"
      type: "string"
    - source_column: "slip_date"
      target_field: "slip_date"
      type: "string"
    - source_column: "slip_no"
      target_field: "slip_no"
      type: "string"
    - source_column: "slip_ope_ban_type"
      target_field: "slip_ope_ban_type"
      type: "string"
    - source_column: "journal_reference_type"
      target_field: "journal_reference_type"
      type: "string"
    - source_column: "input_unit_no"
      target_field: "input_unit_no"
      type: "string"
    - source_column: "xml_db_seq_key"
      target_field: "xml_db_seq_key"
      type: "string"
    - source_column: "transfer_link_key"
      target_field: "transfer_link_key"
      type: "string"
    - source_column: "sys_reserve1"
      target_field: "sys_reserve1"
      type: "string"
    - source_column: "fixes_reason_code"
      target_field: "fixes_reason_code"
      type: "string"
    - source_column: "business_code"
      target_field: "business_code"
      type: "string"
    - source_column: "form_code"
      target_field: "form_code"
      type: "string"
    - source_column: "order_top_char"
      target_field: "order_top_char"
      type: "string"
    - source_column: "order_item"
      target_field: "order_item"
      type: "string"
    - source_column: "order_other"
      target_field: "order_other"
      type: "string"
    - source_column: "wh_code"
      target_field: "wh_code"
      type: "string"
    - source_column: "join_segment_code_1"
      target_field: "join_segment_code_1"
      type: "string"
    - source_column: "join_segment_code_2"
      target_field: "join_segment_code_2"
      type: "string"
    - source_column: "join_segment_code_3"
      target_field: "join_segment_code_3"
      type: "string"
    - source_column: "counter_corp_cd"
      target_field: "counter_corp_cd"
      type: "string"
    - source_column: "counter_corp_join_segment_code_1"
      target_field: "counter_corp_join_segment_code_1"
      type: "string"
    - source_column: "counter_corp_join_segment_code_2"
      target_field: "counter_corp_join_segment_code_2"
      type: "string"
    - source_column: "counter_corp_join_segment_code_3"
      target_field: "counter_corp_join_segment_code_3"
      type: "string"
    - source_column: "slip_user_open_date_1"
      target_field: "slip_user_open_date_1"
      type: "string"
    - source_column: "slip_user_open_code_1"
      target_field: "slip_user_open_code_1"
      type: "string"
    - source_column: "slip_user_open_code_2"
      target_field: "slip_user_open_code_2"
      type: "string"
    - source_column: "sys_reserve2"
      target_field: "sys_reserve2"
      type: "string"
    - source_column: "slip_remarks"
      target_field: "slip_remarks"
      type: "string"
    - source_column: "approval_remarks"
      target_field: "approval_remarks"
      type: "string"
    - source_column: "slip_user_open_area"
      target_field: "slip_user_open_area"
      type: "string"
    - source_column: "slip_user_open_area_2"
      target_field: "slip_user_open_area_2"
      type: "string"
    - source_column: "line_num"
      target_field: "line_num"
      type: "string"
    - source_column: "slip_detail_lending_type"
      target_field: "slip_detail_lending_type"
      type: "string"
    - source_column: "account_code"
      target_field: "account_code"
      type: "string"
    - source_column: "accounting_dept_code"
      target_field: "accounting_dept_code"
      type: "string"
    - source_column: "details_type"
      target_field: "details_type"
      type: "string"
    - source_column: "details_code"
      target_field: "details_code"
      type: "string"
    - source_column: "items_type"
      target_field: "items_type"
      type: "string"
    - source_column: "items_code"
      target_field: "items_code"
      type: "string"
    - source_column: "count_ext_code_1"
      target_field: "count_ext_code_1"
      type: "string"
    - source_column: "count_ext_code_1_type"
      target_field: "count_ext_code_1_type"
      type: "string"
    - source_column: "count_ext_code_2"
      target_field: "count_ext_code_2"
      type: "string"
    - source_column: "count_ext_code_2_type"
      target_field: "count_ext_code_2_type"
      type: "string"
    - source_column: "count_ext_code_3"
      target_field: "count_ext_code_3"
      type: "string"
    - source_column: "count_ext_code_3_type"
      target_field: "count_ext_code_3_type"
      type: "string"
    - source_column: "count_ext_code_4"
      target_field: "count_ext_code_4"
      type: "string"
    - source_column: "count_ext_code_4_type"
      target_field: "count_ext_code_4_type"
      type: "string"
    - source_column: "count_ext_code_5"
      target_field: "count_ext_code_5"
      type: "string"
    - source_column: "count_ext_code_5_type"
      target_field: "count_ext_code_5_type"
      type: "string"
    - source_column: "search_ext_code_1"
      target_field: "search_ext_code_1"
      type: "string"
    - source_column: "search_ext_code_1_type"
      target_field: "search_ext_code_1_type"
      type: "string"
    - source_column: "search_ext_code_2"
      target_field: "search_ext_code_2"
      type: "string"
    - source_column: "search_ext_code_2_type"
      target_field: "search_ext_code_2_type"
      type: "string"
    - source_column: "search_ext_code_3"
      target_field: "search_ext_code_3"
      type: "string"
    - source_column: "search_ext_code_3_type"
      target_field: "search_ext_code_3_type"
      type: "string"
    - source_column: "search_ext_code_4"
      target_field: "search_ext_code_4"
      type: "string"
    - source_column: "search_ext_code_4_type"
      target_field: "search_ext_code_4_type"
      type: "string"
    - source_column: "search_ext_code_5"
      target_field: "search_ext_code_5"
      type: "string"
    - source_column: "search_ext_code_5_type"
      target_field: "search_ext_code_5_type"
      type: "string"
    - source_column: "business_partner_code"
      target_field: "business_partner_code"
      type: "string"
    - source_column: "segment_code"
      target_field: "segment_code"
      type: "string"
    - source_column: "cost_burden_center_code"
      target_field: "cost_burden_center_code"
      type: "string"
    - source_column: "bill_cash_code"
      target_field: "bill_cash_code"
      type: "string"
    - source_column: "business_segment_code"
      target_field: "business_segment_code"
      type: "string"
    - source_column: "region_segment_code"
      target_field: "region_segment_code"
      type: "string"
    - source_column: "customer_segment_code"
      target_field: "customer_segment_code"
      type: "string"
    - source_column: "user_open_segment_code_1"
      target_field: "user_open_segment_code_1"
      type: "string"
    - source_column: "user_open_segment_code_2"
      target_field: "user_open_segment_code_2"
      type: "string"
    - source_column: "match_key"
      target_field: "match_key"
      type: "string"
    - source_column: "tran_currency_code"
      target_field: "tran_currency_code"
      type: "string"
    - source_column: "tran_currency_exchange_rate_type"
      target_field: "tran_currency_exchange_rate_type"
      type: "string"
    - source_column: "tran_currency_rate"
      target_field: "tran_currency_rate"
      type: "string"
    - source_column: "view_currency_exchange_rate_type_1"
      target_field: "view_currency_exchange_rate_type_1"
      type: "string"
    - source_column: "view_currency_rate_1"
      target_field: "view_currency_rate_1"
      type: "string"
    - source_column: "view_currency_exchange_rate_type_2"
      target_field: "view_currency_exchange_rate_type_2"
      type: "string"
    - source_column: "view_currency_rate_2"
      target_field: "view_currency_rate_2"
      type: "string"
    - source_column: "view_currency_exchange_rate_type_3"
      target_field: "view_currency_exchange_rate_type_3"
      type: "string"
    - source_column: "view_currency_rate_3"
      target_field: "view_currency_rate_3"
      type: "string"
    - source_column: "funding_code"
      target_field: "funding_code"
      type: "string"
    - source_column: "tax_type_code"
      target_field: "tax_type_code"
      type: "string"
    - source_column: "sys_reserve3"
      target_field: "sys_reserve3"
      type: "string"
    - source_column: "tax_rate_type"
      target_field: "tax_rate_type"
      type: "string"
    - source_column: "function_currency_amout"
      target_field: "function_currency_amout"
      type: "string"
    - source_column: "tran_currency_amout"
      target_field: "tran_currency_amout"
      type: "string"
    - source_column: "reference_tax"
      target_field: "reference_tax"
      type: "string"
    - source_column: "user_open_num_1"
      target_field: "user_open_num_1"
      type: "string"
    - source_column: "tax_type"
      target_field: "tax_type"
      type: "string"
    - source_column: "history_property_code"
      target_field: "history_property_code"
      type: "string"
    - source_column: "counter_account_code"
      target_field: "counter_account_code"
      type: "string"
    - source_column: "sys_reserve4"
      target_field: "sys_reserve4"
      type: "string"
    - source_column: "sys_reserve5"
      target_field: "sys_reserve5"
      type: "string"
    - source_column: "sys_reserve6"
      target_field: "sys_reserve6"
      type: "string"
    - source_column: "sys_reserve7"
      target_field: "sys_reserve7"
      type: "string"
    - source_column: "sys_reserve8"
      target_field: "sys_reserve8"
      type: "string"
    - source_column: "sys_reserve9"
      target_field: "sys_reserve9"
      type: "string"
    - source_column: "sys_reserve10"
      target_field: "sys_reserve10"
      type: "string"
    - source_column: "sys_reserve11"
      target_field: "sys_reserve11"
      type: "string"
    - source_column: "sys_reserve12"
      target_field: "sys_reserve12"
      type: "string"
    - source_column: "sys_reserve13"
      target_field: "sys_reserve13"
      type: "string"
    - source_column: "sys_reserve14"
      target_field: "sys_reserve14"
      type: "string"
    - source_column: "sys_reserve15"
      target_field: "sys_reserve15"
      type: "string"
    - source_column: "sys_reserve16"
      target_field: "sys_reserve16"
      type: "string"
    - source_column: "sys_reserve17"
      target_field: "sys_reserve17"
      type: "string"
    - source_column: "sys_reserve18"
      target_field: "sys_reserve18"
      type: "string"
    - source_column: "sys_reserve19"
      target_field: "sys_reserve19"
      type: "string"
    - source_column: "sys_reserve20"
      target_field: "sys_reserve20"
      type: "string"
    - source_column: "sys_reserve21"
      target_field: "sys_reserve21"
      type: "string"
    - source_column: "sys_reserve22"
      target_field: "sys_reserve22"
      type: "string"
    - source_column: "sys_reserve23"
      target_field: "sys_reserve23"
      type: "string"
    - source_column: "sys_reserve24"
      target_field: "sys_reserve24"
      type: "string"
    - source_column: "sys_reserve25"
      target_field: "sys_reserve25"
      type: "string"
    - source_column: "sys_reserve26"
      target_field: "sys_reserve26"
      type: "string"
    - source_column: "sys_reserve27"
      target_field: "sys_reserve27"
      type: "string"
    - source_column: "sys_reserve28"
      target_field: "sys_reserve28"
      type: "string"
    - source_column: "quantity"
      target_field: "quantity"
      type: "string"
    - source_column: "unit_cd"
      target_field: "unit_cd"
      type: "string"
    - source_column: "quantity_sub"
      target_field: "quantity_sub"
      type: "string"
    - source_column: "unit_cd_sub"
      target_field: "unit_cd_sub"
      type: "string"
    - source_column: "function_currency_price"
      target_field: "function_currency_price"
      type: "string"
    - source_column: "tran_currency_price"
      target_field: "tran_currency_price"
      type: "string"
    - source_column: "ext_num_1"
      target_field: "ext_num_1"
      type: "string"
    - source_column: "ext_num_2"
      target_field: "ext_num_2"
      type: "string"
    - source_column: "ext_num_3"
      target_field: "ext_num_3"
      type: "string"
    - source_column: "user_open_date_1"
      target_field: "user_open_date_1"
      type: "string"
    - source_column: "user_open_code_1"
      target_field: "user_open_code_1"
      type: "string"
    - source_column: "user_open_code_2"
      target_field: "user_open_code_2"
      type: "string"
    - source_column: "user_open_code_3"
      target_field: "user_open_code_3"
      type: "string"
    - source_column: "user_open_code_4"
      target_field: "user_open_code_4"
      type: "string"
    - source_column: "user_open_code_5"
      target_field: "user_open_code_5"
      type: "string"
    - source_column: "user_open_code_6"
      target_field: "user_open_code_6"
      type: "string"
    - source_column: "user_open_code_7"
      target_field: "user_open_code_7"
      type: "string"
    - source_column: "user_open_area_1"
      target_field: "user_open_area_1"
      type: "string"
    - source_column: "sys_reserve29"
      target_field: "sys_reserve29"
      type: "string"
    - source_column: "sys_reserve30"
      target_field: "sys_reserve30"
      type: "string"
    - source_column: "user_open_area_2"
      target_field: "user_open_area_2"
      type: "string"
    - source_column: "user_open_area_3"
      target_field: "user_open_area_3"
      type: "string"
    - source_column: "user_open_code_8"
      target_field: "user_open_code_8"
      type: "string"
    - source_column: "user_open_area_5"
      target_field: "user_open_area_5"
      type: "string"
    - source_column: "user_open_area_6"
      target_field: "user_open_area_6"
      type: "string"
    - source_column: "user_open_area_7"
      target_field: "user_open_area_7"
      type: "string"
    - source_column: "user_open_area_8"
      target_field: "user_open_area_8"
      type: "string"
    - source_column: "sys_reserve31"
      target_field: "sys_reserve31"
      type: "string"
    - source_column: "user_open_date_2"
      target_field: "user_open_date_2"
      type: "string"
    - source_column: "text_description_bill_remarks"
      target_field: "text_description_bill_remarks"
      type: "string"
    - source_column: "detail_user_open_area"
      target_field: "detail_user_open_area"
      type: "string"
    - source_column: "detail_user_open_area_2"
      target_field: "detail_user_open_area_2"
      type: "string"
    - source_column: "individual_application_key"
      target_field: "individual_application_key"
      type: "string"
    - source_column: "recovery_payment_dept_code"
      target_field: "recovery_payment_dept_code"
      type: "string"
    - source_column: "contract_no"
      target_field: "contract_no"
      type: "string"
    - source_column: "invoice_no"
      target_field: "invoice_no"
      type: "string"
    - source_column: "recovery_payment_schedule_date"
      target_field: "recovery_payment_schedule_date"
      type: "string"
    - source_column: "bill_cash_closing_date"
      target_field: "bill_cash_closing_date"
      type: "string"
    - source_column: "upd_sub_sys_type"
      target_field: "upd_sub_sys_type"
      type: "string"
    - source_column: "property_control_number"
      target_field: "property_control_number"
      type: "string"
    - source_column: "bill_no"
      target_field: "bill_no"
      type: "string"
    - source_column: "bill_kind_type"
      target_field: "bill_kind_type"
      type: "string"
    - source_column: "bill_type"
      target_field: "bill_type"
      type: "string"
    - source_column: "transition_type"
      target_field: "transition_type"
      type: "string"
    - source_column: "bill_cash_settlement_date"
      target_field: "bill_cash_settlement_date"
      type: "string"
    - source_column: "bill_split_type_sys_reserve"
      target_field: "bill_split_type_sys_reserve"
      type: "string"
    - source_column: "effort_payment_advice_date"
      target_field: "effort_payment_advice_date"
      type: "string"
    - source_column: "cash_schedule_date"
      target_field: "cash_schedule_date"
      type: "string"
    - source_column: "bill_site"
      target_field: "bill_site"
      type: "string"
    - source_column: "sys_reserve32"
      target_field: "sys_reserve32"
      type: "string"
    - source_column: "sys_reserve33"
      target_field: "sys_reserve33"
      type: "string"
    - source_column: "bank_account_holder"
      target_field: "bank_account_holder"
      type: "string"
    - source_column: "payment_place_counter_bank_code"
      target_field: "payment_place_counter_bank_code"
      type: "string"
    - source_column: "payment_place"
      target_field: "payment_place"
      type: "string"
    - source_column: "bill_effort_company_bank_code"
      target_field: "bill_effort_company_bank_code"
      type: "string"
    - source_column: "bill_discount_fee"
      target_field: "bill_discount_fee"
      type: "string"
    - source_column: "telegraph_document_transfer_type"
      target_field: "telegraph_document_transfer_type"
      type: "string"
    - source_column: "fee_burden_type"
      target_field: "fee_burden_type"
      type: "string"
    - source_column: "fb_transfer_process_type"
      target_field: "fb_transfer_process_type"
      type: "string"
    - source_column: "company_bank_account_type"
      target_field: "company_bank_account_type"
      type: "string"
    - source_column: "company_bank_account_no"
      target_field: "company_bank_account_no"
      type: "string"
    - source_column: "counter_bank_account_type"
      target_field: "counter_bank_account_type"
      type: "string"
    - source_column: "counter_bank_account_no"
      target_field: "counter_bank_account_no"
      type: "string"
    - source_column: "sys_reserve34"
      target_field: "sys_reserve34"
      type: "string"
    - source_column: "sys_reserve35"
      target_field: "sys_reserve35"
      type: "string"
