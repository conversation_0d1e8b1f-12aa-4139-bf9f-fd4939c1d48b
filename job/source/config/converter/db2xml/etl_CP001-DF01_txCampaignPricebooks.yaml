common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "pricebooks"
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: "datetime"
      description: "同期基準日時"
      required: true
    diff_base_timestamp:
      type: "datetime"
      description: "差分基準時刻"
      required: true

  # メインクエリ: pricebook情報を取得（キャンペーンごとに1レコード）
  pricebook_query: |
    SELECT 
      campaign_instructions_code
      ,pricebook_id
      ,campaign_instructions_name
      ,campaign_start_date
      ,campaign_end_date
      ,single_product_flg
    FROM wk_cp001_df01_pricebook_main
    WHERE split_num = :split_num

  # サブクエリ: 各pricebookに含まれる商品価格情報を取得
  # 単商品の場合
  single_product_price_table_query: |
    --CSV出力によって取得

  # 複数商品の場合
  multiple_products_price_table_query: |
    --CSV出力によって取得

  mappings:
    # ルート要素：pricebooks
    - target_element: "pricebooks"
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/pricebook/2006-10-31"
      child_elements:
        # pricebook要素 - 複数のpricebookを生成
        - target_element: "pricebook"
          query: "pricebook_query"
          child_elements:
            # header要素
            - target_element: "header"
              attributes:
                pricebook-id: "{pricebook_id}"
              child_elements:
                - target_element: "currency"
                  value: "JPY"
                - target_element: "display-name"
                  source_column: "campaign_instructions_name"
                  type: "string"
                  attributes:
                    xml:lang: "x-default"
                - target_element: "online-flag"
                  value: "true"
                - target_element: "online-from"
                  condition: "campaign_start_date is not None"
                  source_column: "campaign_start_date"
                  type: "datetime"
                  format: "%Y-%m-%dT%H:%M:%S.000+09:00"
                - target_element: "online-to"
                  condition: "campaign_end_date is not None"
                  source_column: "campaign_end_date"
                  type: "datetime"
                  format: "%Y-%m-%dT%H:%M:%S.000+09:00"
            # price-tables要素
            - target_element: "price-tables"
              child_elements:
                # price-table要素 - サブクエリを使ってprice-tableを生成
                # 単商品の場合
                - target_element: "price-table"
                  condition: "single_product_flg == '1'"
                  sub_query: "single_product_price_table_query"
                  attributes:
                    product-id: "{joken}"
                  child_elements:
                    - target_element: "amount"
                      source_column: "discount_retail_price"
                      type: "number"
                      attributes:
                        quantity: "1"
                # 複数商品の場合
                - target_element: "price-table"
                  condition: "single_product_flg is None or single_product_flg == '0'"
                  sub_query: "multiple_products_price_table_query"
                  attributes:
                    product-id: "{commodity_code}"
                  child_elements:
                    - target_element: "amount"
                      source_column: "discount_retail_price"
                      type: "number"
                      attributes:
                        quantity: "1"

  transformations:
    # 日時型変換（UTC形式に統一）
    - column: "campaign_start_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+09:00"

    - column: "campaign_end_date"
      type: "datetime_convert"
      format: "%Y-%m-%dT%H:%M:%S.000+09:00"
