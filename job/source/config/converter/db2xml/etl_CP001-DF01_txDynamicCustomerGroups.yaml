# 動的顧客グループXML変換の設定
# XMLの構造：
# /customer-groups                              # ルート要素
#     /customer-group                           # 顧客グループ要素
#         @group-id                             # 属性：顧客グループID
#         /description                          # 説明要素
#         /membership-rule                      # メンバーシップルール要素
#             /included-customers               # 含める顧客要素
#                 /condition-group              # 条件グループ要素
#                     @match-mode               # 属性：マッチモード
#                     /condition                # 条件要素
#                         /attribute-path       # 属性パス要素
#                         /operator             # 演算子要素
#                         /value                # 値要素
#             /excluded-customers               # 除外する顧客要素
#                 /condition-group              # 条件グループ要素
#                     @match-mode               # 属性：マッチモード
#         /group-assignment                     # グループ割り当て要素（customer-groupの子要素）
#             @group-id                         # 属性：顧客グループID
#             @customer-no                      # 属性：顧客番号
#
# データマッピング：
# - customer-group/@group-id: "txGroupId_" + キャンペーン設定コード
# - description: キャンペーン設定.キャンペーン内容
# - condition-group/@match-mode: "all"（固定値）
# - attribute-path: "顧客オブジェクト.会員ランク"（固定値）
# - operator: "is-equal"（固定値）
# - value: キャンペーン設定条件.条件内容
# - group-assignment/@group-id: customer-groupと同じgroup-id
# - group-assignment/@customer-no: キャンペーン設定顧客.顧客コード
#
# コード定義：
# - condition_type1: '1'（会員）、'2'（購入商品）、'3'（受注）
# - condition_type2: '101'（会員ランク）、'201'（商品コード）、'205'（商品リスト）、
#                    '202'（部門）、'203'（大分類）、'204'（商品シリーズ）、
#                    '206'（基準数量）、'304'（販売経路）
# - promotion_type: '01'（プレゼント）、'02'（値引）、'03'（割引）、'04'（送料指定）

common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "customer-groups" # XMLのルート要素名
  indent: 2 # インデントスペース数

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # キャンペーン基本情報取得クエリ
  # - キャンペーンの基本情報を取得
  # - キャンペーンプロモーションマスタからキャンペーン名称を取得
  # - 更新された親データの取得
  campaign_query: |
    SELECT 
      campaign_instructions_code
      group_id
      campaign_instructions_name
    FROM wk_cp001_df01_dynamic_customer_groups_main
    WHERE split_num = :split_num

  # 条件グループ取得クエリ(適用条件)
  condition_group_include_query: |
    --CSV出力によって取得

  # 条件取得クエリ(適用条件)
  condition_include_query: |
    --CSV出力によって取得

  # 条件グループ取得クエリ(除外条件)
  condition_group_exclude_query: |
    --CSV出力によって取得

  # 条件取得クエリ(除外条件)
  condition_exclude_query: |
    --CSV出力によって取得

  # マッピング定義
  mappings:
    # ルート要素：customer-groups
    - target_element: customer-groups
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/customergroup/2007-06-30"
      child_elements:
        # 顧客グループ要素
        - target_element: customer-group
          query: campaign_query # キャンペーン基本情報取得（MainQuery）
          attributes:
            group-id: "group_id" # グループID
          child_elements:
            # キャンペーン説明
            - target_element: description
              source_column: campaign_instructions_name # キャンペーンプロモーションマスタ.キャンペーン名称
              type: string

            # メンバーシップルール要素
            - target_element: membership-rule
              child_elements:
                # 含める顧客の条件
                - target_element: included-customers
                  child_elements:
                    - target_element: condition-group
                      sub_query: condition_group_include_query # 条件グループ取得
                      attributes:
                        match-mode: "all" # 固定値
                      child_elements:
                        - target_element: condition
                          sub_query: condition_include_query # 条件取得
                          child_elements:
                            - target_element: attribute-path
                              value: "customer.profile.custom.txRank" # 顧客属性パスを変更
                            - target_element: operator
                              value: "is-equal" # 固定値
                            - target_element: string # 修正: value → string (値の型を明示)
                              source_column: joken # 会員ランク値
                              type: string
                - target_element: excluded-customers
                  child_elements:
                    - target_element: condition-group
                      sub_query: condition_group_exclude_query # 条件グループ取得
                      attributes:
                        match-mode: "all" # 固定値
                      child_elements:
                        - target_element: condition
                          sub_query: condition_exclude_query # 条件取得
                          child_elements:
                            - target_element: attribute-path
                              value: "customer.profile.custom.txRank" # 顧客属性パスを変更
                            - target_element: operator
                              value: "is-equal" # 固定値
                            - target_element: string # 修正: value → string (値の型を明示)
                              source_column: joken # 会員ランク値
                              type: string
