# 静的顧客グループXML変換の設定
# XMLの構造：
# /customer-groups                              # ルート要素
#     /customer-group                           # 顧客グループ要素
#         @group-id                             # 属性：顧客グループID
#         /description                          # 説明要素
#     /customer-group/group-assignment          # グループ割り当て要素（customer-groupの子要素）
#         @group-id                             # 属性：顧客グループID
#         @customer-no                          # 属性：顧客番号
#
# データマッピング：
# - customer-group/@group-id: "txGroupId_" + キャンペーン設定コード
# - description: キャンペーン設定.キャンペーン内容
# - group-assignment/@group-id: customer-groupと同じgroup-id
# - group-assignment/@customer-no: キャンペーン設定顧客.顧客コード
#
# コード定義：
# - condition_type1: '1'（会員）、'2'（購入商品）、'3'（受注）
# - condition_type2: '101'（会員ランク）、'201'（商品コード）、'205'（商品リスト）、
#                    '202'（部門）、'203'（大分類）、'204'（商品シリーズ）、
#                    '206'（基準数量）、'304'（販売経路）
# - promotion_type: '01'（プレゼント）、'02'（値引）、'03'（割引）、'04'（送料指定）

common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "customer-groups" # XMLのルート要素名
  indent: 2 # インデントスペース数

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # キャンペーン基本情報取得クエリ
  campaign_query: |
    SELECT 
      group_id
      campaign_instructions_name
    FROM wk_cp001_df01_static_customer_groups_campaign_main
    WHERE split_num = :split_num

  # 顧客グループ情報取得クエリ（独立したメインクエリとして修正）
  customer_group_query: |
    SELECT 
      group_id
      customer_no
    FROM wk_cp001_df01_static_customer_groups_main
    WHERE split_num = :split_num

  # マッピング定義
  mappings:
    # ルート要素：customer-groups
    - target_element: customer-groups
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/customergroup/2007-06-30"
      child_elements:
        # 顧客グループ要素
        - target_element: customer-group
          query: campaign_query # キャンペーン基本情報取得
          attributes:
            group-id: "{group_id}" # グループID
          child_elements:
            # キャンペーン説明
            - target_element: description
              source_column: campaign_instructions_name
              type: string

            # 空のcustom-attributes要素を追加
            - target_element: custom-attributes

        # グループ割り当て情報（customer-groupsの直接の子要素として配置）
        - target_element: group-assignment
          query: customer_group_query # 独立したメインクエリから顧客情報を取得
          attributes:
            group-id: "{group_id}" # キャンペーン毎に異なるグループID
            customer-no: "{customer_no}" # 顧客番号
