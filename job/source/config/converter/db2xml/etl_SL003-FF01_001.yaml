common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "inventory"
  record_element: "inventory-list"
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: "datetime"
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: "datetime"
      description: "差分基準時刻"
      required: true
    split_num:
      type: "string"
      description: "分割単位"
      required: true

  storeid_query: |
    SELECT
      store_id,
      allocation_timestamp
    FROM wk_sl003_ff01_storeid
    WHERE split_num = :split_num
    ORDER BY store_id

  inventory_query: |
    --CSV出力によって取得

  mappings:
    - target_element: "inventory"
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/inventory/2007-05-31"
      child_elements:
        - target_element: "inventory-list"
          query: "storeid_query"
          child_elements:
            - target_element: "header"
              attributes:
                list-id: "dhc_inventory-shop_{store_id}"
              child_elements:
                - target_element: "default-instock"
                  value: "false"
                - target_element: "on-order"
                  value: "false"
                  type: "boolean"
            - target_element: "records"
              child_elements:
                - target_element: "record"
                  sub_query: inventory_query
                  attributes:
                    product-id: "{warehouse_management_no}"
                  child_elements:
                    - target_element: "allocation"
                      source_column: "stock_quantity"
                      type: "number"
                    - target_element: "allocation-timestamp"
                      source_column: "allocation_timestamp"
                    - target_element: "perpetual"
                      value: "false"
                      type: "boolean"
