common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "inventory"
  record_element: "inventory-list"
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: "datetime"
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: "datetime"
      description: "差分基準時刻"
      required: true
    split_num:
      type: "string"
      description: "分割単位"
      required: true

  inventory_query: |
    SELECT
      commodity_code,
      stock_quantity,
      stock_arrival_date,
      arrival_quantity,
      allocation,
      allocation_timestamp
    FROM wk_st002_df01_inventory
    WHERE split_num = :split_num
    ORDER BY commodity_code

  mappings:
    - target_element: "inventory"
      attributes:
        xmlns: "http://www.demandware.com/xml/impex/inventory/2007-05-31"
      child_elements:
        - target_element: "inventory-list"
          child_elements:
            - target_element: "header"
              attributes:
                list-id: "dhc_inventory"
              child_elements:
                - target_element: "default-instock"
                  value: "false"
                - target_element: "on-order"
                  value: "false"
                  type: "boolean"
            - target_element: "records"
              child_elements:
                - target_element: "record"
                  query: "inventory_query"
                  attributes:
                    product-id: "{commodity_code}"
                  child_elements:
                    - target_element: "allocation"
                      source_column: "allocation"
                      type: "number"
                    - target_element: "allocation-timestamp"
                      source_column: "allocation_timestamp"
                      type: "string"
                    - target_element: "perpetual"
                      value: "false"
                      type: "boolean"
                    - target_element: "custom-attributes"
                      child_elements:
                        - target_element: "custom-attribute"
                          attributes:
                            attribute-id: "txArrivalQuantity"
                          source_column: "arrival_quantity"
                          type: "number"
                        - target_element: "custom-attribute"
                          attributes:
                            attribute-id: "txArrivalDate"
                          source_column: "stock_arrival_date"
