common:
  encoding: shift_jis
  format: fixed2tsv
input:
  fixed_options:
    fields:
      - length: 1
        name: record_gb
      - length: 2
        name: smcc_gb
      - length: 30
        name: pass
      - length: 16
        name: card_no
      - length: 8
        name: admission_date
      - length: 8
        name: subscription_date
      - length: 20
        name: cust_name
      - length: 729
        name: pass
      - length: 4
        name: valid_date
      - length: 1
        name: trust_withdrawal_yn
      - length: 1
        name: trust_accident_yn
      - length: 1
        name: trust_attention_yn
      - length: 27
        name: pass
      - length: 5
        name: shop_code
      - length: 16
        name: pass
      - length: 1
        name: subscription_gb
      - length: 19
        name: staff_code
      - length: 22
        name: pass
      - length: 20
        name: memb_no
      - length: 20
        name: cust_no
      - length: 1
        name: modify_cust_name_yn
      - length: 3
        name: pass
      - length: 1
        name: modify_valid_date_yn
      - length: 1
        name: modify_cooperation_yn
      - length: 1
        name: modify_trust_yn
      - length: 242
        name: pass
    total_length: 1200
output:
  tsv_options:
    delimiter: "\t"
    has_header: false
    line_ending: "\r\n"
    quote_char: ""
