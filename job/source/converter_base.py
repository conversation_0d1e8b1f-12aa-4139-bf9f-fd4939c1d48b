#!/usr/bin/env python
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, List, Optional
from source.glue_logger import GlueLogger
from source.common_util import load_yaml_config

from source.db_connector import DbConnector


class ConverterBase(ABC):
    """コンバーター基底クラス"""

    def __init__(self, jobnet_id: str, etl_config: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
        """
        self.jobnet_id = jobnet_id
        self.etl_config = etl_config
        self.logger = GlueLogger(jobnet_id)
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        YAML設定ファイルを読み込む
        Returns:
            Dict: 設定内容
        """
        try:
            # ETL設定ファイルの読み込み
            yaml_file = f"converter/{self.converter_type}/{self.etl_config}.yaml"
            return load_yaml_config(yaml_file)
        except Exception as e:
            self.logger.error_common(f"設定ファイル読み込み: {e}")
            raise

    def _validate_config(self, required_sections: List[str]):
        """
        設定ファイルの必須セクションを検証
        Args:
            required_sections: 必須セクションのリスト
        """
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required section in config: {section}")

    @abstractmethod
    def execute_conversion(self, output_path: str) -> str:
        """
        データ取得と変換を実行
        Args:
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        pass

    @abstractmethod
    def convert_from_data(self, data: List[Dict], output_path: str) -> str:
        """
        既存データの変換
        Args:
            data: 変換元データ
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        pass


class DBSourceConverter(ConverterBase):
    """データベースソースコンバーター基底クラス"""

    def __init__(
        self,
        jobnet_id: str,
        etl_config: str,
        db_connection: DbConnector,
        last_sync_timestamp: Optional[datetime] = None,
        diff_base_timestamp: Optional[datetime] = None,
    ):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
            db_connection: DBコネクション
            last_sync_timestamp: 前回同期タイムスタンプ（オプション）
            diff_base_timestamp: 差分基準時刻（オプション）
        """
        self.db_connection = db_connection
        self._last_sync_timestamp = last_sync_timestamp
        self._diff_base_timestamp = diff_base_timestamp
        super().__init__(jobnet_id, etl_config)

    def _prepare_parameters(self, param_definitions: Dict) -> Dict:
        """
        ETL定義のパラメータを実行用パラメータに変換（基底クラス実装）
        Args:
            param_definitions: ETL定義のパラメータ定義
        Returns:
            Dict: 実行用パラメータ
        """
        params = {}
        for param_name, param_def in param_definitions.items():
            param_type = param_def.get("type")
            if param_type == "datetime":
                if param_name == "sync_timestamp":
                    if self._last_sync_timestamp is None and param_def.get(
                        "required", False
                    ):
                        raise ValueError(
                            f"Required parameter '{param_name}' is missing"
                        )
                    params[param_name] = self._last_sync_timestamp
                elif param_name == "diff_base_timestamp":
                    if self._diff_base_timestamp is None and param_def.get(
                        "required", False
                    ):
                        raise ValueError(
                            f"Required parameter '{param_name}' is missing"
                        )
                    params[param_name] = self._diff_base_timestamp

        return params

    def _execute_query(
        self, query_name: str, params: Dict[str, Any] = None
    ) -> List[Dict]:
        """
        クエリを実行してデータを取得
        Args:
            query_name: クエリ名
            params: クエリパラメータ
        Returns:
            List[Dict]: 取得データ
        """
        result = None
        try:
            if query_name not in self.config["etl"]:
                raise ValueError(f"Query not found in config: {query_name}")

            query = self.config["etl"][query_name]

            # クエリ実行前のログ出力
            self.logger.debug(f"\n=== Query Execution: {query_name} ===")
            self.logger.debug(f"Parameters: {params}")
            self.logger.debug(f"SQL:\n{query}")

            # クエリ実行
            result = self.db_connection.exec(query, params or {})
            rows = [dict(row._mapping) for row in result.fetchall()]

            # 結果の詳細をログ出力
            self.logger.debug(f"\n=== Query Results: {query_name} ===")
            self.logger.debug(f"Total records: {len(rows)}")

            if rows:
                # カラム情報の出力
                columns = list(rows[0].keys())
                self.logger.debug(f"Columns ({len(columns)}): {columns}")

                # レコードの出力（最大10件まで）
                max_records = 10
                for i, row in enumerate(rows[:max_records]):
                    self.logger.debug(f"\nRecord {i + 1}:")
                    for col, val in row.items():
                        self.logger.debug(f"  {col}: {val}")

                # 出力を省略したレコードがある場合
                if len(rows) > max_records:
                    self.logger.debug(
                        f"\n... {len(rows) - max_records} more records ..."
                    )

            self.logger.debug("\n==============================\n")
            return rows

        except Exception as e:
            self.logger.error_common(str(e))
            raise

        finally:
            if result is not None:
                result.close()


class FileSourceConverter(ConverterBase):
    """ファイルソースコンバーター基底クラス"""

    def _read_source_file(self, file_path: str) -> List[Dict]:
        """
        ソースファイルを読み込む
        Args:
            file_path: ファイルパス
        Returns:
            List[Dict]: 読み込んだデータ
        """
        raise NotImplementedError("Subclass must implement abstract method")
