#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime
from typing import List, Dict, Optional
from source.converter_base import DBSourceConverter
from source.db_connector import DbConnector
from source.fixed_width_sjis import convert_to_fixed_width_sjis


class DB2FixedConverter(DBSourceConverter):
    """固定長ファイル出力機能"""

    def __init__(
        self,
        jobnet_id: str,
        etl_config: str,
        db_connection: DbConnector,
        last_sync_timestamp: Optional[datetime] = None,
        diff_base_timestamp: Optional[datetime] = None,
    ):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
            db_connection: DBコネクション
            last_sync_timestamp: 前回同期タイムスタンプ（オプション）
            diff_base_timestamp: 差分基準時刻（オプション）
        """
        self.converter_type = "db2fixed"
        super().__init__(
            jobnet_id,
            etl_config,
            db_connection,
            last_sync_timestamp,
            diff_base_timestamp,
        )
        # 設定ファイルの検証
        self._validate_config(["etl", "output", "fixed_length"])

    def _validate_config(self, required_sections: List[str]):
        """
        親クラスのメソッドをオーバーライドし、
        固定長変換特有の設定検証を追加
        """
        # 親クラスの基本的な検証を呼び出し
        super()._validate_config(required_sections)

        # 固定長設定の詳細検証
        fixed_length_config = self.config.get("fixed_length", {})
        total_length = fixed_length_config.get("total_length")
        fields = fixed_length_config.get("fields", [])

        # フィールド長の合計計算
        total_fields_length = sum(field.get("length", 0) for field in fields)

        # total_lengthとフィールド長の合計が一致するかチェック
        if total_fields_length != total_length:
            raise ValueError(
                f"フィールド長の合計 ({total_fields_length}) が "
                f"total_length ({total_length}) と一致しません"
            )

    def execute_conversion(self, output_path: str) -> str:
        """
        データ取得と変換を実行
        Args:
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        try:
            # データ取得
            params = self._prepare_parameters(
                self.config.get("etl", {}).get("parameters", {})
            )
            data = self._execute_query("main_query", params)

            # 固定長変換
            result = self._convert_data(data, output_path)

            return result
        except Exception as e:
            self.logger.error_common(f"データ取得と変換: {e}")
            raise

    def convert_from_data(self, data: List[Dict], output_path: str) -> str:
        """
        既存データの変換
        Args:
            data: 変換元データ
            output_path: 出力パス
        Returns:
            str: 変換後のファイルデータ
        """
        try:
            # カラム仕様の定義
            column_specs = [
                {
                    "name": field["name"],
                    "width": field["length"],
                    "align": field.get(
                        "align",
                        "left" if field.get("type", "string") == "string" else "right",
                    ),
                    "padding": field.get(
                        "padding_char",
                        " " if field.get("type", "string") == "string" else "0",
                    ),
                }
                for field in self.config.get("fixed_length", {}).get("fields", [])
            ]

            # 変換対象データの準備
            converted_data = []
            for row in data:
                converted_row = {
                    field["name"]: row.get(field["name"], "") for field in column_specs
                }
                converted_data.append(converted_row)

            # 固定長変換
            fixed_width_lines = convert_to_fixed_width_sjis(
                converted_data, column_specs
            )

            # 文字列として結合
            result = "".join(fixed_width_lines)

            return result

        except Exception as e:
            self.logger.error_common(f"データの変換: {e}")
            raise

    def _convert_data(self, data: List[Dict], output_path: str) -> str:
        """
        固定長ファイル出力
        Args:
            data: 変換元データ
            output_path: 出力パス
        Returns:
            str: 変換後のファイルデータ
        """
        return self.convert_from_data(data, output_path)
