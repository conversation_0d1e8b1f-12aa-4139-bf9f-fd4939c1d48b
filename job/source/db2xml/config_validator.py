#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, Set, Optional
import re


class ConfigValidatorMixin:
    """設定検証の責務を担当"""

    def _validate_xml_config(self):
        """XML固有の設定を検証"""
        if "output" not in self.config:
            raise ValueError("XML出力設定が未定義です")

        output_config = self.config["output"]
        if "root_element" not in output_config:
            raise ValueError("必須パラメータ root_element が未設定です")

        # 要素名の妥当性チェック
        element_pattern = re.compile(r"^[a-zA-Z][a-zA-Z0-9_-]*$")
        if not element_pattern.match(output_config["root_element"]):
            raise ValueError(f"無効なルート要素名: {output_config['root_element']}")

    def _validate_query_hierarchy(
        self, mapping: Dict[str, Any], in_subtree: bool = False
    ) -> None:
        """
        クエリの階層関係を検証
        Args:
            mapping: 検査対象のマッピング定義
            in_subtree: SubTree内かどうかのフラグ
        Raises:
            ValueError: 不正なクエリ使用を検出した場合
        """
        if "query" in mapping and in_subtree:
            raise ValueError(
                f"SubTree内でのquery使用は禁止されています（sub_queryを使用してください）: {mapping['target_element']}"
            )

        if "child_elements" in mapping:
            for child in mapping["child_elements"]:
                self._validate_query_hierarchy(
                    child, in_subtree=True if "query" in mapping else in_subtree
                )

    def _detect_circular_reference(
        self, mapping: Dict, visited: Optional[set] = None
    ) -> None:
        """
        マッピング定義の循環参照をチェック
        Args:
            mapping: 検査対象のマッピング定義
            visited: チェック済みクエリ名のセット
        Raises:
            ValueError: 循環参照を検出した場合
        """
        visited = visited or set()
        query_name = mapping.get("query") or mapping.get("sub_query")

        if query_name:
            if query_name in visited:
                raise ValueError(f"循環参照を検出: {query_name}")
            visited.add(query_name)

        if "child_elements" in mapping:
            for child in mapping["child_elements"]:
                self._detect_circular_reference(child, visited.copy())

    def _validate_query_references(self) -> None:
        """
        クエリ参照の検証と階層関係の妥当性チェック
        Raises:
            ValueError: 無効なクエリ参照を検出した場合
        """
        try:
            mappings = self.config.get("etl", {}).get("mappings", [])
            etl_queries = set(
                k
                for k, v in self.config.get("etl", {}).items()
                if isinstance(v, str) or isinstance(v, list)
                and k not in ["parameters", "mappings", "transformations"]
            )

            def validate_mapping(mapping: Dict) -> None:
                if "query" in mapping:
                    query_name = mapping["query"]
                    if query_name not in etl_queries:
                        raise ValueError(f"未定義のクエリを参照: {query_name}")

                if "sub_query" in mapping:
                    sub_query_name = mapping["sub_query"]
                    if sub_query_name not in etl_queries:
                        raise ValueError(f"未定義のサブクエリを参照: {sub_query_name}")

                if "child_elements" in mapping:
                    for child in mapping["child_elements"]:
                        validate_mapping(child)

            # 各マッピングの検証
            for mapping in mappings:
                validate_mapping(mapping)
                self._validate_query_hierarchy(mapping)
                self._detect_circular_reference(mapping)

        except Exception as e:
            self.logger.error_common(f"クエリ参照の検証エラー: {str(e)}")
            raise

    def _validate_transformation_rules(self):
        """変換ルールの検証"""
        if "etl" not in self.config:
            raise ValueError("ETL設定が未定義です")

        etl_config = self.config["etl"]
        if "mappings" not in etl_config:
            raise ValueError("マッピング定義が未定義です")

        if "transformations" in etl_config:
            transforms = etl_config["transformations"]
            valid_types = [
                "html_encode",
                "replace_null",
                "boolean_convert",
                "datetime_convert",
            ]

            for transform in transforms:
                if "column" not in transform:
                    raise ValueError("変換ルールにcolumnが定義されていません")
                if "type" not in transform:
                    raise ValueError("変換ルールにtypeが定義されていません")
                if transform["type"] not in valid_types:
                    raise ValueError(f"無効な変換タイプです: {transform['type']}")
