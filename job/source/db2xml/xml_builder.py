#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, List, Optional
import xml.etree.ElementTree as ET
from xml.dom import minidom
import re
from .utils import convert_to_dict, escape_xml_chars


class XMLBuilderMixin:
    """XML構築とフォーマット処理の責務を担当"""

    def _escape_xml_chars(self, text: str) -> str:
        """XML特殊文字のエスケープ処理"""
        return escape_xml_chars(text)

    def _is_valid_xml_chars(self, text: str) -> bool:
        """XML 1.0で許可される文字のみを含むかチェック"""
        if not text:
            return True
        for char in text:
            code = ord(char)
            if not (
                code == 0x9 or code == 0xA or code == 0xD or (0x20 <= code <= 0xD7FF)
            ):
                return False
        return True

    def _convert_attribute_names(self, mapping: Dict) -> Dict:
        """属性名を変換する"""
        if "attributes" in mapping and "id" in mapping["attributes"]:
            new_mapping = mapping.copy()
            new_mapping["attributes"] = mapping["attributes"].copy()
            new_mapping["attributes"]["record-id"] = new_mapping["attributes"].pop("id")
            return new_mapping
        return mapping

    def _should_create_element(
        self,
        mapping: Dict[str, Any],
        records: List[Dict],
        element_name: str,
        target_element: Optional[ET.Element],
        parent_record: Optional[Dict] = None,
    ) -> bool:
        """要素を生成すべきかを判断する"""
        try:
            self.logger.debug(f"要素生成判断開始: {element_name}")

            # 0. ルート要素は必ず生成
            if target_element is None:
                self.logger.debug(f"ルート要素のため生成: {element_name}")
                return True

            # 1. DB依存要素の判定
            is_db_dependent = self._is_db_dependent_element(mapping)
            has_records = bool(records)

            # 条件がある場合は親レコードで評価
            if "condition" in mapping and parent_record:
                self.logger.debug(f"条件評価: {mapping['condition']}")
                if not self._evaluate_condition(parent_record, mapping["condition"]):
                    self.logger.debug(f"条件不一致のため生成しない: {element_name}")
                    return False
                self.logger.debug(f"条件一致: {element_name}")

            # 2. DB非依存の要素は常に生成
            if not is_db_dependent:
                self.logger.debug(f"DB非依存要素のため生成: {element_name}")
                return True

            # 3. 固定値がある場合は生成
            has_fixed_value = "value" in mapping
            if has_fixed_value:
                self.logger.debug(f"固定値があるため生成: {element_name}")
                return True

            # 4. DB依存の子要素判定
            has_children = "child_elements" in mapping
            # 子要素の有効性を確認
            has_valid_child = False
            if has_children:
                has_valid_child = any(
                    self._is_db_dependent_element(child)
                    for child in mapping["child_elements"]
                )
                if has_valid_child:
                    return True

            # 4. レコードがある場合は生成
            has_data = has_records
            return has_data

        except Exception as e:
            self.logger.error_common(f"要素生成判断でエラー: {str(e)}")
            raise

    def _is_db_dependent_element(self, element_def: Dict[str, Any]) -> bool:
        """要素がDBデータに依存するかを判定する"""
        # クエリの有無
        has_query = "query" in element_def or "sub_query" in element_def

        # 条件式の有無
        has_condition = "condition" in element_def

        # ソースカラムの有無
        has_source = "source_column" in element_def

        # いずれかの条件を満たす場合、DB依存と判断
        return has_query or has_condition or has_source

    def _build_xml_element(
        self,
        query_results: Dict[str, List[Dict]],
        mapping: Dict[str, Any],
        target_element: ET.Element,
        parent_record: Optional[Dict] = None,
    ) -> ET.Element:
        """マッピング定義に従ってXML要素を構築"""
        try:
            self.logger.debug("要素構築開始")

            element_name = mapping.get("target_element")
            if not element_name:
                raise ValueError("target_element must be specified")

            # 要素名のバリデーション
            if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_-]*$", element_name):
                raise ValueError(f"要素名に不正な文字が含まれています: {element_name}")

            # 1. レコード処理の準備
            records = []
            if "query" in mapping or "sub_query" in mapping:
                self.logger.debug(f"クエリからデータを取得: {element_name}")
                data = self._get_subtree_data(mapping, query_results, parent_record)

                # データが存在しない場合の早期リターン
                if not data:
                    self.logger.debug(f"クエリ結果が0件のため要素作成をスキップ: {element_name}")
                    return target_element  # 親要素をそのまま返す

                # サブクエリ結果に親レコードの情報をマージ
                records = []
                for row in data:
                    records.append({**(parent_record or {}), **convert_to_dict(row)})
            elif parent_record and "source_column" in mapping:
                self.logger.debug(f"親レコードから値を取得: {element_name}")
                records = [parent_record]

            # 2. 要素生成の判断
            can_create = self._should_create_element(
                mapping, records, element_name, target_element, parent_record
            )
            if not can_create:
                self.logger.debug(f"要素を生成しないためスキップ: {element_name}")
                return target_element

            # 要素の作成
            element = (
                ET.Element(element_name)
                if target_element is None
                else ET.SubElement(target_element, element_name)
            )
            self.logger.debug(f"要素を作成: {element_name}")

            if records:
                # レコードベースの要素生成
                self.logger.debug(f"レコード処理開始: {len(records)}件")
                for i, record in enumerate(records):
                    # 条件チェック
                    if "condition" in mapping and not self._evaluate_condition(
                        record, mapping["condition"]
                    ):
                        self.logger.debug(
                            f"条件不一致のためスキップ: {element_name}, "
                            f"condition={mapping['condition']}, record={record}"
                        )
                        continue

                    # レコードごとの要素処理
                    current = (
                        element
                        if i == 0  # 最初のレコードは既存の要素を使用
                        else ET.SubElement(
                            target_element, element_name
                        )  # 2件目以降は新規作成
                    )

                    if i == 0:
                        self.logger.debug(f"既存の要素を使用: {element_name}")
                    else:
                        self.logger.debug(
                            f"新しい要素を作成: {element_name} (record {i+1})"
                        )

                    # 属性と値を設定
                    self._set_record_attributes(current, record, mapping)

                    # 子要素の処理
                    if "child_elements" in mapping:
                        self._process_child_elements(
                            query_results, mapping["child_elements"], current, record
                        )
            else:
                # レコードなしの場合の処理
                # 1. 属性の設定（親レコードの情報を使用）
                if "attributes" in mapping:
                    parent_data = parent_record or {}
                    try:
                        self._set_element_attributes(
                            element, parent_data, mapping["attributes"]
                        )
                    except ValueError as e:
                        # 元のエラーをそのまま再スロー
                        raise
                        self.logger.debug(f"親レコードから属性を設定: {element_name}")
                    except Exception as e:
                        raise ValueError("属性値の設定に失敗しました") from e

                # 2. 固定値の設定
                if "value" in mapping:
                    value = str(mapping["value"])
                    element.text = self._escape_xml_chars(value)
                    self.logger.debug(f"固定値を設定: {element_name}={value}")

                # 3. 子要素の処理（親レコードの情報を引き継ぐ）
                if "child_elements" in mapping:
                    self._process_child_elements(
                        query_results, mapping["child_elements"], element, parent_record
                    )

            self.logger.debug(f"要素構築完了: {element_name}")

            return element

        except Exception as e:
            self.logger.error_common(f"要素構築エラー: {str(e)}")
            raise

    def _set_record_attributes(
        self, element: ET.Element, record: Dict[str, Any], mapping: Dict[str, Any]
    ) -> None:
        """レコードに基づく属性と値を設定"""
        try:
            value_set = False  # 要素に値が設定されたかどうかのフラグ

            # 属性の設定
            if "attributes" in mapping:
                try:
                    self._set_element_attributes(element, record, mapping["attributes"])
                except Exception as e:
                    self.logger.warning(f"属性設定をスキップ: {str(e)}")

            # source_columnからの値設定
            if "source_column" in mapping and not value_set:
                source_value = record.get(mapping["source_column"])
                if source_value is not None:
                    self.logger.debug(
                        f"source_column値を取得: {element.tag}.{mapping['source_column']}={source_value}"
                    )
                    try:
                        value = self._convert_value(
                            source_value,
                            mapping.get("type", "string"),
                            mapping.get("format"),
                        )
                        if value is not None and str(value).strip():
                            element.text = str(value)
                            value_set = True
                            self.logger.debug(f"値を設定: {element.tag}={value}")
                    except Exception as e:
                        self.logger.warning(f"値の設定に失敗: {str(e)}")

            # 固定値の設定（source_columnの値が設定されていない場合のみ）
            if not value_set and "value" in mapping:
                try:
                    fixed_value = str(mapping["value"])
                    if fixed_value.strip():
                        element.text = self._escape_xml_chars(fixed_value)
                        self.logger.debug(f"固定値を設定: {element.tag}={fixed_value}")

                except Exception as e:
                    self.logger.warning(f"固定値の設定に失敗: {str(e)}")

        except Exception as e:
            self.logger.warning(f"レコード属性設定に失敗: {str(e)}")

    def _process_child_elements(
        self,
        query_results: Dict[str, List[Dict]],
        child_elements: List[Dict],
        target_element: ET.Element,
        parent_record: Optional[Dict] = None,
    ) -> None:
        """子要素の処理"""
        for child_mapping in child_elements:
            # サブクエリが指定されている場合、結果を事前にチェック
            if "sub_query" in child_mapping:
                # サブクエリの結果を取得
                sub_data = self._get_subtree_data(
                    child_mapping, query_results, parent_record
                )
                # 結果が0件の場合は子要素の生成をスキップ
                if not sub_data:
                    self.logger.debug(
                        f"サブクエリ {child_mapping.get('sub_query')} の結果が0件のため、"
                        f"子要素 {child_mapping.get('target_element')} の生成をスキップします"
                    )
                    continue

            # 既存の処理を継続（結果があるか、サブクエリがない場合）
            self._build_xml_element(
                query_results=query_results,
                mapping=child_mapping,
                target_element=target_element,
                parent_record=parent_record,
            )

    def _get_subtree_data(
        self,
        mapping: Dict[str, Any],
        query_results: Dict[str, List[Dict]],
        parent_record: Optional[Dict],
    ) -> List[Dict]:
        """XMLサブツリーのデータを取得"""
        try:
            target_element = mapping.get("target_element", "unknown")
            self.logger.debug(f"サブツリーデータ取得: {target_element}")

            # MainQueryの確認
            query_name = mapping.get("query")
            if query_name:
                # MainQueryの場合は既存の結果を使用
                if query_name in query_results:
                    result = query_results[query_name]
                    if not result:
                        self.logger.debug(f"MainQuery {query_name} の結果が0件です")
                    self.logger.debug(f"既存のMainQuery結果を使用: {query_name}, 件数: {len(result)}件")
                    return result
                else:
                    # MainQueryの実行
                    params = self._get_query_params()
                    results = self._execute_query(query_name, params)
                    query_results[query_name] = results
                    if not results:
                        self.logger.debug(f"MainQuery {query_name} の実行結果が0件です")
                    self.logger.debug(f"MainQuery実行結果: {len(results)}件")
                    return results

            # SubQueryの確認
            sub_query_name = mapping.get("sub_query")
            if sub_query_name:
                if not parent_record:
                    self.logger.debug("親レコードなしのためSubQueryをスキップ")
                    return []

                # SubQueryの実行
                params = self._get_query_params()
                results = self._execute_sub_query(sub_query_name, params, parent_record)
                if not results:
                    self.logger.debug(f"SubQuery {sub_query_name} の実行結果が0件です")
                self.logger.debug(f"SubQuery実行結果: {len(results)}件")
                return results

            # クエリがない場合は親レコードを返す
            self.logger.debug("クエリなし - 親レコードを返却")
            return [parent_record] if parent_record else []

        except Exception as e:
            self.logger.error_common(f"サブツリーデータ取得エラー: {str(e)}")
            raise

    def _set_element_attributes(
        self, element: ET.Element, record: Dict[str, Any], attributes: Dict[str, str]
    ) -> None:
        """要素の属性を設定"""
        try:
            for attr_name, attr_value in attributes.items():
                try:
                    # 属性名のバリデーション
                    if not re.match(
                        r"^(xml:|xmlns:)?[a-zA-Z_][a-zA-Z0-9_-]*$", attr_name
                    ):
                        raise ValueError(f"不正な属性名です: {attr_name}")

                    if isinstance(attr_value, str) and "{" in attr_value:
                        # 変数展開
                        format_vars = convert_to_dict(record)
                        value = attr_value.format(**format_vars)
                    else:
                        # 直接の値参照
                        value = record.get(attr_value, attr_value)

                    # 制御文字のチェック
                    if isinstance(value, str) and not self._is_valid_xml_chars(value):
                        raise ValueError(
                            f"属性値に不正な文字が含まれています: {repr(value)}"
                        )

                    escaped_value = self._escape_xml_chars(
                        str(value) if value is not None else ""
                    )
                    element.set(attr_name, escaped_value)
                except ValueError as e:
                    raise  # 例外を再スロー
        except Exception as e:
            raise ValueError(str(e)) from e

    def _format_xml(self, root: ET.Element) -> str:
        """XML文書を整形"""
        try:
            self.logger.debug("XML整形開始")

            # XML変換
            rough_string = ET.tostring(root, encoding="unicode")

            # XMLパーサーで検証
            try:
                minidom.parseString(rough_string)
            except Exception as parse_error:
                self.logger.error_common(f"XML解析エラー: {parse_error}")
                raise

            # 整形処理
            reparsed = minidom.parseString(rough_string)
            indent = " " * self.config["output"].get("indent", 2)
            xml_str = reparsed.toprettyxml(
                indent=indent, encoding=self.config["output"].get("encoding", "utf-8")
            )

            formatted_xml = xml_str.decode(
                self.config["output"].get("encoding", "utf-8")
            )
            self.logger.debug("XML整形完了")
            return formatted_xml

        except Exception as e:
            self.logger.error_common(f"XML整形エラー: {str(e)}")
            raise ValueError(f"XMLの整形に失敗しました: {str(e)}")

    def _evaluate_condition(self, record: Dict[str, Any], condition: str) -> bool:
        """条件式を評価"""
        try:
            self.logger.debug(f"条件評価: {condition}")
            local_vars = convert_to_dict(record)
            result = eval(condition, {"__builtins__": {}}, local_vars)
            self.logger.debug(f"評価結果: {result}")
            return bool(result)
        except Exception as e:
            self.logger.error_common(f"条件評価エラー: {str(e)}")
            raise
