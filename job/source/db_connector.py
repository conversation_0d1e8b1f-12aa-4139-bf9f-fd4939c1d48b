#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DB接続共通部品
# データベース接続とトランザクション操作を管理するクラスです。このクラスは、指定された秘密IDを使用して、
# AWS Secrets Manager からデータベース接続に必要な情報を取得します。

使用例

[更新系処理の場合]
db_connector = DBConnector(logger, secret_id)
try:
    db_connector.connect()                                    # db接続
    db_connector.begin()                                      # transaction 開始
    db_connector.exec(sql_query, (parameter1, parameter2))    # sql実行
    db_connector.commit()                                     # transaction 完了
except Exception as e:
    db_connector.rollback()                                   # rollback
finally:
    db_connector.close()                                      # db接続中止

[参照系処理の場合]
db_connector = DBConnector(logger, secret_id)
try:
    db_connector.connect()                                    # db接続
    result = db_connector.exec(sql_query, params)            # sql実行（SELECT文）
    rows = result.fetchall()                                 # データ取得
finally:
    result.close()                                           # カーソルのclose
    db_connector.close()                                     # db接続中止
"""

import json
import boto3
from sqlalchemy import create_engine, text
from sqlalchemy.orm import scoped_session, sessionmaker
from source.glue_logger import GlueLogger


# DB操作用コネクタ部品
class DbConnector:
    def __init__(self, logger: GlueLogger, secret_id: str):
        """
        Method:
            DB操作用コネクタ部品のコンストラクタ
        Args:
            common: 共通部品Common
            logger: ロガー
            secret_id: DB接続情報格納するsecret_id
        """
        try:
            # DB接続情報格納secret_id
            self.secret_id = secret_id
            # secret_manager クライアント
            self.secrets_client = boto3.client("secretsmanager")

            response = self.secrets_client.get_secret_value(SecretId=self.secret_id)
            secret_string = response["SecretString"]
            secret_data = json.loads(secret_string)

            # Extract the required information
            self.username = secret_data.get("username")
            self.password = secret_data.get("password")
            self.dbname = secret_data.get("dbname")
            self.host = secret_data.get("host")
            self.port = secret_data.get("port")
            print(
                "db_user_name: [%s], db_password: [%s]" % (self.username, "**********")
            )
            print(
                "db_host_name: [%s], db_port: [%s], db_name: [%s]"
                % (self.host, str(self.port), self.dbname)
            )
            logger.debug(
                "db_user_name: [%s], db_password: [%s]" % (self.username, "**********")
            )

            logger.debug(
                "db_host_name: [%s], db_port: [%s], db_name: [%s]"
                % (self.host, str(self.port), self.dbname)
            )

        except Exception as e:
            logger.error_common("DBコネクション初期化が失敗しました。")
            logger.error_common("共通処理DbConnectorで異常終了しました。")
            raise e

        self.logger = logger

    def connect(self):
        """
        Method:
            DBに接続する
        Args:
            null
        """
        DATABASE = "postgresql://%s:%s@%s:%s/%s" % (
            self.username,
            self.password,
            self.host,
            self.port,
            self.dbname,
        )

        # echo=Trueだと実行のたびにSQLが出力される
        # connect_timeoutはタイムアウト設定、単位:秒
        ENGINE = create_engine(
            DATABASE,
            echo=True,
            connect_args={"connect_timeout": -1, "client_encoding": "utf8"},
        )

        # Sessionの作成
        self.db = scoped_session(
            # ORM実行時の設定。自動コミットするか、自動反映するなど。
            sessionmaker(autocommit=False, autoflush=True, bind=ENGINE)
        )

    def close(self):
        """
        Method:
            DBコネクションを閉じる
        Args:
            null
        """
        if hasattr(self, "db") and self.db:
            try:
                self.db.close()
            except Exception as e:
                self.logger.error_common(
                    "DBコネクションを閉じる時にエラーが発生しました。"
                )
                self.logger.error_common("共通処理DbConnectorで異常終了しました。")
                raise e

    def exec(self, sql_template, values=None):
        """
        Method:
            SQL文実行
        Args:
            sql_template: SQL文テンプレート
            values: SQLパラメータ(dict or tuple)
        Return:
            SQL文の実行結果、戻り型はsqlalchemy.engine.cursor.LegacyCursorResult
        """
        self.logger.debug("sql: [%s]" % sql_template)
        if values:
            self.logger.debug("params: %s" % str(values))

        try:
            return self.db.execute(text(sql_template), values)
        except Exception as e:
            self.logger.error_common(
                "SQL文の実行が失敗しました。sql:[%s]" % sql_template
            )
            self.logger.error_common("共通処理DbConnectorで異常終了しました。")
            raise e

    def begin(self):
        """
        Method:
            セッションを開始する
        Args:
            null
        """
        if hasattr(self, "db") and self.db:
            try:
                self.db.begin()
            except Exception as e:
                self.logger.error_common(
                    "DBコネクションを開始時にエラーが発生しました。"
                )
                self.logger.error_common("共通処理DbConnectorで異常終了しました。")
                raise e

    def rollback(self):
        """
        Method:
            セッションをロールバックする
        Args:
            null
        """
        if hasattr(self, "db") and self.db:
            try:
                self.db.rollback()
            except Exception as e:
                self.logger.error_common(
                    "DBコネクションをロールバックする時にエラーが発生しました。"
                )
                self.logger.error_common("共通処理DbConnectorで異常終了しました。")
                raise e

    def commit(self):
        """
        Method:
            セッションをコミットする
        Args:
            null
        """
        if hasattr(self, "db") and self.db:
            try:
                self.db.commit()
            except Exception as e:
                self.logger.error_common(
                    "DBコネクションをコミットする時にエラーが発生しました。"
                )
                self.logger.error_common("共通処理DbConnectorで異常終了しました。")
                raise e
