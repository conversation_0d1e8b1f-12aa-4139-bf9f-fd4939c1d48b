#!/usr/bin/env python
# -*- coding: utf-8 -*-

from abc import abstractmethod
import csv
import io
import os
import pandas as pd
from typing import Dict, Optional, Union
from source.common_util import normalize_encoding
from source.converter_base import FileSourceConverter


class DelimitedFileConverter(FileSourceConverter):
    """区切り文字ファイルコンバーター基底クラス"""

    VALID_CONFIGS = {
        "csv2tsv": {"input": ["csv_options"], "output": ["tsv_options"]},
        "tsv2csv": {"input": ["tsv_options"], "output": ["csv_options"]},
    }

    VALID_ENCODINGS = {"utf_8", "ms932"}
    VALID_LINE_ENDINGS = ["\r\n", "\n", "\r"]

    def __init__(self, jobnet_id: str, etl_config: str, converter_type: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
            converter_type: 変換タイプ(csv2tsv/tsv2csv)
        """
        self.converter_type = converter_type
        self.input_path = None
        super().__init__(jobnet_id, etl_config)
        self._normalize_config()
        self._validate_delimited_config()
        self._validate_format_specific_config()

    def _normalize_config(self):
        """設定内容の正規化"""
        if "common" in self.config and isinstance(self.config["common"], dict):
            if "encoding" in self.config["common"]:
                # エンコーディングの正規化
                self.config["common"]["encoding"] = normalize_encoding(
                    self.config["common"]["encoding"]
                )

    def _validate_delimited_config(self):
        """
        区切り文字ファイル変換の基本設定を検証
        Raises:
            ValueError: 設定が不正な場合
        """
        # commonセクションの検証
        if "common" not in self.config:
            raise ValueError("必須セクション'common'がありません")

        common = self.config["common"]

        # format検証
        if "format" not in common:
            raise ValueError("変換形式(format)が指定されていません")
        if common["format"] not in self.VALID_CONFIGS:
            raise ValueError(
                f"サポートされていない変換形式です: {common['format']}\n"
                f"サポートされている形式: {', '.join(self.VALID_CONFIGS.keys())}"
            )

        # encoding検証
        encoding = common.get("encoding")
        if not encoding:
            raise ValueError("文字コード(encoding)が指定されていません")
        if encoding not in self.VALID_ENCODINGS:
            raise ValueError(
                f"サポートされていない文字コードです: {encoding}\n"
                f"サポートされている文字コード: shift_jis, utf-8"
            )

        # 必須セクションの検証
        required_sections = self.VALID_CONFIGS[common["format"]]
        for section, subsections in required_sections.items():
            if section not in self.config:
                raise ValueError(f"必須セクション'{section}'がありません")
            for subsection in subsections:
                if subsection not in self.config[section]:
                    raise ValueError(
                        f"必須サブセクション'{section}.{subsection}'がありません"
                    )

        # 基本オプションの検証
        for section in ["input", "output"]:
            if section in self.config:
                options_key = next(iter(required_sections[section]))
                options = self.config[section][options_key]

                # has_headerの検証
                if "has_header" in options and not isinstance(
                    options["has_header"], bool
                ):
                    raise ValueError(
                        f"'{section}.{options_key}.has_header'はbool型である必要があります"
                    )

                # quote_charの検証
                if "quote_char" in options and options["quote_char"] not in ['"', ""]:
                    raise ValueError(
                        f"'{section}.{options_key}.quote_char'は'\"'または空文字である必要があります"
                    )

                # 出力設定の場合、line_endingを検証
                if section == "output" and "line_ending" in options:
                    if options["line_ending"] not in self.VALID_LINE_ENDINGS:
                        raise ValueError(
                            f"サポートされていない改行コードです: {options['line_ending']}\n"
                            f"サポートされている改行コード: {', '.join(self.VALID_LINE_ENDINGS)}"
                        )

    def _validate_format_specific_config(self):
        """
        変換タイプ固有の設定を検証（継承クラスでオーバーライド）
        """
        pass

    def _get_pandas_read_options(self, io_type: str) -> Dict:
        """
        pandas.read_csvのオプションを取得
        Args:
            io_type: 入出力タイプ(input/output)
        Returns:
            Dict: pandasの読み込みオプション
        """
        # 変換タイプと入出力タイプに基づいて適切なオプション形式を決定
        format_type = self.VALID_CONFIGS[self.converter_type][io_type][0]
        options = self.config[io_type][format_type]

        return {
            "sep": self._get_delimiter(io_type),
            "encoding": self.config["common"]["encoding"],
            "header": 0 if options.get("has_header", False) else None,
            "quoting": (
                csv.QUOTE_NONE if options.get("quote_char") == "" else csv.QUOTE_ALL
            ),
            "dtype": str,
            "na_filter": False,
            "keep_default_na": False,
            "on_bad_lines": "skip",
            "encoding_errors": "replace",  # エンコーディングエラー時の動作を指定
        }

    def _get_pandas_write_options(self, io_type: str) -> Dict:
        """
        pandas.to_csvのオプションを取得
        Args:
            io_type: 入出力タイプ(input/output)
        Returns:
            Dict: pandasの書き込みオプション
        """
        # 変換タイプと入出力タイプに基づいて適切なオプション形式を決定
        format_type = self.VALID_CONFIGS[self.converter_type][io_type][0]
        options = self.config[io_type][format_type]

        # クォーティングの設定
        quoting = (
            csv.QUOTE_NONE if options.get("quote_char") == "" else csv.QUOTE_MINIMAL
        )

        # ヘッダー値の決定ロジック
        header_value = options.get("has_header", False) # デフォルトは has_header の値
        header_definition_columns = self.config.get("header_definition", {}).get("columns")

        if header_definition_columns is not None:
            # header_definition.columns が存在する場合
            if isinstance(header_definition_columns, list):
                if header_definition_columns and isinstance(header_definition_columns[0], dict):
                    # 辞書のリストの場合、'name'キーの値を取得してリスト化
                    try:
                        header_value = [item['name'] for item in header_definition_columns]
                    except (TypeError, KeyError) as e:
                        # 'name' キーがない、または要素が辞書でない場合
                        raise ValueError(f"header_definition.columns の形式が不正です。'name' キーを持つ辞書のリストである必要があります。エラー: {e}")
                else:
                     # 文字列のリストなど、そのまま使える形式の場合
                     header_value = header_definition_columns
            else:
                # リスト以外の予期しない形式の場合
                raise ValueError("header_definition.columns はリスト形式である必要があります。")

        # header_value が False でなく、かつリストでない場合は bool 値として扱う (Pandas の仕様に合わせる)
        # (例: header: true のような指定の場合)
        if header_value is not False and not isinstance(header_value, list):
             header_value = bool(header_value)


        return {
            "sep": self._get_delimiter(io_type),
            "encoding": self.config["common"]["encoding"],
            "index": False,
            "header": header_value, # 決定したヘッダー値を使用
            "quoting": quoting,
            "lineterminator": options.get("line_ending", "\n"),
            "quotechar": options.get("quote_char", '"'),
        }

    def _get_delimiter(self, io_type: str) -> str:
        """
        入出力タイプと変換タイプに基づいて区切り文字を取得

        Args:
            io_type: 入出力タイプ（'input' または 'output'）

        Returns:
            str: 区切り文字（カンマまたはタブ）

        Raises:
            ValueError: io_typeが'input'または'output'以外の場合
        """
        # 入力検証を先に実施
        if io_type not in ["input", "output"]:
            raise ValueError("io_typeはinputまたはoutputを指定してください")

        # 変換タイプと入出力タイプに基づいて適切なオプション形式を決定
        format_type = self.VALID_CONFIGS[self.converter_type][io_type][0]

        # 区切り文字を返却
        return "," if "csv" in format_type else "\t"

    def convert(
        self, source_path: str, output_path: Optional[str] = None
    ) -> Union[str, bytes]:
        """
        ファイル変換を実行
        Args:
            source_path: 入力ファイルパス
            output_path: 出力パス
        Returns:
            Union[str, bytes]: 変換後データ
        """
        try:
            # 空ファイルチェック
            if os.path.getsize(source_path) == 0:
                self.logger.error_common(f"入力ファイルが空です: {source_path}")
                if output_path:
                    with open(output_path, "wb") as f:
                        f.write(b"")
                return b""

            encoding = self.config["common"]["encoding"]

            # ファイル読み込み（バイナリモード）
            with open(source_path, "rb") as f:
                input_data = f.read()

            # 入力データをデコード
            input_text = input_data.decode(encoding)

            # StringIOに変換してpandasで読み込み
            with io.StringIO(input_text) as string_buffer:
                df = pd.read_csv(
                    string_buffer,
                    sep=self._get_delimiter("input"),
                    header=(
                        0
                        if self.config["input"][
                            self.VALID_CONFIGS[self.converter_type]["input"][0]
                        ].get("has_header", False)
                        else None
                    ),
                    quoting=(
                        csv.QUOTE_NONE
                        if self.config["input"][
                            self.VALID_CONFIGS[self.converter_type]["input"][0]
                        ].get("quote_char")
                        == ""
                        else csv.QUOTE_ALL
                    ),
                    dtype=str,
                    na_filter=False,
                    keep_default_na=False,
                    on_bad_lines="skip",
                )

            # 空データチェック
            if df.empty:
                self.logger.error_common(f"読み込みデータが空です: {source_path}")
                if output_path:
                    with open(output_path, "wb") as f:
                        f.write(b"")
                return b""

            # 変換
            buffer = io.StringIO()
            df.to_csv(buffer, **self._get_pandas_write_options("output"))
            converted_text = buffer.getvalue()
            buffer.close()

            # エンコード
            converted_data = converted_text.encode(encoding)

            # 必要に応じてファイル出力
            if output_path:
                with open(output_path, "wb") as f:
                    f.write(converted_data)

            return converted_data

        except Exception as e:
            # 継承先クラスのエラーメッセージを使用
            self.logger.error_common(f"{self.get_error_prefix()}変換エラー: {e}")
            raise

    def execute_conversion(
        self, file_path: str, output_path: Optional[str] = None
    ) -> str:
        """
        データ取得と変換を実行
        Args:
            file_path: 入力ファイルパス
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        self.input_path = file_path
        return self.convert(file_path, output_path)

    def convert_from_data(
        self, data: Union[str, io.StringIO], output_path: Optional[str] = None
    ) -> str:
        """
        このクラスではconvert_from_dataメソッドを使用しません。
        代わりにexecute_conversionまたはconvertメソッドを使用してください。

        Raises:
            NotImplementedError: このメソッドは意図的に実装されていません
        """
        raise NotImplementedError(
            "このコンバーターではconvert_from_dataメソッドは使用できません。"
            "execute_conversionまたはconvertメソッドを使用してください。"
        )

    @abstractmethod
    def get_error_prefix(self) -> str:
        """変換タイプに応じたエラーメッセージのプレフィックスを返す"""
        pass


class Csv2TsvConverter(DelimitedFileConverter):
    """CSVからTSVへの変換クラス"""

    def __init__(self, jobnet_id: str, etl_config: str):
        super().__init__(jobnet_id, etl_config, "csv2tsv")

    def _validate_format_specific_config(self):
        """
        CSV->TSV変換固有の設定を検証
        """
        # 入力設定（CSV）の検証
        csv_options = self.config["input"]["csv_options"]
        if "delimiter" in csv_options and csv_options["delimiter"] != ",":
            raise ValueError("CSV入力の区切り文字はカンマである必要があります")

        # 出力設定（TSV）の検証
        tsv_options = self.config["output"]["tsv_options"]
        if "delimiter" in tsv_options and tsv_options["delimiter"] != "\t":
            raise ValueError("TSV出力の区切り文字はタブである必要があります")

    def get_error_prefix(self) -> str:
        return "CSV to TSV"


class Tsv2CsvConverter(DelimitedFileConverter):
    """TSVからCSVへの変換クラス"""

    def __init__(self, jobnet_id: str, etl_config: str):
        super().__init__(jobnet_id, etl_config, "tsv2csv")

    def _validate_format_specific_config(self):
        """
        TSV->CSV変換固有の設定を検証
        """
        # 入力設定（TSV）の検証
        tsv_options = self.config["input"]["tsv_options"]
        if "delimiter" in tsv_options and tsv_options["delimiter"] != "\t":
            raise ValueError("TSV入力の区切り文字はタブである必要があります")

        # 出力設定（CSV）の検証
        csv_options = self.config["output"]["csv_options"]
        if "delimiter" in csv_options and csv_options["delimiter"] != ",":
            raise ValueError("CSV出力の区切り文字はカンマである必要があります")

    def get_error_prefix(self) -> str:
        return "TSV to CSV"
