#!/usr/bin/env python
# -*- coding: utf-8 -*-

import csv
import io
from typing import Dict, List, Optional
from source.common_util import normalize_encoding
from source.converter_base import FileSourceConverter


class Fixed2TsvConverter(FileSourceConverter):
    """固定長からTSVへの変換クラス"""

    # 固定長ファイルは必ずms932
    VALID_ENCODINGS = {"ms932"}

    def __init__(self, jobnet_id: str, etl_config: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
        """
        self.converter_type = "fixed2tsv"
        self.input_path = None  # 入力ファイルパスを保持
        super().__init__(jobnet_id, etl_config)
        self._normalize_config()
        self._validate_fixed_config()

    def _normalize_config(self):
        """設定内容の正規化"""
        if "common" in self.config and isinstance(self.config["common"], dict):
            if "encoding" in self.config["common"]:
                # エンコーディングの正規化
                self.config["common"]["encoding"] = normalize_encoding(
                    self.config["common"]["encoding"]
                )

    def _validate_fixed_config(self):
        """
        固定長→TSV変換の設定を検証
        Raises:
            ValueError: 設定が不正な場合
        """
        # commonセクションの検証
        if "common" not in self.config:
            raise ValueError("必須セクション'common'がありません")

        common = self.config["common"]

        # format検証
        format_type = common.get("format")
        if not format_type:
            raise ValueError("変換形式(format)が指定されていません")
        if format_type != "fixed2tsv":
            raise ValueError("変換形式はfixed2tsvを指定してください")

        # 必須セクションの検証
        required_sections = {"input": ["fixed_options"], "output": ["tsv_options"]}
        for section, subsections in required_sections.items():
            if section not in self.config:
                raise ValueError(f"必須セクション'{section}'がありません")

            for subsection in subsections:
                if subsection not in self.config[section]:
                    raise ValueError(
                        f"必須サブセクション'{section}.{subsection}'がありません"
                    )

        # エンコーディング検証
        encoding = common["encoding"]
        if encoding not in self.VALID_ENCODINGS:
            raise ValueError(
                "固定長ファイルでは文字コードはshift_jisのみサポートしています\n"
            )

        # 固定長フィールド定義の検証
        fixed_options = self.config["input"]["fixed_options"]
        if "fields" not in fixed_options:
            raise ValueError("固定長フィールド定義(fields)がありません")

        fields = fixed_options["fields"]
        if not isinstance(fields, list) or not fields:
            raise ValueError("フィールド定義は1つ以上必要です")

        total_length = 0
        for field in fields:
            if "name" not in field:
                raise ValueError("フィールド名(name)が指定されていません")
            if "length" not in field:
                raise ValueError(
                    f"フィールド長(length)が指定されていません: {field['name']}"
                )

            total_length += field["length"]

        expected_length = fixed_options.get("total_length")
        if expected_length and total_length != expected_length:
            raise ValueError(
                f"フィールド長の合計({total_length})が"
                f"total_length({expected_length})と一致しません"
            )

    def execute_conversion(self, file_path: str, output_path: str) -> str:
        """
        データ取得と変換を実行（基底クラスのメソッドをオーバーライド）
        Args:
            file_path: 入力ファイルパス
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        self.input_path = file_path
        return self.convert(file_path, output_path)

    def _read_fixed_length(self, file_path: str) -> List[Dict[str, str]]:
        """
        固定長ファイルの読み込みと分割を行う
        Args:
            file_path: 入力ファイルパス
        Returns:
            List[Dict[str, str]]: フィールド名をキーとする辞書のリスト
        Raises:
            ValueError: レコード長が不正な場合
        """
        try:
            # 固定長フィールドの定義を取得
            fields = self.config["input"]["fixed_options"]["fields"]
            total_length = self.config["input"]["fixed_options"]["total_length"]
            encoding = self.config["common"]["encoding"]  # 正規化済みのエンコーディング

            # データ読み込み
            rows = []

            # バイナリモードで読み込み
            with open(file_path, "rb") as f:
                content = f.read()

                # ファイル全体の長さチェック
                if len(content) % total_length != 0:
                    error_msg = (
                        f"固定長ファイルのレコード長が不正です: "
                        f"ファイル長={len(content)}バイト, "
                        f"レコード長={total_length}バイト"
                    )
                    self.logger.error_common(error_msg)
                    raise ValueError(error_msg)

                # レコード長（バイト単位）で分割
                for i in range(0, len(content), total_length):
                    record_bytes = content[i : i + total_length]

                    # 各フィールドの切り出し
                    row = {}
                    current_pos = 0
                    for field in fields:
                        field_length = field["length"]
                        field_bytes = record_bytes[
                            current_pos : current_pos + field_length
                        ]

                        try:
                            # 設定されたエンコーディングでデコードして両端の空白を除去
                            value = field_bytes.decode(encoding).strip()
                            row[field["name"]] = value
                            current_pos += field_length
                        except UnicodeDecodeError as e:
                            error_msg = (
                                f"レコード{i//total_length + 1}の"
                                f"フィールド'{field['name']}'のデコードに失敗: {e}"
                            )
                            self.logger.error_common(error_msg)
                            raise ValueError(error_msg)

                    rows.append(row)

            return rows

        except Exception as e:
            self.logger.error_common(f"固定長ファイルの読み込みエラー: {e}")
            raise

    def convert(self, source_path: str, output_path: Optional[str] = None) -> str:
        """
        固定長ファイルをTSVに変換
        Args:
            source_path: 入力固定長ファイルパス
            output_path: 出力TSVファイルパス
        Returns:
            str: TSV形式データ
        """
        try:
            # 設定ファイルの検証
            self._validate_config(["input", "output"])

            # エンコーディングの取得（YAMLから）
            encoding = self.config["common"]["encoding"]

            # 固定長ファイルの読み込み
            rows = self._read_fixed_length(source_path)

            # データが空の場合
            if not rows:
                self.logger.error_common(f"変換元データがありません: {source_path}")
                if output_path:
                    with open(output_path, "w", encoding=encoding) as f:
                        f.write("")
                return ""

            # フィールド名の取得
            fields = [
                field["name"]
                for field in self.config["input"]["fixed_options"]["fields"]
            ]

            # 出力オプションを取得
            tsv_options = self.config["output"]["tsv_options"]
            quote_char = tsv_options.get("quote_char", "")
            line_ending = tsv_options.get("line_ending", "\n")
            has_header = tsv_options.get("has_header", True)

            # TSVファイルへの変換
            buffer = io.StringIO()
            writer = csv.DictWriter(
                buffer,
                fieldnames=fields,
                delimiter="\t",
                quoting=csv.QUOTE_NONE if quote_char == "" else csv.QUOTE_ALL,
                quotechar=None if quote_char == "" else '"',  # 囲み文字なしの場合はNone
                lineterminator=line_ending,
                escapechar=None,
            )

            # ヘッダー行の出力（オプション）
            if has_header:
                writer.writeheader()

            # データ行の出力
            writer.writerows(rows)

            # 変換データの取得
            converted_data = buffer.getvalue()

            # output_pathが指定されている場合のみファイル出力
            if output_path:
                with open(output_path, "w", encoding=encoding) as f:
                    f.write(converted_data)

            return converted_data

        except Exception as e:
            self.logger.error_common(f"固定長 to TSV変換エラー: {e}")
            raise

    def convert_from_data(self, data: List[Dict], output_path: str) -> str:
        """
        データリストからTSVへの変換（未実装）
        Args:
            data: 変換元データ
            output_path: 出力パス
        Raises:
            NotImplementedError: このメソッドは現在サポートされていません
        """
        raise NotImplementedError(
            "固定長ファイルからのデータ直接変換は現在サポートされていません"
        )
