import unicodedata
from typing import List, Dict, Union, Any


class ShiftJISConversionError(Exception):
    """
    Shift-JIS への変換が不可能な文字が存在する場合に発生する例外クラス。

    Attributes:
        value (str): 変換に失敗した文字列値
        column_name (str): 変換に失敗したカラム名
    """

    def __init__(self, value: str, column_name: str):
        self.value = value
        self.column_name = column_name
        super().__init__(
            f"Column '{column_name}': Cannot convert '{value}' to Shift-JIS(MS932)"
        )


def get_shift_jis_length(text: str) -> int:
    """
    文字列の Shift-JIS(ms932) エンコーディングでのバイト長を計算する。

    Args:
        text (str): 長さを測定する文字列

    Returns:
        int: Shift-JIS エンコーディングでのバイト長

    Raises:
        ShiftJISConversionError: 文字列を Shift-JIS(ms932) にエンコードできない場合
    """
    try:
        return len(text.encode("ms932"))
    except UnicodeEncodeError:
        raise ShiftJISConversionError(text, "unknown")


def truncate_to_shift_jis_width(text: str, max_width: int) -> str:
    """
    指定された最大バイト長に従って、文字列を Shift-JIS(MS932) ベースで切り捨てる。

    Args:
        text (str): 元の文字列
        max_width (int): 最大許容バイト長

    Returns:
        str: 指定されたバイト長以下に切り捨てられた文字列

    Raises:
        ShiftJISConversionError: 文字列を Shift-JIS にエンコードできない場合
    """
    # 入力値を文字列に変換（Noneなどの場合を考慮）
    text_str = str(text) if text is not None else "" # Ensure text is string

    # 一文字ずつチェックして切り捨て
    truncated_text = ""
    for char in text_str:
        try:
            # 現在の文字を追加した場合の長さをチェック
            if get_shift_jis_length(truncated_text + char) <= max_width:
                truncated_text += char # 長さが許容範囲内なら文字を追加
            else:
                break
        except ShiftJISConversionError:
            raise

    return truncated_text


def convert_to_fixed_width_sjis(
    data: List[Dict[str, Any]], column_specs: List[Dict[str, Union[str, int]]]
) -> List[str]:
    """
    データをShift-JIS固定長フォーマットに変換する。

    Args:
        data (List[Dict]): 変換元のデータリスト
            各辞書は、カラム名をキーとする値を持つ
        column_specs (list): カラムごとの仕様を定義するリスト
            各要素は以下の形式の辞書:
            {
                'name': カラム名 (str),
                'width': バイト単位の最大幅 (int),
                'align': 文字列の配置 ('left' または 'right', デフォルト: 'left'),
                'padding': パディング文字 (str, デフォルト: ' ')
            }

    Returns:
        list: 固定長 Shift-JIS 文字列のリスト

    Raises:
        ShiftJISConversionError: Shift-JIS に変換できない文字が存在する場合
    """

    def format_column(value: Any, spec: Dict[str, Union[str, int]]) -> str:
        # 値を文字列に変換
        str_value = str(value) if value is not None else ""

        # Shift-JIS に変換可能かチェック
        try:
            sjis_value = str_value.encode("ms932", errors="strict").decode("ms932")
        except UnicodeEncodeError:
            raise ShiftJISConversionError(str_value, spec["name"])

        # Shift-JIS のバイト長で切り捨て
        sjis_value = truncate_to_shift_jis_width(sjis_value, spec["width"])

        # パディング処理
        padding_char = spec.get("padding", " ")
        if spec.get("align", "left") == "left":
            while get_shift_jis_length(sjis_value) < spec["width"]:
                sjis_value += padding_char
        else:
            while get_shift_jis_length(sjis_value) < spec["width"]:
                sjis_value = padding_char + sjis_value

        return sjis_value

    # 各行を固定長文字列に変換
    fixed_width_lines = []
    for row in data:
        line = "".join(
            format_column(row.get(spec["name"]), spec) for spec in column_specs
        )
        fixed_width_lines.append(line)

    return fixed_width_lines
