#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from source.common_util import load_yaml_config
from source.delimited_file_converter import (
    Csv2TsvConverter,
    Tsv2CsvConverter,
)
from source.fixed_length_converter import (
    Fixed2TsvConverter,
)


class FormatConverterFactory:
    """
    入力と出力のフォーマットに基づいて適切なコンバーターを選択するファクトリクラス
    """

    @staticmethod
    def get_converter(
        jobnet_id: str,
        etl_config: str,
    ):
        """
        ETL設定に基づいて適切なコンバーターを返す

        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID

        Returns:
            コンバータークラスのインスタンス

        Raises:
            ValueError: サポートされていない変換パターンの場合
        """
        converters = {
            # サポートされている変換パターン
            "csv2tsv": Csv2TsvConverter,
            "tsv2csv": Tsv2CsvConverter,
            "fixed2tsv": Fixed2TsvConverter,
        }

        _etl_config = load_yaml_config(f"converter/{etl_config}.yaml")

        # commonセクションからformatを取得
        format_type = _etl_config.get("common", {}).get("format")

        if not format_type:
            raise ValueError("ETL設定に変換形式(format)が指定されていません。")

        converter_class = converters.get(format_type)

        if not converter_class:
            supported_formats = ", ".join(converters.keys())
            raise ValueError(
                f"サポートされていない変換形式が指定されました。\n"
                f"サポートされている変換形式: {supported_formats}"
            )

        return converter_class(jobnet_id, os.path.basename(etl_config))
