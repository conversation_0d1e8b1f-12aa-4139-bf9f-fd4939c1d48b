#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AWS Glue環境での依存関係管理を担当するモジュール

このモジュールは、AWS Glue環境において、deps.zipとして配布される依存関係パッケージを
正しく解決するための機能を提供する。特に、botocoreなどのデータファイルを必要とする
パッケージがZIPファイルから直接ロードできない問題を解決する。

使用例:
    from job.glue_deps_handler import setup_glue_dependencies

    # ジョブスクリプトの先頭で実行
    setup_glue_dependencies()

    # これ以降のインポートはdeps_extracted内のパッケージを使用
    import boto3
    ...
"""

import os
import sys
import glob
import zipfile
from typing import Optional, List, Dict


def debug_modules() -> None:
    """現在ロード済みのモジュールを表示"""
    print("---------- スクリプト開始時のロード済みモジュール ----------")
    loaded_modules = sorted(sys.modules.keys())
    aws_modules = [m for m in loaded_modules if m.startswith('boto') or m.startswith('aws')]
    print(f"AWS関連モジュール: {aws_modules}")
    print(f"全モジュール数: {len(loaded_modules)}")
    print("-----------------------------------------------------------")


def remove_boto_modules() -> int:
    """
    ロード済みのboto/AWS関連モジュールをアンロード

    Returns:
        int: アンロードされたモジュール数
    """
    modules_to_remove = []
    for module_name in list(sys.modules.keys()):
        if module_name.startswith('boto') or module_name.startswith('aws'):
            modules_to_remove.append(module_name)

    for module_name in modules_to_remove:
        try:
            del sys.modules[module_name]
            print(f"✅ アンロード成功: {module_name}")
        except KeyError:
            print(f"⚠️ アンロード失敗: {module_name} (既に削除済み)")

    print(f"合計 {len(modules_to_remove)} モジュールをアンロードしました")
    return len(modules_to_remove)


def find_deps_zip() -> Optional[str]:
    """
    Glueが自動的にダウンロードしたdeps.zipファイルを検索

    Returns:
        Optional[str]: deps.zipのフルパス。見つからない場合はNone
    """
    # /tmp/glue-python-libs-* ディレクトリを検索
    glue_tmp_dirs = glob.glob('/tmp/glue-python-libs-*')

    if not glue_tmp_dirs:
        print("⚠️ /tmp/glue-python-libs-* ディレクトリが見つかりません")
        return None

    print(f"Glue一時ディレクトリを発見: {glue_tmp_dirs}")

    # 各ディレクトリ内でdeps.zipを検索
    for tmp_dir in glue_tmp_dirs:
        # ディレクトリ内のファイル一覧を表示（デバッグ用）
        print(f"{tmp_dir}の内容:")
        for item in os.listdir(tmp_dir):
            print(f" - {item}")

            # deps.zipを見つけたら保存
            if item == 'deps.zip':
                deps_zip_path = os.path.join(tmp_dir, item)
                print(f"👍 deps.zipを発見: {deps_zip_path}")
                return deps_zip_path

    print("⚠️ deps.zipが見つかりません")
    return None


def extract_zip(zip_path: str) -> Optional[str]:
    """
    deps.zipをGlue一時ディレクトリ内に展開

    Args:
        zip_path: deps.zipのフルパス

    Returns:
        Optional[str]: 展開先ディレクトリのパス。展開に失敗した場合はNone
    """
    try:
        # ZIPファイルがあるディレクトリを特定
        zip_dir = os.path.dirname(zip_path)

        # 展開先ディレクトリを作成（ZIPファイルと同じディレクトリに）
        extract_dir = os.path.join(zip_dir, 'deps_extracted')
        if not os.path.exists(extract_dir):
            os.makedirs(extract_dir)

        print(f"deps.zipを展開します: {zip_path} → {extract_dir}")

        # ZIPファイルを展開
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)

        return extract_dir
    except Exception as e:
        print(f"⚠️ ZIPファイル展開中にエラーが発生: {str(e)}")
        return None


def inspect_extracted_dir(extract_dir: str) -> None:
    """
    展開されたディレクトリの内容を検査してログに出力

    Args:
        extract_dir: 展開先ディレクトリのパス
    """
    print(f"\n---------- 展開されたディレクトリの内容検査 ({extract_dir}) ----------")

    # boto3とbotocoreのデータディレクトリを確認
    boto3_dir = os.path.join(extract_dir, 'boto3')
    botocore_dir = os.path.join(extract_dir, 'botocore')
    botocore_data_dir = os.path.join(botocore_dir, 'data')

    print(f"boto3ディレクトリ存在: {os.path.exists(boto3_dir)}")
    print(f"botocoreディレクトリ存在: {os.path.exists(botocore_dir)}")
    print(f"botocore/dataディレクトリ存在: {os.path.exists(botocore_data_dir)}")

    # endpoints.jsonファイルを確認
    endpoints_file = os.path.join(botocore_data_dir, 'endpoints.json')
    if os.path.exists(endpoints_file):
        print(f"✅ endpoints.jsonファイルを確認: {endpoints_file}")
        print(f"  - ファイルサイズ: {os.path.getsize(endpoints_file)} バイト")
    else:
        print(f"⚠️ endpoints.jsonファイルが見つかりません")


def add_to_path(directory: str) -> bool:
    """
    指定されたディレクトリをsys.pathの先頭に追加

    Args:
        directory: 追加するディレクトリのパス

    Returns:
        bool: 追加に成功したかどうか
    """
    if not os.path.exists(directory):
        print(f"⚠️ 指定されたディレクトリが存在しません: {directory}")
        return False

    if directory not in sys.path:
        sys.path.insert(0, directory)
        print(f"✅ sys.pathに追加しました: {directory}")

        # 現在のパスをデバッグ表示
        print("現在のsys.path（優先順）:")
        for i, path in enumerate(sys.path):
            print(f"{i}: {path}")

        return True
    else:
        print(f"ℹ️ 既にsys.pathに含まれています: {directory}")
        return False


def verify_imports() -> bool:
    """
    boto3とbotocoreが正しくインポートできるかを検証

    Returns:
        bool: インポートに成功したかどうか
    """
    try:
        print("\n---------- boto3/botocoreインポート検証 ----------")

        import importlib
        import boto3
        import botocore

        print(f"✅ boto3 バージョン: {boto3.__version__}")
        print(f"  - ファイルパス: {boto3.__file__}")

        print(f"✅ botocore バージョン: {botocore.__version__}")
        print(f"  - ファイルパス: {botocore.__file__}")

        # セッション初期化のテスト
        session = boto3.session.Session()
        print(f"✅ boto3セッション初期化成功")

        return True
    except Exception as e:
        print(f"⚠️ boto3/botocoreインポート中にエラー: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def is_running_under_pytest() -> bool:
    """
    pytestから実行されているかどうかを判定

    Returns:
        bool: pytestから実行されている場合はTrue
    """
    return 'pytest' in sys.modules


def setup_glue_dependencies(verbose: bool = True) -> bool:
    """
    Glue環境での依存関係セットアップをハンドリングする主要関数

    Args:
        verbose: 詳細なログ出力を行うかどうか

    Returns:
        bool: セットアップに成功したかどうか
    """
    # pytestからの実行かどうかを判定
    if is_running_under_pytest():
        if verbose:
            print("pytestから実行中のため、Glue依存関係のセットアップをスキップします")
        return True

    if verbose:
        # 現在ロード済みのモジュールを表示
        debug_modules()

    # 1. deps.zipを検索
    deps_zip_path = find_deps_zip()
    if not deps_zip_path:
        print("⚠️ deps.zipが見つからないため、セットアップを中止します")
        return False

    # 2. boto関連モジュールをアンロード
    remove_boto_modules()

    # 3. ZIPファイルを展開
    extract_dir = extract_zip(deps_zip_path)
    if not extract_dir:
        print("⚠️ deps.zipの展開に失敗したため、セットアップを中止します")
        return False

    # 4. 展開されたディレクトリの内容を検査
    if verbose:
        inspect_extracted_dir(extract_dir)

    # 5. 展開先をパスに追加
    if not add_to_path(extract_dir):
        print("⚠️ sys.pathへの追加に失敗したため、セットアップを中止します")
        return False

    # 6. インポートの検証
    if not verify_imports():
        print("⚠️ boto3/botocoreのインポート検証に失敗しました")
        # 失敗してもセットアップは継続（次の共通処理でインポートが成功する可能性がある）

    print("✅ Glue依存関係のセットアップが完了しました")
    return True


if __name__ == "__main__":
    # モジュールとして直接実行された場合のテスト
    setup_glue_dependencies(verbose=True)
