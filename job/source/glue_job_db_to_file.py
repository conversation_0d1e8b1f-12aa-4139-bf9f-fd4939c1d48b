#!/usr/bin/env python
# -*- coding: utf-8 -*-
# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import os
import sys
import boto3
import json
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime
from source.common import (
    initialize_env,
    get_job_params,
    retry_function,
)
from source.common_util import load_sql_config
from source.glue_logger import GlueLogger
from source.db_connector import DbConnector
from source.db2xml_converter import DB2XMLConverter
from source.db2fixed_converter import DB2FixedConverter


class Constants:
    """定数クラス"""

    # タイムスタンプのデフォルト値
    DEFAULT_TIMESTAMP = datetime(1900, 1, 1, 0, 0, 0)

    # ログメッセージID
    LOG_INFO_START = "I_job_db_to_file_001"  # 開始ログ
    LOG_INFO_END = "I_job_db_to_file_002"  # 正常終了ログ
    LOG_INFO_RETRY = "I_job_db_to_file_003"  # リトライログ
    LOG_ERROR_JOB = "E_job_db_to_file_001"  # ジョブエラー
    LOG_ERROR_PROCESS = "E_job_db_to_file_002"  # 処理エラー
    LOG_ERROR_EXCEPTION = "E_job_db_to_file_003"  # 例外エラー


class GlueJobDbToFile:
    """DBデータのファイル化ジョブ"""

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")
        self.params = None

    def connect_db(self, secret_name: str) -> DbConnector:
        """
        データベースに接続
        Args:
            secret_name: シークレット名
        Returns:
            DbConnector: DB接続オブジェクト
        """
        try:
            db_connector = DbConnector(self.logger, secret_id=secret_name)
            db_connector.connect()
            return db_connector
        except Exception as e:
            self.logger.error_common(f"DB接続エラー: {str(e)}")
            raise

    def get_diff_base_timestamp(self, db_connector: DbConnector) -> datetime:
        """
        差分基準時刻を取得
        Args:
            db_connector: DB接続オブジェクト
        Returns:
            datetime: 差分基準時刻
            データが存在しない場合は1900-01-01を返す
        """
        try:
            # 差分基準時刻クエリの取得と実行
            query_id = self.params["diff_base_timestamp_query"]
            query = load_sql_config(f"{query_id}.sql")
            self.logger.debug(f"diff_base_timestamp query: {query}")

            result = db_connector.exec(query)
            row = result.fetchone()
            self.logger.debug(f"diff_base_timestamp result: {row}")

            if not row or row[0] is None:
                # データが存在しない場合は暗黙値を返す
                default_time = Constants.DEFAULT_TIMESTAMP
                self.logger.debug(
                    f"差分基準時刻がありません。デフォルト値を使用します。(default_time={default_time})"
                )
                return default_time
            return row[0]
        except Exception as e:
            self.logger.error_common(f"差分基準時刻取得エラー: {str(e)}")
            raise

    def get_last_sync_timestamp(self, db_connector: DbConnector) -> datetime:
        """
        前回同期済タイムスタンプを取得
        Args:
            db_connector: DB接続オブジェクト
        Returns:
            datetime: 同期タイムスタンプ
            初回実行時は1900-01-01を返す
        """
        try:
            sync_query = load_sql_config("sql_common_select_001.sql")
            sync_params = {
                "job_schedule_id": self.jobnet_id,
                "file_name": self.params["file_id"],
            }
            result = db_connector.exec(sync_query, sync_params)
            row = result.fetchone()
            self.logger.debug(f"last_sync query result: {row}")

            if row and row[0]:
                self.logger.debug(f"前回同期タイムスタンプ: {row[0]}")
                return row[0]

            # 初回実行時はデフォルト値を返す
            default_time = Constants.DEFAULT_TIMESTAMP
            self.logger.debug(
                f"前回同期タイムスタンプがありません。デフォルト値を使用します。(default_time={default_time})"
            )
            return Constants.DEFAULT_TIMESTAMP

        except Exception as e:
            self.logger.error("E_job_db_to_file_002", ("クエリ実行",))
            raise

    def execute_query(
        self,
        db_connector: DbConnector,
        query_id: str,
        diff_base_timestamp: datetime,
        last_sync_timestamp: datetime,
        batch_size: int = 1000,
    ) -> Tuple[List[Dict[str, Any]], Optional[datetime], List[str]]:
        """
        クエリを実行してデータを取得
        Args:
            db_connector: DB接続オブジェクト
            query_id: クエリID
            diff_base_timestamp: 差分基準時刻
            last_sync_timestamp: 前回同期タイムスタンプ
            batch_size: バッチサイズ
        Returns:
            Tuple[List[Dict[str, Any]], Optional[datetime], List[str]]: 取得したデータと前回同期タイムスタンプとカラム名リスト
        """
        try:
            # メインクエリの取得と実行
            main_query = load_sql_config(f"{query_id}.sql")
            self.logger.debug(f"main_query: {main_query}")
            # メインクエリの実行
            result = db_connector.exec(
                main_query,
                {
                    "sync_datetime": last_sync_timestamp,
                    "diff_base_timestamp": diff_base_timestamp,
                },
            )
            # ResultProxyからカラム名を取得（結果セットの有無に関係なく取得可能）
            column_names = result.keys()
            rows = result.fetchall()

            # 0件の場合は空リストを返却
            if not rows:
                self.logger.debug("抽出データが0件です。")
                return [], last_sync_timestamp, column_names

            self.logger.debug(f"column_names: {column_names}")
            data = [row._mapping for row in rows]
            self.logger.debug(f"query result: {data}")
            return data, last_sync_timestamp, column_names
        except Exception as e:
            self.logger.error("E_job_db_to_file_002", ("クエリ実行",))
            raise

    def execute_query_streaming(self, db_connector, query, params, batch_size=1000):
        result = db_connector.exec(query, params)
        column_names = result.keys()
        while True:
            rows = result.fetchmany(batch_size)
            if not rows:
                break
            yield rows, column_names

    def convert_to_csv_tsv(
        self,
        data: list,
        file_setting: Dict[str, Any],
        file_type: str,
        column_names: List[str],
    ) -> str:
        """
        データをCSV/TSV形式に変換
        Args:
            data: 変換元データ
            file_setting: ファイル設定
            file_type: ファイル形式（csv/tsv）
        Returns:
            str: 変換後データ
        """
        try:
            delimiter = "," if file_type == "csv" else "\t"
            result = []

            # クォート文字の取得
            quote_char = file_setting.get("quote_char", '"')
            no_quote_columns = [col_num - 1 for col_num in file_setting.get("no_quote_column_nums", [])]

            def quote_value(value, col_index):
                """値をクォートする"""
                if col_index in no_quote_columns:
                    return str(value)
                return f"{quote_char}{value}{quote_char}"

         # ヘッダー出力（column_names情報から作成）
            if file_setting.get("header", True):
                # ヘッダーは常にクォートする
                headers = [f"{quote_char}{col}{quote_char}" for col in column_names]
                result.append(delimiter.join(headers))

            # データ行の出力
            for row in data:
                line = []
                for idx, (col_name, value) in enumerate(row.items()):
                    # NULL値は空文字列に変換
                    if value is None:
                        value = ""
                    line.append(quote_value(str(value), idx))
                result.append(delimiter.join(line))

            # 改行コードの処理
            line_ending = file_setting.get("line_ending", "\n")
            content = line_ending.join(result)
            self.logger.debug(f"converted content: {content}")
            return content
        except Exception as e:
            self.logger.error_common(f"ファイル変換エラー: {str(e)}")
            raise

    def upload_to_s3(self, content: bytes, output_path: str):
        """
        S3にバイナリデータをアップロード（リトライ処理付き）
        Args:
            content: アップロードするバイナリデータ
            output_path: 出力パス
        """

        def _upload():
            self.s3_client.put_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=output_path,
                Body=content,
            )

        try:
            retry_function(
                _upload,
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT", 3)),
                retry_interval=float(os.environ.get("S3_RETRY_INTERVAL", 1.0)),
                logger=self.logger,
                process_name="S3ファイル配置",
                info_msg_id=Constants.LOG_INFO_RETRY,
                error_msg_id=Constants.LOG_ERROR_PROCESS,
            )
        except Exception as e:
            self.logger.error_common(f"S3ファイル配置エラー: {str(e)}")
            raise

    def update_sync_timestamp(self, db_connector: DbConnector, timestamp: datetime):
        """
        同期タイムスタンプの更新
        Args:
            db_connector: DB接続オブジェクト
            timestamp: 更新タイムスタンプ
        """
        try:
            update_query = load_sql_config("sql_common_update_001.sql")
            params = {
                "job_schedule_id": self.jobnet_id,
                "file_name": self.params["file_id"],
                "timestamp": timestamp,
                "user": self.jobnet_id,  # ジョブネットIDをユーザーとして使用
            }
            db_connector.exec(update_query, params)

            # トランザクションのコミット
            db_connector.commit()  # トランザクションのコミット

        except Exception as e:
            db_connector.rollback()  # エラー時はロールバック
            self.logger.error("E_job_db_to_file_002", ("クエリ実行",))
            raise

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
        """
        db_connector = None
        db_connector2 = None  # 同期タイムスタンプ用のDB接続
        try:
            self.params = params
            # 開始処理
            self.logger.info(
                Constants.LOG_INFO_START,
                (params["file_name"],),
            )

            # file_typeのバリデーション
            valid_file_types = ["csv", "tsv", "xml", "fixed"]
            if params["file_type"] not in valid_file_types:
                raise ValueError(
                    f"不正なファイル形式が指定されました。指定可能な形式: {', '.join(valid_file_types)}"
                )

            # DB接続
            db_connector = self.connect_db(params["secret_name"])
            db_connector2 = self.connect_db("DLPF_DB_INFO")  # 同期タイムスタンプ用

            # 差分基準時刻の取得
            diff_base = self.get_diff_base_timestamp(db_connector)
            self.logger.debug(f"差分基準時刻: {diff_base}")

            # 前回同期時刻の取得
            last_sync = self.get_last_sync_timestamp(db_connector2)
            # 差分基準時刻が前回同期時刻より前の場合はエラー
            if diff_base < last_sync:
                raise ValueError(f"差分基準時刻（{diff_base}）が前回同期時刻（{last_sync}）より前です")

            # ファイル形式に応じた処理
            content = None
            if params["file_type"] in ["csv", "tsv"]:
                # CSV/TSVの場合はSQLクエリを実行してデータを取得
                main_query = load_sql_config(f"{params['execute_query']}.sql")
                file_setting = json.loads(params.get("file_setting", "{}"))

                # ストリーミングでデータを取得して処理
                content_lines = []
                is_first_batch = True

                for batch, column_names in self.execute_query_streaming(
                    db_connector,
                    main_query,
                    {
                        "sync_datetime": last_sync,
                        "diff_base_timestamp": diff_base,
                    },
                    int(params.get("batch_size", "1000")),
                ):
                    # SQLAlchemyのRowオブジェクトを辞書型に変換
                    converted_batch = [dict(row._mapping) for row in batch]

                    # 現在のfile_settingをコピー
                    current_file_setting = file_setting.copy()

                    # 最初のバッチ以外ではヘッダーを出力しない
                    if not is_first_batch:
                        current_file_setting["header"] = False

                    # バッチデータをCSV/TSVに変換
                    content_lines.append(
                        self.convert_to_csv_tsv(converted_batch, current_file_setting, params["file_type"], column_names)
                    )

                    # 2回目以降のバッチにはヘッダーを出力しない
                    is_first_batch = False

                # 全てのバッチを結合
                content = "\n".join(content_lines)

            elif params["file_type"] == "xml":
                if "tmp_csv_file" in params:
                    converter = DB2XMLConverter(
                        self.jobnet_id,
                        params["execute_query"],
                        db_connector,
                        last_sync_timestamp=last_sync,  # 前回同期タイムスタンプを設定
                        diff_base_timestamp=diff_base,
                        split_num=params.get("split_num"),
                        tmp_csv_file=params["tmp_csv_file"],
                        parent_column=params["parent_column"]
                    )
                else:
                    converter = DB2XMLConverter(
                        self.jobnet_id,
                        params["execute_query"],
                        db_connector,
                        last_sync_timestamp=last_sync,  # 前回同期タイムスタンプを設定
                        diff_base_timestamp=diff_base,
                        split_num=params.get("split_num")
                    )
                content = converter.execute_conversion(params["output_file_dir"])
            elif params["file_type"] == "fixed":
                converter = DB2FixedConverter(
                    self.jobnet_id,
                    params["execute_query"],
                    db_connector,
                    last_sync_timestamp=last_sync,
                    diff_base_timestamp=diff_base,
                )
                content = converter.execute_conversion(params["output_file_dir"])

            # ファイル形式に応じたバイナリ変換
            if params["file_type"] in ["csv", "tsv", "xml"]:
                # UTF-8エンコード
                binary_content = content.encode("utf-8")
            elif params["file_type"] == "fixed":
                # 固定長は既にバイナリ
                binary_content = content
            else:
                raise ValueError(f"不正なファイル形式: {params['file_type']}")

            # S3へのアップロード
            output_path = os.path.join(params["output_file_dir"], params["file_name"])
            self.upload_to_s3(binary_content, output_path)

            # タイムスタンプ更新
            # Redmine 7628 XML作成並列化　タイムスタンプをdb_to_fileで更新したくないときのための分岐追加
            if params.get("sync_update_skip_flg") != "1":
                self.update_sync_timestamp(db_connector2, diff_base)

            # 終了処理
            self.logger.info(
                Constants.LOG_INFO_END,
                (params["file_name"],),
            )

        except Exception as e:
            # 例外処理
            self.logger.error(Constants.LOG_ERROR_EXCEPTION, (str(e),))
            self.logger.error(
                Constants.LOG_ERROR_JOB,
                (params["file_name"],),
            )
            raise

        finally:
            if db_connector:
                db_connector.close()
            if db_connector2:
                db_connector2.close()


def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # パラメータの取得
        params = get_job_params()

        # 必須パラメータのチェック
        required_params = [
            "secret_name",  # Secrets Managerシークレット名
            "execute_query",  # 実行するSQLクエリ_ID/ETL処理_ID
            "output_file_dir",  # アウトプットファイルディレクトリ
            "file_name",  # ファイル名
            "file_type",  # ファイル形式
            "jobnet_id",  # ジョブネットID
            "diff_base_timestamp_query",  # 差分基準時刻クエリID
            "file_id",  # 出力ファイル識別子
        ]
        for param in required_params:
            if param not in params:
                raise ValueError(f"Required parameter '{param}' is missing")

        # 任意パラメータのデフォルト値設定
        params.setdefault("batch_size", "1000")  # 一度に取得するレコード数
        params.setdefault("file_setting", "{}")  # CSV/TSVファイル属性

        # ジョブの実行
        job = GlueJobDbToFile(params["jobnet_id"])
        job.execute(params)

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
