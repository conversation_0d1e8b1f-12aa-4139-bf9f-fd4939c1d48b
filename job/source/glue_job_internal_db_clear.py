# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

# 連携基盤内DB削除
# import os
import sys
import traceback

from source.common import initialize_env, get_job_params
from source.glue_logger import G<PERSON><PERSON>ogger
from source.common_util import load_sql_config
from source.db_connector import DbConnector
from typing import Dict, Any


class GlueJobDbDelete:

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)

    def connect_db(self, secret_name: str):
        """
        データベースに接続
        Args:
            secret_name: シークレット名
        Returns:
            DbConnector: DB接続オブジェクト
        """
        try:
            self.db_connector = DbConnector(self.logger, secret_id=secret_name)
            self.db_connector.connect()
        except Exception as e:
            self.logger.error(
                msg_id="E_job_db_to_file_002", msg_values=("DB接続")
            )
            raise e

    def execute_query(
        self,
        db_connector: DbConnector,
        query_id: str,
        truncate_table=None,
        delete_start=None,
        delete_end=None,
    ) -> list:
        """
        クエリを実行してデータを取得
        Args:
            db_connector: DB接続オブジェクト
            query_id: クエリID
        """
        try:
            # メインクエリの取得と実行
            main_query = load_sql_config(f"{query_id}.sql")
            self.logger.info(
                "I_job_db_delete_001",
                msg_values=("test3"),
            )
            # db_connector.exec(main_query)
            if delete_start is None and delete_end is None:
                main_query = main_query.replace(
                    ":table_name", truncate_table
                )
                db_connector.exec(main_query)
            elif delete_start and delete_end is None:
                params = {
                    "stat_date": delete_start,
                }
                db_connector.exec(main_query, values=params)
            elif delete_end and delete_start is None:
                params = {
                    "end_date": delete_end,
                }
                db_connector.exec(main_query, values=params)
            else:
                params = {
                    "stat_date": delete_start,
                    "end_date": delete_end,
                }
                db_connector.exec(main_query, values=params)
        except Exception as e:
            self.logger.error("E_job_db_delete_002", ("クエリ実行"))
            raise e

    def excute_db_delete(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
                - secret_name: Secrets Managerシークレット名
                - query_delete: 実行するSQLクエリ_ID(Delete)
                - truncate_table: TRUNCATE対象テーブル
                - delete_start: 削除開始日
                - delete_end: 削除終了日
                - jobnet_id: ジョブネットID
        """

        try:
            # 2.4.2 開始ログ出力
            self.logger.info(
                "I_job_db_delete_001",
                msg_values=(params["truncate_table"]),
            )

            # 2.4.4.DB接続
            # DB接続
            self.connect_db(params["secret_name"])

            # トランザクション
            self.db_connector.begin()

            # 2.4.5.削除条件判定
            delete_start = None
            delete_end = None
            if "delete_start" in params:
                delete_start = params["delete_start"]
            if "delete_end" in params:
                delete_start = params["delete_end"]
            try:
                if not delete_start and not delete_end:
                    # sql実行(truncate)
                    self.execute_query(
                        self.db_connector, "sql_common_truncate_001", params["truncate_table"]
                    )
                else:
                    # sql実行(delete)
                    self.execute_query(
                        self.db_connector, params["query_delete"], "", delete_start, delete_end
                    )
                # トランザクションのコミット
                self.db_connector.commit()

            except BaseException as e:
                self.db_connector.rollback()
                raise e
            finally:
                self.db_connector.close

            # 2.4.6 終了処理
            self.logger.info("I_job_db_delete_002", msg_values=(params["truncate_table"]))
        except BaseException as e:
            # 2.4.7 例外処理
            self.logger.error(
                "E_job_db_delete_003", msg_values=(params["jobnet_id"])
            )
            self.logger.error("E_job_db_delete_001", msg_values=(params["truncate_table"]))
            raise e


def get_params():
    # 2.4.1 パラメータ取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "secret_name",  # Secrets Managerシークレット名
        "jobnet_id",  # ジョブネットID
    ]
    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    return params


def main():
    """メイン関数"""
    try:
        # 環境変数の初期化
        initialize_env()

        # 2.4.1 パラメータ取得
        params = get_params()

        # ジョブの実行
        job = GlueJobDbDelete(params["jobnet_id"])
        job.excute_db_delete(params)

    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e


if __name__ == "__main__":
    main()