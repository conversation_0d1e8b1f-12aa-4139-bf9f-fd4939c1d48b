# スクリプト先頭
from source.glue_deps_handler import setup_glue_dependencies

# 依存関係のセットアップを実行
setup_glue_dependencies()

import json
import os
import io
import sys
import boto3
import paramiko
from ftplib import FTP
import requests
from io import String<PERSON>
from typing import Dict, Any
from source.common import initialize_env, get_job_params, retry_function, find_s3_prefix, find_s3_file, ExternalConnection as ExCon
from source.glue_logger import GlueLogger


class GlueJobSendFile:
    """ファイル送信ジョブ"""

    # 定数定義
    PROTOCOL_TYPE_HTTP = "HTTP"
    PROTOCOL_TYPE_HTTPS = "HTTPS"
    PROTOCOL_TYPE_FTP = "FTP"
    PROTOCOL_TYPE_SFTP = "SFTP"

    def __init__(self, jobnet_id: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
        """
        self.jobnet_id = jobnet_id
        self.logger = GlueLogger(jobnet_id)
        self.s3_client = boto3.client("s3")
        self.secrets_client = boto3.client("secretsmanager")

        # ExternalConnectionインスタンスの生成
        self.ex_con = ExCon()

    def get_connection_info(self, secret_name: str) -> Dict[str, Any]:
        """
        Secrets Managerから外部システム接続情報を取得
        Args:
            secret_name: シークレット名
        Returns:
            Dict: 外部システム接続情報
        """
        try:
            response = self.secrets_client.get_secret_value(SecretId=secret_name)
            return json.loads(response["SecretString"])
        except Exception:
            self.logger.error("E_job_send_file_002", msg_values=("接続情報取得"))
            raise

    def send_file(
        self,
        connection_info,
        s3_file_data,
        external_system_destination_directory: str,
        file_name: str,
    ):
        """
        ファイル送信
        Args:
            connection_info: 外部システム接続情報
            s3_file_data: s3取得ファイルデータ(byte配列)
            external_system_destination_directory: 外部システム送信先ディレクトリ
            file_name: ファイル名称
        """
        try:
            # 外部システム接続情報からprotocolを取得する
            protocol = connection_info.get("protocol")
            # 取得した接続情報.protocolが「ftp」の場合
            if protocol.upper() == self.PROTOCOL_TYPE_FTP:
                host = connection_info.get("host")
                username = connection_info.get("username")
                password = connection_info.get("password")
                remotePath = external_system_destination_directory + file_name

                if host and username and password and remotePath:
                    with FTP(host) as ftp:
                        self.ex_con.ftp_login(ftp, username, password)
                        # ファイルをアップロード
                        self.ex_con.ftp_storbinary(ftp, f"STOR {remotePath}", s3_file_data['Body'])
                        ftp.quit()
                else:
                    raise ValueError(
                        f"There were no value: host or username or password or external_system_destination_directory "
                    )

            # 取得した接続情報.protocolが「sftp」の場合
            elif protocol.upper() == self.PROTOCOL_TYPE_SFTP:
                host = connection_info.get("host")
                username = connection_info.get("username")
                password = connection_info.get("password", "")
                private_key = connection_info.get("private_key", "")

                if host and username and (password or private_key) and external_system_destination_directory:

                    # 接続の確立（認証情報を提供）
                    transport = paramiko.Transport((host, 22))
                    if password:
                        self.ex_con.transport_connect(transport, username, password=password)
                    else:
                        private_key = private_key.replace('\\\\n', '\n')
                        pkey = paramiko.RSAKey.from_private_key(StringIO(private_key))
                        self.ex_con.transport_connect(transport, username, pkey=pkey)

                    # SFTPセッションを開く
                    sftp = paramiko.SFTPClient.from_transport(transport)

                    # ファイルをアップロード
                    output_full_path = external_system_destination_directory + file_name
                    self.ex_con.sftp_putfo(sftp, s3_file_data['Body'], output_full_path)

                    if "sftp" in locals():
                        sftp.close()
                    transport.close()
                else:
                    raise ValueError(
                        f"There were no value: host or username or password or private_key or external_system_destination_directory "
                    )

            # 取得した接続情報.protocolが「https」または「http」の場合
            elif (
                protocol.upper() == self.PROTOCOL_TYPE_HTTPS
                or protocol.upper() == self.PROTOCOL_TYPE_HTTP
            ):
                site_url = connection_info.get("site_url")
                username = connection_info.get("username")
                password = connection_info.get("password", "")
                # private_key = connection_info.get("private_key", "")

                if site_url and username and password:
                    # ファイルとしてアップロードするための辞書を作成
                    files = {"file": (file_name, s3_file_data)}
                    requests.post(site_url, files=files)
                else:
                    raise ValueError(
                        f"There were no value: site_url or username or password or external_system_destination_directory "
                    )
            else:
                raise ValueError(f"There were no protocol: '{protocol}' cases")

        except BaseException as e:
            method_name = f"{connection_info.get('protocol')}".upper() + "ファイル送信"
            self.logger.error(
                "E_job_send_file_002",
                msg_values=(method_name),
            )
            raise e

    def get_s3_file(self, s3_full_path: str):
        """
        S3ファイル取得
        Args:
            s3_full_path: S3フルパス
        Returns:
            str: ファイル内容
        """
        try:
            def _get():
                return self.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=s3_full_path,
                )

            method_name = "S3ファイル取得"
            return retry_function(
                _get,
                self.logger,
                method_name,
                "I_job_send_file_003",
                "E_job_send_file_002",
                retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
                retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
            )
        except Exception as e:
            self.logger.error(
                "E_job_send_file_002",
                msg_values=("S3ファイル取得"),
            )
            raise e

    def backup_input_file(
        self, s3_storage_file_full_path: str, backup_file_dir: str, file_name: str
    ):
        """
        インプットファイルバックアップ
        Args:
            s3_storage_file_full_path: S3ストレージファイルパス
            backup_file_dir: バックアップファイルディレクトリ
            file_name: 連携元のファイルパス[ファイル名]
        """

        def _copy():
            bucket_name = os.environ.get("S3_BUCKET_NAME")
            self.s3_client.copy_object(
                Bucket=bucket_name,
                Key=backup_file_dir + file_name,
                CopySource={"Bucket": bucket_name, "Key": s3_storage_file_full_path},
            )

        method_name = "インプットファイルバックアップ"
        retry_function(
            _copy,
            self.logger,
            method_name,
            "I_job_send_file_003",
            "E_job_send_file_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def delete_input_file(self, s3_full_path: str):
        """
        インプットファイル削除
        Args:
            s3_full_path: S3フルパス
        """

        def _delete():
            self.s3_client.delete_object(
                Bucket=os.environ.get("S3_BUCKET_NAME"), Key=s3_full_path
            )

        method_name = "インプットファイル削除"
        retry_function(
            _delete,
            self.logger,
            method_name,
            "I_job_send_file_003",
            "E_job_send_file_002",
            retry_limit=int(os.environ.get("S3_RETRY_LIMIT")),
            retry_interval=int(os.environ.get("S3_RETRY_INTERVAL")),
        )

    def execute(self, params: Dict[str, Any]):
        """
        メイン処理
        Args:
            params: 実行パラメータ
                - s3_full_path: S3フルパス
                - secret_name: 外部システムのSecrets Managerシークレット名
                - external_system_destination_directory: 外部システム送信先ディレクトリ
                - external_system_transmission_file_name: 外部システム送信IFファイル名称
                - backup_flag: バックアップフラグ
                - backup_s3_directory: バックアップ用S3ディレクトリ
                - jobnet_id: ジョブネットID
        """
        try:
            self.params = params
            s3_full_path = params["s3_full_path"]
            secret_name = params["secret_name"]
            external_system_destination_directory = params[
                "external_system_destination_directory"
            ]
            file_name = params["external_system_transmission_file_name"]
            backup_flag = params["backup_flag"]
            backup_s3_directory = params["backup_s3_directory"]
            dir_name_origin = os.path.dirname(s3_full_path)
            # 入力のfile_dirにタイムスタンプがついてない場合、s3上一番古いタイムスタンプをついて変数に入れ替える 
            dir_name = find_s3_prefix(dir_name_origin, os.environ["S3_BUCKET_NAME"])[0]
            base_name = os.path.basename(s3_full_path)
            s3_full_path = os.path.join(dir_name, base_name)
            if "*" in base_name:
                s3_full_path = os.path.join(
                    dir_name, find_s3_file(
                        dir_name,
                        base_name,
                        os.environ["S3_BUCKET_NAME"]
                    )
                )

            # 開始ログ出力
            self.logger.info(
                "I_job_send_file_001",
                msg_values=(file_name),
            )

            # 外部システム接続情報取得
            connection_info = self.get_connection_info(secret_name)

            # 2.4.2 boto3ライブラリを用いてS3からファイルを取得
            s3_file_data = self.get_s3_file(s3_full_path)

            # 2.4.3 ファイル送信
            self.send_file(
                connection_info,
                s3_file_data,
                external_system_destination_directory,
                file_name,
            )

            # 2.4.4 インプットファイルのバックアップ
            if backup_flag:
                self.backup_input_file(s3_full_path, backup_s3_directory, os.path.basename(s3_full_path))

            # インプットファイル削除
            self.delete_input_file(s3_full_path)

            # 2.4.5 終了処理
            self.logger.info(
                "I_job_send_file_002",
                msg_values=(file_name),
            )
        except Exception as e:
            # 2.4.6 例外処理
            self.logger.error(
                "E_job_send_file_003",
                msg_values=(str(e)),
            )
            self.logger.error(
                "E_job_send_file_001",
                msg_values=(file_name),
            )
            raise e


def get_params():
    # 2.4.1 パラメータ取得
    params = get_job_params()

    # 必須パラメータのチェック
    required_params = [
        "s3_full_path",  # S3フルパス
        "secret_name",  # 外部システムのSecrets Managerシークレット名
        "external_system_destination_directory",  # 外部システム送信先ディレクトリ
        "external_system_transmission_file_name",  # 外部システム送信IFファイル名称
        "backup_s3_directory",  # バックアップ用S3ディレクトリ
        "jobnet_id",  # ジョブネットID
    ]

    for param in required_params:
        if param not in params:
            raise ValueError(f"Required parameter '{param}' is missing")

    # 任意パラメータのデフォルト値設定
    params.setdefault("backup_flag", False)  # バックアップフラグ

    return params


# メイン処理
def main():
    """メイン関数"""
    # 環境変数の初期化
    initialize_env()

    # 2.4.1 パラメータ取得
    params = get_params()

    # ジョブの実行
    job = GlueJobSendFile(params["jobnet_id"])
    job.execute(params)


if __name__ == "__main__":
    main()
