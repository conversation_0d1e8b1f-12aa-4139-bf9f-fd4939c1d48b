CREATE OR REPLACE FUNCTION convert_full_to_half_katakana(input_text TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
    c CHAR;
    i INT;
BEGIN
    FOR i IN 1..length(input_text) LOOP
    c := substring(input_text FROM i FOR 1);
        CASE c
            WHEN 'ア' THEN result := result || 'ｱ';
            WHEN 'イ' THEN result := result || 'ｲ';
            WHEN 'ウ' THEN result := result || 'ｳ';
            WHEN 'エ' THEN result := result || 'ｴ';
            WHEN 'オ' THEN result := result || 'ｵ';
            WHEN 'カ' THEN result := result || 'ｶ';
            WHEN 'キ' THEN result := result || 'ｷ';
            WHEN 'ク' THEN result := result || 'ｸ';
            WHEN 'ケ' THEN result := result || 'ｹ';
            WHEN 'コ' THEN result := result || 'ｺ';
            WHEN 'サ' THEN result := result || 'ｻ';
            WHEN 'シ' THEN result := result || 'ｼ';
            WHEN 'ス' THEN result := result || 'ｽ';
            WHEN 'セ' THEN result := result || 'ｾ';
            WHEN 'ソ' THEN result := result || 'ｿ';
            WHEN 'タ' THEN result := result || 'ﾀ';
            WHEN 'チ' THEN result := result || 'ﾁ';
            WHEN 'ツ' THEN result := result || 'ﾂ';
            WHEN 'テ' THEN result := result || 'ﾃ';
            WHEN 'ト' THEN result := result || 'ﾄ';
            WHEN 'ナ' THEN result := result || 'ﾅ';
            WHEN 'ニ' THEN result := result || 'ﾆ';
            WHEN 'ヌ' THEN result := result || 'ﾇ';
            WHEN 'ネ' THEN result := result || 'ﾈ';
            WHEN 'ノ' THEN result := result || 'ﾉ';
            WHEN 'ハ' THEN result := result || 'ﾊ';
            WHEN 'ヒ' THEN result := result || 'ﾋ';
            WHEN 'フ' THEN result := result || 'ﾌ';
            WHEN 'ヘ' THEN result := result || 'ﾍ';
            WHEN 'ホ' THEN result := result || 'ﾎ';
            WHEN 'マ' THEN result := result || 'ﾏ';
            WHEN 'ミ' THEN result := result || 'ﾐ';
            WHEN 'ム' THEN result := result || 'ﾑ';
            WHEN 'メ' THEN result := result || 'ﾒ';
            WHEN 'モ' THEN result := result || 'ﾓ';
            WHEN 'ヤ' THEN result := result || 'ﾔ';
            WHEN 'ユ' THEN result := result || 'ﾕ';
            WHEN 'ヨ' THEN result := result || 'ﾖ';
            WHEN 'ラ' THEN result := result || 'ﾗ';
            WHEN 'リ' THEN result := result || 'ﾘ';
            WHEN 'ル' THEN result := result || 'ﾙ';
            WHEN 'レ' THEN result := result || 'ﾚ';
            WHEN 'ロ' THEN result := result || 'ﾛ';
            WHEN 'ワ' THEN result := result || 'ﾜ';
            WHEN 'ヲ' THEN result := result || 'ｦ';
            WHEN 'ン' THEN result := result || 'ﾝ';
            WHEN 'ガ' THEN result := result || 'ｶﾞ';
            WHEN 'ギ' THEN result := result || 'ｷﾞ';
            WHEN 'グ' THEN result := result || 'ｸﾞ';
            WHEN 'ゲ' THEN result := result || 'ｹﾞ';
            WHEN 'ゴ' THEN result := result || 'ｺﾞ';
            WHEN 'ザ' THEN result := result || 'ｻﾞ';
            WHEN 'ジ' THEN result := result || 'ｼﾞ';
            WHEN 'ズ' THEN result := result || 'ｽﾞ';
            WHEN 'ゼ' THEN result := result || 'ｾﾞ';
            WHEN 'ゾ' THEN result := result || 'ｿﾞ';
            WHEN 'ダ' THEN result := result || 'ﾀﾞ';
            WHEN 'ヂ' THEN result := result || 'ﾁﾞ';
            WHEN 'ヅ' THEN result := result || 'ﾂﾞ';
            WHEN 'デ' THEN result := result || 'ﾃﾞ';
            WHEN 'ド' THEN result := result || 'ﾄﾞ';
            WHEN 'バ' THEN result := result || 'ﾊﾞ';
            WHEN 'ビ' THEN result := result || 'ﾋﾞ';
            WHEN 'ブ' THEN result := result || 'ﾌﾞ';
            WHEN 'ベ' THEN result := result || 'ﾍﾞ';
            WHEN 'ボ' THEN result := result || 'ﾎﾞ';
            WHEN 'パ' THEN result := result || 'ﾊﾟ';
            WHEN 'ピ' THEN result := result || 'ﾋﾟ';
            WHEN 'プ' THEN result := result || 'ﾌﾟ';
            WHEN 'ペ' THEN result := result || 'ﾍﾟ';
            WHEN 'ポ' THEN result := result || 'ﾎﾟ';
            WHEN 'ヴ' THEN result := result || 'ｳﾞ';
            WHEN 'ァ' THEN result := result || 'ｧ';
            WHEN 'ィ' THEN result := result || 'ｨ';
            WHEN 'ゥ' THEN result := result || 'ｩ';
            WHEN 'ェ' THEN result := result || 'ｪ';
            WHEN 'ォ' THEN result := result || 'ｫ';
            WHEN 'ャ' THEN result := result || 'ｬ';
            WHEN 'ュ' THEN result := result || 'ｭ';
            WHEN 'ョ' THEN result := result || 'ｮ';
            WHEN 'ッ' THEN result := result || 'ｯ';
            WHEN 'ー' THEN result := result || 'ｰ';
            WHEN '「' THEN result := result || '｢';
            WHEN '」' THEN result := result || '｣';
            WHEN '、' THEN result := result || '､';
            WHEN '。' THEN result := result || '｡';
            WHEN '・' THEN result := result || '･';
            WHEN '゛' THEN result := result || 'ﾞ';
            WHEN '゜' THEN result := result || 'ﾟ';
            ELSE
                result := result || c;
        END CASE;
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;
