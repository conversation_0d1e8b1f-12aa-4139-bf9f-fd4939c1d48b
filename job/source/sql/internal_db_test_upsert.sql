
        INSERT INTO db_import_test(
        test_key,
        test_vc,
        test_num,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
        )
        VALUES(
        NULL,
        'vc1',
        11,
        'inserted_user',
        CURRENT_TIMESTAMP,
        'inserted_user',
        CURRENT_TIMESTAMP,
        1
        )

        ON CONFLICT (test_key)
        DO
        UPDATE SET
        test_vc = 'updated_vc',
        test_num = '111',
        d_updated_user = 'updated_user',
        d_updated_datetime= CURRENT_TIMESTAMP,
        d_version=db_import_test.d_version / 0
        ;


        