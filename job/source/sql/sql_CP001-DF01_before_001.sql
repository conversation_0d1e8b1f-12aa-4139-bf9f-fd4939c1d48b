-- sync_timestampのtempに基準日時を入れる
UPDATE sync_timestamp 
SET sync_datetime_temp = NOW() 
WHERE job_schedule_id = 'JN_CP001-DF01_001'
  AND file_name 
  IN('txCampaignPricebooks.xml'
  , 'txCampaignPromotions.xml'
  , 'txDynamicCustomerGroups.xml'
  , 'txStaticCustomerGroups.xml');
TRUNCATE TABLE wk_CP001_DF01_updated_campaign_promotion_list;
TRUNCATE TABLE wk_CP001_DF01_campaign_export;
TRUNCATE TABLE wk_CP001_DF01_product_campaign_flag_list;
TRUNCATE TABLE wk_cp001_df01_campaign_main;
TRUNCATE TABLE wk_cp001_df01_promotion_main;
TRUNCATE TABLE wk_cp001_df01_campaign_promotion_assignment_main;
TRUNCATE TABLE wk_cp001_df01_pricebook_main;
TRUNCATE TABLE wk_cp001_df01_static_customer_groups_campaign_main;
TRUNCATE TABLE wk_cp001_df01_static_customer_groups_main;
TRUNCATE TABLE wk_cp001_df01_dynamic_customer_groups_main;
--更新されたプロモーションの一覧
WITH updated_campaign_promotion_list AS (
  SELECT
    campaign_instructions_code
  FROM campaign_instructions --キャンペーン設定
  WHERE
    d_updated_datetime > :sync_datetime
  UNION ALL 
  SELECT
    campaign_instructions_code
  FROM campaign_promotion --キャンペーンプロモーション
  WHERE
    d_updated_datetime > :sync_datetime
  UNION ALL 
  SELECT
    campaign_instructions_code
  FROM campaign_order --キャンペーン設定条件
  WHERE
    d_updated_datetime > :sync_datetime
  UNION ALL 
  SELECT
    campaign_instructions_code
  FROM campaign_order_group --キャンペーン設定条件グループ
  WHERE
    d_updated_datetime > :sync_datetime
  UNION ALL
  SELECT
    campaign_instructions_code
  FROM campaign_customer --顧客グループ
  WHERE
    d_updated_datetime > :sync_datetime
  --最後にUNIONで重複削除
  UNION
  SELECT
    campaign_instructions_code
  FROM campaign_instructions_commodity --キャンペーン設定商品
  WHERE
    d_updated_datetime > :sync_datetime
)
-- 更新されたプロモーションがEC向けもしくは全体向けか確認
,target_promotion_work AS (
  SELECT
    ci.campaign_instructions_code
    ,MAX(CASE WHEN co.joken_kind2 = '304' AND co.joken = '21' THEN 1 ELSE 0 END) AS ec_flg --1ならEC向け
    ,MIN(CASE WHEN co.joken_kind2 = '304' THEN 0 ELSE 1 END) AS all_flg --1なら全体向け
  FROM updated_campaign_promotion_list AS ucpl
  INNER JOIN campaign_instructions AS ci ON --キャンペーン設定
    ucpl.campaign_instructions_code = ci.campaign_instructions_code
  INNER JOIN campaign_order AS co ON --キャンペーン設定条件
    ci.campaign_instructions_code = co.campaign_instructions_code
  WHERE ci.campaign_applied_scope = '01' --単商品明細単位
  GROUP BY ci.campaign_instructions_code
)
-- ↑からEC向けもしくは全体向けのプロモーションを取得
,target_promotion_work2 AS (
  SELECT
    campaign_instructions_code
  FROM target_promotion_work
  WHERE ec_flg = 1
    OR all_flg = 1
)
-- ↑のうち条件種別が連携対象のもの
,target_promotion_ec AS (
  SELECT 
    tpw.campaign_instructions_code
  FROM target_promotion_work2 AS tpw
  INNER JOIN campaign_order AS co ON
    tpw.campaign_instructions_code = co.campaign_instructions_code
  WHERE
      co.joken_kind1 = '1' --会員
    OR 
      co.joken_kind1 = '2' --購入商品
    OR 
      (co.joken_kind1 = '3' AND co.joken_kind2 = '302') --受注かつ初回購入
)
--プロモーションで連携するキャンペーンコードリスト
--キャンペーン価格、静的顧客、動的顧客の連携フラグ付き
INSERT INTO wk_CP001_DF01_updated_campaign_promotion_list (campaign_instructions_code, campaign_price_flg, static_customer_flg, dynamic_customer_flg)
SELECT 
  tpe.campaign_instructions_code
  ,CASE 
    WHEN cp.campaign_instructions_code IS NOT NULL THEN '1'
    ELSE '0'
  END
  ,CASE
    WHEN MAX(CASE WHEN co.joken_kind1 = '1' AND co.joken_kind2 = '102' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
  ,CASE 
    WHEN MAX(CASE WHEN co.joken_kind1 = '1' AND co.joken_kind2 = '101' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END
FROM target_promotion_ec AS tpe
--キャンペーン価格連携フラグの判定
LEFT OUTER JOIN campaign_promotion AS cp ON
tpe.campaign_instructions_code = cp.campaign_instructions_code
  AND cp.promotion_type = '03'
  AND cp.discount_retail_price IS NOT NULL
--静的顧客、動的顧客の連携対象か判定
INNER JOIN campaign_order AS co ON 
tpe.campaign_instructions_code = co.campaign_instructions_code
GROUP BY tpe.campaign_instructions_code, cp.campaign_instructions_code;
-- プロモーション出力対象からキャンペーン出力対象(キャンペーン設定またはキャンペーン設定条件が更新されているもの)を抽出
WITH target_campaign_ec AS (
  SELECT
    ucpl.campaign_instructions_code
    ,co.joken_kind2
    ,co.joken
  FROM
    wk_CP001_DF01_updated_campaign_promotion_list AS ucpl --キャンペーン設定
  INNER JOIN campaign_instructions AS ci ON
    ucpl.campaign_instructions_code = ci.campaign_instructions_code
  INNER JOIN campaign_order AS co ON --キャンペーン設定条件
    ci.campaign_instructions_code = co.campaign_instructions_code
  WHERE
      (ci.d_updated_datetime > :sync_datetime)
    OR  
      (co.d_updated_datetime > :sync_datetime)
)
-- 連携対象のキャンペーンをexportテーブルに登録
-- キャンペーンの種別（会員や商品など）がマッピングの条件となっているため、FLGで条件分岐できるようにする
INSERT INTO
  wk_CP001_DF01_campaign_export (campaign_instructions_code,campaign_customer_flg,campaign_item_flg,campaign_order_flg)
SELECT
  campaign_instructions_code
  ,CAST(MAX(campaign_customer_flg) AS VARCHAR)
  ,CAST(MAX(campaign_item_flg) AS VARCHAR)
  ,CAST(MAX(campaign_order_flg) AS VARCHAR)
FROM (
  SELECT
    tce.campaign_instructions_code
    ,CASE WHEN co.joken_kind1 = '1' THEN 1 ELSE 0 END AS campaign_customer_flg
    ,CASE WHEN co.joken_kind1 = '2' THEN 1 ELSE 0 END AS campaign_item_flg
    ,CASE WHEN co.joken_kind1 = '3' and co.joken_kind2 = '302' THEN 1 ELSE 0 END AS campaign_order_flg
  FROM
    target_campaign_ec AS tce
  INNER JOIN campaign_order AS co ON
    tce.campaign_instructions_code = co.campaign_instructions_code
) AS tcew
GROUP BY campaign_instructions_code;
-- キャンペーンのメインクエリ
INSERT INTO wk_cp001_df01_campaign_main (campaign_instructions_code, campaign_instructions_name, enabled_flag, campaign_start_date, campaign_end_date, customer_groups_group_id, campaign_applied_scope)
SELECT
  ce.campaign_instructions_code
  ,ci.campaign_instructions_name
  ,CASE
    WHEN ci.delete_flg = 0 THEN 'true'
    ELSE 'false'
  END
  ,ci.campaign_start_date
  ,ci.campaign_end_date
  ,CASE
    WHEN ce.campaign_customer_flg = '1' THEN 'txGroupId' || ce.campaign_instructions_code
    ELSE 'Everyone'
  END
  ,ci.campaign_applied_scope
FROM wk_cp001_df01_campaign_export AS ce
INNER JOIN campaign_instructions AS ci ON
ce.campaign_instructions_code = ci.campaign_instructions_code;
-- キャンペーン対象商品が優待商品かどうかの判定(単商品の場合)
-- 単商品の場合の商品コードはcampaign_order.jokenに設定される
INSERT INTO wk_CP001_DF01_product_campaign_flag_list (campaign_instructions_code, preferential_product_flg, single_product_flg)
SELECT DISTINCT ON (co.campaign_instructions_code)
  co.campaign_instructions_code
  ,CASE
    WHEN pl.preferential_product_flg = '1' THEN '1'
    ELSE '0'
  END AS preferential_product_flg
  ,'1'
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_order AS co ON
  ucpl.campaign_instructions_code = co.campaign_instructions_code
LEFT OUTER JOIN mdm.product_linkage AS pl ON
  co.joken = pl.mail_order_product_cd
WHERE co.joken_kind1 = '2' AND co.joken_kind2 = '201';
-- キャンペーン対象商品が優待商品かどうかの判定(複数商品の場合)
-- 複数商品の場合の商品コードはcampaign_instructions_commodity.commodity_codeに設定される
INSERT INTO wk_CP001_DF01_product_campaign_flag_list (campaign_instructions_code, preferential_product_flg, single_product_flg)
SELECT
  co.campaign_instructions_code
  ,CASE
    WHEN MAX(CASE WHEN pl.preferential_product_flg = '1' THEN 1 ELSE 0 END) = 1 THEN '1'
    ELSE '0'
  END AS preferential_product_flg
  ,'0'
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_order AS co ON
  ucpl.campaign_instructions_code = co.campaign_instructions_code
INNER JOIN campaign_instructions_commodity AS cic ON
  co.joken_kind1 = '2'
  AND co.joken_kind2 = '205'
  AND co.campaign_instructions_code = cic.campaign_instructions_code
LEFT OUTER JOIN mdm.product_linkage AS pl ON
cic.commodity_code = pl.mail_order_product_cd
GROUP BY co.campaign_instructions_code;
-- プロモーションのメインクエリ
WITH first_order_only_sub_flg_list AS (
  SELECT
    ucpl.campaign_instructions_code
    ,MAX(CASE WHEN co.joken_kind1 = '3' AND co.joken_kind2 = '302' THEN 1 ELSE 0 END) AS txIsFirstOrderOnlySubscription
  FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
  INNER JOIN campaign_order AS co ON
  ucpl.campaign_instructions_code = co.campaign_instructions_code
  GROUP BY ucpl.campaign_instructions_code
)
INSERT INTO wk_cp001_df01_promotion_main (campaign_instructions_code, promotion_id, name, promotion_type, present_product_code, first_order_only, discount_rate, oneshot_order_limit, campaign_quantity_limit, campaign_end_date, preferential_product_flg, baitai_code, campaign_priority, discount_amount, discount_retail_price, single_product_flg, discount_amount_amount, discount_rate_amount, pricebook_id)
SELECT
  ci.campaign_instructions_code
  ,ci.campaign_instructions_code || '_' || cp.promotion_no
  ,ci.campaign_instructions_name
  ,cp.promotion_type
  ,CASE 
    WHEN cp.promotion_type = '01' THEN cp.commodity_code
    ELSE NULL
  END
  ,CASE 
    WHEN fo.txIsFirstOrderOnlySubscription = 1 THEN 'true'
    ELSE 'false'
  END
  ,cp.discount_rate
  ,ci.oneshot_order_limit
  ,ci.campaign_quantity_limit
  ,ci.campaign_end_date
  ,CASE
    WHEN pcfl.preferential_product_flg = '1' THEN 'true'
    ELSE 'false'
  END
  ,ci.baitai_code
  ,ci.campaign_priority
  ,cp.discount_amount
  ,cp.discount_retail_price
  ,pcfl.single_product_flg
  ,CAST(COALESCE(cp.discount_amount, 0) AS VARCHAR) || '.0'
  ,CAST(COALESCE(cp.discount_rate, 0) AS VARCHAR) || '.0'
  ,'txCampaignPricebookId' || ci.campaign_instructions_code
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_instructions AS ci ON
ucpl.campaign_instructions_code = ci.campaign_instructions_code
INNER JOIN campaign_promotion AS cp ON
ci.campaign_instructions_code = cp.campaign_instructions_code
INNER JOIN first_order_only_sub_flg_list AS fo ON
cp.campaign_instructions_code = fo.campaign_instructions_code
LEFT OUTER JOIN wk_CP001_DF01_product_campaign_flag_list AS pcfl ON
ci.campaign_instructions_code = pcfl.campaign_instructions_code;
--キャンペーンプロモーションのサブクエリ(キャンペーン設定条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT
        co.campaign_instructions_code
        ,jsonb_build_object(
          ''campaign_instructions_code''
          ,co.campaign_instructions_code
          ,''campaign_group_no''
          ,co.campaign_group_no
          ,''joken_type''
          ,co.joken_type
          ,''campaign_joken_no''
          ,co.campaign_joken_no
          ,''joken_kind1''
          ,co.joken_kind1
          ,''joken_kind2''
          ,co.joken_kind2
          ,''joken''
          ,co.joken
        ) AS campaign_condition_group
      FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
      INNER JOIN campaign_order AS co ON
      ucpl.campaign_instructions_code = co.campaign_instructions_code;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pro_order_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--キャンペーンプロモーションのサブクエリ(値引きプロモーションルール(単商品))
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT 
        campaign_instructions_code
        ,joken
      FROM campaign_order
      WHERE joken_kind1 = ''2'' 
        AND joken_kind2 = ''201'';',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pro_single_disc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--キャンペーンプロモーションのサブクエリ(値引きプロモーションルール(複数商品))
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT 
        campaign_instructions_code
        ,commodity_code
      FROM campaign_instructions_commodity;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pro_multi_disc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--プロモーション割り当て情報のメインクエリ
INSERT INTO wk_cp001_df01_campaign_promotion_assignment_main (promotion_id, campaign_instructions_code)
SELECT 
  ucpl.campaign_instructions_code || '_' || cp.promotion_no AS promotion_id
  ,ucpl.campaign_instructions_code
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_promotion AS cp ON
ucpl.campaign_instructions_code = cp.campaign_instructions_code;
--価格表のメインクエリ
INSERT INTO wk_cp001_df01_pricebook_main (campaign_instructions_code, pricebook_id, campaign_instructions_name, campaign_start_date, campaign_end_date, single_product_flg)
SELECT DISTINCT ON (ucpl.campaign_instructions_code)
  ucpl.campaign_instructions_code
  ,'txCampaignPricebookId' || cp.campaign_instructions_code
  ,ci.campaign_instructions_name
  ,ci.campaign_start_date
  ,ci.campaign_end_date
  ,pcfl.single_product_flg
FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
INNER JOIN campaign_instructions AS ci ON
ucpl.campaign_price_flg = '1'
  AND ucpl.campaign_instructions_code = ci.campaign_instructions_code
INNER JOIN campaign_promotion AS cp ON 
ci.campaign_instructions_code = cp.campaign_instructions_code
LEFT OUTER JOIN wk_cp001_df01_product_campaign_flag_list AS pcfl ON
cp.campaign_instructions_code = pcfl.campaign_instructions_code
WHERE cp.promotion_type = '03'
  AND cp.discount_retail_price IS NOT NULL;
--価格表のサブクエリ(単商品)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT ON (ucpl.campaign_instructions_code, co.joken)
      ucpl.campaign_instructions_code,
      co.joken AS commodity_code,
      cp.discount_retail_price
    FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
    INNER JOIN campaign_promotion AS cp ON 
    ucpl.campaign_price_flg = ''1''
      AND ucpl.campaign_instructions_code = cp.campaign_instructions_code
    INNER JOIN campaign_order AS co ON 
    cp.campaign_instructions_code = co.campaign_instructions_code
    WHERE cp.promotion_type = ''03''
      AND cp.discount_retail_price IS NOT NULL
      AND co.joken_kind1 = ''2'' 
      AND co.joken_kind2 = ''201''
    ORDER BY ucpl.campaign_instructions_code, co.joken;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pri_single_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--価格表のサブクエリ(複数商品)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT
      ucpl.campaign_instructions_code,
      cic.commodity_code,
      cp.discount_retail_price
    FROM wk_CP001_DF01_updated_campaign_promotion_list AS ucpl
    INNER JOIN campaign_promotion AS cp ON 
    ucpl.campaign_price_flg = ''1''
      AND ucpl.campaign_instructions_code = cp.campaign_instructions_code
    INNER JOIN campaign_instructions_commodity AS cic ON
    cp.campaign_instructions_code = cic.campaign_instructions_code
    WHERE cp.promotion_type = ''03''
      AND cp.discount_retail_price IS NOT NULL;',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :pri_multi_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--静的顧客のキャンペーン基本情報メインクエリ
INSERT INTO wk_cp001_df01_static_customer_groups_campaign_main (group_id, campaign_instructions_name)
SELECT 
  'txGroupId' || uecl.campaign_instructions_code
  ,ci.campaign_instructions_name
FROM wk_CP001_DF01_updated_campaign_promotion_list AS uecl 
INNER JOIN campaign_instructions AS ci ON
uecl.static_customer_flg = '1'
  AND uecl.campaign_instructions_code = ci.campaign_instructions_code;
--静的顧客の顧客グループ情報メインクエリ
INSERT INTO wk_cp001_df01_static_customer_groups_main (group_id, customer_no)
SELECT 
  'txGroupId' || uecl.campaign_instructions_code
  ,c.customer_no
FROM wk_CP001_DF01_updated_campaign_promotion_list AS uecl 
INNER JOIN campaign_instructions AS ci ON
uecl.static_customer_flg = '1'
  AND uecl.campaign_instructions_code = ci.campaign_instructions_code
INNER JOIN campaign_customer AS cc ON
ci.campaign_instructions_code = cc.campaign_instructions_code
  AND cc.joken_type = '1'
INNER JOIN customer AS c ON
cc.customer_code = c.customer_code;
--動的顧客のキャンペーン基本情報メインクエリ
INSERT INTO wk_cp001_df01_dynamic_customer_groups_main (campaign_instructions_code, group_id, campaign_instructions_name)
SELECT 
  uecl.campaign_instructions_code
  ,'txGroupId' || uecl.campaign_instructions_code
  ,campaign_instructions_name
FROM wk_CP001_DF01_updated_campaign_promotion_list AS uecl 
INNER JOIN campaign_instructions AS ci ON
uecl.dynamic_customer_flg = '1'
AND uecl.campaign_instructions_code = ci.campaign_instructions_code;
--動的顧客の条件グループ取得クエリ(適用条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT
      cog.campaign_instructions_code
      ,cog.campaign_group_no
    FROM campaign_order_group AS cog
    INNER JOIN campaign_order AS co ON
    cog.campaign_instructions_code = co.campaign_instructions_code
      AND cog.campaign_group_no = co.campaign_group_no
    WHERE co.joken_kind1 = ''1''
      AND co.joken_type = ''1''
      AND co.joken_kind2 = ''101'';',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_grp_inc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--動的顧客の条件取得クエリ(適用条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT 
      campaign_instructions_code,
      campaign_group_no,
      joken
    FROM campaign_order
    WHERE joken_kind1 = ''1''
      AND joken_type = ''1''
      AND joken_kind2 = ''101'';',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_ord_inc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--動的顧客の条件グループ取得クエリ(除外条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT DISTINCT
      cog.campaign_instructions_code
      ,cog.campaign_group_no
    FROM campaign_order_group AS cog
    INNER JOIN campaign_order AS co ON
    cog.campaign_instructions_code = co.campaign_instructions_code
      AND cog.campaign_group_no = co.campaign_group_no
    WHERE co.joken_kind1 = ''1''
      AND co.joken_type = ''2''
      AND co.joken_kind2 = ''101'';',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_grp_exc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
--動的顧客の条件取得クエリ(除外条件)
SELECT * FROM
  aws_s3.query_export_to_s3(
    'SELECT 
      campaign_instructions_code,
      campaign_group_no,
      joken
    FROM campaign_order
    WHERE joken_kind1 = ''1''
      AND joken_type = ''2''
      AND joken_kind2 = ''101'';',
    aws_commons.create_s3_uri(
        :bucket_name,
        -- bucket名
        :dy_ord_exc_object_path,
        -- object名
        'ap-northeast-1' -- region名
    ),
    options := 'format csv, header true, quote ''"'' '
  );
-- ワークテーブルに分割単位を入れる
-- プロモーション
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :promotions_parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT campaign_instructions_code FROM wk_cp001_df01_promotion_main)
)
UPDATE wk_cp001_df01_promotion_main AS pm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE pm.campaign_instructions_code = t.campaign_instructions_code;
-- キャンペーン
UPDATE wk_cp001_df01_campaign_main AS cm
SET split_num = pm.split_num
FROM wk_cp001_df01_promotion_main AS pm
WHERE cm.campaign_instructions_code = pm.campaign_instructions_code;
-- プロモーション割り当て
UPDATE wk_cp001_df01_campaign_promotion_assignment_main AS pam
SET split_num = pm.split_num
FROM wk_cp001_df01_promotion_main AS pm
WHERE pam.campaign_instructions_code = pm.campaign_instructions_code;
-- 価格表
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :pricebooks_parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT campaign_instructions_code FROM wk_cp001_df01_pricebook_main)
)
UPDATE wk_cp001_df01_pricebook_main AS pm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE pm.campaign_instructions_code = t.campaign_instructions_code;
-- 静的顧客(キャンペーン基本情報)
WITH temp_parallel_num AS (
  SELECT
     group_id
    ,MOD(ROW_NUMBER() OVER (ORDER BY group_id) - 1, :static_parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT group_id FROM wk_cp001_df01_static_customer_groups_campaign_main)
)
UPDATE wk_cp001_df01_static_customer_groups_campaign_main AS scgcm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE scgcm.group_id = t.group_id;
-- 静的顧客(キャンペーン顧客グループ情報)
UPDATE wk_cp001_df01_static_customer_groups_main AS scgm
SET split_num = scgcm.split_num
FROM wk_cp001_df01_static_customer_groups_campaign_main AS scgcm
WHERE scgcm.group_id = scgm.group_id;
-- 動的顧客
WITH temp_parallel_num AS (
  SELECT
     campaign_instructions_code
    ,MOD(ROW_NUMBER() OVER (ORDER BY campaign_instructions_code) - 1, :dynamic_parallel_num) + 1 AS parallel_num
  FROM (SELECT DISTINCT campaign_instructions_code FROM wk_cp001_df01_dynamic_customer_groups_main)
)
UPDATE wk_cp001_df01_dynamic_customer_groups_main AS dcgm
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE dcgm.campaign_instructions_code = t.campaign_instructions_code;