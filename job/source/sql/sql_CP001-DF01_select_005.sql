SELECT campaign_instructions_code,
    promotion_no,
    promotion_type,
    shop_code,
    commodity_code,
    commodity_name,
    present_qt,
    discount_rate,
    discount_amount,
    discount_retail_price,
    shipping_charge,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM campaign_promotion
WHERE updated_datetime >= :sync_datetime
    AND updated_datetime <= :diff_base_timestamp
ORDER BY campaign_instructions_code,
    promotion_no;
