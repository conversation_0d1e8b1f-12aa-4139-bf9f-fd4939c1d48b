WITH source_data AS (
    SELECT campaign_instructions_code,
        campaign_instructions_name,
        campaign_type,
        delete_flg,
        campaign_priority,
        campaign_applied_scope,
        campaign_use_limit,
        oneshot_order_limit,
        campaign_quantity_limit,
        campaign_start_date,
        campaign_end_date,
        present_use_flg,
        campaign_customer_flg,
        campaign_combi_limit_flg,
        permanent_campaign_flg,
        baitai_code,
        campaign_description,
        change_user_code,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime
    FROM dlpf.campaign_instructions_work
)
INSERT INTO dlpf.campaign_instructions (
        campaign_instructions_code,
        campaign_instructions_name,
        campaign_type,
        delete_flg,
        campaign_priority,
        campaign_applied_scope,
        campaign_use_limit,
        oneshot_order_limit,
        campaign_quantity_limit,
        campaign_start_date,
        campaign_end_date,
        present_use_flg,
        campaign_customer_flg,
        campaign_combi_limit_flg,
        permanent_campaign_flg,
        baitai_code,
        campaign_description,
        change_user_code,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_CP001-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_CP001-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET campaign_instructions_name = EXCLUDED.campaign_instructions_name,
    campaign_type = EXCLUDED.campaign_type,
    delete_flg = EXCLUDED.delete_flg,
    campaign_priority = EXCLUDED.campaign_priority,
    campaign_applied_scope = EXCLUDED.campaign_applied_scope,
    campaign_use_limit = EXCLUDED.campaign_use_limit,
    oneshot_order_limit = EXCLUDED.oneshot_order_limit,
    campaign_quantity_limit = EXCLUDED.campaign_quantity_limit,
    campaign_start_date = EXCLUDED.campaign_start_date,
    campaign_end_date = EXCLUDED.campaign_end_date,
    present_use_flg = EXCLUDED.present_use_flg,
    campaign_customer_flg = EXCLUDED.campaign_customer_flg,
    campaign_combi_limit_flg = EXCLUDED.campaign_combi_limit_flg,
    permanent_campaign_flg = EXCLUDED.permanent_campaign_flg,
    baitai_code = EXCLUDED.baitai_code,
    campaign_description = EXCLUDED.campaign_description,
    change_user_code = EXCLUDED.change_user_code,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_CP001-DF01_001',
    d_updated_datetime = NOW(),
    d_version = campaign_instructions.d_version + 1;
