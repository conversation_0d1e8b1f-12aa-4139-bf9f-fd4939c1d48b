WITH source_data AS (
    SELECT campaign_instructions_code,
        promotion_no,
        promotion_type,
        shop_code,
        commodity_code,
        commodity_name,
        present_qt,
        discount_rate,
        discount_amount,
        discount_retail_price,
        shipping_charge,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime
    FROM dlpf.campaign_promotion_work
)
INSERT INTO dlpf.campaign_promotion (
        campaign_instructions_code,
        promotion_no,
        promotion_type,
        shop_code,
        commodity_code,
        commodity_name,
        present_qt,
        discount_rate,
        discount_amount,
        discount_retail_price,
        shipping_charge,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_CP001-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_CP001-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (campaign_instructions_code, promotion_no) DO
UPDATE
SET promotion_type = EXCLUDED.promotion_type,
    shop_code = EXCLUDED.shop_code,
    commodity_code = EXCLUDED.commodity_code,
    commodity_name = EXCLUDED.commodity_name,
    present_qt = EXCLUDED.present_qt,
    discount_rate = EXCLUDED.discount_rate,
    discount_amount = EXCLUDED.discount_amount,
    discount_retail_price = EXCLUDED.discount_retail_price,
    shipping_charge = EXCLUDED.shipping_charge,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_CP001-DF01_001',
    d_updated_datetime = NOW(),
    d_version = campaign_promotion.d_version + 1;
