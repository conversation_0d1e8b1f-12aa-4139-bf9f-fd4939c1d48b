SELECT campaign_instructions_code,
    campaign_instructions_name,
    delete_flg,
    campaign_priority,
    campaign_applied_scope,
    campaign_use_limit,
    oneshot_order_limit,
    campaign_quantity_limit,
    campaign_start_date,
    campaign_end_date,
    present_use_flg,
    campaign_customer_flg,
    campaign_combi_limit_flg,
    baitai_code,
    campaign_description,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM campaign_instructions
WHERE d_updated_datetime > :sync_datetime
    AND d_updated_datetime <= :diff_base_timestamp;