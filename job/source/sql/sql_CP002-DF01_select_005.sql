SELECT campaign_instructions_code,
    campaign_group_no,
    joken_type,
    campaign_joken_no,
    joken_kind1,
    joken_kind2,
    joken,
    joken_min,
    joken_max,
    regular_kaiji,
    joken_month_num,
    commodity_name,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM campaign_order
WHERE d_updated_datetime > :sync_datetime
    AND d_updated_datetime <= :diff_base_timestamp;