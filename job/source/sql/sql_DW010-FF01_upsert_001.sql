INSERT INTO
    shop_mst (
        shop_cd,
        group_cd,
        area_cd,
        district_cd,
        shop_kind,
        shop_name_full,
        shop_name_half,
        shop_name_short,
        shop_name_english,
        zip_cd,
        prefecture_cd,
        address1,
        address2,
        address3,
        address4,
        tel_num,
        fax_num,
        tax_inclusive_round_kind,
        tax_exclusive_round_kind,
        condition_kind,
        condition_detail_kind,
        channel_kind,
        is_stock_control,
        begin_date,
        end_date,
        shop_floor_space,
        is_abandon_price_change,
        is_abandon_stock_transfer,
        form_management_kind,
        is_shop_terminal,
        allow_transfer_group_cd,
        main_brand_cd,
        disc_round_position,
        disc_round_kind,
        is_emp_salse_place,
        is_move_operation,
        is_sales_register,
        is_stock_display,
        brock_cd,
        pricechange_disc_round_position,
        pricechange_disc_round_kind,
        numeric_reserve1,
        numeric_reserve2,
        numeric_reserve3,
        numeric_reserve4,
        numeric_reserve5,
        string_reserve1,
        string_reserve2,
        string_reserve3,
        string_reserve4,
        string_reserve5,
        is_deleted,
        spare_numeric_reserve1,
        spare_numeric_reserve2,
        spare_numeric_reserve3,
        apare_numeric_reserve4,
        spare_numeric_reserve5,
        spare_numeric_reserve6,
        spare_numeric_reserve7,
        spare_numeric_reserve8,
        spare_numeric_reserve9,
        spare_numeric_reserve10,
        spare_numeric_reserve11,
        spare_numeric_reserve12,
        spare_numeric_reserve13,
        spare_numeric_reserve14,
        spare_numeric_reserve15,
        spare_numeric_reserve16,
        spare_numeric_reserve17,
        spare_numeric_reserve18,
        spare_numeric_reserve19,
        spare_numeric_reserve20,
        spare_numeric_reserve21,
        spare_numeric_reserve22,
        spare_numeric_reserve23,
        spare_numeric_reserve24,
        spare_numeric_reserve25,
        spare_numeric_reserve26,
        spare_numeric_reserve27,
        spare_numeric_reserve28,
        spare_numeric_reserve29,
        spare_numeric_reserve30,
        spare_string_reserve1,
        spare_string_reserve2,
        spare_string_reserve3,
        spare_string_reserve4,
        spare_string_reserve5,
        spare_string_reserve6,
        spare_string_reserve7,
        spare_string_reserve8,
        spare_string_reserve9,
        spare_string_reserve10,
        spare_string_reserve11,
        spare_string_reserve12,
        spare_string_reserve13,
        spare_string_reserve14,
        spare_string_reserve15,
        spare_string_reserve16,
        spare_string_reserve17,
        spare_string_reserve18,
        spare_string_reserve19,
        spare_string_reserve20,
        spare_string_reserve21,
        spare_string_reserve22,
        spare_string_reserve23,
        spare_string_reserve24,
        spare_string_reserve25,
        spare_string_reserve26,
        spare_string_reserve27,
        spare_string_reserve28,
        spare_string_reserve29,
        spare_string_reserve30,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    shop_cd,
    group_cd,
    area_cd,
    district_cd,
    shop_kind,
    shop_name_full,
    shop_name_half,
    shop_name_short,
    shop_name_english,
    zip_cd,
    prefecture_cd,
    address1,
    address2,
    address3,
    address4,
    tel_num,
    fax_num,
    tax_inclusive_round_kind,
    tax_exclusive_round_kind,
    condition_kind,
    condition_detail_kind,
    channel_kind,
    is_stock_control,
    begin_date,
    end_date,
    shop_floor_space,
    is_abandon_price_change,
    is_abandon_stock_transfer,
    form_management_kind,
    is_shop_terminal,
    allow_transfer_group_cd,
    main_brand_cd,
    disc_round_position,
    disc_round_kind,
    is_emp_salse_place,
    is_move_operation,
    is_sales_register,
    is_stock_display,
    brock_cd,
    pricechange_disc_round_position,
    pricechange_disc_round_kind,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    is_deleted,
    spare_numeric_reserve1,
    spare_numeric_reserve2,
    spare_numeric_reserve3,
    apare_numeric_reserve4,
    spare_numeric_reserve5,
    spare_numeric_reserve6,
    spare_numeric_reserve7,
    spare_numeric_reserve8,
    spare_numeric_reserve9,
    spare_numeric_reserve10,
    spare_numeric_reserve11,
    spare_numeric_reserve12,
    spare_numeric_reserve13,
    spare_numeric_reserve14,
    spare_numeric_reserve15,
    spare_numeric_reserve16,
    spare_numeric_reserve17,
    spare_numeric_reserve18,
    spare_numeric_reserve19,
    spare_numeric_reserve20,
    spare_numeric_reserve21,
    spare_numeric_reserve22,
    spare_numeric_reserve23,
    spare_numeric_reserve24,
    spare_numeric_reserve25,
    spare_numeric_reserve26,
    spare_numeric_reserve27,
    spare_numeric_reserve28,
    spare_numeric_reserve29,
    spare_numeric_reserve30,
    spare_string_reserve1,
    spare_string_reserve2,
    spare_string_reserve3,
    spare_string_reserve4,
    spare_string_reserve5,
    spare_string_reserve6,
    spare_string_reserve7,
    spare_string_reserve8,
    spare_string_reserve9,
    spare_string_reserve10,
    spare_string_reserve11,
    spare_string_reserve12,
    spare_string_reserve13,
    spare_string_reserve14,
    spare_string_reserve15,
    spare_string_reserve16,
    spare_string_reserve17,
    spare_string_reserve18,
    spare_string_reserve19,
    spare_string_reserve20,
    spare_string_reserve21,
    spare_string_reserve22,
    spare_string_reserve23,
    spare_string_reserve24,
    spare_string_reserve25,
    spare_string_reserve26,
    spare_string_reserve27,
    spare_string_reserve28,
    spare_string_reserve29,
    spare_string_reserve30,
    'JN_DW010-FF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_DW010-FF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM
    shop_mst_work ON CONFLICT (shop_cd) DO
UPDATE
SET
    shop_cd = EXCLUDED.shop_cd,
    group_cd = EXCLUDED.group_cd,
    area_cd = EXCLUDED.area_cd,
    district_cd = EXCLUDED.district_cd,
    shop_kind = EXCLUDED.shop_kind,
    shop_name_full = EXCLUDED.shop_name_full,
    shop_name_half = EXCLUDED.shop_name_half,
    shop_name_short = EXCLUDED.shop_name_short,
    shop_name_english = EXCLUDED.shop_name_english,
    zip_cd = EXCLUDED.zip_cd,
    prefecture_cd = EXCLUDED.prefecture_cd,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    address3 = EXCLUDED.address3,
    address4 = EXCLUDED.address4,
    tel_num = EXCLUDED.tel_num,
    fax_num = EXCLUDED.fax_num,
    tax_inclusive_round_kind = EXCLUDED.tax_inclusive_round_kind,
    tax_exclusive_round_kind = EXCLUDED.tax_exclusive_round_kind,
    condition_kind = EXCLUDED.condition_kind,
    condition_detail_kind = EXCLUDED.condition_detail_kind,
    channel_kind = EXCLUDED.channel_kind,
    is_stock_control = EXCLUDED.is_stock_control,
    begin_date = EXCLUDED.begin_date,
    end_date = EXCLUDED.end_date,
    shop_floor_space = EXCLUDED.shop_floor_space,
    is_abandon_price_change = EXCLUDED.is_abandon_price_change,
    is_abandon_stock_transfer = EXCLUDED.is_abandon_stock_transfer,
    form_management_kind = EXCLUDED.form_management_kind,
    is_shop_terminal = EXCLUDED.is_shop_terminal,
    allow_transfer_group_cd = EXCLUDED.allow_transfer_group_cd,
    main_brand_cd = EXCLUDED.main_brand_cd,
    disc_round_position = EXCLUDED.disc_round_position,
    disc_round_kind = EXCLUDED.disc_round_kind,
    is_emp_salse_place = EXCLUDED.is_emp_salse_place,
    is_move_operation = EXCLUDED.is_move_operation,
    is_sales_register = EXCLUDED.is_sales_register,
    is_stock_display = EXCLUDED.is_stock_display,
    brock_cd = EXCLUDED.brock_cd,
    pricechange_disc_round_position = EXCLUDED.pricechange_disc_round_position,
    pricechange_disc_round_kind = EXCLUDED.pricechange_disc_round_kind,
    numeric_reserve1 = EXCLUDED.numeric_reserve1,
    numeric_reserve2 = EXCLUDED.numeric_reserve2,
    numeric_reserve3 = EXCLUDED.numeric_reserve3,
    numeric_reserve4 = EXCLUDED.numeric_reserve4,
    numeric_reserve5 = EXCLUDED.numeric_reserve5,
    string_reserve1 = EXCLUDED.string_reserve1,
    string_reserve2 = EXCLUDED.string_reserve2,
    string_reserve3 = EXCLUDED.string_reserve3,
    string_reserve4 = EXCLUDED.string_reserve4,
    string_reserve5 = EXCLUDED.string_reserve5,
    is_deleted = EXCLUDED.is_deleted,
    spare_numeric_reserve1 = EXCLUDED.spare_numeric_reserve1,
    spare_numeric_reserve2 = EXCLUDED.spare_numeric_reserve2,
    spare_numeric_reserve3 = EXCLUDED.spare_numeric_reserve3,
    apare_numeric_reserve4 = EXCLUDED.apare_numeric_reserve4,
    spare_numeric_reserve5 = EXCLUDED.spare_numeric_reserve5,
    spare_numeric_reserve6 = EXCLUDED.spare_numeric_reserve6,
    spare_numeric_reserve7 = EXCLUDED.spare_numeric_reserve7,
    spare_numeric_reserve8 = EXCLUDED.spare_numeric_reserve8,
    spare_numeric_reserve9 = EXCLUDED.spare_numeric_reserve9,
    spare_numeric_reserve10 = EXCLUDED.spare_numeric_reserve10,
    spare_numeric_reserve11 = EXCLUDED.spare_numeric_reserve11,
    spare_numeric_reserve12 = EXCLUDED.spare_numeric_reserve12,
    spare_numeric_reserve13 = EXCLUDED.spare_numeric_reserve13,
    spare_numeric_reserve14 = EXCLUDED.spare_numeric_reserve14,
    spare_numeric_reserve15 = EXCLUDED.spare_numeric_reserve15,
    spare_numeric_reserve16 = EXCLUDED.spare_numeric_reserve16,
    spare_numeric_reserve17 = EXCLUDED.spare_numeric_reserve17,
    spare_numeric_reserve18 = EXCLUDED.spare_numeric_reserve18,
    spare_numeric_reserve19 = EXCLUDED.spare_numeric_reserve19,
    spare_numeric_reserve20 = EXCLUDED.spare_numeric_reserve20,
    spare_numeric_reserve21 = EXCLUDED.spare_numeric_reserve21,
    spare_numeric_reserve22 = EXCLUDED.spare_numeric_reserve22,
    spare_numeric_reserve23 = EXCLUDED.spare_numeric_reserve23,
    spare_numeric_reserve24 = EXCLUDED.spare_numeric_reserve24,
    spare_numeric_reserve25 = EXCLUDED.spare_numeric_reserve25,
    spare_numeric_reserve26 = EXCLUDED.spare_numeric_reserve26,
    spare_numeric_reserve27 = EXCLUDED.spare_numeric_reserve27,
    spare_numeric_reserve28 = EXCLUDED.spare_numeric_reserve28,
    spare_numeric_reserve29 = EXCLUDED.spare_numeric_reserve29,
    spare_numeric_reserve30 = EXCLUDED.spare_numeric_reserve30,
    spare_string_reserve1 = EXCLUDED.spare_string_reserve1,
    spare_string_reserve2 = EXCLUDED.spare_string_reserve2,
    spare_string_reserve3 = EXCLUDED.spare_string_reserve3,
    spare_string_reserve4 = EXCLUDED.spare_string_reserve4,
    spare_string_reserve5 = EXCLUDED.spare_string_reserve5,
    spare_string_reserve6 = EXCLUDED.spare_string_reserve6,
    spare_string_reserve7 = EXCLUDED.spare_string_reserve7,
    spare_string_reserve8 = EXCLUDED.spare_string_reserve8,
    spare_string_reserve9 = EXCLUDED.spare_string_reserve9,
    spare_string_reserve10 = EXCLUDED.spare_string_reserve10,
    spare_string_reserve11 = EXCLUDED.spare_string_reserve11,
    spare_string_reserve12 = EXCLUDED.spare_string_reserve12,
    spare_string_reserve13 = EXCLUDED.spare_string_reserve13,
    spare_string_reserve14 = EXCLUDED.spare_string_reserve14,
    spare_string_reserve15 = EXCLUDED.spare_string_reserve15,
    spare_string_reserve16 = EXCLUDED.spare_string_reserve16,
    spare_string_reserve17 = EXCLUDED.spare_string_reserve17,
    spare_string_reserve18 = EXCLUDED.spare_string_reserve18,
    spare_string_reserve19 = EXCLUDED.spare_string_reserve19,
    spare_string_reserve20 = EXCLUDED.spare_string_reserve20,
    spare_string_reserve21 = EXCLUDED.spare_string_reserve21,
    spare_string_reserve22 = EXCLUDED.spare_string_reserve22,
    spare_string_reserve23 = EXCLUDED.spare_string_reserve23,
    spare_string_reserve24 = EXCLUDED.spare_string_reserve24,
    spare_string_reserve25 = EXCLUDED.spare_string_reserve25,
    spare_string_reserve26 = EXCLUDED.spare_string_reserve26,
    spare_string_reserve27 = EXCLUDED.spare_string_reserve27,
    spare_string_reserve28 = EXCLUDED.spare_string_reserve28,
    spare_string_reserve29 = EXCLUDED.spare_string_reserve29,
    spare_string_reserve30 = EXCLUDED.spare_string_reserve30,
    d_updated_user = 'JN_DW010-FF01_001',
    d_updated_datetime = NOW(),
    d_version = shop_mst.d_version + 1;