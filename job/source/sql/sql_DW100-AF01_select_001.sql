SELECT
    id  as "id" ,
    owner_id as "ownerid",
    is_deleted as "isdeleted",
    name as "name",
    TO_CHAR(created_date, 'YYYY-MM-DD HH24:MI:SS') as "createddate" ,
    created_by_id as "createdbyid",
    TO_CHAR(last_modified_date, 'YYYY-MM-DD HH24:MI:SS') as "lastmodifieddate",
    last_modified_by_id as "lastmodifiedbyid",
    TO_CHAR(system_modstamp, 'YYYY-MM-DD HH24:MI:SS') as "systemmodstamp" ,
    account_id__c as "accountid__c",
    product_id__c as "productid__c",
    product_code__c as "productcode__c",
    product_name_c as "productname__c",
    is_deleted__c as "isdeleted__c"
FROM
    favorite_product
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;
