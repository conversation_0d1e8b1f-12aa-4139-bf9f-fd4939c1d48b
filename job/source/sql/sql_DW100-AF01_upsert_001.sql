WITH source_data AS (
    SELECT id,
    owner_id,
    is_deleted,
    name,
    created_date + INTERVAL '9 hours',
    created_by_id,
    last_modified_date + INTERVAL '9 hours',
    last_modified_by_id,
    system_modstamp + INTERVAL '9 hours',
    account_id__c,
    product_id__c,
    product_code__c,
    product_name_c,
    is_deleted__c
    FROM favorite_product_work
)
INSERT INTO favorite_product (
        id,
        owner_id,
        is_deleted,
        name,
        created_date,
        created_by_id,
        last_modified_date,
        last_modified_by_id,
        system_modstamp,
        account_id__c,
        product_id__c,
        product_code__c,
        product_name_c,
        is_deleted__c,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW100-AF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW100-AF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (id) DO
UPDATE SET
    id = EXCLUDED.id,
    owner_id = EXCLUDED.owner_id,
    is_deleted = EXCLUDED.is_deleted,
    name = EXCLUDED.name,
    created_date = EXCLUDED.created_date + INTERVAL '9 hours',
    created_by_id = EXCLUDED.created_by_id,
    last_modified_date = EXCLUDED.last_modified_date + INTERVAL '9 hours',
    last_modified_by_id = EXCLUDED.last_modified_by_id,
    system_modstamp = EXCLUDED.system_modstamp + INTERVAL '9 hours',
    account_id__c = EXCLUDED.account_id__c,
    product_id__c = EXCLUDED.product_id__c,
    product_code__c = EXCLUDED.product_code__c,
    product_name_c = EXCLUDED.product_name_c,
    is_deleted__c = EXCLUDED.is_deleted__c,
    d_updated_user = 'JN_DW100-AF01_001',
    d_updated_datetime =   NOW(),
    d_version = favorite_product.d_version + 1 ;
