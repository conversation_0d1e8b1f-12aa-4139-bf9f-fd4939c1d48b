SELECT
    id as "id" ,
    is_deleted as "isdeleted" ,
    master_record_id as "masterrecordid" ,
    name as "name" ,
    last_name as "lastname" ,
    first_name as "firstname" ,
    salutation as "salutation" ,
    type as "type" ,
    record_type_id as "recordtypeid" ,
    parent_id as "parentid" ,
    person_mailing_street as "personmailingstreet" ,
    person_mailing_city as "personmailingcity" ,
    person_mailing_state as "personmailingstate" ,
    person_mailing_state_code as "personmailingstatecode" ,
    person_mailing_postal_code as "personmailingpostalcode" ,
    person_mailing_country as "personmailingcountry" ,
    person_mailing_latitude as "personmailinglatitude" ,
    person_mailing_longitude as "personmailinglongitude" ,
    person_mailing_geocode_accuracy as "personmailinggeocodeaccuracy" ,
    '' as "personmailingaddress" ,
    shipping_street as "shippingstreet" ,
    shipping_city as "shippingcity" ,
    shipping_state as "shippingstate" ,
    shipping_postal_code as "shippingpostalcode" ,
    shipping_country as "shippingcountry" ,
    shipping_latitude as "shippinglatitude" ,
    shipping_longitude as "shippinglongitude" ,
    shipping_geocode_accuracy as "shippinggeocodeaccuracy" ,
    '' as "shippingaddress" ,
    phone as "phone" ,
    fax as "fax" ,
    account_number as "accountnumber" ,
    website as "website" ,
    photo_url as "photourl" ,
    sic as "sic" ,
    industry as "industry" ,
    annual_revenue as "annualrevenue" ,
    number_of_employees as "numberofemployees" ,
    ownership as "ownership" ,
    ticker_symbol as "tickersymbol" ,
    description as "description" ,
    rating as "rating" ,
    site as "site" ,
    owner_id as "ownerid" ,
    TO_CHAR(created_date,'YYYY-MM-DD HH24:MI:SS') as "createddate" ,
    created_by_id as "createdbyid" ,
    TO_CHAR(last_modified_date,'YYYY-MM-DD HH24:MI:SS') as "lastmodifieddate" ,
    last_modified_by_id as "lastmodifiedbyid" ,
    TO_CHAR(system_modstamp,'YYYY-MM-DD HH24:MI:SS') as "systemmodstamp" ,
    TO_CHAR(last_activity_date,'YYYY-MM-DD') as "lastactivitydate" ,
    TO_CHAR(last_viewed_date,'YYYY-MM-DD HH24:MI:SS') as "lastvieweddate" ,
    TO_CHAR(last_referenced_date,'YYYY-MM-DD HH24:MI:SS') as "lastreferenceddate" ,
    is_person_account as "ispersonaccount" ,
    billing_street as "billingstreet" ,
    billing_city as "billingcity" ,
    billing_state as "billingstate" ,
    billing_postal_code as "billingpostalcode" ,
    billing_country as "billingcountry" ,
    billing_latitude as "billinglatitude" ,
    billing_longitude as "billinglongitude" ,
    billing_geocode_accuracy as "billinggeocodeaccuracy" ,
    '' as "billingaddress" ,
    person_other_street as "personotherstreet" ,
    person_other_city as "personothercity" ,
    person_other_state as "personotherstate" ,
    person_other_postal_code as "personotherpostalcode" ,
    person_other_country as "personothercountry" ,
    person_other_latitude as "personotherlatitude" ,
    person_other_longitude as "personotherlongitude" ,
    person_other_geocode_accuracy as "personothergeocodeaccuracy" ,
    '' as "personotheraddress" ,
    person_mobile_phone as "personmobilephone" ,
    person_other_phone as "personotherphone" ,
    person_assistant_phone as "personassistantphone" ,
    person_email as "personemail" ,
    person_title as "persontitle" ,
    person_department as "persondepartment" ,
    person_assistant_name as "personassistantname" ,
    person_lead_source as "personleadsource" ,
    TO_CHAR(person_birthdate,'YYYY-MM-DD') as "personbirthdate" ,
    person_has_opted_out_of_email as "personhasoptedoutofemail" ,
    person_has_opted_out_of_fax as "personhasoptedoutoffax" ,
    person_do_not_call as "persondonotcall" ,
    TO_CHAR(person_last_cu_request_date,'YYYY-MM-DD HH24:MI:SS') as "personlastcurequestdate" ,
    TO_CHAR(person_last_cu_update_date,'YYYY-MM-DD HH24:MI:SS') as "personlastcuupdatedate" ,
    person_email_bounced_reason as "personemailbouncedreason" ,
    TO_CHAR(person_email_bounced_date,'YYYY-MM-DD HH24:MI:SS') as "personemailbounceddate" ,
    person_individual_id as "personindividualid" ,
    person_pronouns as "personpronouns" ,
    person_gender_identity as "persongenderidentity" ,
    jigsaw as "jigsaw" ,
    jigsaw_company_id as "jigsawcompanyid" ,
    account_source as "accountsource" ,
    sic_desc as "sicdesc" ,
    gender_c as "gender__c" ,
    number_c as "number__c" ,
    preferred_shipment_service_c as "preferredshipmentservice__c" ,
    TO_CHAR(account_closed_date_c,'YYYY-MM-DD') as "accountcloseddate__c" ,
    account_closed_reason_c as "accountclosedreason__c" ,
    shop_card_barcode_c as "shopcardbarcode__c" ,
    person_mailing_address_c as "personmailingaddress__c" ,
    is_employee_c as "isemployee__c" ,
    is_opted_in_emal_magazine_c as "isoptedinemalmagazine__c" ,
    TO_CHAR(email_magazine_unsubscribed_date_c,'YYYY-MM-DD') as "emailmagazineunsubscribeddate__c" ,
    TO_CHAR(email_magazine_subscribed_date_c,'YYYY-MM-DD') as "emailmagazinesubscribeddate__c" ,
    beauty_catalog_send_type_c as "beautycatalogsendtype__c" ,
    health_catalog_send_type_c as "healthcatalogsendtype__c" ,
    apparel_catalog_send_type_c as "apparelcatalogsendtype__c" ,
    medicine_catalog_send_type_c as "medicinecatalogsendtype__c" ,
    pet_catalog_send_type_c as "petcatalogsendtype__c" ,
    fax_purchase_order_send_type_c as "faxpurchaseordersendtype__c" ,
    last_name_kana_c as "lastnamekana__c" ,
    first_name_kana_c as "firstnamekana__c" ,
    rank_c as "rank__c" ,
    source_c as "source__c" ,
    status_c as "status__c" ,
    memo_c as "memo__c" ,
    memo_for_store_c as "memoforstore__c" ,
    is_dhc_credit_card_owner_c as "isdhccreditcardowner__c" ,
    age_c as "age__c" ,
    is_opted_in_dm_c as "isoptedindm__c" ,
    is_opted_in_catalog_c as "isoptedincatalog__c" ,
    is_unmailable_postway_c as "isunmailablepostway__c" ,
    is_unmailable_post_c as "isunmailablepost__c" ,
    is_stop_order_c as "isstoporder__c" ,
    is_order_monitoring_c as "isordermonitoring__c" ,
    is_required_caution_c as "isrequiredcaution__c" ,
    guest_order_only_c as "guestorderonly__c" ,
    customer_number_c as "customernumber__c" ,
    is_opted_in_survey_c as "isoptedinsurvey__c" ,
    marged_account_id_c as "margedaccountid__c" ,
    TO_CHAR(rank_expiry_date_c,'YYYY-MM-DD') as "rankexpirydate__c" ,
    preferred_contact_way_c as "preferredcontactway__c" ,
    line_mini_app_user_id_c as "lineminiappuserid__c" ,
    name_kana__c as "namekana__c" ,
    TO_CHAR(birthdate__c,'YYYY-MM-DD') as "birthdate__c" ,
    email_magazine_opted_out_url__c as "emailmagazineoptedouturl__c" ,
    person_email__c as "personemail__c" ,
    optin_store_email_magazine_status__c as "optinstoreemailmagazinestatus__c" ,
    store_email_magazine_email__c as "storeemailmagazineemail__c" ,
    store_email_magazine_opted_out_key__c as "storeemailmagazineoptedoutkey__c" ,
    store_email_magazine_opted_out_url__c as "storeemailmagazineoptedouturl__c" ,
    tonariwaid__c as "tonariwaid__c" ,
    store_email_magazine_store_code__c as "storeemailmagazinestorecode__c" ,
    unmailable_reason__c as "unmailablereason__c"
FROM
    account_dwh
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;
