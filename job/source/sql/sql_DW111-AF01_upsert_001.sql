WITH source_data AS (
    SELECT id,
    is_deleted,
    master_record_id,
    name,
    last_name,
    first_name,
    salutation,
    type,
    record_type_id,
    parent_id,
    person_mailing_street,
    person_mailing_city,
    person_mailing_state,
    person_mailing_state_code,
    person_mailing_postal_code,
    person_mailing_country,
    person_mailing_latitude,
    person_mailing_longitude,
    person_mailing_geocode_accuracy,
    shipping_street,
    shipping_city,
    shipping_state,
    shipping_postal_code,
    shipping_country,
    shipping_latitude,
    shipping_longitude,
    shipping_geocode_accuracy,
    phone,
    fax,
    account_number,
    website,
    photo_url,
    sic,
    industry,
    annual_revenue,
    number_of_employees,
    ownership,
    ticker_symbol,
    description,
    rating,
    site,
    owner_id,
    created_date + INTERVAL '9 hours',
    created_by_id,
    last_modified_date + INTERVAL '9 hours',
    last_modified_by_id,
    system_modstamp + INTERVAL '9 hours',
    last_activity_date,
    last_viewed_date + INTERVAL '9 hours',
    last_referenced_date + INTERVAL '9 hours',
    is_person_account,
    billing_street,
    billing_city,
    billing_state,
    billing_postal_code,
    billing_country,
    billing_latitude,
    billing_longitude,
    billing_geocode_accuracy,
    person_other_street,
    person_other_city,
    person_other_state,
    person_other_postal_code,
    person_other_country,
    person_other_latitude,
    person_other_longitude,
    person_other_geocode_accuracy,
    person_mobile_phone,
    person_other_phone,
    person_assistant_phone,
    person_email,
    person_title,
    person_department,
    person_assistant_name,
    person_lead_source,
    person_birthdate,
    person_has_opted_out_of_email,
    person_has_opted_out_of_fax,
    person_do_not_call,
    person_last_cu_request_date + INTERVAL '9 hours',
    person_last_cu_update_date + INTERVAL '9 hours',
    person_email_bounced_reason,
    person_email_bounced_date + INTERVAL '9 hours',
    person_individual_id,
    person_pronouns,
    person_gender_identity,
    jigsaw,
    jigsaw_company_id,
    account_source,
    sic_desc,
    gender_c,
    number_c,
    preferred_shipment_service_c,
    account_closed_date_c,
    account_closed_reason_c,
    shop_card_barcode_c,
    person_mailing_address_c,
    is_employee_c,
    is_opted_in_emal_magazine_c,
    email_magazine_unsubscribed_date_c,
    email_magazine_subscribed_date_c,
    beauty_catalog_send_type_c,
    health_catalog_send_type_c,
    apparel_catalog_send_type_c,
    medicine_catalog_send_type_c,
    pet_catalog_send_type_c,
    fax_purchase_order_send_type_c,
    last_name_kana_c,
    first_name_kana_c,
    rank_c,
    source_c,
    status_c,
    memo_c,
    memo_for_store_c,
    is_dhc_credit_card_owner_c,
    age_c,
    is_opted_in_dm_c,
    is_opted_in_catalog_c,
    is_unmailable_postway_c,
    is_unmailable_post_c,
    is_stop_order_c,
    is_order_monitoring_c,
    is_required_caution_c,
    guest_order_only_c,
    customer_number_c,
    is_opted_in_survey_c,
    marged_account_id_c,
    rank_expiry_date_c,
    preferred_contact_way_c,
    line_mini_app_user_id_c,
    name_kana__c,
    birthdate__c,
    email_magazine_opted_out_url__c,
    person_email__c,
    optin_store_email_magazine_status__c,
    store_email_magazine_email__c,
    store_email_magazine_opted_out_key__c,
    store_email_magazine_opted_out_url__c,
    tonariwaid__c,
    store_email_magazine_store_code__c,
    unmailable_reason__c
    FROM account_dwh_work
)
INSERT INTO account_dwh (
        id,
        is_deleted,
        master_record_id,
        name,
        last_name,
        first_name,
        salutation,
        type,
        record_type_id,
        parent_id,
        person_mailing_street,
        person_mailing_city,
        person_mailing_state,
        person_mailing_state_code,
        person_mailing_postal_code,
        person_mailing_country,
        person_mailing_latitude,
        person_mailing_longitude,
        person_mailing_geocode_accuracy,
        shipping_street,
        shipping_city,
        shipping_state,
        shipping_postal_code,
        shipping_country,
        shipping_latitude,
        shipping_longitude,
        shipping_geocode_accuracy,
        phone,
        fax,
        account_number,
        website,
        photo_url,
        sic,
        industry,
        annual_revenue,
        number_of_employees,
        ownership,
        ticker_symbol,
        description,
        rating,
        site,
        owner_id,
        created_date,
        created_by_id,
        last_modified_date,
        last_modified_by_id,
        system_modstamp,
        last_activity_date,
        last_viewed_date,
        last_referenced_date,
        is_person_account,
        billing_street,
        billing_city,
        billing_state,
        billing_postal_code,
        billing_country,
        billing_latitude,
        billing_longitude,
        billing_geocode_accuracy,
        person_other_street,
        person_other_city,
        person_other_state,
        person_other_postal_code,
        person_other_country,
        person_other_latitude,
        person_other_longitude,
        person_other_geocode_accuracy,
        person_mobile_phone,
        person_other_phone,
        person_assistant_phone,
        person_email,
        person_title,
        person_department,
        person_assistant_name,
        person_lead_source,
        person_birthdate,
        person_has_opted_out_of_email,
        person_has_opted_out_of_fax,
        person_do_not_call,
        person_last_cu_request_date,
        person_last_cu_update_date,
        person_email_bounced_reason,
        person_email_bounced_date,
        person_individual_id,
        person_pronouns,
        person_gender_identity,
        jigsaw,
        jigsaw_company_id,
        account_source,
        sic_desc,
        gender_c,
        number_c,
        preferred_shipment_service_c,
        account_closed_date_c,
        account_closed_reason_c,
        shop_card_barcode_c,
        person_mailing_address_c,
        is_employee_c,
        is_opted_in_emal_magazine_c,
        email_magazine_unsubscribed_date_c,
        email_magazine_subscribed_date_c,
        beauty_catalog_send_type_c,
        health_catalog_send_type_c,
        apparel_catalog_send_type_c,
        medicine_catalog_send_type_c,
        pet_catalog_send_type_c,
        fax_purchase_order_send_type_c,
        last_name_kana_c,
        first_name_kana_c,
        rank_c,
        source_c,
        status_c,
        memo_c,
        memo_for_store_c,
        is_dhc_credit_card_owner_c,
        age_c,
        is_opted_in_dm_c,
        is_opted_in_catalog_c,
        is_unmailable_postway_c,
        is_unmailable_post_c,
        is_stop_order_c,
        is_order_monitoring_c,
        is_required_caution_c,
        guest_order_only_c,
        customer_number_c,
        is_opted_in_survey_c,
        marged_account_id_c,
        rank_expiry_date_c,
        preferred_contact_way_c,
        line_mini_app_user_id_c,
        name_kana__c,
        birthdate__c,
        email_magazine_opted_out_url__c,
        person_email__c,
        optin_store_email_magazine_status__c,
        store_email_magazine_email__c,
        store_email_magazine_opted_out_key__c,
        store_email_magazine_opted_out_url__c,
        tonariwaid__c,
        store_email_magazine_store_code__c,
        unmailable_reason__c,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW111-AF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW111-AF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (id) DO
UPDATE SET
    id = EXCLUDED.id,
    is_deleted = EXCLUDED.is_deleted,
    master_record_id = EXCLUDED.master_record_id,
    name = EXCLUDED.name,
    last_name = EXCLUDED.last_name,
    first_name = EXCLUDED.first_name,
    salutation = EXCLUDED.salutation,
    type = EXCLUDED.type,
    record_type_id = EXCLUDED.record_type_id,
    parent_id = EXCLUDED.parent_id,
    person_mailing_street = EXCLUDED.person_mailing_street,
    person_mailing_city = EXCLUDED.person_mailing_city,
    person_mailing_state = EXCLUDED.person_mailing_state,
    person_mailing_state_code = EXCLUDED.person_mailing_state_code,
    person_mailing_postal_code = EXCLUDED.person_mailing_postal_code,
    person_mailing_country = EXCLUDED.person_mailing_country,
    person_mailing_latitude = EXCLUDED.person_mailing_latitude,
    person_mailing_longitude = EXCLUDED.person_mailing_longitude,
    person_mailing_geocode_accuracy = EXCLUDED.person_mailing_geocode_accuracy,
    shipping_street = EXCLUDED.shipping_street,
    shipping_city = EXCLUDED.shipping_city,
    shipping_state = EXCLUDED.shipping_state,
    shipping_postal_code = EXCLUDED.shipping_postal_code,
    shipping_country = EXCLUDED.shipping_country,
    shipping_latitude = EXCLUDED.shipping_latitude,
    shipping_longitude = EXCLUDED.shipping_longitude,
    shipping_geocode_accuracy = EXCLUDED.shipping_geocode_accuracy,
    phone = EXCLUDED.phone,
    fax = EXCLUDED.fax,
    account_number = EXCLUDED.account_number,
    website = EXCLUDED.website,
    photo_url = EXCLUDED.photo_url,
    sic = EXCLUDED.sic,
    industry = EXCLUDED.industry,
    annual_revenue = EXCLUDED.annual_revenue,
    number_of_employees = EXCLUDED.number_of_employees,
    ownership = EXCLUDED.ownership,
    ticker_symbol = EXCLUDED.ticker_symbol,
    description = EXCLUDED.description,
    rating = EXCLUDED.rating,
    site = EXCLUDED.site,
    owner_id = EXCLUDED.owner_id,
    created_date = EXCLUDED.created_date + INTERVAL '9 hours',
    created_by_id = EXCLUDED.created_by_id,
    last_modified_date = EXCLUDED.last_modified_date + INTERVAL '9 hours',
    last_modified_by_id = EXCLUDED.last_modified_by_id,
    system_modstamp = EXCLUDED.system_modstamp + INTERVAL '9 hours',
    last_activity_date = EXCLUDED.last_activity_date,
    last_viewed_date = EXCLUDED.last_viewed_date + INTERVAL '9 hours',
    last_referenced_date = EXCLUDED.last_referenced_date + INTERVAL '9 hours',
    is_person_account = EXCLUDED.is_person_account,
    billing_street = EXCLUDED.billing_street,
    billing_city = EXCLUDED.billing_city,
    billing_state = EXCLUDED.billing_state,
    billing_postal_code = EXCLUDED.billing_postal_code,
    billing_country = EXCLUDED.billing_country,
    billing_latitude = EXCLUDED.billing_latitude,
    billing_longitude = EXCLUDED.billing_longitude,
    billing_geocode_accuracy = EXCLUDED.billing_geocode_accuracy,
    person_other_street = EXCLUDED.person_other_street,
    person_other_city = EXCLUDED.person_other_city,
    person_other_state = EXCLUDED.person_other_state,
    person_other_postal_code = EXCLUDED.person_other_postal_code,
    person_other_country = EXCLUDED.person_other_country,
    person_other_latitude = EXCLUDED.person_other_latitude,
    person_other_longitude = EXCLUDED.person_other_longitude,
    person_other_geocode_accuracy = EXCLUDED.person_other_geocode_accuracy,
    person_mobile_phone = EXCLUDED.person_mobile_phone,
    person_other_phone = EXCLUDED.person_other_phone,
    person_assistant_phone = EXCLUDED.person_assistant_phone,
    person_email = EXCLUDED.person_email,
    person_title = EXCLUDED.person_title,
    person_department = EXCLUDED.person_department,
    person_assistant_name = EXCLUDED.person_assistant_name,
    person_lead_source = EXCLUDED.person_lead_source,
    person_birthdate = EXCLUDED.person_birthdate,
    person_has_opted_out_of_email = EXCLUDED.person_has_opted_out_of_email,
    person_has_opted_out_of_fax = EXCLUDED.person_has_opted_out_of_fax,
    person_do_not_call = EXCLUDED.person_do_not_call,
    person_last_cu_request_date = EXCLUDED.person_last_cu_request_date + INTERVAL '9 hours',
    person_last_cu_update_date = EXCLUDED.person_last_cu_update_date + INTERVAL '9 hours',
    person_email_bounced_reason = EXCLUDED.person_email_bounced_reason,
    person_email_bounced_date = EXCLUDED.person_email_bounced_date + INTERVAL '9 hours',
    person_individual_id = EXCLUDED.person_individual_id,
    person_pronouns = EXCLUDED.person_pronouns,
    person_gender_identity = EXCLUDED.person_gender_identity,
    jigsaw = EXCLUDED.jigsaw,
    jigsaw_company_id = EXCLUDED.jigsaw_company_id,
    account_source = EXCLUDED.account_source,
    sic_desc = EXCLUDED.sic_desc,
    gender_c = EXCLUDED.gender_c,
    number_c = EXCLUDED.number_c,
    preferred_shipment_service_c = EXCLUDED.preferred_shipment_service_c,
    account_closed_date_c = EXCLUDED.account_closed_date_c,
    account_closed_reason_c = EXCLUDED.account_closed_reason_c,
    shop_card_barcode_c = EXCLUDED.shop_card_barcode_c,
    person_mailing_address_c = EXCLUDED.person_mailing_address_c,
    is_employee_c = EXCLUDED.is_employee_c,
    is_opted_in_emal_magazine_c = EXCLUDED.is_opted_in_emal_magazine_c,
    email_magazine_unsubscribed_date_c = EXCLUDED.email_magazine_unsubscribed_date_c,
    email_magazine_subscribed_date_c = EXCLUDED.email_magazine_subscribed_date_c,
    beauty_catalog_send_type_c = EXCLUDED.beauty_catalog_send_type_c,
    health_catalog_send_type_c = EXCLUDED.health_catalog_send_type_c,
    apparel_catalog_send_type_c = EXCLUDED.apparel_catalog_send_type_c,
    medicine_catalog_send_type_c = EXCLUDED.medicine_catalog_send_type_c,
    pet_catalog_send_type_c = EXCLUDED.pet_catalog_send_type_c,
    fax_purchase_order_send_type_c = EXCLUDED.fax_purchase_order_send_type_c,
    last_name_kana_c = EXCLUDED.last_name_kana_c,
    first_name_kana_c = EXCLUDED.first_name_kana_c,
    rank_c = EXCLUDED.rank_c,
    source_c = EXCLUDED.source_c,
    status_c = EXCLUDED.status_c,
    memo_c = EXCLUDED.memo_c,
    memo_for_store_c = EXCLUDED.memo_for_store_c,
    is_dhc_credit_card_owner_c = EXCLUDED.is_dhc_credit_card_owner_c,
    age_c = EXCLUDED.age_c,
    is_opted_in_dm_c = EXCLUDED.is_opted_in_dm_c,
    is_opted_in_catalog_c = EXCLUDED.is_opted_in_catalog_c,
    is_unmailable_postway_c = EXCLUDED.is_unmailable_postway_c,
    is_unmailable_post_c = EXCLUDED.is_unmailable_post_c,
    is_stop_order_c = EXCLUDED.is_stop_order_c,
    is_order_monitoring_c = EXCLUDED.is_order_monitoring_c,
    is_required_caution_c = EXCLUDED.is_required_caution_c,
    guest_order_only_c = EXCLUDED.guest_order_only_c,
    customer_number_c = EXCLUDED.customer_number_c,
    is_opted_in_survey_c = EXCLUDED.is_opted_in_survey_c,
    marged_account_id_c = EXCLUDED.marged_account_id_c,
    rank_expiry_date_c = EXCLUDED.rank_expiry_date_c,
    preferred_contact_way_c = EXCLUDED.preferred_contact_way_c,
    line_mini_app_user_id_c = EXCLUDED.line_mini_app_user_id_c,
    name_kana__c = EXCLUDED.name_kana__c,
    birthdate__c = EXCLUDED.birthdate__c,
    email_magazine_opted_out_url__c = EXCLUDED.email_magazine_opted_out_url__c,
    person_email__c = EXCLUDED.person_email__c,
    optin_store_email_magazine_status__c = EXCLUDED.optin_store_email_magazine_status__c,
    store_email_magazine_email__c = EXCLUDED.store_email_magazine_email__c,
    store_email_magazine_opted_out_key__c = EXCLUDED.store_email_magazine_opted_out_key__c,
    store_email_magazine_opted_out_url__c = EXCLUDED.store_email_magazine_opted_out_url__c,
    tonariwaid__c = EXCLUDED.tonariwaid__c,
    store_email_magazine_store_code__c = EXCLUDED.store_email_magazine_store_code__c,
    unmailable_reason__c = EXCLUDED.unmailable_reason__c,
    d_updated_user = 'JN_DW111-AF01_001',
    d_updated_datetime =   NOW(),
    d_version = account_dwh.d_version + 1 ;
