SELECT
    accept_no ,
    cust_no ,
    record_no ,
    cust_name ,
    post_no ,
    addr1 ,
    addr2 ,
    addr3 ,
    tel_no ,
    prefecture_code ,
    cust_flg ,
    TO_CHAR(order_date, 'YYYY-MM-DD') as order_date ,
    pay_kb ,
    total_price ,
    delive_cust_name ,
    delive_post_no ,
    delive_addr1 ,
    delive_addr2 ,
    delive_addr3 ,
    delive_tel_no ,
    gift_flg ,
    TO_CHAR(kibou_ymd, 'YYYY-MM-DD') as kibou_ymd ,
    night_flg ,
    cosme_price ,
    health_price ,
    inner_price ,
    TO_CHAR(update_date, 'YYYY-MM-DD') as update_date ,
    TO_CHAR(chit_print_date, 'YYYY-MM-DD') as chit_print_date ,
    yamato_bar_code ,
    gyosha_flg ,
    status_flg ,
    slip_ono ,
    order_no ,
    clinic_name ,
    TO_CHAR(shipment_date, 'YYYY-MM-DD') as shipment_date ,
    TO_CHAR(shipment_plan_date, 'YYYY-MM-DD') as shipment_plan_date ,
    pack_cnt ,
    store_code ,
    period_flg ,
    delivery_box_gb ,
    air_delivery_yn ,
    tax_amt ,
    conveni_yn ,
    pudo_yn ,
    inplan_yn ,
    over_stock_yn ,
    reserve_order_yn ,
    gift_rapping_yn ,
    kanshi_yn ,
    fusoku_yn ,
    airplane_yn ,
    satofuru_yn ,
    tokusha_yn ,
    rakugaki_yn ,
    multi_sample_yn ,
    slip_size_code ,
    wh_code ,
    agent_cd ,
    import_yn ,
    TO_CHAR(import_date,'YYYY-MM-DD') as import_date
FROM
    out_indicate_header
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '1 day') and
    d_updated_datetime <= :diff_base_timestamp
