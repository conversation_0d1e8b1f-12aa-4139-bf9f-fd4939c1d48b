SELECT
    order_no ,
    order_payment_no ,
    payment_type ,
    payment_identify_code ,
    payment_process_type ,
    payment_process_status ,
    payment_process_price ,
    TO_CHAR(payment_process_datetime, 'YYYY-MM-DD HH24:MI:SS') as payment_process_datetime ,
    payment_process_send_content ,
    payment_process_receive_content ,
    henpin_request_no ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    order_payment_mng
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;