SELECT
    earnings_jisseki_no ,
    order_henpin_kbn ,
    sales_detail_kbn ,
    shipping_no ,
    shipping_detail_no ,
    order_no ,
    order_detail_no ,
    TO_CHAR(shipping_date,'YYYY-MM-DD') AS shipping_date ,
    TO_CHAR(sales_recording_date,'YYYY-MM-DD') AS sales_recording_date ,
    marketing_channel ,
    ext_payment_method_type ,
    shop_code ,
    main_product_no ,
    product_no ,
    period_flg ,
    baitai_code ,
    commodity_code ,
    hinban_code ,
    commodity_name ,
    commodity_kind ,
    commodity_group ,
    commodity_series ,
    commodity_segment ,
    business_segment ,
    parent_commodity_code ,
    commodity_amount ,
    campaign_instructions_code ,
    coupon_management_code ,
    incurred_price ,
    tax_group_code ,
    tax_no ,
    tax_rate ,
    commodity_tax_type ,
    business_partner_code ,
    sales_bumon_cd ,
    channel_cd ,
    sales_link_status ,
    TO_CHAR(sales_link_datetime,'YYYY-MM-DD HH24:MI:SS') AS sales_link_datetime ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') AS created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') AS updated_datetime
FROM
    sales_record
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;
