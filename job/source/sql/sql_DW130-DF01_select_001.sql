SELECT
    regular_contract_no ,
    regular_contract_detail_no ,
    composition_no ,
    shop_code ,
    parent_commodity_code ,
    parent_sku_code ,
    child_commodity_code ,
    child_sku_code ,
    commodity_name ,
    composition_quantity ,
    regular_sale_composition_no ,
    regular_sale_composition_name ,
    regular_sale_commodity_type ,
    regular_order_count_min_limit ,
    regular_order_count_max_limit ,
    regular_order_count_interval ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    regular_sale_cont_composition
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;