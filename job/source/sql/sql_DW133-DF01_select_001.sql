SELECT
    henpin_request_no ,
    order_no ,
    customer_code ,
    neo_customer_no ,
    henpin_confirm_status ,
    TO_CHAR(henpin_request_datetime, 'YYYY-MM-DD HH24:MI:SS') as henpin_request_datetime ,
    TO_CHAR(henpin_confirm_datetime, 'YYYY-MM-DD HH24:MI:SS') as henpin_confirm_datetime ,
    henpin_recieve_user_code ,
    henpin_change_kbn ,
    henpin_reason_kbn ,
    henpin_bill_cancel_flg ,
    henpin_shipping_flg ,
    henpin_souko_kbn ,
    total_shipping_amount ,
    adjustment_amount ,
    bill_price ,
    bill_price_bf_henpin ,
    appropriation_amount_bf_henpin ,
    deposit_occur_amount ,
    delivery_note_republish_flg ,
    credit_cancel_flg ,
    request_af_henpin_confirm_kbn ,
    amzn_refund_flg ,
    amzn_refund_id ,
    amzn_refund_status ,
    TO_CHAR(amzn_refund_initiated_datetime, 'YYYY-MM-DD HH24:MI:SS') as amzn_refund_initiated_datetime ,
    TO_CHAR(amzn_refunded_datetime, 'YYYY-MM-DD HH24:MI:SS') as amzn_refunded_datetime ,
    refund_amount ,
    bank_name ,
    bank_branch_name ,
    account_type ,
    account_no ,
    account_name ,
    registered_mail_address ,
    registered_mail_name ,
    registered_remarks ,
    TO_CHAR(appropriation_date, 'YYYY-MM-DD') as appropriation_date ,
    appropriation_amount ,
    wms_contact_flg ,
    wms_auto_confirm_flg ,
    TO_CHAR(sales_recording_date, 'YYYY-MM-DD') as sales_recording_date ,
    sales_recording_flg ,
    inquiry_kanri_no ,
    cancel_flg ,
    change_user_code ,
    before_grant_point_total ,
    before_reduction_point_total ,
    after_grant_plan_point_prod ,
    after_grant_plan_point_other ,
    after_grant_plan_point_total ,
    after_grant_point_prod ,
    after_grant_point_other ,
    after_grant_point_total ,
    after_reduction_plan_point_total ,
    after_reduction_point_total ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    returns_header
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;