SELECT
    henpin_request_no ,
    shipping_no ,
    shipping_detail_no ,
    order_no ,
    order_detail_no ,
    shop_code ,
    sku_code ,
    commodity_code ,
    commodity_name ,
    commodity_kind ,
    baitai_code ,
    baitai_name ,
    hinban_code ,
    benefits_code ,
    benefits_name ,
    henpin_qt ,
    regular_contract_no ,
    unit_price ,
    discount_price ,
    discount_amount ,
    retail_price ,
    retail_tax_group_code ,
    retail_tax_no ,
    retail_tax_rate ,
    retail_tax ,
    commodity_tax ,
    commodity_tax_type ,
    purchasing_amount ,
    henpin_price ,
    henpin_yoyaku_qt ,
    change_qt ,
    henpin_support_kind ,
    wms_henpin_rireki_no ,
    copy_soko_shiji ,
    grant_plan_point_prod_detail ,
    reduction_plan_point_prod_detail ,
    campaign_instructions_code ,
    campaign_instructions_name ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    returns_detail
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;