SELECT
    regular_contract_no ,
    shop_code ,
    TO_CHAR(regular_sale_cont_datetime, 'YYYY-MM-DD HH24:MI:SS') as regular_sale_cont_datetime ,
    customer_code ,
    neo_customer_no ,
    payment_method_no ,
    address_no ,
    regular_sale_cont_status ,
    TO_CHAR(next_delivery_request_date, 'YYYY-MM-DD') as next_delivery_request_date ,
    external_order_no ,
    order_user_code ,
    TO_CHAR(regular_update_datetime, 'YYYY-MM-DD HH24:MI:SS') as regular_update_datetime ,
    change_user_code ,
    regular_update_reason_kbn ,
    otodoke_hope_time_kbn ,
    marketing_channel ,
    delivery_type_no ,
    shipping_method_flg ,
    ext_payment_method_type ,
    card_brand ,
    credit_card_kanri_no ,
    credit_card_kanri_detail_no ,
    credit_card_no ,
    credit_card_meigi ,
    credit_card_valid_year ,
    credit_card_valid_month ,
    credit_card_pay_count ,
    amzn_charge_permission_id ,
    bill_address_kbn ,
    bill_print_otodoke_id ,
    o_name_disp_kbn ,
    delivery_note_flg ,
    include_flg ,
    receipt_flg ,
    receipt_to ,
    receipt_detail ,
    TO_CHAR(first_shipping_date, 'YYYY-MM-DD') as first_shipping_date ,
    TO_CHAR(lastest_shipping_date, 'YYYY-MM-DD') as lastest_shipping_date ,
    TO_CHAR(first_delivery_date, 'YYYY-MM-DD') as first_delivery_date ,
    TO_CHAR(lastest_delivery_date, 'YYYY-MM-DD') as lastest_delivery_date ,
    TO_CHAR(regular_stop_date, 'YYYY-MM-DD') as regular_stop_date ,
    regular_stop_reason_kbn ,
    TO_CHAR(regular_hold_date, 'YYYY-MM-DD') as regular_hold_date ,
    TO_CHAR(regular_hold_clear_date, 'YYYY-MM-DD') as regular_hold_clear_date ,
    regular_kaiji ,
    shipped_regular_count ,
    delivery_memo ,
    regular_hold_reason_kbn ,
    niyose_flg ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    regular_sale_cont_header
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;