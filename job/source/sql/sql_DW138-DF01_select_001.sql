SELECT
    shipping_no ,
    order_no ,
    shop_code ,
    customer_code ,
    neo_customer_no ,
    address_no ,
    address_last_name ,
    address_first_name ,
    address_last_name_kana ,
    address_first_name_kana ,
    postal_code ,
    prefecture_code ,
    address1 ,
    address2 ,
    address3 ,
    address4 ,
    corporation_post_name ,
    phone_number ,
    delivery_remark ,
    acquired_point ,
    delivery_slip_no ,
    shipping_charge ,
    shipping_charge_tax_type ,
    shipping_charge_tax_group_code ,
    shipping_charge_tax_no ,
    shipping_charge_tax_rate ,
    shipping_charge_tax ,
    delivery_type_no ,
    shipping_method ,
    delivery_type_name ,
    TO_CHAR(delivery_appointed_date, 'YYYY-MM-DD') as delivery_appointed_date ,
    delivery_appointed_time_start ,
    delivery_appointed_time_end ,
    TO_CHAR(arrival_date, 'YYYY-MM-DD') as arrival_date ,
    arrival_time_start ,
    arrival_time_end ,
    fixed_sales_status ,
    shipping_status ,
    TO_CHAR(shipping_direct_date, 'YYYY-MM-DD') as shipping_direct_date ,
    TO_CHAR(shipping_date, 'YYYY-MM-DD') as shipping_date ,
    original_shipping_no ,
    TO_CHAR(return_item_date, 'YYYY-MM-DD') as return_item_date ,
    return_item_type ,
    shipping_area ,
    delivery_note_flg ,
    include_flg ,
    delivery_memo ,
    shipping_bill_price ,
    shipping_dokon_shiji_code ,
    o_name_disp_kbn ,
    member_stage ,
    possession_point ,
    TO_CHAR(sales_recording_date, 'YYYY-MM-DD') as sales_recording_date ,
    prod_pack_type ,
    shipping_method_kbn ,
    box_code ,
    delivery_note_message ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    shipping_header
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;