SELECT
    MDM_INTEGRATION_MANAGEMENT_CD ,
    PRODUCT_PICTURE_ID ,
    PRODUCT_NO ,
    MAIL_ORDER_PRODUCT_CD ,
    STORE_SALES_PRODUCT_CD ,
    WAREHOUSE_MANAGEMENT_CD ,
    <PERSON><PERSON> ,
    <PERSON><PERSON>_ISSUE_FLG ,
    MAIN_PRODUCT_NO ,
    CORE_PRODUCT_NAME ,
    WEB_PRODUCT_NAME ,
    PRODUCT_NAME ,
    REGISTRATION_NAME ,
    LAW_CAT_CD ,
    PRODUCT_SEGMENT ,
    BUSINESS_SEGMENT ,
    PRODUCT_CAT ,
    PRODUCT_SERIES ,
    TO_CHAR(SALE_START_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALE_START_DATE ,
    PERIOD_SET_SALES_CHANNEL_1 ,
    TO_CHAR(SALES_CHANNEL_1_SALE_START_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALES_CHANNEL_1_SALE_START_DATE ,
    TO_CHAR(SALES_CHANNEL_1_SALE_END_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALES_CHANNEL_1_SALE_END_DATE ,
    PERIOD_SET_SALES_CHANNEL_2 ,
    TO_CHAR(SALES_CHANNEL_2_SALE_START_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALES_CHANNEL_2_SALE_START_DATE ,
    TO_CHAR(SALES_CHANNEL_2_SALE_END_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALES_CHANNEL_2_SALE_END_DATE ,
    PERIOD_SET_SALES_CHANNEL_3 ,
    TO_CHAR(SALES_CHANNEL_3_SALE_START_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALES_CHANNEL_3_SALE_START_DATE ,
    TO_CHAR(SALES_CHANNEL_3_SALE_END_DATE, 'YYYY-MM-DD HH24:MI:SS') as SALES_CHANNEL_3_SALE_END_DATE ,
    SALE_STATUS ,
    LGROUP ,
    MGROUP ,
    SGROUP ,
    DGROUP ,
    PRODUCT_TYPE ,
    CORE_DEPARTMENT ,
    ACCOUNTIN_PATTERN_GB ,
    MATERIAL ,
    PREFERENTIAL_PRODUCT_FLG ,
    SET_PRODUCT_FLG ,
    SET_COMPOSITION_FLG ,
    COMPANY_SALES_BUY_FLG ,
    EMPRATE_PMS_FLG ,
    AGE_LIMIT_CD ,
    STORE_PO_GB ,
    WEB ,
    CALLCENTER ,
    BEFORE_RENEWAL_PRODUCT_NO ,
    DEP ,
    REPRESENTATIVE_PRODUCT_CD ,
    ORDER_PER_ORDER_MAX ,
    BUTTOBI_SUBSC_BUNDLE_YN ,
    RETURN_YN ,
    EXCH_YN ,
    LOT_MANAGEMENT_TARGET_PRODUCT ,
    REDUCTION_BASE ,
    DEPTH ,
    WIDTH ,
    HEIGHT ,
    TRADE_CNT ,
    WEIGHT ,
    OUTERBOX_DEPTH ,
    OUTERBOX_WIDTH ,
    OUTERBOX_HEIGHT ,
    OUTERBOX_WEIGHT ,
    CASE_PER_INCLUDE_CNT ,
    INSERTION_DEPTH ,
    INSERTION_WIDTH ,
    INSERTION_HEIGHT ,
    PALETTE_STACK_CNT_FACE ,
    PALETTE_STACK_CNT_LAVEL ,
    CONTENTS ,
    NEKOPOSU_VOLUME_RATE ,
    OUTSIDE_HOME_VOLUME_RATE ,
    COLOR_NAME ,
    COLOR_CD ,
    ORIGINAL_COLOR_CD ,
    SIZE_NAME ,
    SIZE_CD ,
    SHAPE_NAME ,
    SHAPE_CD ,
    SEASON ,
    UNIT_CD ,
    BUY_SEND_RESERVATION_FLG ,
    DELIVERY_NOTICE_UNDISPLAY_FLG ,
    AIRMAIL_RACK_YN ,
    MAIL_DELIVERY_FLG ,
    OUTSIDE_HOME_RECEIVE_SERVICE_FLG ,
    OUT_INDICATE_WAREHOUSE ,
    WAREHOUSE_ASSEMBLY_SET_PRODUCT_FLG ,
    SAGAWA_YN_FLG ,
    FOLDING_FLG ,
    VENDER ,
    USE_POINT_CNT ,
    COMPOSITION_OMS_LINK_FLG ,
    MDM_INTEGRATION_MANAGEMENT_CD_nk ,
    TO_CHAR(PMS_I_YMD, 'YYYY-MM-DD HH24:MI:SS') as INSERT_DATE ,
    PMS_I_USR as INSERT_ID ,
    TO_CHAR(PMS_U_YMD, 'YYYY-MM-DD HH24:MI:SS') as MODIFY_DATE ,
    PMS_U_USR as MODIFY_ID 
FROM
    mdm.product_linkage
