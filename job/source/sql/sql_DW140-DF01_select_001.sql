SELECT
    MDM_INTEGRATION_MANAGEMENT_CD ,
    TAX_EXC ,
    TAX_INC ,
    TAX ,
    TAX_RATE ,
    TO_CHAR(APPLY_START_DATE, 'YYYY/MM/DD HH24:MI:SS') as APPLY_START_DATE ,
    MDM_INTEGRATION_MANAGEMENT_CD_nk ,
    TO_CHAR(PMS_I_YMD, 'YYYY-MM-DD HH24:MI:SS') as INSERT_DATE ,
    PMS_I_USR as INSERT_ID ,
    TO_CHAR(PMS_U_YMD, 'YYYY-MM-DD HH24:MI:SS') as MODIFY_DATE ,
    PMS_U_USR as MODIFY_ID 
FROM
    mdm.period_price_linkage
