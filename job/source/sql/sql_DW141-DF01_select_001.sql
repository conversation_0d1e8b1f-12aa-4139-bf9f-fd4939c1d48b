SELECT
    shop_cd ,
    register_num ,
    TO_CHAR(business_date, 'YYYY-MM-DD') as business_date ,
    receipt_num ,
    line_num ,
    target_line_num ,
    discount_cd ,
    discount_kind ,
    disc_rate ,
    minus_amount ,
    tax_inclusive ,
    TO_CHAR(ins_biz_date, 'YYYY-MM-DD') as ins_biz_date ,
    TO_CHAR(upd_biz_date, 'YYYY-MM-DD') as upd_biz_date ,
    TO_CHAR(ins_date, 'YYYY-MM-DD HH24:MI:SS') as ins_date ,
    TO_CHAR(upd_date, 'YYYY-MM-DD HH24:MI:SS') as upd_date ,
    ins_user_id ,
    upd_user_id ,
    ins_pgm_id ,
    upd_pgm_id 
FROM
    pos_discount_relation
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp) and
    d_updated_datetime <=   :diff_base_timestamp