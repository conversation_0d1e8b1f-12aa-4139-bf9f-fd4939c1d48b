WITH source_data AS (
    SELECT shop_cd,
    register_num,
    business_date,
    receipt_num,
    line_num,
    target_line_num,
    discount_cd,
    discount_kind,
    disc_rate,
    minus_amount,
    tax_inclusive,
    ins_biz_date,
    upd_biz_date,
    ins_date,
    upd_date,
    ins_user_id,
    upd_user_id,
    ins_pgm_id,
    upd_pgm_id
    FROM pos_discount_relation_work
)
INSERT INTO pos_discount_relation (
        shop_cd,
        register_num,
        business_date,
        receipt_num,
        line_num,
        target_line_num,
        discount_cd,
        discount_kind,
        disc_rate,
        minus_amount,
        tax_inclusive,
        ins_biz_date,
        upd_biz_date,
        ins_date,
        upd_date,
        ins_user_id,
        upd_user_id,
        ins_pgm_id,
        upd_pgm_id,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW141-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW141-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (shop_cd, register_num, business_date, receipt_num, line_num, target_line_num) DO
UPDATE SET
    shop_cd = EXCLUDED.shop_cd,
    register_num = EXCLUDED.register_num,
    business_date = EXCLUDED.business_date,
    receipt_num = EXCLUDED.receipt_num,
    line_num = EXCLUDED.line_num,
    target_line_num = EXCLUDED.target_line_num,
    discount_cd = EXCLUDED.discount_cd,
    discount_kind = EXCLUDED.discount_kind,
    disc_rate = EXCLUDED.disc_rate,
    minus_amount = EXCLUDED.minus_amount,
    tax_inclusive = EXCLUDED.tax_inclusive,
    ins_biz_date = EXCLUDED.ins_biz_date,
    upd_biz_date = EXCLUDED.upd_biz_date,
    ins_date = EXCLUDED.ins_date,
    upd_date = EXCLUDED.upd_date,
    ins_user_id = EXCLUDED.ins_user_id,
    upd_user_id = EXCLUDED.upd_user_id,
    ins_pgm_id = EXCLUDED.ins_pgm_id,
    upd_pgm_id = EXCLUDED.upd_pgm_id,
    d_updated_user = 'JN_DW141-DF01_001',
    d_updated_datetime =   NOW(),
    d_version = pos_discount_relation.d_version + 1 ;
