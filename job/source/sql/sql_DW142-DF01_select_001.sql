SELECT
    business_year_month ,
    shop_cd ,
    item_cd ,
    brand_cd ,
    proper_item_kind ,
    retail_price ,
    seling_price ,
    good_stock_count ,
    defective_stock_count ,
    pending_stock_count ,
    reserving_stock_count ,
    take_out_stock_count ,
    shipment_stock_count ,
    reservation_stock_count ,
    reserve_count1 ,
    reserve_count2 ,
    reserve_count3 ,
    reserve_count4 ,
    reserve_count5 ,
    TO_CHAR(ins_biz_date, 'YYYY-MM-DD') as ins_biz_date ,
    TO_CHAR(upd_biz_date, 'YYYY-MM-DD') as upd_biz_date ,
    TO_CHAR(ins_date, 'YYYY-MM-DD HH24:MI:SS') as ins_date ,
    TO_CHAR(upd_date, 'YYYY-MM-DD HH24:MI:SS') as upd_date ,
    ins_user_id ,
    upd_user_id ,
    ins_pgm_id ,
    upd_pgm_id 
FROM
    monthly_stock
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp) and
    d_updated_datetime <=   :diff_base_timestamp