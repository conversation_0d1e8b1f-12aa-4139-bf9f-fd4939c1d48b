WITH source_data AS (
    SELECT business_year_month,
    shop_cd,
    item_cd,
    brand_cd,
    proper_item_kind,
    retail_price,
    seling_price,
    good_stock_count,
    defective_stock_count,
    pending_stock_count,
    reserving_stock_count,
    take_out_stock_count,
    shipment_stock_count,
    reservation_stock_count,
    reserve_count1,
    reserve_count2,
    reserve_count3,
    reserve_count4,
    reserve_count5,
    ins_biz_date,
    upd_biz_date,
    ins_date,
    upd_date,
    ins_user_id,
    upd_user_id,
    ins_pgm_id,
    upd_pgm_id
    FROM monthly_stock_work
)
INSERT INTO monthly_stock (
        business_year_month,
        shop_cd,
        item_cd,
        brand_cd,
        proper_item_kind,
        retail_price,
        seling_price,
        good_stock_count,
        defective_stock_count,
        pending_stock_count,
        reserving_stock_count,
        take_out_stock_count,
        shipment_stock_count,
        reservation_stock_count,
        reserve_count1,
        reserve_count2,
        reserve_count3,
        reserve_count4,
        reserve_count5,
        ins_biz_date,
        upd_biz_date,
        ins_date,
        upd_date,
        ins_user_id,
        upd_user_id,
        ins_pgm_id,
        upd_pgm_id,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW142-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW142-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (business_year_month, shop_cd, item_cd) DO
UPDATE SET
    business_year_month = EXCLUDED.business_year_month,
    shop_cd = EXCLUDED.shop_cd,
    item_cd = EXCLUDED.item_cd,
    brand_cd = EXCLUDED.brand_cd,
    proper_item_kind = EXCLUDED.proper_item_kind,
    retail_price = EXCLUDED.retail_price,
    seling_price = EXCLUDED.seling_price,
    good_stock_count = EXCLUDED.good_stock_count,
    defective_stock_count = EXCLUDED.defective_stock_count,
    pending_stock_count = EXCLUDED.pending_stock_count,
    reserving_stock_count = EXCLUDED.reserving_stock_count,
    take_out_stock_count = EXCLUDED.take_out_stock_count,
    shipment_stock_count = EXCLUDED.shipment_stock_count,
    reservation_stock_count = EXCLUDED.reservation_stock_count,
    reserve_count1 = EXCLUDED.reserve_count1,
    reserve_count2 = EXCLUDED.reserve_count2,
    reserve_count3 = EXCLUDED.reserve_count3,
    reserve_count4 = EXCLUDED.reserve_count4,
    reserve_count5 = EXCLUDED.reserve_count5,
    ins_biz_date = EXCLUDED.ins_biz_date,
    upd_biz_date = EXCLUDED.upd_biz_date,
    ins_date = EXCLUDED.ins_date,
    upd_date = EXCLUDED.upd_date,
    ins_user_id = EXCLUDED.ins_user_id,
    upd_user_id = EXCLUDED.upd_user_id,
    ins_pgm_id = EXCLUDED.ins_pgm_id,
    upd_pgm_id = EXCLUDED.upd_pgm_id,
    d_updated_user = 'JN_DW142-DF01_001',
    d_updated_datetime =   NOW(),
    d_version = monthly_stock.d_version + 1 ;
