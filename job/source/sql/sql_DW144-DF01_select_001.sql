SELECT
    campaign_instructions_code ,
    campaign_instructions_name ,
    campaign_type ,
    delete_flg ,
    campaign_priority ,
    campaign_applied_scope ,
    campaign_use_limit ,
    oneshot_order_limit ,
    campaign_quantity_limit ,
    TO_CHAR(campaign_start_date,'YYYY-MM-DD') as campaign_start_date ,
    TO_CHAR(campaign_end_date,'YYYY-MM-DD') as campaign_end_date ,
    present_use_flg ,
    campaign_customer_flg ,
    campaign_combi_limit_flg ,
    permanent_campaign_flg ,
    baitai_code ,
    campaign_description ,
    change_user_code ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    campaign_instructions
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;