SELECT
    coupon_management_code ,
    coupon_code ,
    coupon_name ,
    coupon_invalid_flag ,
    coupon_type ,
    coupon_issue_type ,
    coupon_use_limit ,
    coupon_use_purchase_price ,
    coupon_discount_type ,
    coupon_discount_rate ,
    coupon_discount_price ,
    TO_CHAR(coupon_start_datetime,'YYYY-MM-DD HH24:MI:SS') as coupon_start_datetime ,
    TO_CHAR(coupon_end_datetime,'YYYY-MM-DD HH24:MI:SS') as coupon_end_datetime ,
    coupon_limit_display_period ,
    coupon_limit_display ,
    coupon_description ,
    coupon_message ,
    coupon_kbn ,
    coupon_post_in_charge ,
    coupon_commodity_flag ,
    marketing_channel_list ,
    goods_group ,
    commodity_category_code ,
    commodity_series ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    coupon
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;