SELECT
    campaign_instructions_code ,
    campaign_group_no ,
    joken_type ,
    campaign_joken_no ,
    joken_kind1 ,
    joken_kind2 ,
    joken ,
    joken_min ,
    joken_max ,
    regular_kaiji ,
    joken_month_num ,
    commodity_name ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime, 'YYYY-MM-DD HH24:MI:SS') as created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime, 'YYYY-MM-DD HH24:MI:SS') as updated_datetime 
FROM
    campaign_order
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                updated_datetime <= :diff_base_timestamp
    else true
    end;