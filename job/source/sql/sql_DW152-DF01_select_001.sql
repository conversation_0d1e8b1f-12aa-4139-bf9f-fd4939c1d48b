SELECT
    shop_cd ,
    register_num ,
    TO_CHAR(business_date, 'YYYY-MM-DD') as business_date ,
    receipt_num ,
    chit_num ,
    TO_CHAR(system_datetime, 'YYYY-MM-DD HH24:MI:SS') as system_datetime ,
    open_count ,
    is_void ,
    check_kind ,
    return_kind ,
    sub_check_kind ,
    sales_group_cd ,
    deposit_kind ,
    operator_cd ,
    operator_name ,
    sales_man_cd ,
    sales_man_name ,
    return_employee_cd ,
    return_employee_name ,
    void_employee_cd ,
    void_employee_name ,
    staff_sale_employee_cd ,
    staff_sale_employee_name ,
    customer_cd ,
    customer_layer_cd ,
    customer_layer_cd2 ,
    purchase_motive_cd ,
    return_reason_cd ,
    void_reason_cd ,
    net_sales_amount_of_outside_tax ,
    net_sales_amount_of_inside_tax ,
    net_sales_amount_of_tax_free ,
    net_sales_outside_tax ,
    net_sales_inside_tax ,
    net_sales_quantity ,
    outside_sales_amount_of_outside_tax ,
    outside_sales_amount_of_inside_tax ,
    outside_sales_amount_of_tax_free ,
    outside_sales_outside_tax ,
    outside_sales_inside_tax ,
    outside_sales_quantity ,
    total_amount ,
    discount_amount ,
    discount_tax_inclusive ,
    is_revenue_stamp ,
    order_line_count ,
    pay_line_count ,
    is_total_display ,
    is_reduced_tax_rate_trade ,
    is_tax_free ,
    customers_num ,
    TO_CHAR(deliver_date, 'YYYY-MM-DD HH24:MI:SS') as deliver_date ,
    sale_attribute_cd1 ,
    sale_attribute_cd2 ,
    sale_attribute_cd3 ,
    sale_attribute_cd4 ,
    campaign_no ,
    TO_CHAR(closing_date, 'YYYY-MM-DD') as closing_date ,
    TO_CHAR(return_date, 'YYYY-MM-DD') as return_date ,
    order_number ,
    employee_meal ,
    employee_code ,
    table_no ,
    TO_CHAR(acceptance_time, 'YYYY-MM-DD HH24:MI:SS') as acceptance_time ,
    TO_CHAR(menu_cook_cmp_time, 'YYYY-MM-DD HH24:MI:SS') as menu_cook_cmp_time ,
    TO_CHAR(menu_offer_cmp_time, 'YYYY-MM-DD HH24:MI:SS') as menu_offer_cmp_time ,
    service_charge_amount_outside_tax ,
    service_charge_amount_inside_tax ,
    service_charge_tax_exclusive ,
    service_charge_tax_inclusive ,
    service_charge_amount1 ,
    service_charge_amount2 ,
    service_charge_minus_amount ,
    service_charge_minus_tax_inclusive ,
    service_charge_target ,
    service_charge_button ,
    service_charge1_button ,
    service_charge2_button ,
    eat_in_amount ,
    takeout_amount ,
    vein_employee_cd ,
    is_vein_authentication ,
    out_calc_flg ,
    sale_goods_flg ,
    point_linkage_kind ,
    numeric_reserve1 ,
    numeric_reserve2 ,
    numeric_reserve3 ,
    numeric_reserve4 ,
    numeric_reserve5 ,
    numeric_reserve6 ,
    numeric_reserve7 ,
    numeric_reserve8 ,
    numeric_reserve9 ,
    numeric_reserve10 ,
    string_reserve1 ,
    string_reserve2 ,
    string_reserve3 ,
    string_reserve4 ,
    string_reserve5 ,
    string_reserve6 ,
    string_reserve7 ,
    string_reserve8 ,
    string_reserve9 ,
    string_reserve10 
FROM
    sales_info_alignment_header
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '1 day') and
    d_updated_datetime <= :diff_base_timestamp