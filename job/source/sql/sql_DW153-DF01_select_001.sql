SELECT
    shop_cd ,
    register_num ,
    TO_CHAR(business_date, 'YYYY-MM-DD') as business_date ,
    receipt_num ,
    line_num ,
    line_kind ,
    is_in_store_marking ,
    brand_cd ,
    dept_cd ,
    class_cd ,
    sub_class_cd ,
    item_cd ,
    item_name ,
    dept_group_cd ,
    parent_item_cd ,
    jan_cd ,
    item_num ,
    color_cd ,
    size_cd ,
    year_cd ,
    season_cd ,
    attribute_cd1 ,
    attribute_cd2 ,
    attribute_cd3 ,
    attribute_cd4 ,
    attribute_cd5 ,
    scan_bar_cd1 ,
    scan_bar_cd2 ,
    discount_cd ,
    bmplan_cd ,
    coupon_num ,
    is_include_sales ,
    item_kind ,
    tax_kind ,
    tax_rate ,
    is_reduced_tax_rate ,
    is_proper_item ,
    is_change_price ,
    master_unit_price ,
    body_price ,
    fixed_price ,
    unit_price ,
    quantity ,
    amount ,
    tax_inclusive ,
    tax_outside ,
    tax ,
    disc_rate ,
    line_minus_amount ,
    line_minus_tax_inclusive ,
    bmset_minus_amount ,
    bmset_minus_tax_inclusive ,
    point_minus_amount ,
    point_minus_tax_inclusive ,
    coupon_minus_amount ,
    coupon_minus_tax_inclusive ,
    sub_total_minus_amount ,
    sub_total_minus_tax_inclusive ,
    before_disc_tax_inclusive ,
    sales_man_cd ,
    stock_kind ,
    is_inventory_counted ,
    is_promotion_ticket ,
    promotion_ticket_bar_cd ,
    is_promotion_ticket_allow_combination ,
    oes_item_flag ,
    oes_slip_no ,
    oes_slip_sub_no ,
    grand_classification ,
    menu_classification ,
    oes_line_num ,
    oes_quantity ,
    oes_minus ,
    pos_minus ,
    takeout_flag ,
    service_charge_minus_amount ,
    service_charge_minus_tax_inclusive ,
    service_charge_flag ,
    service_charge1_flag ,
    service_charge2_flag ,
    service_charge1_manually_flag ,
    service_charge2_manually_flag ,
    oes_service_charge1 ,
    oes_service_charge2 ,
    cooking_directions_time ,
    cooking_complete_time ,
    offer_complete_time ,
    TO_CHAR(acceptance_time, 'YYYY-MM-DD HH24:MI:SS') as acceptance_time ,
    TO_CHAR(menu_cook_cmp_time, 'YYYY-MM-DD HH24:MI:SS') as menu_cook_cmp_time ,
    TO_CHAR(menu_offer_cmp_time, 'YYYY-MM-DD HH24:MI:SS') as menu_offer_cmp_time ,
    disc_amount_limit ,
    is_follow_disc ,
    is_allow_credit ,
    is_employee_acnt_recv ,
    employee_cd ,
    sub_menu_kind ,
    grand_menu_code ,
    grand_menu_index ,
    select_kind ,
    is_quantity_count ,
    is_order_entry ,
    modifier_kind ,
    return_target_line_num ,
    numeric_reserve1 ,
    numeric_reserve2 ,
    numeric_reserve3 ,
    numeric_reserve4 ,
    numeric_reserve5 ,
    numeric_reserve6 ,
    numeric_reserve7 ,
    numeric_reserve8 ,
    numeric_reserve9 ,
    numeric_reserve10 ,
    string_reserve1 ,
    string_reserve2 ,
    string_reserve3 ,
    string_reserve4 ,
    string_reserve5 ,
    string_reserve6 ,
    string_reserve7 ,
    string_reserve8 ,
    string_reserve9 ,
    string_reserve10 
FROM
    sales_info_alignment_detail
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '1 day') and
    d_updated_datetime <= :diff_base_timestamp