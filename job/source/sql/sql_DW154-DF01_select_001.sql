SELECT
    shop_cd ,
    register_num ,
    TO_CHAR(business_date, 'YYYY-MM-DD') as business_date ,
    receipt_num ,
    line_num ,
    pay_kind ,
    pay_cd ,
    credit_linkage_kind ,
    claim_kind ,
    pay_amount ,
    change_amount ,
    surplus_amount ,
    coupon_num ,
    ticket_quantity ,
    ticket_bar_cd ,
    is_worn_cash ,
    is_emoney_cash_back ,
    numeric_reserve1 ,
    numeric_reserve2 ,
    numeric_reserve3 ,
    numeric_reserve4 ,
    numeric_reserve5 ,
    numeric_reserve6 ,
    numeric_reserve7 ,
    numeric_reserve8 ,
    numeric_reserve9 ,
    numeric_reserve10 ,
    string_reserve1 ,
    string_reserve2 ,
    string_reserve3 ,
    string_reserve4 ,
    string_reserve5 ,
    string_reserve6 ,
    string_reserve7 ,
    string_reserve8 ,
    string_reserve9 ,
    string_reserve10 
FROM
    sales_info_alignment_payment
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '1 day') and
    d_updated_datetime <= :diff_base_timestamp