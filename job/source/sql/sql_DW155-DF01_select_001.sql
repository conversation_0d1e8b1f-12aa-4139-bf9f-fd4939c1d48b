SELECT
    shop_cd ,
    group_cd ,
    area_cd ,
    district_cd ,
    shop_kind ,
    shop_name_full ,
    shop_name_half ,
    shop_name_short ,
    shop_name_english ,
    zip_cd ,
    prefecture_cd ,
    address1 ,
    address2 ,
    address3 ,
    address4 ,
    tel_num ,
    fax_num ,
    tax_inclusive_round_kind ,
    tax_exclusive_round_kind ,
    condition_kind ,
    condition_detail_kind ,
    channel_kind ,
    is_stock_control ,
    TO_CHAR(begin_date, 'YYYY-MM-DD') as begin_date ,
    TO_CHAR(end_date, 'YYYY-MM-DD') as end_date ,
    shop_floor_space ,
    is_abandon_price_change ,
    is_abandon_stock_transfer ,
    form_management_kind ,
    is_shop_terminal ,
    allow_transfer_group_cd ,
    main_brand_cd ,
    disc_round_position ,
    disc_round_kind ,
    is_emp_salse_place ,
    is_move_operation ,
    is_sales_register ,
    is_stock_display ,
    brock_cd ,
    pricechange_disc_round_position ,
    pricechange_disc_round_kind ,
    numeric_reserve1 ,
    numeric_reserve2 ,
    numeric_reserve3 ,
    numeric_reserve4 ,
    numeric_reserve5 ,
    string_reserve1 ,
    string_reserve2 ,
    string_reserve3 ,
    string_reserve4 ,
    string_reserve5 ,
    is_deleted ,
    spare_numeric_reserve1 ,
    spare_numeric_reserve2 ,
    spare_numeric_reserve3 ,
    apare_numeric_reserve4 ,
    spare_numeric_reserve5 ,
    spare_string_reserve1 ,
    spare_string_reserve2 ,
    spare_string_reserve3 ,
    spare_string_reserve4 ,
    spare_string_reserve5 ,
    spare_string_reserve6 ,
    spare_string_reserve7 
FROM
    shop_mst
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '1 day') and
    d_updated_datetime <= :diff_base_timestamp