SELECT
    corp_cd ,
    stock_io_type ,
    TO_CHAR(move_date,'YYYY-MM-DD') AS move_date ,
    center_code ,
    stock_group_code ,
    shop_code_swh ,
    sh_control_number ,
    move_quantity ,
    move_center_code ,
    move_stock_group_code ,
    move_shop_code_swh ,
    upd_user_id ,
    TO_CHAR(data_date,'YYYY-MM-DD HH24:MI:SS') AS data_date
FROM
    seruring_move
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp) and
    d_updated_datetime <=   :diff_base_timestamp
