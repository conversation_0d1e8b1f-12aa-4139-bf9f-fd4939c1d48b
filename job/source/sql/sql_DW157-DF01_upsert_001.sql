WITH source_data AS (
    SELECT corp_cd,
        move_date,
        stock_io_type,
        center_code,
        stock_group_code,
        shop_code_swh,
        sh_control_number,
        move_quantity,
        move_center_code,
        move_stock_group_code,
        move_shop_code_swh,
        upd_user_id,
        data_date
    FROM seruring_move_work
)
INSERT INTO seruring_move (
        corp_cd,
        move_date,
        stock_io_type,
        center_code,
        stock_group_code,
        shop_code_swh,
        sh_control_number,
        move_quantity,
        move_center_code,
        move_stock_group_code,
        move_shop_code_swh,
        upd_user_id,
        data_date,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW157-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW157-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ;
