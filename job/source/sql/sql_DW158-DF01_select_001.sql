SELECT
    corp_cd ,
    TO_CHAR(data_date,'YYYY-MM-DD HH24:MI:SS') AS data_date ,
    center_code ,
    stock_group_code ,
    shipping_code ,
    product_cd ,
    first_day_stock_count ,
    arrival_quantity ,
    shipping_quantity ,
    arrival_quantity_irregular ,
    shipping_quantity_irregular ,
    carryover_stock_count 
FROM
    stock_list
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp) and
    d_updated_datetime <=   :diff_base_timestamp