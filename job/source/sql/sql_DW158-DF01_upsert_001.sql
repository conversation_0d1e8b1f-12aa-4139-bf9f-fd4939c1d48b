WITH source_data AS (
    SELECT corp_cd,
    data_date,
    center_code,
    stock_group_code,
    shipping_code,
    product_cd,
    first_day_stock_count,
    arrival_quantity,
    shipping_quantity,
    arrival_quantity_irregular,
    shipping_quantity_irregular,
    carryover_stock_count
    FROM stock_list_work
)
INSERT INTO stock_list (
        corp_cd,
        data_date,
        center_code,
        stock_group_code,
        shipping_code,
        product_cd,
        first_day_stock_count,
        arrival_quantity,
        shipping_quantity,
        arrival_quantity_irregular,
        shipping_quantity_irregular,
        carryover_stock_count,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW158-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW158-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ON CONFLICT (corp_cd,data_date,center_code,stock_group_code,shipping_code,product_cd) DO
UPDATE SET
    corp_cd=EXCLUDED.corp_cd,
    data_date=EXCLUDED.data_date,
    center_code=EXCLUDED.center_code,
    stock_group_code=EXCLUDED.stock_group_code,
    shipping_code=EXCLUDED.shipping_code,
    product_cd=EXCLUDED.product_cd,
    first_day_stock_count=EXCLUDED.first_day_stock_count,
    arrival_quantity=EXCLUDED.arrival_quantity,
    shipping_quantity=EXCLUDED.shipping_quantity,
    arrival_quantity_irregular=EXCLUDED.arrival_quantity_irregular,
    shipping_quantity_irregular=EXCLUDED.shipping_quantity_irregular,
    carryover_stock_count=EXCLUDED.carryover_stock_count,
    d_updated_user = 'JN_DW158-DF01_001',
    d_updated_datetime =   NOW(),
    d_version = stock_list.d_version + 1 ;
