SELECT
    wms_stock_io_type ,
    TO_CHAR(slip_date,'YYYY-MM-DD') AS slip_date ,
    slip_no ,
    sh_control_number ,
    quantity ,
    stock_io_type ,
    center_code ,
    stock_type ,
    swh_cmns_target_shop ,
    center_code_partner ,
    stock_type_partner ,
    swh_counter ,
    receiving_shipping ,
    receiving_shipping_name ,
    reason_code_items ,
    reason_code_name ,
    reason_code_item
FROM
    stock_increase_decrease
WHERE
    d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '1 day') and
    d_updated_datetime <= :diff_base_timestamp
