SELECT
    shop_code ,
    commodity_code ,
    commodity_name ,
    commodity_type ,
    represent_sku_code ,
    represent_sku_unit_price ,
    stock_status_no ,
    stock_management_type ,
    age_limit_code ,
    commodity_tax_type ,
    tax_group_code ,
    short_description ,
    commodity_search_words ,
    prior_printing_description ,
    posterior_printing_description ,
    delivery_description ,
    TO_CHAR(sale_start_datetime,'YYYY-MM-DD HH24:MI:SS') AS sale_start_datetime ,
    TO_CHAR(sale_end_datetime,'YYYY-MM-DD HH24:MI:SS') AS sale_end_datetime ,
    TO_CHAR(discount_price_start_datetime,'YYYY-MM-DD HH24:MI:SS') AS discount_price_start_datetime ,
    TO_CHAR(discount_price_end_datetime,'YYYY-MM-DD HH24:MI:SS') AS discount_price_end_datetime ,
    TO_CHAR(reservation_start_datetime,'YYYY-MM-DD HH24:MI:SS') AS reservation_start_datetime ,
    TO_CHAR(reservation_end_datetime,'YYYY-MM-DD HH24:MI:SS') AS reservation_end_datetime ,
    TO_CHAR(prior_printing_start_date,'YYYY-MM-DD HH24:MI:SS') AS prior_printing_start_date ,
    TO_CHAR(prior_printing_end_date,'YYYY-MM-DD HH24:MI:SS') AS prior_printing_end_date ,
    TO_CHAR(posterior_printing_start_date,'YYYY-MM-DD HH24:MI:SS') AS posterior_printing_start_date ,
    TO_CHAR(posterior_printing_end_date,'YYYY-MM-DD HH24:MI:SS') AS posterior_printing_end_date ,
    delivery_type_no ,
    sales_method_type ,
    manufacturer_model_no ,
    link_url ,
    recommend_commodity_rank ,
    commodity_popular_rank ,
    commodity_standard1_name ,
    commodity_standard2_name ,
    commodity_point_rate ,
    TO_CHAR(commodity_point_start_datetime,'YYYY-MM-DD HH24:MI:SS') AS commodity_point_start_datetime ,
    TO_CHAR(commodity_point_end_datetime,'YYYY-MM-DD HH24:MI:SS') AS commodity_point_end_datetime ,
    sale_flg ,
    noshi_effective_flg ,
    arrival_goods_flg ,
    oneshot_order_limit ,
    standard_image_type ,
    purchasing_confirm_flg_pc ,
    purchasing_confirm_flg_sp ,
    commodity_kind ,
    keihi_hurikae_target_flg ,
    charge_user_code ,
    commodity_remark ,
    channel_cc_sale_flg ,
    channel_ec_sale_flg ,
    shipping_charge_target_flg ,
    first_purchase_limit_flg ,
    purchase_hold_flg ,
    commodity_exclude_flg ,
    commodity_subsubcategory_code ,
    pack_calc_pattern ,
    pad_type ,
    fall_down_flg ,
    height ,
    width ,
    deepness ,
    weight ,
    tracking_out_flg ,
    mdm_management_code ,
    commodity_segment ,
    business_segment ,
    commodity_group ,
    commodity_series ,
    core_department ,
    accounting_pattern_type ,
    return_enabled_flg ,
    exchange_enabled_flg ,
    exterior_box_weight ,
    nekoposu_volume_rate ,
    warehouse_assembly_flg ,
    mail_delivery_flg ,
    before_renewal_commodity_code ,
    preorder_enable_days ,
    main_product_no ,
    product_no ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') AS created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') AS updated_datetime
FROM
    commodity_header
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;
