SELECT
    shop_code ,
    sku_code ,
    commodity_code ,
    unit_price ,
    discount_price ,
    reservation_price ,
    jan_code ,
    standard_detail1_name ,
    standard_detail2_name ,
    hinban_code ,
    hinban_kind ,
    member_price_applied_flg ,
    member_price_discount_rate ,
    member_price ,
    air_transport_flg ,
    commodity_prod_pack_type ,
    delivery_note_no_disp_flg ,
    reduction_point ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') AS created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') AS updated_datetime 
FROM
    commodity_detail
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;