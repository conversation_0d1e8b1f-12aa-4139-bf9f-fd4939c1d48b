SELECT
    shop_code ,
    regular_sale_code ,
    regular_sale_composition_no ,
    regular_sale_composition_name ,
    regular_order_count_min_limit ,
    regular_order_count_max_limit ,
    regular_order_count_interval ,
    retail_price ,
    regular_sale_commodity_point ,
    display_order ,
    orm_rowid ,
    created_user ,
    TO_CHAR(created_datetime,'YYYY-MM-DD HH24:MI:SS') AS created_datetime ,
    updated_user ,
    TO_CHAR(updated_datetime,'YYYY-MM-DD HH24:MI:SS') AS updated_datetime 
FROM
    regular_sale_composition
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;