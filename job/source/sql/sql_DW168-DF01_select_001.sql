SELECT
    shop_code ,
    regular_sale_code ,
    regular_sale_composition_no ,
    sku_code ,
    commodity_code ,
    display_order ,
    regular_sale_commodity_type ,
    regular_sale_commodity_point ,
    difference_price ,
    orm_rowid ,
    created_user ,
    created_datetime ,
    updated_user ,
    updated_datetime 
FROM
    regular_sale_commodity
WHERE
    case
    when to_timestamp('1900/01/01 00:00:00','YYYY/MM/DD HH24:MI:SS') <>  :sync_datetime 
          then d_updated_datetime >=    date_trunc('day',  :diff_base_timestamp  -INTERVAL '2 day') and
                d_updated_datetime <= :diff_base_timestamp
    else true
    end;