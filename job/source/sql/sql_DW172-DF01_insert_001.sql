WITH source_data AS (
    SELECT stock_arrival_date,
    warehouse_cd,
    po_no,
    warehouse_management_no,
    order_count,
    agent_cd,
    order_registrant_name,
    comment_code
    FROM arrival_schedule_other_work
)
INSERT INTO arrival_schedule_other (
        stock_arrival_date,
        warehouse_cd,
        po_no,
        warehouse_management_no,
        order_count,
        agent_cd,
        order_registrant_name,
        comment_code,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT s.*,
    'JN_DW172-DF01_001',
    -- デ連登録ユーザ（ジョブネットID）
    NOW(),
    -- デ連登録日時
    'JN_DW172-DF01_001',
    -- デ連更新ユーザ（ジョブネットID）
    NOW(),
    -- デ連更新日時
    1 -- デ連バージョン（新規は1）
FROM source_data s ;
