INSERT INTO arrival_schedule_other (
        stock_arrival_date,
        warehouse_cd,
        po_no,
        warehouse_management_no,
        order_count,
        agent_cd,
        order_registrant_name,
        comment_code,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT stock_arrival_date,
    warehouse_cd,
    po_no,
    warehouse_management_no,
    order_count,
    agent_cd,
    order_registrant_name,
    comment_code,
    'JN_IS001-FF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_IS001-FF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM arrival_schedule_other_work ON CONFLICT (po_no, warehouse_management_no) DO
UPDATE
SET stock_arrival_date = EXCLUDED.stock_arrival_date,
    warehouse_cd = EXCLUDED.warehouse_cd,
    po_no = EXCLUDED.po_no,
    warehouse_management_no = EXCLUDED.warehouse_management_no,
    order_count = EXCLUDED.order_count,
    agent_cd = EXCLUDED.agent_cd,
    order_registrant_name = EXCLUDED.order_registrant_name,
    comment_code = EXCLUDED.comment_code,
    d_updated_user = 'JN_IS001-FF01_001',
    d_updated_datetime = NOW(),
    d_version = arrival_schedule_other.d_version + 1;
