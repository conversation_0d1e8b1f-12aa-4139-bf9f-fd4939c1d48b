INSERT INTO
    arrival_achievements (
        warehouse_cd,
        receiving_stock_date,
        receiving_stock_former_cd,
        receiving_stock_slip_no,
        receiving_stock_slip_row_no,
        po_no,
        product_cd,
        receiving_stock_cnt,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    warehouse_cd,
    receiving_stock_date,
    receiving_stock_former_cd,
    receiving_stock_slip_no,
    receiving_stock_slip_row_no,
    po_no,
    product_cd,
    receiving_stock_cnt,
    'JN_IS003-DF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_IS003-DF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM
    arrival_achievements_work ON CONFLICT (
        receiving_stock_slip_no,
        receiving_stock_slip_row_no
    ) DO
UPDATE
SET
    warehouse_cd = EXCLUDED.warehouse_cd,
    receiving_stock_date = EXCLUDED.receiving_stock_date,
    receiving_stock_former_cd = EXCLUDED.receiving_stock_former_cd,
    po_no = EXCLUDED.po_no,
    product_cd = EXCLUDED.product_cd,
    receiving_stock_cnt = EXCLUDED.receiving_stock_cnt,
    d_updated_user = 'JN_IS003-DF01_001',
    d_updated_datetime = NOW(),
    d_version = arrival_achievements.d_version + 1;