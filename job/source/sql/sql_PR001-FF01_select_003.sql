SELECT
    category_code,
    category_name_pc,
    category_name_sp,
    parent_category_code,
    path,
    depth,
    display_order,
    commodity_count,
    last_related_count_datetime,
    public_commodity_count,
    last_public_count_datetime,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    (SELECT
        CAST(LGROUP AS VARCHAR) AS category_code,
        LGROUP_NAME AS category_name_pc,
        '未使用' AS category_name_sp,
        '0' AS parent_category_code,
        '' AS path,
        '1' AS depth,
        '1' AS display_order,
        '' AS commodity_count,
        '' AS last_related_count_datetime,
        '' AS public_commodity_count,
        '' AS last_public_count_datetime,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        1 AS sort_no
    FROM
        mdm.lgroup
    WHERE
        pms_u_ymd > :sync_datetime AND pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        CONCAT(CAST(LGROUP AS VARCHAR),  CAST(MGROUP AS VARCHAR)) AS category_code,
        MGROUP_NAME AS category_name_pc,
        '未使用' AS category_name_sp,
        LGROUP AS parent_category_code,
        '' AS path,
        '2' AS depth,
        '1' AS display_order,
        '' AS commodity_count,
        '' AS last_related_count_datetime,
        '' AS public_commodity_count,
        '' AS last_public_count_datetime,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        2 AS sort_no
    FROM
        mdm.mgroup
    WHERE
        pms_u_ymd > :sync_datetime AND pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        CONCAT(CAST(LGROUP AS VARCHAR),  CAST(MGROUP AS VARCHAR),  CAST(SGROUP AS VARCHAR)) AS category_code,
        SGROUP_NAME AS category_name_pc,
        '未使用' AS category_name_sp,
        CONCAT(CAST(LGROUP AS VARCHAR),  CAST(MGROUP AS VARCHAR)) AS parent_category_code,
        '' AS path,
        '3' AS depth,
        '1' AS display_order,
        '' AS commodity_count,
        '' AS last_related_count_datetime,
        '' AS public_commodity_count,
        '' AS last_public_count_datetime,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        3 AS sort_no
    FROM
        mdm.sgroup
    WHERE
        pms_u_ymd > :sync_datetime AND pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        CONCAT(CAST(LGROUP AS VARCHAR),  CAST(MGROUP AS VARCHAR),  CAST(SGROUP AS VARCHAR),  CAST(DGROUP AS VARCHAR)) AS  category_code,
        DGROUP_NAME AS category_name_pc,
        '未使用' AS category_name_sp,
        CONCAT(CAST(LGROUP AS VARCHAR),  CAST(MGROUP AS VARCHAR),  CAST(SGROUP AS VARCHAR)) AS parent_category_code,
        '' AS path,
        '4' AS depth,
        '1' AS display_order,
        '' AS commodity_count,
        '' AS last_related_count_datetime,
        '' AS public_commodity_count,
        '' AS last_public_count_datetime,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        4 AS sort_no
    FROM
        mdm.dgroup
    WHERE
        pms_u_ymd > :sync_datetime AND pms_u_ymd <= :diff_base_timestamp
    )
ORDER BY
    sort_no;
