WITH combined_products AS (
    SELECT
        pl.MAIL_ORDER_PRODUCT_CD,
        pl.lgroup,
        pl.mgroup,
        pl.sgroup,
        pl.dgroup,
        pl.pms_u_ymd
    FROM
        mdm.product_linkage pl
    UNION
    SELECT
        pe.MAIL_ORDER_PRODUCT_CD,
        pe.lgroup,
        pe.mgroup,
        pe.sgroup,
        pe.dgroup,
        pe.pms_u_ymd
    FROM
        mdm.product_edit pe
    WHERE
        pe.COMPOSITION_OMS_LINK_FLG = '1'
        AND NOT EXISTS (
        SELECT 1
        FROM mdm.product_linkage pl_inner
        WHERE pl_inner.mdm_integration_management_cd = pe.mdm_integration_management_cd
    )
)

SELECT
    shop_code,
    category_code,
    commodity_code,
    category_search_path,
    search_category_code0,
    search_category_code1,
    search_category_code2,
    search_category_code3,
    search_category_code4,
    search_category_code5,
    search_category_code6,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    (SELECT
        '0000' AS shop_code,
        '0' AS category_code,
        combined_products.MAIL_ORDER_PRODUCT_CD AS commodity_code,
        '' AS category_search_path,
        '' AS search_category_code0,
        '' AS search_category_code1,
        '' AS search_category_code2,
        '' AS search_category_code3,
        '' AS search_category_code4,
        '' AS search_category_code5,
        '' AS search_category_code6,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        1 AS sort_no
    FROM
        combined_products
    WHERE
        combined_products.pms_u_ymd > :sync_datetime AND combined_products.pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        '0000' AS shop_code,
        lgroup.LGROUP AS category_code,
        combined_products.MAIL_ORDER_PRODUCT_CD AS commodity_code,
        '' AS category_search_path,
        '' AS search_category_code0,
        '' AS search_category_code1,
        '' AS search_category_code2,
        '' AS search_category_code3,
        '' AS search_category_code4,
        '' AS search_category_code5,
        '' AS search_category_code6,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        2 AS sort_no
    FROM
        combined_products
    JOIN
        mdm.lgroup
    ON
        lpad(combined_products.LGROUP,2,'0') = lgroup.LGROUP
        AND lgroup.pms_u_ymd <= :diff_base_timestamp
    WHERE
        combined_products.pms_u_ymd > :sync_datetime AND combined_products.pms_u_ymd <= :diff_base_timestamp
        OR
        lgroup.pms_u_ymd > :sync_datetime AND lgroup.pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        '0000' AS shop_code,
        CONCAT(mgroup.LGROUP, mgroup.MGROUP) AS category_code,
        cp.MAIL_ORDER_PRODUCT_CD AS commodity_code,
        '' AS category_search_path,
        '' AS search_category_code0,
        '' AS search_category_code1,
        '' AS search_category_code2,
        '' AS search_category_code3,
        '' AS search_category_code4,
        '' AS search_category_code5,
        '' AS search_category_code6,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        3 AS sort_no
    FROM
        combined_products cp
    JOIN
        mdm.mgroup
    ON
        CONCAT(lpad(cp.LGROUP,2,'0'), lpad(cp.MGROUP,2,'0'))  = CONCAT(mgroup.LGROUP, mgroup.MGROUP)
        AND mgroup.pms_u_ymd <= :diff_base_timestamp
    WHERE
        cp.pms_u_ymd > :sync_datetime AND cp.pms_u_ymd <= :diff_base_timestamp
        OR
        mgroup.pms_u_ymd > :sync_datetime AND mgroup.pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        '0000' AS shop_code,
        CONCAT(sgroup.LGROUP,  sgroup.MGROUP,  sgroup.SGROUP) AS category_code,
        cp.MAIL_ORDER_PRODUCT_CD AS commodity_code,
        '' AS category_search_path,
        '' AS search_category_code0,
        '' AS search_category_code1,
        '' AS search_category_code2,
        '' AS search_category_code3,
        '' AS search_category_code4,
        '' AS search_category_code5,
        '' AS search_category_code6,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        4 AS sort_no
    FROM
        combined_products cp
    JOIN
        mdm.sgroup
    ON
        CONCAT(lpad(cp.LGROUP,2,'0'), lpad(cp.MGROUP,2,'0'), lpad(cp.SGROUP,2,'0'))  = CONCAT(sgroup.LGROUP, sgroup.MGROUP, sgroup.SGROUP)
        AND sgroup.pms_u_ymd <= :diff_base_timestamp
    WHERE
        cp.pms_u_ymd > :sync_datetime AND cp.pms_u_ymd <= :diff_base_timestamp
        OR
        sgroup.pms_u_ymd > :sync_datetime AND sgroup.pms_u_ymd <= :diff_base_timestamp
    UNION ALL
    SELECT
        '0000' AS shop_code,
        CONCAT(dgroup.LGROUP, dgroup.MGROUP, dgroup.SGROUP, dgroup.DGROUP) AS  category_code,
        cp.MAIL_ORDER_PRODUCT_CD AS commodity_code,
        '' AS category_search_path,
        '' AS search_category_code0,
        '' AS search_category_code1,
        '' AS search_category_code2,
        '' AS search_category_code3,
        '' AS search_category_code4,
        '' AS search_category_code5,
        '' AS search_category_code6,
        '' AS orm_rowid,
        '' AS created_user,
        '' AS created_datetime,
        '' AS updated_user,
        '' AS updated_datetime,
        5 AS sort_no
    FROM
        combined_products cp
    JOIN
        mdm.dgroup
    ON
        CONCAT(lpad(cp.LGROUP,2,'0'), lpad(cp.MGROUP,2,'0'), lpad(cp.SGROUP,2,'0'), lpad(cp.DGROUP,2,'0'))  = CONCAT(dgroup.LGROUP, dgroup.MGROUP, dgroup.SGROUP, dgroup.DGROUP)
        AND dgroup.pms_u_ymd <= :diff_base_timestamp
    WHERE
        cp.pms_u_ymd > :sync_datetime AND cp.pms_u_ymd <= :diff_base_timestamp
        OR
        dgroup.pms_u_ymd > :sync_datetime AND dgroup.pms_u_ymd <= :diff_base_timestamp
    )
ORDER BY
    sort_no

