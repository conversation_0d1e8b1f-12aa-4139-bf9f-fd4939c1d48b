SELECT MAX(latest_date) AS max_date
FROM (
    SELECT MAX(pms_u_ymd) AS latest_date
    FROM mdm.product_linkage
    UNION ALL
    SELECT MAX(pms_u_ymd) AS latest_date
    FROM mdm.product_edit
    UNION ALL
    SELECT MAX(pms_u_ymd) AS latest_date
    FROM mdm.lgroup
    UNION ALL
    SELECT MAX(pms_u_ymd) AS latest_date
    FROM mdm.mgroup
    UNION ALL
    SELECT MAX(pms_u_ymd) AS latest_date
    FROM mdm.sgroup
    UNION ALL
    SELECT MAX(pms_u_ymd) AS latest_date
    FROM mdm.dgroup
) AS all_dates;
