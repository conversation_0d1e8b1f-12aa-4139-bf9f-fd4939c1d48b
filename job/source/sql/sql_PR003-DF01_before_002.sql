-- ワークテーブルクリア
TRUNCATE TABLE wk_PR003_DF01_product_price_list;
TRUNCATE TABLE wk_PR003_DF01_set_commodity_composition_list;
-- 価格適用開始日が本日の削除されていない価格リスト
-- 商品ごとに適用開始日時が最も遅いもの(ただし今日まで)を取得する
WITH today_start_price AS (
    SELECT
        DISTINCT ON (mdm_integration_management_cd) mdm_integration_management_cd,
        tax_exc,
        apply_start_date
    FROM
        mdm.period_price_linkage
    WHERE
        DATE(apply_start_date) <= current_date
        AND DATE(apply_start_date) > :sync_datetime
        AND mdm_integration_management_cd_nk IS NOT NULL
    ORDER BY
        mdm_integration_management_cd,
        apply_start_date DESC
) -- 価格が更新された商品の、最も適用開始日時が新しい価格(ただし今日まで)のリスト
,
latest_start_price AS (
    SELECT
        DISTINCT ON (mdm_integration_management_cd) mdm_integration_management_cd,
        tax_exc,
        apply_start_date
    FROM
        mdm.period_price_linkage
    WHERE
        mdm_integration_management_cd IN (
            SELECT
                mdm_integration_management_cd
            FROM
                mdm.period_price_linkage
            WHERE
                pms_u_ymd > :sync_datetime
        )
        AND mdm_integration_management_cd_nk IS NOT NULL
        AND DATE(apply_start_date) <= current_date
    ORDER BY
        mdm_integration_management_cd,
        apply_start_date DESC
) -- 上記2リストに存在する通常商品ごとに、最も適用開始日時が遅い価格を価格ワークテーブルに登録
INSERT INTO
    wk_PR003_DF01_product_price_list (product_id, product_div, amount)
SELECT
    DISTINCT ON (pl.mail_order_product_cd) pl.mail_order_product_cd,
    '01',
    ppl.tax_exc
FROM
    (
        SELECT
            mdm_integration_management_cd,
            tax_exc,
            apply_start_date
        FROM
            today_start_price
        UNION
        ALL
        SELECT
            mdm_integration_management_cd,
            tax_exc,
            apply_start_date
        FROM
            latest_start_price
    ) AS ppl
    LEFT OUTER JOIN mdm.product_linkage AS pl ON ppl.mdm_integration_management_cd = pl.mdm_integration_management_cd
WHERE
    pl.mdm_integration_management_cd_nk IS NOT NULL
    AND pl.set_product_flg = '0'
    AND pl.product_type != '11'
ORDER BY
    pl.mail_order_product_cd,
    ppl.apply_start_date DESC;
-- 子商品の値段が更新されたセット商品の商品コードリスト
WITH price_updated_set_commodity_list AS (
    SELECT
        DISTINCT commodity_code
    FROM
        set_commodity_composition
    WHERE
        child_commodity_code IN (
            SELECT
                product_id
            FROM
                wk_PR003_DF01_product_price_list
        )
) -- セット商品構成が更新されたセット商品の商品コードリスト
,
composition_updated_set_commodity_list AS (
    SELECT
        DISTINCT commodity_code
    FROM
        set_commodity_composition
    WHERE
        d_updated_datetime > :sync_datetime
) -- 上記2リストに存在するセット商品の子商品価格一覧をセット商品ワークテーブルに登録
INSERT INTO
    wk_PR003_DF01_set_commodity_composition_list (
        product_id,
        child_commodity_code,
        composition_quantity,
        tax_exc
    )
SELECT
    scc.commodity_code,
    scc.child_commodity_code,
    SUM(scc.composition_quantity),
    ppl.tax_exc
FROM
    (
        SELECT
            commodity_code
        FROM
            price_updated_set_commodity_list
        UNION
        SELECT
            commodity_code
        FROM
            composition_updated_set_commodity_list
    ) AS cl
    JOIN set_commodity_composition AS scc ON cl.commodity_code = scc.commodity_code
    LEFT OUTER JOIN mdm.product_linkage AS pl ON scc.child_commodity_code = pl.mail_order_product_cd
    LEFT OUTER JOIN (
        SELECT
            DISTINCT ON (mdm_integration_management_cd) mdm_integration_management_cd,
            tax_exc
        FROM
            mdm.period_price_linkage
        WHERE
            mdm_integration_management_cd_nk IS NOT NULL
            AND DATE(apply_start_date) <= current_date
        ORDER BY
            mdm_integration_management_cd,
            apply_start_date DESC
    ) AS ppl ON pl.mdm_integration_management_cd = ppl.mdm_integration_management_cd
WHERE
    ppl.tax_exc IS NOT NULL
GROUP BY
    scc.commodity_code,
    scc.child_commodity_code,
    ppl.tax_exc;
-- セット商品用ワークテーブルからセット商品の値段を計算して価格ワークテーブルに登録
INSERT INTO
    wk_PR003_DF01_product_price_list (product_id, product_div, amount)
SELECT
    product_id,
    '02',
    SUM(composition_quantity * tax_exc)
FROM
    wk_PR003_DF01_set_commodity_composition_list
GROUP BY
    product_id;
-- 価格が更新された定期便商品の価格を価格ワークテーブルに登録
INSERT INTO
    wk_PR003_DF01_product_price_list (product_id, product_div, amount)
SELECT
    rsb.commodity_code,
    '03',
    rsc.retail_price
FROM
    regular_sale_base AS rsb
    JOIN regular_sale_composition AS rsc ON rsb.shop_code = rsc.shop_code
    AND rsb.regular_sale_code = rsc.regular_sale_code
    AND rsc.d_updated_datetime > :sync_datetime;
--分割番号を設定
WITH temp_parallel_num AS (
    SELECT
        product_id,
        MOD(
            ROW_NUMBER() OVER (
                ORDER BY
                    product_id
            ) - 1,
            :split_num
        ) + 1 AS parallel_num
    FROM
        (
            SELECT
                product_id
            FROM
                wk_PR003_DF01_product_price_list
        )
)
UPDATE
    wk_PR003_DF01_product_price_list
SET
    split_num = t.parallel_num
FROM
    temp_parallel_num AS t
WHERE
    wk_PR003_DF01_product_price_list.product_id = t.product_id;