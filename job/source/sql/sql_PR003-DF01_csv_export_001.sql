-- セット商品関連情報一時csv出力
select
    *
from
    aws_s3.query_export_to_s3(
        'SELECT
            commodity_code AS mail_order_product_cd,
            child_commodity_code AS product_id,
            SUM(composition_quantity) AS quantity,
            MAX(composition_order) AS composition_order
        FROM
            set_commodity_composition
        GROUP BY
            commodity_code,
            child_commodity_code
        ORDER BY
            composition_order',
        aws_commons.create_s3_uri(
            :bucket_name,
            -- bucket名
            :object_path,
            -- object名
            'ap-northeast-1' -- region名
        ),
        options := 'format csv, header true, quote ''"'' '
    );