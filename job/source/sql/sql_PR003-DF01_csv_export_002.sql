-- 定期便商品関連情報一時csv出力
select
    *
from
    aws_s3.query_export_to_s3(
        'SELECT
            regular_sale_base.sku_code AS mail_order_product_cd,
            regular_sale_commodity.sku_code AS product_id
        FROM
            regular_sale_base
            INNER JOIN regular_sale_commodity ON regular_sale_base.shop_code = regular_sale_commodity.shop_code
            AND regular_sale_base.regular_sale_code = regular_sale_commodity.regular_sale_code',
        aws_commons.create_s3_uri(
            :bucket_name,
            -- bucket名
            :object_path,
            -- object名
            'ap-northeast-1' -- region名
        ),
        options := 'format csv, header true, quote ''"'' '
    );