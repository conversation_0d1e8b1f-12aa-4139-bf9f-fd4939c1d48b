SELECT shop_code,
    commodity_code,
    commodity_name,
    commodity_type,
    represent_sku_code,
    represent_sku_unit_price,
    stock_status_no,
    stock_management_type,
    age_limit_code,
    commodity_tax_type,
    tax_group_code,
    short_description,
    commodity_search_words,
    prior_printing_description,
    posterior_printing_description,
    delivery_description,
    sale_start_datetime,
    sale_end_datetime,
    discount_price_start_datetime,
    discount_price_end_datetime,
    reservation_start_datetime,
    reservation_end_datetime,
    prior_printing_start_date,
    prior_printing_end_date,
    posterior_printing_start_date,
    posterior_printing_end_date,
    delivery_type_no,
    sales_method_type,
    manufacturer_model_no,
    link_url,
    recommend_commodity_rank,
    commodity_popular_rank,
    commodity_standard1_name,
    commodity_standard2_name,
    commodity_point_rate,
    commodity_point_start_datetime,
    commodity_point_end_datetime,
    sale_flg,
    noshi_effective_flg,
    arrival_goods_flg,
    oneshot_order_limit,
    standard_image_type,
    purchasing_confirm_flg_pc,
    purchasing_confirm_flg_sp,
    commodity_kind,
    keihi_hurikae_target_flg,
    charge_user_code,
    commodity_remark,
    channel_cc_sale_flg,
    channel_ec_sale_flg,
    shipping_charge_target_flg,
    first_purchase_limit_flg,
    purchase_hold_flg,
    commodity_exclude_flg,
    commodity_subsubcategory_code,
    pack_calc_pattern,
    pad_type,
    fall_down_flg,
    height,
    width,
    deepness,
    weight,
    tracking_out_flg,
    mdm_management_code,
    commodity_segment,
    business_segment,
    commodity_group,
    commodity_series,
    core_department,
    accounting_pattern_type,
    return_enabled_flg,
    exchange_enabled_flg,
    exterior_box_weight,
    nekoposu_volume_rate,
    warehouse_assembly_flg,
    mail_delivery_flg,
    before_renewal_commodity_code,
    preorder_enable_days,
    main_product_no,
    product_no,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM commodity_header
where updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;
