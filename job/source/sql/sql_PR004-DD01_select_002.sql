SELECT
    shop_code,
    sku_code,
    commodity_code,
    unit_price,
    discount_price,
    reservation_price,
    jan_code,
    standard_detail1_name,
    standard_detail2_name,
    hinban_code,
    hinban_kind,
    member_price_applied_flg,
    member_price_discount_rate,
    member_price,
    air_transport_flg,
    commodity_prod_pack_type,
    delivery_note_no_disp_flg,
    reduction_point,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    commodity_detail
where
    updated_datetime > :sync_datetime
AND updated_datetime <= :diff_base_timestamp;