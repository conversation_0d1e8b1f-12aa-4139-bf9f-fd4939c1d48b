INSERT INTO commodity_header (
        shop_code,
        commodity_code,
        commodity_name,
        commodity_type,
        represent_sku_code,
        represent_sku_unit_price,
        stock_status_no,
        stock_management_type,
        age_limit_code,
        commodity_tax_type,
        tax_group_code,
        short_description,
        commodity_search_words,
        prior_printing_description,
        posterior_printing_description,
        delivery_description,
        sale_start_datetime,
        sale_end_datetime,
        discount_price_start_datetime,
        discount_price_end_datetime,
        reservation_start_datetime,
        reservation_end_datetime,
        prior_printing_start_date,
        prior_printing_end_date,
        posterior_printing_start_date,
        posterior_printing_end_date,
        delivery_type_no,
        sales_method_type,
        manufacturer_model_no,
        link_url,
        recommend_commodity_rank,
        commodity_popular_rank,
        commodity_standard1_name,
        commodity_standard2_name,
        commodity_point_rate,
        commodity_point_start_datetime,
        commodity_point_end_datetime,
        sale_flg,
        noshi_effective_flg,
        arrival_goods_flg,
        oneshot_order_limit,
        standard_image_type,
        purchasing_confirm_flg_pc,
        purchasing_confirm_flg_sp,
        commodity_kind,
        keihi_hurikae_target_flg,
        charge_user_code,
        commodity_remark,
        channel_cc_sale_flg,
        channel_ec_sale_flg,
        shipping_charge_target_flg,
        first_purchase_limit_flg,
        purchase_hold_flg,
        commodity_exclude_flg,
        commodity_subsubcategory_code,
        pack_calc_pattern,
        pad_type,
        fall_down_flg,
        height,
        width,
        deepness,
        weight,
        tracking_out_flg,
        mdm_management_code,
        commodity_segment,
        business_segment,
        commodity_group,
        commodity_series,
        core_department,
        accounting_pattern_type,
        return_enabled_flg,
        exchange_enabled_flg,
        exterior_box_weight,
        nekoposu_volume_rate,
        warehouse_assembly_flg,
        mail_delivery_flg,
        before_renewal_commodity_code,
        preorder_enable_days,
        main_product_no,
        product_no,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT shop_code,
    commodity_code,
    commodity_name,
    commodity_type,
    represent_sku_code,
    represent_sku_unit_price,
    stock_status_no,
    stock_management_type,
    age_limit_code,
    commodity_tax_type,
    tax_group_code,
    short_description,
    commodity_search_words,
    prior_printing_description,
    posterior_printing_description,
    delivery_description,
    sale_start_datetime,
    sale_end_datetime,
    discount_price_start_datetime,
    discount_price_end_datetime,
    reservation_start_datetime,
    reservation_end_datetime,
    prior_printing_start_date,
    prior_printing_end_date,
    posterior_printing_start_date,
    posterior_printing_end_date,
    delivery_type_no,
    sales_method_type,
    manufacturer_model_no,
    link_url,
    recommend_commodity_rank,
    commodity_popular_rank,
    commodity_standard1_name,
    commodity_standard2_name,
    commodity_point_rate,
    commodity_point_start_datetime,
    commodity_point_end_datetime,
    sale_flg,
    noshi_effective_flg,
    arrival_goods_flg,
    oneshot_order_limit,
    standard_image_type,
    purchasing_confirm_flg_pc,
    purchasing_confirm_flg_sp,
    commodity_kind,
    keihi_hurikae_target_flg,
    charge_user_code,
    commodity_remark,
    channel_cc_sale_flg,
    channel_ec_sale_flg,
    shipping_charge_target_flg,
    first_purchase_limit_flg,
    purchase_hold_flg,
    commodity_exclude_flg,
    commodity_subsubcategory_code,
    pack_calc_pattern,
    pad_type,
    fall_down_flg,
    height,
    width,
    deepness,
    weight,
    tracking_out_flg,
    mdm_management_code,
    commodity_segment,
    business_segment,
    commodity_group,
    commodity_series,
    core_department,
    accounting_pattern_type,
    return_enabled_flg,
    exchange_enabled_flg,
    exterior_box_weight,
    nekoposu_volume_rate,
    warehouse_assembly_flg,
    mail_delivery_flg,
    before_renewal_commodity_code,
    preorder_enable_days,
    main_product_no,
    product_no,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_PR004-DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_PR004-DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM commodity_header_work ON CONFLICT (shop_code, commodity_code) DO
UPDATE
SET commodity_name = EXCLUDED.commodity_name,
    commodity_type = EXCLUDED.commodity_type,
    represent_sku_code = EXCLUDED.represent_sku_code,
    represent_sku_unit_price = EXCLUDED.represent_sku_unit_price,
    stock_status_no = EXCLUDED.stock_status_no,
    stock_management_type = EXCLUDED.stock_management_type,
    age_limit_code = EXCLUDED.age_limit_code,
    commodity_tax_type = EXCLUDED.commodity_tax_type,
    tax_group_code = EXCLUDED.tax_group_code,
    short_description = EXCLUDED.short_description,
    commodity_search_words = EXCLUDED.commodity_search_words,
    prior_printing_description = EXCLUDED.prior_printing_description,
    posterior_printing_description = EXCLUDED.posterior_printing_description,
    delivery_description = EXCLUDED.delivery_description,
    sale_start_datetime = EXCLUDED.sale_start_datetime,
    sale_end_datetime = EXCLUDED.sale_end_datetime,
    discount_price_start_datetime = EXCLUDED.discount_price_start_datetime,
    discount_price_end_datetime = EXCLUDED.discount_price_end_datetime,
    reservation_start_datetime = EXCLUDED.reservation_start_datetime,
    reservation_end_datetime = EXCLUDED.reservation_end_datetime,
    prior_printing_start_date = EXCLUDED.prior_printing_start_date,
    prior_printing_end_date = EXCLUDED.prior_printing_end_date,
    posterior_printing_start_date = EXCLUDED.posterior_printing_start_date,
    posterior_printing_end_date = EXCLUDED.posterior_printing_end_date,
    delivery_type_no = EXCLUDED.delivery_type_no,
    sales_method_type = EXCLUDED.sales_method_type,
    manufacturer_model_no = EXCLUDED.manufacturer_model_no,
    link_url = EXCLUDED.link_url,
    recommend_commodity_rank = EXCLUDED.recommend_commodity_rank,
    commodity_popular_rank = EXCLUDED.commodity_popular_rank,
    commodity_standard1_name = EXCLUDED.commodity_standard1_name,
    commodity_standard2_name = EXCLUDED.commodity_standard2_name,
    commodity_point_rate = EXCLUDED.commodity_point_rate,
    commodity_point_start_datetime = EXCLUDED.commodity_point_start_datetime,
    commodity_point_end_datetime = EXCLUDED.commodity_point_end_datetime,
    sale_flg = EXCLUDED.sale_flg,
    noshi_effective_flg = EXCLUDED.noshi_effective_flg,
    arrival_goods_flg = EXCLUDED.arrival_goods_flg,
    oneshot_order_limit = EXCLUDED.oneshot_order_limit,
    standard_image_type = EXCLUDED.standard_image_type,
    purchasing_confirm_flg_pc = EXCLUDED.purchasing_confirm_flg_pc,
    purchasing_confirm_flg_sp = EXCLUDED.purchasing_confirm_flg_sp,
    commodity_kind = EXCLUDED.commodity_kind,
    keihi_hurikae_target_flg = EXCLUDED.keihi_hurikae_target_flg,
    charge_user_code = EXCLUDED.charge_user_code,
    commodity_remark = EXCLUDED.commodity_remark,
    channel_cc_sale_flg = EXCLUDED.channel_cc_sale_flg,
    channel_ec_sale_flg = EXCLUDED.channel_ec_sale_flg,
    shipping_charge_target_flg = EXCLUDED.shipping_charge_target_flg,
    first_purchase_limit_flg = EXCLUDED.first_purchase_limit_flg,
    purchase_hold_flg = EXCLUDED.purchase_hold_flg,
    commodity_exclude_flg = EXCLUDED.commodity_exclude_flg,
    commodity_subsubcategory_code = EXCLUDED.commodity_subsubcategory_code,
    pack_calc_pattern = EXCLUDED.pack_calc_pattern,
    pad_type = EXCLUDED.pad_type,
    fall_down_flg = EXCLUDED.fall_down_flg,
    height = EXCLUDED.height,
    width = EXCLUDED.width,
    deepness = EXCLUDED.deepness,
    weight = EXCLUDED.weight,
    tracking_out_flg = EXCLUDED.tracking_out_flg,
    mdm_management_code = EXCLUDED.mdm_management_code,
    commodity_segment = EXCLUDED.commodity_segment,
    business_segment = EXCLUDED.business_segment,
    commodity_group = EXCLUDED.commodity_group,
    commodity_series = EXCLUDED.commodity_series,
    core_department = EXCLUDED.core_department,
    accounting_pattern_type = EXCLUDED.accounting_pattern_type,
    return_enabled_flg = EXCLUDED.return_enabled_flg,
    exchange_enabled_flg = EXCLUDED.exchange_enabled_flg,
    exterior_box_weight = EXCLUDED.exterior_box_weight,
    nekoposu_volume_rate = EXCLUDED.nekoposu_volume_rate,
    warehouse_assembly_flg = EXCLUDED.warehouse_assembly_flg,
    mail_delivery_flg = EXCLUDED.mail_delivery_flg,
    before_renewal_commodity_code = EXCLUDED.before_renewal_commodity_code,
    preorder_enable_days = EXCLUDED.preorder_enable_days,
    main_product_no = EXCLUDED.main_product_no,
    product_no = EXCLUDED.product_no,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = commodity_header.d_version + 1;
