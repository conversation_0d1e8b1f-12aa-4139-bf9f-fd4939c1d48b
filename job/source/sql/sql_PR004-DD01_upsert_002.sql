INSERT INTO commodity_detail (
    shop_code,
    sku_code,
    commodity_code,
    unit_price,
    discount_price,
    reservation_price,
    jan_code,
    standard_detail1_name,
    standard_detail2_name,
    hinban_code,
    hinban_kind,
    member_price_applied_flg,
    member_price_discount_rate,
    member_price,
    air_transport_flg,
    commodity_prod_pack_type,
    delivery_note_no_disp_flg,
    reduction_point,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_code,
    sku_code,
    commodity_code,
    unit_price,
    discount_price,
    reservation_price,
    jan_code,
    standard_detail1_name,
    standard_detail2_name,
    hinban_code,
    hinban_kind,
    member_price_applied_flg,
    member_price_discount_rate,
    member_price,
    air_transport_flg,
    commodity_prod_pack_type,
    delivery_note_no_disp_flg,
    reduction_point,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_PR004-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_PR004-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM
    commodity_detail_work
ON CONFLICT (shop_code, sku_code)  -- 複合主キーやユニーク制約が必要
DO UPDATE SET
    commodity_code = EXCLUDED.commodity_code,
    unit_price = EXCLUDED.unit_price,
    discount_price = EXCLUDED.discount_price,
    reservation_price = EXCLUDED.reservation_price,
    jan_code = EXCLUDED.jan_code,
    standard_detail1_name = EXCLUDED.standard_detail1_name,
    standard_detail2_name = EXCLUDED.standard_detail2_name,
    hinban_code = EXCLUDED.hinban_code,
    hinban_kind = EXCLUDED.hinban_kind,
    member_price_applied_flg = EXCLUDED.member_price_applied_flg,
    member_price_discount_rate = EXCLUDED.member_price_discount_rate,
    member_price = EXCLUDED.member_price,
    air_transport_flg = EXCLUDED.air_transport_flg,
    commodity_prod_pack_type = EXCLUDED.commodity_prod_pack_type,
    delivery_note_no_disp_flg = EXCLUDED.delivery_note_no_disp_flg,
    reduction_point = EXCLUDED.reduction_point,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = commodity_detail.d_version + 1;