INSERT INTO regular_sale_commodity (
    shop_code,
    regular_sale_code,
    regular_sale_composition_no,
    sku_code,
    commodity_code,
    display_order,
    regular_sale_commodity_type,
    regular_sale_commodity_point,
    difference_price,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_code,
    regular_sale_code,
    regular_sale_composition_no,
    sku_code,
    commodity_code,
    display_order,
    regular_sale_commodity_type,
    regular_sale_commodity_point,
    difference_price,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_PR004-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_PR004-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM
    regular_sale_commodity_work
ON CONFLICT (shop_code, regular_sale_code, regular_sale_composition_no, sku_code)
DO UPDATE SET
    commodity_code = EXCLUDED.commodity_code,
    display_order = EXCLUDED.display_order,
    regular_sale_commodity_type = EXCLUDED.regular_sale_commodity_type,
    regular_sale_commodity_point = EXCLUDED.regular_sale_commodity_point,
    difference_price = EXCLUDED.difference_price,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = regular_sale_commodity.d_version + 1;