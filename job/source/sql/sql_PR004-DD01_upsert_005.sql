INSERT INTO regular_sale_composition (
    shop_code,
    regular_sale_code,
    regular_sale_composition_no,
    regular_sale_composition_name,
    regular_order_count_min_limit,
    regular_order_count_max_limit,
    regular_order_count_interval,
    retail_price,
    regular_sale_commodity_point,
    display_order,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_code,
    regular_sale_code,
    regular_sale_composition_no,
    regular_sale_composition_name,
    regular_order_count_min_limit,
    regular_order_count_max_limit,
    regular_order_count_interval,
    retail_price,
    regular_sale_commodity_point,
    display_order,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_PR004-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_PR004-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM
    regular_sale_composition_work
ON CONFLICT (shop_code, regular_sale_code, regular_sale_composition_no)
DO UPDATE SET
    regular_sale_composition_name = EXCLUDED.regular_sale_composition_name,
    regular_order_count_min_limit = EXCLUDED.regular_order_count_min_limit,
    regular_order_count_max_limit = EXCLUDED.regular_order_count_max_limit,
    regular_order_count_interval = EXCLUDED.regular_order_count_interval,
    retail_price = EXCLUDED.retail_price,
    regular_sale_commodity_point = EXCLUDED.regular_sale_commodity_point,
    display_order = EXCLUDED.display_order,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = regular_sale_composition.d_version + 1;