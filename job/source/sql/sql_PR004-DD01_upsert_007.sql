INSERT INTO regular_sale_payment (
    shop_code,
    regular_sale_code,
    payment_method_no,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_code,
    regular_sale_code,
    payment_method_no,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_PR004-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_PR004-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM
    regular_sale_payment_work
ON CONFLICT (shop_code, regular_sale_code, payment_method_no)
DO UPDATE SET
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_PR004-DD01_001',
    d_updated_datetime = NOW(),
    d_version = regular_sale_payment.d_version + 1;