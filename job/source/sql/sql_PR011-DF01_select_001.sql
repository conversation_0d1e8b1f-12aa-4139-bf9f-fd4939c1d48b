SELECT
    '10' AS 連携元区分,
    p.MAIL_ORDER_PRODUCT_CD AS 通販商品番号,
    p.PRODUCT_NO AS 代表商品コード
FROM
    mdm.product_linkage p
WHERE
    p.MAIL_ORDER_PRODUCT_CD IS NOT NULL
    AND p.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND p.BUSINESS_SEGMENT != 'ZS02'
    AND p.PRODUCT_SEGMENT != 'ZM00'
UNION
ALL
SELECT
    '20' AS 連携元区分,
    RIGHT(p.JAN, 6) AS 通販商品番号,
    p.PRODUCT_NO AS 代表商品コード
FROM
    mdm.product_linkage p
WHERE
    p.JAN IS NOT NULL
    AND p.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND p.BUSINESS_SEGMENT != 'ZS02'
    AND p.PRODUCT_SEGMENT != 'ZM00'
UNION
ALL
SELECT
    '30' AS 連携元区分,
    a.ADDRESS_ITEM_CODE AS 通販商品番号,
    p.PRODUCT_NO AS 代表商品コード
FROM
    mdm.product_linkage p
    JOIN mdm.address_item_code_linkage a ON p.MDM_INTEGRATION_MANAGEMENT_CD = a.MDM_INTEGRATION_MANAGEMENT_CD
WHERE
    p.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND a.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND p.BUSINESS_SEGMENT != 'ZS02'
    AND p.PRODUCT_SEGMENT != 'ZM00';