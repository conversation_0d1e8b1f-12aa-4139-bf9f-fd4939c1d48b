WITH price_group AS (
    SELECT
        pp.MDM_INTEGRATION_MANAGEMENT_CD,
        pp.TAX_EXC,
        pp.TAX_INC,
        pp.TAX_RATE
    FROM mdm.period_price_linkage pp
    JOIN (
        select
        mdm_integration_management_cd,
        max(apply_start_date) as max_apply_start_date
        from
        mdm.period_price_linkage as pp3
        where
        pp3.mdm_integration_management_cd_nk IS NOT NULL and
        pp3.apply_start_date <= NOW()
        group by mdm_integration_management_cd
    ) as pp2 ON
    pp.mdm_integration_management_cd = pp2.mdm_integration_management_cd AND
    pp.apply_start_date = pp2.max_apply_start_date
),
converted AS (
    SELECT
        '10' AS 連携元コード区分,
        a.MAIL_ORDER_PRODUCT_CD AS 連携元製品コード,
        CONCAT(REGEXP_REPLACE(a.PRODUCT_NAME, '^DHC ', ''), ' ', a.ORIGINAL_COLOR_CD, ' ', a.SIZE_CD) AS 製品名称,
        convert_full_to_half_katakana(CONCAT(REGEXP_REPLACE(a.PRODUCT_NAME, '^DHC ', ''), ' ', a.ORIGINAL_COLOR_CD, ' ', a.SIZE_CD)) AS 製品名称（短）,
        a.PRODUCT_NO AS 商品管理システムコード,
        a.MAIN_PRODUCT_NO AS 代表商品コード,
        a.ACCOUNTIN_PATTERN_GB AS 会計区分,
        CASE
            WHEN a.PERIOD_SET_SALES_CHANNEL_1 = '50'
                      OR a.PERIOD_SET_SALES_CHANNEL_2 = '50'
                OR a.PERIOD_SET_SALES_CHANNEL_3 = '50'
            THEN '2'
            ELSE '1'
            END AS 製品属性,
        CASE a.OUT_INDICATE_WAREHOUSE
            WHEN '201' THEN 'WH003'
            ELSE 'WH001'
            END AS 主要倉庫コード,
        '301' AS 在庫種別コード,
        '1' AS 利用期限,
        SALE_START_DATE AS 販売開始日時,
        GREATEST(a.SALES_CHANNEL_1_SALE_END_DATE, a.SALES_CHANNEL_2_SALE_END_DATE, a.SALES_CHANNEL_3_SALE_END_DATE) AS 販売終了日時,
        b.TAX_EXC AS 税抜価格,
        b.TAX_INC AS 税込価格,
        b.TAX_RATE AS 税率,
        a.ORIGINAL_COLOR_CD AS 独自色コード,
        '' AS 属性,
        '' AS 形態,
        a.WEIGHT AS 単品重量,
        a.WIDTH AS 単品Ｗ,
        a.DEPTH AS 単品Ｄ,
        a.HEIGHT AS 単品Ｈ,
        a.CASE_PER_INCLUDE_CNT AS ケース入り数,
        a.OUTERBOX_WEIGHT AS ケース重量,
        a.OUTERBOX_WIDTH AS ケースＷ,
        a.OUTERBOX_DEPTH AS ケースＤ,
        a.OUTERBOX_HEIGHT AS ケースＨ,
        '' AS 登録順,
        'dpms' AS 予備1
    FROM
        mdm.product_linkage a
    JOIN
        price_group b
    ON
        a.MDM_INTEGRATION_MANAGEMENT_CD = b.MDM_INTEGRATION_MANAGEMENT_CD
    WHERE
        a.MAIL_ORDER_PRODUCT_CD IS NOT NULL
        AND a.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
        AND a.BUSINESS_SEGMENT != 'ZS02'
        AND a.PRODUCT_SEGMENT != 'ZM00'

    UNION ALL

    SELECT
        '20' AS 連携元コード区分,
        RIGHT(a.JAN, 6) AS 連携元製品コード,
        CONCAT(REGEXP_REPLACE(a.PRODUCT_NAME, '^DHC ', ''), ' ', a.ORIGINAL_COLOR_CD, ' ', a.SIZE_CD) AS 製品名称,
        convert_full_to_half_katakana(CONCAT(REGEXP_REPLACE(a.PRODUCT_NAME, '^DHC ', ''), ' ', a.ORIGINAL_COLOR_CD, ' ', a.SIZE_CD)) AS 製品名称（短）,
        a.PRODUCT_NO AS 商品管理システムコード,
        a.MAIN_PRODUCT_NO AS 代表商品コード,
        a.ACCOUNTIN_PATTERN_GB AS 会計区分,
        CASE
            WHEN a.PERIOD_SET_SALES_CHANNEL_1 = '50'
                      OR a.PERIOD_SET_SALES_CHANNEL_2 = '50'
                OR a.PERIOD_SET_SALES_CHANNEL_3 = '50'
            THEN '2'
            ELSE '1'
            END AS 製品属性,
        CASE a.OUT_INDICATE_WAREHOUSE
            WHEN '201' THEN 'WH003'
            ELSE 'WH001'
            END AS 主要倉庫コード,
        '301' AS 在庫種別コード,
        '1' AS 利用期限,
        SALE_START_DATE AS 販売開始日時,
        GREATEST(a.SALES_CHANNEL_1_SALE_END_DATE, a.SALES_CHANNEL_2_SALE_END_DATE, a.SALES_CHANNEL_3_SALE_END_DATE) AS 販売終了日時,
        b.TAX_EXC AS 税抜価格,
        b.TAX_INC AS 税込価格,
        b.TAX_RATE AS 税率,
        a.ORIGINAL_COLOR_CD AS 独自色コード,
        '' AS 属性,
        '' AS 形態,
        a.WEIGHT AS 単品重量,
        a.WIDTH AS 単品Ｗ,
        a.DEPTH AS 単品Ｄ,
        a.HEIGHT AS 単品Ｈ,
        a.CASE_PER_INCLUDE_CNT AS ケース入り数,
        a.OUTERBOX_WEIGHT AS ケース重量,
        a.OUTERBOX_WIDTH AS ケースＷ,
        a.OUTERBOX_DEPTH AS ケースＤ,
        a.OUTERBOX_HEIGHT AS ケースＨ,
        '' AS 登録順,
        'dpms' AS 予備1
    FROM
        mdm.product_linkage a
    JOIN
        price_group b
    ON
        a.MDM_INTEGRATION_MANAGEMENT_CD = b.MDM_INTEGRATION_MANAGEMENT_CD
    WHERE
        a.JAN IS NOT NULL
        AND a.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
        AND a.BUSINESS_SEGMENT != 'ZS02'
        AND a.PRODUCT_SEGMENT != 'ZM00'

    UNION ALL

    SELECT
        '30' AS 連携元コード区分,
        c.ADDRESS_ITEM_CODE AS 連携元製品コード,
        CONCAT(REGEXP_REPLACE(a.PRODUCT_NAME, '^DHC ', ''), ' ', a.ORIGINAL_COLOR_CD, ' ', a.SIZE_CD) AS 製品名称,
        convert_full_to_half_katakana(CONCAT(REGEXP_REPLACE(a.PRODUCT_NAME, '^DHC ', ''), ' ', a.ORIGINAL_COLOR_CD, ' ', a.SIZE_CD)) AS 製品名称（短）,
        a.PRODUCT_NO AS 商品管理システムコード,
        a.MAIN_PRODUCT_NO AS 代表商品コード,
        a.ACCOUNTIN_PATTERN_GB AS 会計区分,
        CASE
            WHEN a.PERIOD_SET_SALES_CHANNEL_1 = '50'
                      OR a.PERIOD_SET_SALES_CHANNEL_2 = '50'
                OR a.PERIOD_SET_SALES_CHANNEL_3 = '50'
            THEN '2'
            ELSE '1'
            END AS 製品属性,
        CASE a.OUT_INDICATE_WAREHOUSE
            WHEN '201' THEN 'WH003'
            ELSE 'WH001'
            END AS 主要倉庫コード,
        '301' AS 在庫種別コード,
        '1' AS 利用期限,
        SALE_START_DATE AS 販売開始日時,
        GREATEST(a.SALES_CHANNEL_1_SALE_END_DATE, a.SALES_CHANNEL_2_SALE_END_DATE, a.SALES_CHANNEL_3_SALE_END_DATE) AS 販売終了日時,
        b.TAX_EXC AS 税抜価格,
        b.TAX_INC AS 税込価格,
        b.TAX_RATE AS 税率,
        a.ORIGINAL_COLOR_CD AS 独自色コード,
        '' AS 属性,
        '' AS 形態,
        a.WEIGHT AS 単品重量,
        a.WIDTH AS 単品Ｗ,
        a.DEPTH AS 単品Ｄ,
        a.HEIGHT AS 単品Ｈ,
        a.CASE_PER_INCLUDE_CNT AS ケース入り数,
        a.OUTERBOX_WEIGHT AS ケース重量,
        a.OUTERBOX_WIDTH AS ケースＷ,
        a.OUTERBOX_DEPTH AS ケースＤ,
        a.OUTERBOX_HEIGHT AS ケースＨ,
        '' AS 登録順,
        'dpms' AS 予備1
    FROM
        mdm.product_linkage a
    JOIN
        price_group b
    ON
        a.MDM_INTEGRATION_MANAGEMENT_CD = b.MDM_INTEGRATION_MANAGEMENT_CD
    JOIN
        mdm.address_item_code_linkage c
    ON
        a.MDM_INTEGRATION_MANAGEMENT_CD = c.MDM_INTEGRATION_MANAGEMENT_CD
    WHERE
        c.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
        AND a.MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
        AND a.BUSINESS_SEGMENT != 'ZS02'
        AND a.PRODUCT_SEGMENT != 'ZM00'
),
byte_limited AS (
  SELECT
    連携元コード区分,
    連携元製品コード,
    製品名称,
    製品名称（短）,
    (SELECT MAX(pos)
     FROM generate_series(1, char_length(製品名称（短）)) AS s(pos)
     WHERE get_sjis_byte(substring(製品名称（短） FROM 1 FOR pos)) <= 16) AS byte_length,
    商品管理システムコード,
    代表商品コード,
    会計区分,
    製品属性,
    主要倉庫コード,
    在庫種別コード,
    利用期限,
    販売開始日時,
    販売終了日時,
    税抜価格,
    税込価格,
    税率,
    独自色コード,
    属性,
    形態,
    単品重量,
    単品Ｗ,
    単品Ｄ,
    単品Ｈ,
    ケース入り数,
    ケース重量,
    ケースＷ,
    ケースＤ,
    ケースＨ,
    登録順,
    予備1
  FROM
    converted
)
SELECT
    連携元コード区分,
    連携元製品コード,
    連携元製品コード,
    製品名称,
    substring(製品名称（短） FROM 1 FOR byte_length) AS 製品名称（短）,
    商品管理システムコード,
    代表商品コード,
    会計区分,
    製品属性,
    主要倉庫コード,
    在庫種別コード,
    利用期限,
    販売開始日時,
    販売終了日時,
    税抜価格,
    税込価格,
    税率,
    独自色コード,
    属性,
    形態,
    単品重量,
    単品Ｗ,
    単品Ｄ,
    単品Ｈ,
    ケース入り数,
    ケース重量,
    ケースＷ,
    ケースＤ,
    ケースＨ,
    登録順,
    予備1
FROM
  byte_limited;
