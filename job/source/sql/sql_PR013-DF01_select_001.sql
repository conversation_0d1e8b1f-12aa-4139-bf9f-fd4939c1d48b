WITH converted AS (
  SELECT
    MAIN_PRODUCT_NO AS セグメントID,
    PRODUCT_SEGMENT AS セグメントコード,
    BUSINESS_SEGMENT AS 事業セグメントコード,
    PRODUCT_CAT AS 商品分類コード,
    PRODUCT_SERIES AS 商品シリーズコード,
    MAIN_PRODUCT_NO AS 代表商品コード,
    CONCAT(
      REGEXP_REPLACE(PRODUCT_NAME, '^DHC ', ''),
      ' ',
      ORIGINAL_COLOR_CD,
      ' ',
      SIZE_CD
    ) AS 代表商品名称,
    convert_full_to_half_katakana(
      CONCAT(
        REGEXP_REPLACE(PRODUCT_NAME, '^DHC ', ''),
        ' ',
        ORIGINAL_COLOR_CD,
        ' ',
        SIZE_CD
      )
    ) AS 代表商品略称,
    '' AS 代表商品名称（ｶﾅ）,
    '' AS 連携元区分,
    '1' AS ステータス,
    '' AS 登録日,
    '' AS 登録担当者コード,
    '' AS 登録担当者名,
    '' AS 更新日,
    '' AS 更新担当者コード,
    '' AS 更新担当者名
  FROM
    mdm.product_linkage
  WHERE
    MDM_INTEGRATION_MANAGEMENT_CD_nk IS NOT NULL
    AND BUSINESS_SEGMENT != 'ZS02'
    AND PRODUCT_SEGMENT != 'ZM00'
),
byte_limited AS (
  SELECT
    セグメントID,
    セグメントコード,
    事業セグメントコード,
    商品分類コード,
    商品シリーズコード,
    代表商品コード,
    代表商品名称,
    代表商品略称,
    (
      SELECT
        MAX(pos)
      FROM
        generate_series(1, char_length(代表商品略称)) AS s(pos)
      WHERE
        get_sjis_byte(
          substring(
            代表商品略称
            FROM
              1 FOR pos
          )
        ) <= 16
    ) AS byte_length,
    代表商品名称（ｶﾅ）,
    連携元区分,
    ステータス,
    登録日,
    登録担当者コード,
    登録担当者名,
    更新日,
    更新担当者コード,
    更新担当者名
  FROM
    converted
)
SELECT
  セグメントID,
  セグメントコード,
  事業セグメントコード,
  商品分類コード,
  商品シリーズコード,
  代表商品コード,
  代表商品名称,
  substring(
    代表商品略称
    FROM
      1 FOR byte_length
  ) AS 代表商品略称,
  代表商品名称（ｶﾅ）,
  連携元区分,
  ステータス,
  登録日,
  登録担当者コード,
  登録担当者名,
  更新日,
  更新担当者コード,
  更新担当者名
FROM
  byte_limited;