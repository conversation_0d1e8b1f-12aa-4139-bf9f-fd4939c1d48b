SELECT
    external_customer_id,
    alternative_customer_code,
    CASE
        WHEN customer_status = 'AccountClosed' THEN 'DELETED'
        ELSE ''
    END AS customer_status,
    last_name || first_name AS name,
    last_name_hiragana || first_name_hiragana AS name_hiragana,
    zip_code,
    state_type,
    city,
    address1,
    tel_number,
    REPLACE(birthday, '-', '/') AS birthday,
    CASE
        WHEN sex_type = 'Male' THEN 'MALE'
        WHEN sex_type = 'Female' THEN 'FEMALE'
        ELSE 'OTHERS'
    END AS sex_type,
    mail_address
FROM
    account ac
WHERE
    ac.d_updated_datetime > :sync_datetime
    AND ac.d_updated_datetime <= :diff_base_timestamp