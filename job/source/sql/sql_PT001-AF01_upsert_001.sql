INSERT INTO point_history (
    tran_timestamp,
    external_customer_id,
    trade_type,
    reason,
    point,
    point_amount,
    point_expiration_timestamp,
    tran_id,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    tran_timestamp,
    external_customer_id,
    trade_type,
    reason,
    point,
    point_amount,
    point_expiration_timestamp,
    tran_id,
    'JN_PT001-AF01_001' AS d_created_user,
    NOW() AS d_created_datetime,
    'JN_PT001-AF01_001' AS d_updated_user,
    NOW() AS d_updated_datetime,
    1 AS d_version
FROM point_history_work
ON CONFLICT (tran_id)
DO UPDATE SET
    tran_timestamp = EXCLUDED.tran_timestamp,
    external_customer_id = EXCLUDED.external_customer_id,
    trade_type = EXCLUDED.trade_type,
    reason = EXCLUDED.reason,
    point = EXCLUDED.point,
    point_amount = EXCLUDED.point_amount,
    point_expiration_timestamp = EXCLUDED.point_expiration_timestamp,
    tran_id = EXCLUDED.tran_id,
    d_updated_user = 'JN_PT001-AF01_001',
    d_updated_datetime = NOW(),
    d_version = point_history.d_version + 1;