INSERT INTO
    account (
        external_customer_id,
        alternative_customer_code,
        customer_status,
        last_name,
        first_name,
        last_name_hiragana,
        first_name_hiragana,
        zip_code,
        state_type,
        city,
        address1,
        tel_number,
        birthday,
        sex_type,
        mail_address,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    external_customer_id,
    alternative_customer_code,
    customer_status,
    last_name,
    first_name,
    last_name_hiragana,
    first_name_hiragana,
    zip_code,
    state_type,
    city,
    address1,
    tel_number,
    birthday,
    sex_type,
    mail_address,
    'JN_PT001-AF01_001' AS d_created_user,
    NOW() AS d_created_datetime,
    'JN_PT001-AF01_001' AS d_updated_user,
    NOW() AS d_updated_datetime,
    1 AS d_version
FROM
    account_work ON CONFLICT (alternative_customer_code) DO
UPDATE
SET
    external_customer_id = EXCLUDED.external_customer_id,
    customer_status = EXCLUDED.customer_status,
    last_name = EXCLUDED.last_name,
    first_name = EXCLUDED.first_name,
    last_name_hiragana = EXCLUDED.last_name_hiragana,
    first_name_hiragana = EXCLUDED.first_name_hiragana,
    zip_code = EXCLUDED.zip_code,
    state_type = EXCLUDED.state_type,
    city = EXCLUDED.city,
    address1 = EXCLUDED.address1,
    tel_number = EXCLUDED.tel_number,
    birthday = EXCLUDED.birthday,
    sex_type = EXCLUDED.sex_type,
    mail_address = EXCLUDED.mail_address,
    d_updated_user = 'JN_PT001-AF01_001',
    d_updated_datetime = NOW(),
    d_version = account.d_version + 1;