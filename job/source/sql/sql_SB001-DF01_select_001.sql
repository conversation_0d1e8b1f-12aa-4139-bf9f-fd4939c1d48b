WITH TARGET_DATA AS (
    SELECT DISTINCT 
        A.customer_code,
        B.commodity_code AS GOODS_CODE
    FROM regular_sale_cont_header A
    JOIN (SELECT cd.regular_contract_no, COALESCE(cc.child_commodity_code, cd.commodity_code) AS commodity_code, next_shipping_date
        FROM regular_sale_cont_detail cd
        LEFT JOIN regular_sale_cont_composition cc ON cd.commodity_code = cc.parent_commodity_code
    ) B ON A.regular_contract_no = B.regular_contract_no
    WHERE A.regular_sale_cont_status = '1' 
      AND TO_CHAR(B.next_shipping_date, 'YYYYMM') >= TO_CHAR(CURRENT_DATE, 'YYYYMM')
),
RESULT_DATA AS (
    SELECT 
        TO_CHAR(CURRENT_DATE, 'YYYYMM') AS YEAR_MONTH,
        LPAD(X.GOODS_CODE, 10, '0') AS GOODS_CODE,
        COUNT(1) AS CNT
    FROM TARGET_DATA X
    GROUP BY X.GOODS_CODE
)
SELECT 
    X.YEAR_MONTH AS "YEAR_MONTH",
    X.GOODS_CODE AS "PRODUCT_CD",
    TO_CHAR(X.CNT, '9999999999') AS "KANYUSHA_SU"
FROM RESULT_DATA X
ORDER BY LPAD(X.GOODS_CODE, 10, '0');