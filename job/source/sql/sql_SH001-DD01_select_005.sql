SELECT
    shipping_no,
    order_no,
    shop_code,
    customer_code,
    neo_customer_no,
    address_no,
    address_last_name,
    address_first_name,
    address_last_name_kana,
    address_first_name_kana,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    corporation_post_name,
    phone_number,
    delivery_remark,
    acquired_point,
    delivery_slip_no,
    shipping_charge,
    shipping_charge_tax_type,
    shipping_charge_tax_group_code,
    shipping_charge_tax_no,
    shipping_charge_tax_rate,
    shipping_charge_tax,
    delivery_type_no,
    shipping_method,
    delivery_type_name,
    delivery_appointed_date,
    delivery_appointed_time_start,
    delivery_appointed_time_end,
    arrival_date,
    arrival_time_start,
    arrival_time_end,
    fixed_sales_status,
    shipping_status,
    shipping_direct_date,
    shipping_date,
    original_shipping_no,
    return_item_date,
    return_item_type,
    shipping_area,
    delivery_note_flg,
    include_flg,
    delivery_memo,
    shipping_bill_price,
    shipping_dokon_shiji_code,
    o_name_disp_kbn,
    member_stage,
    possession_point,
    sales_recording_date,
    prod_pack_type,
    shipping_method_kbn,
    box_code,
    delivery_note_message,
    sagawa_collect_date,
    delivery_info_no,
    receive_shop_type,
    receive_shop_id,
    receive_shop_name,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    shipping_header
where
    updated_datetime > :sync_datetime
AND updated_datetime <= :diff_base_timestamp;