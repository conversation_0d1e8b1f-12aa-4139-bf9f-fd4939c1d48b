SELECT
    user_code,
    shop_code,
    user_login_id,
    password,
    user_name,
    email,
    login_error_count,
    login_locked_flg,
    login_datetime,
    memo,
    password_last_updated_datetime,
    auth_secret_key,
    role_id,
    login_token,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    user_account
where
    updated_datetime > :sync_datetime
AND updated_datetime <= :diff_base_timestamp;