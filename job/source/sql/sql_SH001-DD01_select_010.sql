SELECT
    customer_code,
    address_no,
    address_alias,
    address_last_name,
    address_first_name,
    address_last_name_kana,
    address_first_name_kana,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    phone_number,
    corporation_post_name,
    address_id,
    crm_address_id,
    crm_address_updated_datetime,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime
FROM
    customer_address
where
    updated_datetime > :sync_datetime
    AND updated_datetime <= :diff_base_timestamp;