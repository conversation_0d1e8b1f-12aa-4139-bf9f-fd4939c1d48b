INSERT INTO order_header (
    order_no,
    shop_code,
    order_datetime,
    customer_code,
    neo_customer_no,
    guest_flg,
    last_name,
    first_name,
    last_name_kana,
    first_name_kana,
    email,
    birth_date,
    sex,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    corporation_post_name,
    phone_number,
    advance_later_flg,
    payment_method_no,
    payment_method_type,
    payment_method_name,
    ext_payment_method_type,
    payment_commission,
    payment_commission_tax_gr_code,
    payment_commission_tax_no,
    payment_commission_tax_rate,
    payment_commission_tax,
    payment_commission_tax_type,
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_type,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_price,
    coupon_discount_rate,
    coupon_used_amount,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_kbn,
    goods_group,
    commodity_category_code,
    commodity_series,
    coupon_commodity_code_display,
    baitai_name,
    used_point,
    total_amount,
    ec_promotion_id,
    ec_promotion_name,
    ec_promotion_discount_price,
    ec_campaign_id,
    ec_campaign_name,
    payment_date,
    payment_limit_date,
    payment_status,
    ext_payment_status,
    customer_group_code,
    data_transport_status,
    order_status,
    ext_order_status,
    tax_reference_date,
    cancel_date,
    client_group,
    caution,
    message,
    payment_order_id,
    cvs_code,
    payment_receipt_no,
    payment_receipt_url,
    receipt_no,
    customer_no,
    confirm_no,
    career_key,
    order_create_error_code,
    order_display_status,
    order_kind_kbn,
    marketing_channel,
    original_order_no,
    external_order_no,
    order_recieve_datetime,
    order_update_datetime,
    order_update_reason_kbn,
    cancel_reason_kbn,
    uncollectible_date,
    order_total_price,
    account_receivable_balance,
    appropriate_amount,
    bill_address_kbn,
    receipt_flg,
    receipt_to,
    receipt_detail,
    bill_price,
    bill_no,
    bill_print_count,
    authority_result_kbn,
    authority_no,
    card_password,
    authority_approval_no,
    authority_date,
    authority_price,
    authority_cancel_approval_no,
    authority_cancel_date,
    credit_payment_no,
    credit_payment_date,
    credit_payment_price,
    credit_cancel_payment_no,
    credit_cancel_payment_date,
    credit_result_kbn,
    card_brand,
    credit_card_kanri_no,
    credit_card_kanri_detail_no,
    credit_card_no,
    credit_card_meigi,
    credit_card_valid_year,
    credit_card_valid_month,
    credit_card_pay_count,
    payment_bar_code,
    amzn_charge_permission_id,
    amzn_charge_id,
    amzn_charge_status,
    amzn_authorization_datetime,
    amzn_capture_initiated_datetime,
    amzn_captured_datetime,
    amzn_canceled_datetime,
    order_user_code,
    order_user,
    change_user_code,
    change_user,
    demand_kbn,
    demand1_ref_date,
    demand1_date,
    demand1_limit_date,
    demand1_amount,
    demand1_bar_code,
    demand2_ref_date,
    demand2_date,
    demand2_limit_date,
    demand2_amount,
    demand2_bar_code,
    demand3_ref_date,
    demand3_date,
    demand3_limit_date,
    demand3_amount,
    demand3_bar_code,
    kashidaore_date,
    demand_exclude_reason_kbn,
    demand_exclude_start_date,
    demand_exclude_end_date,
    bill_sei_kj,
    bill_mei_kj,
    bill_sei_kn,
    bill_mei_kn,
    bill_tel_no,
    bill_zipcd,
    bill_addr1,
    bill_addr2,
    bill_addr3,
    bill_addr4,
    bill_corporation_post_name,
    nohinsyo_uketsuke_tanto,
    grant_plan_point_prod,
    grant_plan_point_other,
    grant_plan_point_total,
    grant_point_prod,
    grant_point_other,
    grant_point_total,
    reduction_plan_point_total,
    reduction_point_total,
    subtotal_before_campaign,
    subtotal_after_campaign,
    total_before_campaign,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    order_no,
    shop_code,
    order_datetime,
    customer_code,
    neo_customer_no,
    guest_flg,
    last_name,
    first_name,
    last_name_kana,
    first_name_kana,
    email,
    birth_date,
    sex,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    corporation_post_name,
    phone_number,
    advance_later_flg,
    payment_method_no,
    payment_method_type,
    payment_method_name,
    ext_payment_method_type,
    payment_commission,
    payment_commission_tax_gr_code,
    payment_commission_tax_no,
    payment_commission_tax_rate,
    payment_commission_tax,
    payment_commission_tax_type,
    coupon_management_code,
    coupon_code,
    coupon_name,
    coupon_type,
    coupon_use_purchase_price,
    coupon_discount_type,
    coupon_discount_price,
    coupon_discount_rate,
    coupon_used_amount,
    coupon_start_datetime,
    coupon_end_datetime,
    coupon_kbn,
    goods_group,
    commodity_category_code,
    commodity_series,
    coupon_commodity_code_display,
    baitai_name,
    used_point,
    total_amount,
    ec_promotion_id,
    ec_promotion_name,
    ec_promotion_discount_price,
    ec_campaign_id,
    ec_campaign_name,
    payment_date,
    payment_limit_date,
    payment_status,
    ext_payment_status,
    customer_group_code,
    data_transport_status,
    order_status,
    ext_order_status,
    tax_reference_date,
    cancel_date,
    client_group,
    caution,
    message,
    payment_order_id,
    cvs_code,
    payment_receipt_no,
    payment_receipt_url,
    receipt_no,
    customer_no,
    confirm_no,
    career_key,
    order_create_error_code,
    order_display_status,
    order_kind_kbn,
    marketing_channel,
    original_order_no,
    external_order_no,
    order_recieve_datetime,
    order_update_datetime,
    order_update_reason_kbn,
    cancel_reason_kbn,
    uncollectible_date,
    order_total_price,
    account_receivable_balance,
    appropriate_amount,
    bill_address_kbn,
    receipt_flg,
    receipt_to,
    receipt_detail,
    bill_price,
    bill_no,
    bill_print_count,
    authority_result_kbn,
    authority_no,
    card_password,
    authority_approval_no,
    authority_date,
    authority_price,
    authority_cancel_approval_no,
    authority_cancel_date,
    credit_payment_no,
    credit_payment_date,
    credit_payment_price,
    credit_cancel_payment_no,
    credit_cancel_payment_date,
    credit_result_kbn,
    card_brand,
    credit_card_kanri_no,
    credit_card_kanri_detail_no,
    credit_card_no,
    credit_card_meigi,
    credit_card_valid_year,
    credit_card_valid_month,
    credit_card_pay_count,
    payment_bar_code,
    amzn_charge_permission_id,
    amzn_charge_id,
    amzn_charge_status,
    amzn_authorization_datetime,
    amzn_capture_initiated_datetime,
    amzn_captured_datetime,
    amzn_canceled_datetime,
    order_user_code,
    order_user,
    change_user_code,
    change_user,
    demand_kbn,
    demand1_ref_date,
    demand1_date,
    demand1_limit_date,
    demand1_amount,
    demand1_bar_code,
    demand2_ref_date,
    demand2_date,
    demand2_limit_date,
    demand2_amount,
    demand2_bar_code,
    demand3_ref_date,
    demand3_date,
    demand3_limit_date,
    demand3_amount,
    demand3_bar_code,
    kashidaore_date,
    demand_exclude_reason_kbn,
    demand_exclude_start_date,
    demand_exclude_end_date,
    bill_sei_kj,
    bill_mei_kj,
    bill_sei_kn,
    bill_mei_kn,
    bill_tel_no,
    bill_zipcd,
    bill_addr1,
    bill_addr2,
    bill_addr3,
    bill_addr4,
    bill_corporation_post_name,
    nohinsyo_uketsuke_tanto,
    grant_plan_point_prod,
    grant_plan_point_other,
    grant_plan_point_total,
    grant_point_prod,
    grant_point_other,
    grant_point_total,
    reduction_plan_point_total,
    reduction_point_total,
    subtotal_before_campaign,
    subtotal_after_campaign,
    total_before_campaign,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_SH001-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM order_header_work
ON CONFLICT (order_no) DO UPDATE SET
    shop_code = EXCLUDED.shop_code,
    order_datetime = EXCLUDED.order_datetime,
    customer_code = EXCLUDED.customer_code,
    neo_customer_no = EXCLUDED.neo_customer_no,
    guest_flg = EXCLUDED.guest_flg,
    last_name = EXCLUDED.last_name,
    first_name = EXCLUDED.first_name,
    last_name_kana = EXCLUDED.last_name_kana,
    first_name_kana = EXCLUDED.first_name_kana,
    email = EXCLUDED.email,
    birth_date = EXCLUDED.birth_date,
    sex = EXCLUDED.sex,
    postal_code = EXCLUDED.postal_code,
    prefecture_code = EXCLUDED.prefecture_code,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    address3 = EXCLUDED.address3,
    address4 = EXCLUDED.address4,
    corporation_post_name = EXCLUDED.corporation_post_name,
    phone_number = EXCLUDED.phone_number,
    advance_later_flg = EXCLUDED.advance_later_flg,
    payment_method_no = EXCLUDED.payment_method_no,
    payment_method_type = EXCLUDED.payment_method_type,
    payment_method_name = EXCLUDED.payment_method_name,
    ext_payment_method_type = EXCLUDED.ext_payment_method_type,
    payment_commission = EXCLUDED.payment_commission,
    payment_commission_tax_gr_code = EXCLUDED.payment_commission_tax_gr_code,
    payment_commission_tax_no = EXCLUDED.payment_commission_tax_no,
    payment_commission_tax_rate = EXCLUDED.payment_commission_tax_rate,
    payment_commission_tax = EXCLUDED.payment_commission_tax,
    payment_commission_tax_type = EXCLUDED.payment_commission_tax_type,
    coupon_management_code = EXCLUDED.coupon_management_code,
    coupon_code = EXCLUDED.coupon_code,
    coupon_name = EXCLUDED.coupon_name,
    coupon_type = EXCLUDED.coupon_type,
    coupon_use_purchase_price = EXCLUDED.coupon_use_purchase_price,
    coupon_discount_type = EXCLUDED.coupon_discount_type,
    coupon_discount_price = EXCLUDED.coupon_discount_price,
    coupon_discount_rate = EXCLUDED.coupon_discount_rate,
    coupon_used_amount = EXCLUDED.coupon_used_amount,
    coupon_start_datetime = EXCLUDED.coupon_start_datetime,
    coupon_end_datetime = EXCLUDED.coupon_end_datetime,
    coupon_kbn = EXCLUDED.coupon_kbn,
    goods_group = EXCLUDED.goods_group,
    commodity_category_code = EXCLUDED.commodity_category_code,
    commodity_series = EXCLUDED.commodity_series,
    coupon_commodity_code_display = EXCLUDED.coupon_commodity_code_display,
    baitai_name = EXCLUDED.baitai_name,
    used_point = EXCLUDED.used_point,
    total_amount = EXCLUDED.total_amount,
    ec_promotion_id = EXCLUDED.ec_promotion_id,
    ec_promotion_name = EXCLUDED.ec_promotion_name,
    ec_promotion_discount_price = EXCLUDED.ec_promotion_discount_price,
    ec_campaign_id = EXCLUDED.ec_campaign_id,
    ec_campaign_name = EXCLUDED.ec_campaign_name,
    payment_date = EXCLUDED.payment_date,
    payment_limit_date = EXCLUDED.payment_limit_date,
    payment_status = EXCLUDED.payment_status,
    ext_payment_status = EXCLUDED.ext_payment_status,
    customer_group_code = EXCLUDED.customer_group_code,
    data_transport_status = EXCLUDED.data_transport_status,
    order_status = EXCLUDED.order_status,
    ext_order_status = EXCLUDED.ext_order_status,
    tax_reference_date = EXCLUDED.tax_reference_date,
    cancel_date = EXCLUDED.cancel_date,
    client_group = EXCLUDED.client_group,
    caution = EXCLUDED.caution,
    message = EXCLUDED.message,
    payment_order_id = EXCLUDED.payment_order_id,
    cvs_code = EXCLUDED.cvs_code,
    payment_receipt_no = EXCLUDED.payment_receipt_no,
    payment_receipt_url = EXCLUDED.payment_receipt_url,
    receipt_no = EXCLUDED.receipt_no,
    customer_no = EXCLUDED.customer_no,
    confirm_no = EXCLUDED.confirm_no,
    career_key = EXCLUDED.career_key,
    order_create_error_code = EXCLUDED.order_create_error_code,
    order_display_status = EXCLUDED.order_display_status,
    order_kind_kbn = EXCLUDED.order_kind_kbn,
    marketing_channel = EXCLUDED.marketing_channel,
    original_order_no = EXCLUDED.original_order_no,
    external_order_no = EXCLUDED.external_order_no,
    order_recieve_datetime = EXCLUDED.order_recieve_datetime,
    order_update_datetime = EXCLUDED.order_update_datetime,
    order_update_reason_kbn = EXCLUDED.order_update_reason_kbn,
    cancel_reason_kbn = EXCLUDED.cancel_reason_kbn,
    uncollectible_date = EXCLUDED.uncollectible_date,
    order_total_price = EXCLUDED.order_total_price,
    account_receivable_balance = EXCLUDED.account_receivable_balance,
    appropriate_amount = EXCLUDED.appropriate_amount,
    bill_address_kbn = EXCLUDED.bill_address_kbn,
    receipt_flg = EXCLUDED.receipt_flg,
    receipt_to = EXCLUDED.receipt_to,
    receipt_detail = EXCLUDED.receipt_detail,
    bill_price = EXCLUDED.bill_price,
    bill_no = EXCLUDED.bill_no,
    bill_print_count = EXCLUDED.bill_print_count,
    authority_result_kbn = EXCLUDED.authority_result_kbn,
    authority_no = EXCLUDED.authority_no,
    card_password = EXCLUDED.card_password,
    authority_approval_no = EXCLUDED.authority_approval_no,
    authority_date = EXCLUDED.authority_date,
    authority_price = EXCLUDED.authority_price,
    authority_cancel_approval_no = EXCLUDED.authority_cancel_approval_no,
    authority_cancel_date = EXCLUDED.authority_cancel_date,
    credit_payment_no = EXCLUDED.credit_payment_no,
    credit_payment_date = EXCLUDED.credit_payment_date,
    credit_payment_price = EXCLUDED.credit_payment_price,
    credit_cancel_payment_no = EXCLUDED.credit_cancel_payment_no,
    credit_cancel_payment_date = EXCLUDED.credit_cancel_payment_date,
    credit_result_kbn = EXCLUDED.credit_result_kbn,
    card_brand = EXCLUDED.card_brand,
    credit_card_kanri_no = EXCLUDED.credit_card_kanri_no,
    credit_card_kanri_detail_no = EXCLUDED.credit_card_kanri_detail_no,
    credit_card_no = EXCLUDED.credit_card_no,
    credit_card_meigi = EXCLUDED.credit_card_meigi,
    credit_card_valid_year = EXCLUDED.credit_card_valid_year,
    credit_card_valid_month = EXCLUDED.credit_card_valid_month,
    credit_card_pay_count = EXCLUDED.credit_card_pay_count,
    payment_bar_code = EXCLUDED.payment_bar_code,
    amzn_charge_permission_id = EXCLUDED.amzn_charge_permission_id,
    amzn_charge_id = EXCLUDED.amzn_charge_id,
    amzn_charge_status = EXCLUDED.amzn_charge_status,
    amzn_authorization_datetime = EXCLUDED.amzn_authorization_datetime,
    amzn_capture_initiated_datetime = EXCLUDED.amzn_capture_initiated_datetime,
    amzn_captured_datetime = EXCLUDED.amzn_captured_datetime,
    amzn_canceled_datetime = EXCLUDED.amzn_canceled_datetime,
    order_user_code = EXCLUDED.order_user_code,
    order_user = EXCLUDED.order_user,
    change_user_code = EXCLUDED.change_user_code,
    change_user = EXCLUDED.change_user,
    demand_kbn = EXCLUDED.demand_kbn,
    demand1_ref_date = EXCLUDED.demand1_ref_date,
    demand1_date = EXCLUDED.demand1_date,
    demand1_limit_date = EXCLUDED.demand1_limit_date,
    demand1_amount = EXCLUDED.demand1_amount,
    demand1_bar_code = EXCLUDED.demand1_bar_code,
    demand2_ref_date = EXCLUDED.demand2_ref_date,
    demand2_date = EXCLUDED.demand2_date,
    demand2_limit_date = EXCLUDED.demand2_limit_date,
    demand2_amount = EXCLUDED.demand2_amount,
    demand2_bar_code = EXCLUDED.demand2_bar_code,
    demand3_ref_date = EXCLUDED.demand3_ref_date,
    demand3_date = EXCLUDED.demand3_date,
    demand3_limit_date = EXCLUDED.demand3_limit_date,
    demand3_amount = EXCLUDED.demand3_amount,
    demand3_bar_code = EXCLUDED.demand3_bar_code,
    kashidaore_date = EXCLUDED.kashidaore_date,
    demand_exclude_reason_kbn = EXCLUDED.demand_exclude_reason_kbn,
    demand_exclude_start_date = EXCLUDED.demand_exclude_start_date,
    demand_exclude_end_date = EXCLUDED.demand_exclude_end_date,
    bill_sei_kj = EXCLUDED.bill_sei_kj,
    bill_mei_kj = EXCLUDED.bill_mei_kj,
    bill_sei_kn = EXCLUDED.bill_sei_kn,
    bill_mei_kn = EXCLUDED.bill_mei_kn,
    bill_tel_no = EXCLUDED.bill_tel_no,
    bill_zipcd = EXCLUDED.bill_zipcd,
    bill_addr1 = EXCLUDED.bill_addr1,
    bill_addr2 = EXCLUDED.bill_addr2,
    bill_addr3 = EXCLUDED.bill_addr3,
    bill_addr4 = EXCLUDED.bill_addr4,
    bill_corporation_post_name = EXCLUDED.bill_corporation_post_name,
    nohinsyo_uketsuke_tanto = EXCLUDED.nohinsyo_uketsuke_tanto,
    grant_plan_point_prod = EXCLUDED.grant_plan_point_prod,
    grant_plan_point_other = EXCLUDED.grant_plan_point_other,
    grant_plan_point_total = EXCLUDED.grant_plan_point_total,
    grant_point_prod = EXCLUDED.grant_point_prod,
    grant_point_other = EXCLUDED.grant_point_other,
    grant_point_total = EXCLUDED.grant_point_total,
    reduction_plan_point_total = EXCLUDED.reduction_plan_point_total,
    reduction_point_total = EXCLUDED.reduction_point_total,
    subtotal_before_campaign = EXCLUDED.subtotal_before_campaign,
    subtotal_after_campaign = EXCLUDED.subtotal_after_campaign,
    total_before_campaign = EXCLUDED.total_before_campaign,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = order_header.d_version + 1;
