INSERT INTO regular_sale_cont_header (
    regular_contract_no,
    shop_code,
    regular_sale_cont_datetime,
    customer_code,
    neo_customer_no,
    payment_method_no,
    address_no,
    regular_sale_cont_status,
    next_delivery_request_date,
    external_order_no,
    order_user_code,
    regular_update_datetime,
    change_user_code,
    regular_update_reason_kbn,
    otodoke_hope_time_kbn,
    marketing_channel,
    delivery_type_no,
    shipping_method_flg,
    ext_payment_method_type,
    card_brand,
    credit_card_kanri_no,
    credit_card_kanri_detail_no,
    credit_card_no,
    credit_card_meigi,
    credit_card_valid_year,
    credit_card_valid_month,
    credit_card_pay_count,
    amzn_charge_permission_id,
    bill_address_kbn,
    bill_print_otodoke_id,
    o_name_disp_kbn,
    delivery_note_flg,
    include_flg,
    receipt_flg,
    receipt_to,
    receipt_detail,
    first_shipping_date,
    lastest_shipping_date,
    first_delivery_date,
    lastest_delivery_date,
    regular_stop_date,
    regular_stop_reason_kbn,
    regular_hold_date,
    regular_hold_clear_date,
    regular_kaiji,
    shipped_regular_count,
    delivery_memo,
    regular_hold_reason_kbn,
    niyose_flg,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    regular_contract_no,
    shop_code,
    regular_sale_cont_datetime,
    customer_code,
    neo_customer_no,
    payment_method_no,
    address_no,
    regular_sale_cont_status,
    next_delivery_request_date,
    external_order_no,
    order_user_code,
    regular_update_datetime,
    change_user_code,
    regular_update_reason_kbn,
    otodoke_hope_time_kbn,
    marketing_channel,
    delivery_type_no,
    shipping_method_flg,
    ext_payment_method_type,
    card_brand,
    credit_card_kanri_no,
    credit_card_kanri_detail_no,
    credit_card_no,
    credit_card_meigi,
    credit_card_valid_year,
    credit_card_valid_month,
    credit_card_pay_count,
    amzn_charge_permission_id,
    bill_address_kbn,
    bill_print_otodoke_id,
    o_name_disp_kbn,
    delivery_note_flg,
    include_flg,
    receipt_flg,
    receipt_to,
    receipt_detail,
    first_shipping_date,
    lastest_shipping_date,
    first_delivery_date,
    lastest_delivery_date,
    regular_stop_date,
    regular_stop_reason_kbn,
    regular_hold_date,
    regular_hold_clear_date,
    regular_kaiji,
    shipped_regular_count,
    delivery_memo,
    regular_hold_reason_kbn,
    niyose_flg,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_SH001-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM regular_sale_cont_header_work
ON CONFLICT (regular_contract_no) DO UPDATE SET
    shop_code = EXCLUDED.shop_code,
    regular_sale_cont_datetime = EXCLUDED.regular_sale_cont_datetime,
    customer_code = EXCLUDED.customer_code,
    neo_customer_no = EXCLUDED.neo_customer_no,
    payment_method_no = EXCLUDED.payment_method_no,
    address_no = EXCLUDED.address_no,
    regular_sale_cont_status = EXCLUDED.regular_sale_cont_status,
    next_delivery_request_date = EXCLUDED.next_delivery_request_date,
    external_order_no = EXCLUDED.external_order_no,
    order_user_code = EXCLUDED.order_user_code,
    regular_update_datetime = EXCLUDED.regular_update_datetime,
    change_user_code = EXCLUDED.change_user_code,
    regular_update_reason_kbn = EXCLUDED.regular_update_reason_kbn,
    otodoke_hope_time_kbn = EXCLUDED.otodoke_hope_time_kbn,
    marketing_channel = EXCLUDED.marketing_channel,
    delivery_type_no = EXCLUDED.delivery_type_no,
    shipping_method_flg = EXCLUDED.shipping_method_flg,
    ext_payment_method_type = EXCLUDED.ext_payment_method_type,
    card_brand = EXCLUDED.card_brand,
    credit_card_kanri_no = EXCLUDED.credit_card_kanri_no,
    credit_card_kanri_detail_no = EXCLUDED.credit_card_kanri_detail_no,
    credit_card_no = EXCLUDED.credit_card_no,
    credit_card_meigi = EXCLUDED.credit_card_meigi,
    credit_card_valid_year = EXCLUDED.credit_card_valid_year,
    credit_card_valid_month = EXCLUDED.credit_card_valid_month,
    credit_card_pay_count = EXCLUDED.credit_card_pay_count,
    amzn_charge_permission_id = EXCLUDED.amzn_charge_permission_id,
    bill_address_kbn = EXCLUDED.bill_address_kbn,
    bill_print_otodoke_id = EXCLUDED.bill_print_otodoke_id,
    o_name_disp_kbn = EXCLUDED.o_name_disp_kbn,
    delivery_note_flg = EXCLUDED.delivery_note_flg,
    include_flg = EXCLUDED.include_flg,
    receipt_flg = EXCLUDED.receipt_flg,
    receipt_to = EXCLUDED.receipt_to,
    receipt_detail = EXCLUDED.receipt_detail,
    first_shipping_date = EXCLUDED.first_shipping_date,
    lastest_shipping_date = EXCLUDED.lastest_shipping_date,
    first_delivery_date = EXCLUDED.first_delivery_date,
    lastest_delivery_date = EXCLUDED.lastest_delivery_date,
    regular_stop_date = EXCLUDED.regular_stop_date,
    regular_stop_reason_kbn = EXCLUDED.regular_stop_reason_kbn,
    regular_hold_date = EXCLUDED.regular_hold_date,
    regular_hold_clear_date = EXCLUDED.regular_hold_clear_date,
    regular_kaiji = EXCLUDED.regular_kaiji,
    shipped_regular_count = EXCLUDED.shipped_regular_count,
    delivery_memo = EXCLUDED.delivery_memo,
    regular_hold_reason_kbn = EXCLUDED.regular_hold_reason_kbn,
    niyose_flg = EXCLUDED.niyose_flg,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = regular_sale_cont_header.d_version + 1;