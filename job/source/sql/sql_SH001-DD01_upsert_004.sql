INSERT INTO regular_sale_cont_detail (
    regular_contract_no,
    regular_contract_detail_no,
    shop_code,
    sku_code,
    commodity_code,
    contract_amount,
    commodity_name,
    commodity_subcategory_code,
    commodity_subcategory_code_name,
    baitai_code,
    regular_cycle_delivery_kbn,
    regular_cycle_kijun_date,
    regular_kind,
    regular_cycle_day_int,
    regular_cycle_day,
    regular_cycle_mon_interval,
    regular_cycle_mon_interval_day,
    regular_cycle_week_num,
    regular_cycle_week_kbn,
    regular_cycle_week_mon,
    regular_cycle_week_tue,
    regular_cycle_week_wed,
    regular_cycle_week_thu,
    regular_cycle_week_fri,
    regular_cycle_week_sat,
    regular_cycle_week_sun,
    regular_cycle_week_hol,
    cycle_disp_name,
    next_shipping_plan_date,
    next_shipping_date,
    next_delivery_plan_date,
    next_delivery_date,
    lastest_delivery_date,
    regular_kaiji,
    shipped_regular_count,
    regular_sale_stop_from,
    regular_sale_stop_to,
    hasso_souko_cd,
    shipping_area,
    regular_check_memo,
    regular_memo_hold_flg,
    souko_shiji,
    next_regular_sale_stop_status,
    regular_stop_reason_kbn,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    regular_contract_no,
    regular_contract_detail_no,
    shop_code,
    sku_code,
    commodity_code,
    contract_amount,
    commodity_name,
    commodity_subcategory_code,
    commodity_subcategory_code_name,
    baitai_code,
    regular_cycle_delivery_kbn,
    regular_cycle_kijun_date,
    regular_kind,
    regular_cycle_day_int,
    regular_cycle_day,
    regular_cycle_mon_interval,
    regular_cycle_mon_interval_day,
    regular_cycle_week_num,
    regular_cycle_week_kbn,
    regular_cycle_week_mon,
    regular_cycle_week_tue,
    regular_cycle_week_wed,
    regular_cycle_week_thu,
    regular_cycle_week_fri,
    regular_cycle_week_sat,
    regular_cycle_week_sun,
    regular_cycle_week_hol,
    cycle_disp_name,
    next_shipping_plan_date,
    next_shipping_date,
    next_delivery_plan_date,
    next_delivery_date,
    lastest_delivery_date,
    regular_kaiji,
    shipped_regular_count,
    regular_sale_stop_from,
    regular_sale_stop_to,
    hasso_souko_cd,
    shipping_area,
    regular_check_memo,
    regular_memo_hold_flg,
    souko_shiji,
    next_regular_sale_stop_status,
    regular_stop_reason_kbn,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_SH001-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM regular_sale_cont_detail_work
ON CONFLICT (regular_contract_no, regular_contract_detail_no) DO UPDATE SET
    shop_code = EXCLUDED.shop_code,
    sku_code = EXCLUDED.sku_code,
    commodity_code = EXCLUDED.commodity_code,
    contract_amount = EXCLUDED.contract_amount,
    commodity_name = EXCLUDED.commodity_name,
    commodity_subcategory_code = EXCLUDED.commodity_subcategory_code,
    commodity_subcategory_code_name = EXCLUDED.commodity_subcategory_code_name,
    baitai_code = EXCLUDED.baitai_code,
    regular_cycle_delivery_kbn = EXCLUDED.regular_cycle_delivery_kbn,
    regular_cycle_kijun_date = EXCLUDED.regular_cycle_kijun_date,
    regular_kind = EXCLUDED.regular_kind,
    regular_cycle_day_int = EXCLUDED.regular_cycle_day_int,
    regular_cycle_day = EXCLUDED.regular_cycle_day,
    regular_cycle_mon_interval = EXCLUDED.regular_cycle_mon_interval,
    regular_cycle_mon_interval_day = EXCLUDED.regular_cycle_mon_interval_day,
    regular_cycle_week_num = EXCLUDED.regular_cycle_week_num,
    regular_cycle_week_kbn = EXCLUDED.regular_cycle_week_kbn,
    regular_cycle_week_mon = EXCLUDED.regular_cycle_week_mon,
    regular_cycle_week_tue = EXCLUDED.regular_cycle_week_tue,
    regular_cycle_week_wed = EXCLUDED.regular_cycle_week_wed,
    regular_cycle_week_thu = EXCLUDED.regular_cycle_week_thu,
    regular_cycle_week_fri = EXCLUDED.regular_cycle_week_fri,
    regular_cycle_week_sat = EXCLUDED.regular_cycle_week_sat,
    regular_cycle_week_sun = EXCLUDED.regular_cycle_week_sun,
    regular_cycle_week_hol = EXCLUDED.regular_cycle_week_hol,
    cycle_disp_name = EXCLUDED.cycle_disp_name,
    next_shipping_plan_date = EXCLUDED.next_shipping_plan_date,
    next_shipping_date = EXCLUDED.next_shipping_date,
    next_delivery_plan_date = EXCLUDED.next_delivery_plan_date,
    next_delivery_date = EXCLUDED.next_delivery_date,
    lastest_delivery_date = EXCLUDED.lastest_delivery_date,
    regular_kaiji = EXCLUDED.regular_kaiji,
    shipped_regular_count = EXCLUDED.shipped_regular_count,
    regular_sale_stop_from = EXCLUDED.regular_sale_stop_from,
    regular_sale_stop_to = EXCLUDED.regular_sale_stop_to,
    hasso_souko_cd = EXCLUDED.hasso_souko_cd,
    shipping_area = EXCLUDED.shipping_area,
    regular_check_memo = EXCLUDED.regular_check_memo,
    regular_memo_hold_flg = EXCLUDED.regular_memo_hold_flg,
    souko_shiji = EXCLUDED.souko_shiji,
    next_regular_sale_stop_status = EXCLUDED.next_regular_sale_stop_status,
    regular_stop_reason_kbn = EXCLUDED.regular_stop_reason_kbn,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = regular_sale_cont_detail.d_version + 1;