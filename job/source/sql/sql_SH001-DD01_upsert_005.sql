INSERT INTO shipping_header (
    shipping_no,
    order_no,
    shop_code,
    customer_code,
    neo_customer_no,
    address_no,
    address_last_name,
    address_first_name,
    address_last_name_kana,
    address_first_name_kana,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    corporation_post_name,
    phone_number,
    delivery_remark,
    acquired_point,
    delivery_slip_no,
    shipping_charge,
    shipping_charge_tax_type,
    shipping_charge_tax_group_code,
    shipping_charge_tax_no,
    shipping_charge_tax_rate,
    shipping_charge_tax,
    delivery_type_no,
    shipping_method,
    delivery_type_name,
    delivery_appointed_date,
    delivery_appointed_time_start,
    delivery_appointed_time_end,
    arrival_date,
    arrival_time_start,
    arrival_time_end,
    fixed_sales_status,
    shipping_status,
    shipping_direct_date,
    shipping_date,
    original_shipping_no,
    return_item_date,
    return_item_type,
    shipping_area,
    delivery_note_flg,
    include_flg,
    delivery_memo,
    shipping_bill_price,
    shipping_dokon_shiji_code,
    o_name_disp_kbn,
    member_stage,
    possession_point,
    sales_recording_date,
    prod_pack_type,
    shipping_method_kbn,
    box_code,
    delivery_note_message,
    sagawa_collect_date,
    delivery_info_no,
    receive_shop_type,
    receive_shop_id,
    receive_shop_name,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shipping_no,
    order_no,
    shop_code,
    customer_code,
    neo_customer_no,
    address_no,
    address_last_name,
    address_first_name,
    address_last_name_kana,
    address_first_name_kana,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    corporation_post_name,
    phone_number,
    delivery_remark,
    acquired_point,
    delivery_slip_no,
    shipping_charge,
    shipping_charge_tax_type,
    shipping_charge_tax_group_code,
    shipping_charge_tax_no,
    shipping_charge_tax_rate,
    shipping_charge_tax,
    delivery_type_no,
    shipping_method,
    delivery_type_name,
    delivery_appointed_date,
    delivery_appointed_time_start,
    delivery_appointed_time_end,
    arrival_date,
    arrival_time_start,
    arrival_time_end,
    fixed_sales_status,
    shipping_status,
    shipping_direct_date,
    shipping_date,
    original_shipping_no,
    return_item_date,
    return_item_type,
    shipping_area,
    delivery_note_flg,
    include_flg,
    delivery_memo,
    shipping_bill_price,
    shipping_dokon_shiji_code,
    o_name_disp_kbn,
    member_stage,
    possession_point,
    sales_recording_date,
    prod_pack_type,
    shipping_method_kbn,
    box_code,
    delivery_note_message,
    sagawa_collect_date,
    delivery_info_no,
    receive_shop_type,
    receive_shop_id,
    receive_shop_name,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_SH001-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM shipping_header_work
ON CONFLICT (shipping_no) DO UPDATE SET
    order_no = EXCLUDED.order_no,
    shop_code = EXCLUDED.shop_code,
    customer_code = EXCLUDED.customer_code,
    neo_customer_no = EXCLUDED.neo_customer_no,
    address_no = EXCLUDED.address_no,
    address_last_name = EXCLUDED.address_last_name,
    address_first_name = EXCLUDED.address_first_name,
    address_last_name_kana = EXCLUDED.address_last_name_kana,
    address_first_name_kana = EXCLUDED.address_first_name_kana,
    postal_code = EXCLUDED.postal_code,
    prefecture_code = EXCLUDED.prefecture_code,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    address3 = EXCLUDED.address3,
    address4 = EXCLUDED.address4,
    corporation_post_name = EXCLUDED.corporation_post_name,
    phone_number = EXCLUDED.phone_number,
    delivery_remark = EXCLUDED.delivery_remark,
    acquired_point = EXCLUDED.acquired_point,
    delivery_slip_no = EXCLUDED.delivery_slip_no,
    shipping_charge = EXCLUDED.shipping_charge,
    shipping_charge_tax_type = EXCLUDED.shipping_charge_tax_type,
    shipping_charge_tax_group_code = EXCLUDED.shipping_charge_tax_group_code,
    shipping_charge_tax_no = EXCLUDED.shipping_charge_tax_no,
    shipping_charge_tax_rate = EXCLUDED.shipping_charge_tax_rate,
    shipping_charge_tax = EXCLUDED.shipping_charge_tax,
    delivery_type_no = EXCLUDED.delivery_type_no,
    shipping_method = EXCLUDED.shipping_method,
    delivery_type_name = EXCLUDED.delivery_type_name,
    delivery_appointed_date = EXCLUDED.delivery_appointed_date,
    delivery_appointed_time_start = EXCLUDED.delivery_appointed_time_start,
    delivery_appointed_time_end = EXCLUDED.delivery_appointed_time_end,
    arrival_date = EXCLUDED.arrival_date,
    arrival_time_start = EXCLUDED.arrival_time_start,
    arrival_time_end = EXCLUDED.arrival_time_end,
    fixed_sales_status = EXCLUDED.fixed_sales_status,
    shipping_status = EXCLUDED.shipping_status,
    shipping_direct_date = EXCLUDED.shipping_direct_date,
    shipping_date = EXCLUDED.shipping_date,
    original_shipping_no = EXCLUDED.original_shipping_no,
    return_item_date = EXCLUDED.return_item_date,
    return_item_type = EXCLUDED.return_item_type,
    shipping_area = EXCLUDED.shipping_area,
    delivery_note_flg = EXCLUDED.delivery_note_flg,
    include_flg = EXCLUDED.include_flg,
    delivery_memo = EXCLUDED.delivery_memo,
    shipping_bill_price = EXCLUDED.shipping_bill_price,
    shipping_dokon_shiji_code = EXCLUDED.shipping_dokon_shiji_code,
    o_name_disp_kbn = EXCLUDED.o_name_disp_kbn,
    member_stage = EXCLUDED.member_stage,
    possession_point = EXCLUDED.possession_point,
    sales_recording_date = EXCLUDED.sales_recording_date,
    prod_pack_type = EXCLUDED.prod_pack_type,
    shipping_method_kbn = EXCLUDED.shipping_method_kbn,
    box_code = EXCLUDED.box_code,
    delivery_note_message = EXCLUDED.delivery_note_message,
    sagawa_collect_date = EXCLUDED.sagawa_collect_date,
    delivery_info_no = EXCLUDED.delivery_info_no,
    receive_shop_type = EXCLUDED.receive_shop_type,
    receive_shop_id = EXCLUDED.receive_shop_id,
    receive_shop_name = EXCLUDED.receive_shop_name,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = shipping_header.d_version + 1;