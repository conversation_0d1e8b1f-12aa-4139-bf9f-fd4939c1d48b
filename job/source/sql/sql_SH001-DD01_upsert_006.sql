INSERT INTO user_account (
    user_code,
    shop_code,
    user_login_id,
    password,
    user_name,
    email,
    login_error_count,
    login_locked_flg,
    login_datetime,
    memo,
    password_last_updated_datetime,
    auth_secret_key,
    role_id,
    login_token,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    user_code,
    shop_code,
    user_login_id,
    password,
    user_name,
    email,
    login_error_count,
    login_locked_flg,
    login_datetime,
    memo,
    password_last_updated_datetime,
    auth_secret_key,
    role_id,
    login_token,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_SH001-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM user_account_work
ON CONFLICT (user_code) DO UPDATE SET
    shop_code = EXCLUDED.shop_code,
    user_login_id = EXCLUDED.user_login_id,
    password = EXCLUDED.password,
    user_name = EXCLUDED.user_name,
    email = EXCLUDED.email,
    login_error_count = EXCLUDED.login_error_count,
    login_locked_flg = EXCLUDED.login_locked_flg,
    login_datetime = EXCLUDED.login_datetime,
    memo = EXCLUDED.memo,
    password_last_updated_datetime = EXCLUDED.password_last_updated_datetime,
    auth_secret_key = EXCLUDED.auth_secret_key,
    role_id = EXCLUDED.role_id,
    login_token = EXCLUDED.login_token,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = user_account.d_version + 1;