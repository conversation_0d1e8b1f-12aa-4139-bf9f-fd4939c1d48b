INSERT INTO order_campaign (
    order_no,
    campaign_instructions_code,
    campaign_instructions_name,
    campaign_description,
    campaign_end_date,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    order_no,
    campaign_instructions_code,
    campaign_instructions_name,
    campaign_description,
    campaign_end_date,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',  -- d_created_user
    NOW(),                -- d_created_datetime
    'JN_SH001-DD01_001',  -- d_updated_user
    NOW(),                -- d_updated_datetime
    1                     -- d_version
FROM order_campaign_work
ON CONFLICT (order_no, campaign_instructions_code) DO UPDATE SET
    campaign_instructions_name = EXCLUDED.campaign_instructions_name,
    campaign_description = EXCLUDED.campaign_description,
    campaign_end_date = EXCLUDED.campaign_end_date,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = order_campaign.d_version + 1;