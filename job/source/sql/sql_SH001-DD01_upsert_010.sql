INSERT INTO
    customer_address (
        customer_code,
        address_no,
        address_alias,
        address_last_name,
        address_first_name,
        address_last_name_kana,
        address_first_name_kana,
        postal_code,
        prefecture_code,
        address1,
        address2,
        address3,
        address4,
        phone_number,
        corporation_post_name,
        address_id,
        crm_address_id,
        crm_address_updated_datetime,
        orm_rowid,
        created_user,
        created_datetime,
        updated_user,
        updated_datetime,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    customer_code,
    address_no,
    address_alias,
    address_last_name,
    address_first_name,
    address_last_name_kana,
    address_first_name_kana,
    postal_code,
    prefecture_code,
    address1,
    address2,
    address3,
    address4,
    phone_number,
    corporation_post_name,
    address_id,
    crm_address_id,
    crm_address_updated_datetime,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_SH001-DD01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_SH001-DD01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM
    customer_address_work ON CONFLICT (customer_code, address_no) DO
UPDATE
SET
    address_alias = EXCLUDED.address_alias,
    address_last_name = EXCLUDED.address_last_name,
    address_first_name = EXCLUDED.address_first_name,
    address_last_name_kana = EXCLUDED.address_last_name_kana,
    address_first_name_kana = EXCLUDED.address_first_name_kana,
    postal_code = EXCLUDED.postal_code,
    prefecture_code = EXCLUDED.prefecture_code,
    address1 = EXCLUDED.address1,
    address2 = EXCLUDED.address2,
    address3 = EXCLUDED.address3,
    address4 = EXCLUDED.address4,
    phone_number = EXCLUDED.phone_number,
    corporation_post_name = EXCLUDED.corporation_post_name,
    address_id = EXCLUDED.address_id,
    crm_address_id = EXCLUDED.crm_address_id,
    crm_address_updated_datetime = EXCLUDED.crm_address_updated_datetime,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_SH001-DD01_001',
    d_updated_datetime = NOW(),
    d_version = order_campaign_history.d_version + 1;