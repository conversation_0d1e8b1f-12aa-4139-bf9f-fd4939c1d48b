INSERT INTO sales_info_alignment_header (
    shop_cd,
    register_num,
    business_date,
    receipt_num,
    chit_num,
    system_datetime,
    open_count,
    is_void,
    check_kind,
    return_kind,
    sub_check_kind,
    sales_group_cd,
    deposit_kind,
    operator_cd,
    operator_name,
    sales_man_cd,
    sales_man_name,
    return_employee_cd,
    return_employee_name,
    void_employee_cd,
    void_employee_name,
    staff_sale_employee_cd,
    staff_sale_employee_name,
    customer_cd,
    customer_layer_cd,
    customer_layer_cd2,
    purchase_motive_cd,
    return_reason_cd,
    void_reason_cd,
    net_sales_amount_of_outside_tax,
    net_sales_amount_of_inside_tax,
    net_sales_amount_of_tax_free,
    net_sales_outside_tax,
    net_sales_inside_tax,
    net_sales_quantity,
    outside_sales_amount_of_outside_tax,
    outside_sales_amount_of_inside_tax,
    outside_sales_amount_of_tax_free,
    outside_sales_outside_tax,
    outside_sales_inside_tax,
    outside_sales_quantity,
    total_amount,
    discount_amount,
    discount_tax_inclusive,
    is_revenue_stamp,
    order_line_count,
    pay_line_count,
    is_total_display,
    is_reduced_tax_rate_trade,
    is_tax_free,
    customers_num,
    deliver_date,
    sale_attribute_cd1,
    sale_attribute_cd2,
    sale_attribute_cd3,
    sale_attribute_cd4,
    campaign_no,
    closing_date,
    return_date,
    order_number,
    employee_meal,
    employee_code,
    table_no,
    acceptance_time,
    menu_cook_cmp_time,
    menu_offer_cmp_time,
    service_charge_amount_outside_tax,
    service_charge_amount_inside_tax,
    service_charge_tax_exclusive,
    service_charge_tax_inclusive,
    service_charge_amount1,
    service_charge_amount2,
    service_charge_minus_amount,
    service_charge_minus_tax_inclusive,
    service_charge_target,
    service_charge_button,
    service_charge1_button,
    service_charge2_button,
    eat_in_amount,
    takeout_amount,
    vein_employee_cd,
    is_vein_authentication,
    out_calc_flg,
    sale_goods_flg,
    point_linkage_kind,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    numeric_reserve6,
    numeric_reserve7,
    numeric_reserve8,
    numeric_reserve9,
    numeric_reserve10,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    string_reserve6,
    string_reserve7,
    string_reserve8,
    string_reserve9,
    string_reserve10,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_cd,
    register_num,
    business_date,
    receipt_num,
    chit_num,
    system_datetime,
    open_count,
    is_void,
    check_kind,
    return_kind,
    sub_check_kind,
    sales_group_cd,
    deposit_kind,
    operator_cd,
    operator_name,
    sales_man_cd,
    sales_man_name,
    return_employee_cd,
    return_employee_name,
    void_employee_cd,
    void_employee_name,
    staff_sale_employee_cd,
    staff_sale_employee_name,
    customer_cd,
    customer_layer_cd,
    customer_layer_cd2,
    purchase_motive_cd,
    return_reason_cd,
    void_reason_cd,
    net_sales_amount_of_outside_tax,
    net_sales_amount_of_inside_tax,
    net_sales_amount_of_tax_free,
    net_sales_outside_tax,
    net_sales_inside_tax,
    net_sales_quantity,
    outside_sales_amount_of_outside_tax,
    outside_sales_amount_of_inside_tax,
    outside_sales_amount_of_tax_free,
    outside_sales_outside_tax,
    outside_sales_inside_tax,
    outside_sales_quantity,
    total_amount,
    discount_amount,
    discount_tax_inclusive,
    is_revenue_stamp,
    order_line_count,
    pay_line_count,
    is_total_display,
    is_reduced_tax_rate_trade,
    is_tax_free,
    customers_num,
    deliver_date,
    sale_attribute_cd1,
    sale_attribute_cd2,
    sale_attribute_cd3,
    sale_attribute_cd4,
    campaign_no,
    closing_date,
    return_date,
    order_number,
    employee_meal,
    employee_code,
    table_no,
    acceptance_time,
    menu_cook_cmp_time,
    menu_offer_cmp_time,
    service_charge_amount_outside_tax,
    service_charge_amount_inside_tax,
    service_charge_tax_exclusive,
    service_charge_tax_inclusive,
    service_charge_amount1,
    service_charge_amount2,
    service_charge_minus_amount,
    service_charge_minus_tax_inclusive,
    service_charge_target,
    service_charge_button,
    service_charge1_button,
    service_charge2_button,
    eat_in_amount,
    takeout_amount,
    vein_employee_cd,
    is_vein_authentication,
    out_calc_flg,
    sale_goods_flg,
    point_linkage_kind,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    numeric_reserve6,
    numeric_reserve7,
    numeric_reserve8,
    numeric_reserve9,
    numeric_reserve10,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    string_reserve6,
    string_reserve7,
    string_reserve8,
    string_reserve9,
    string_reserve10,
    'JN_SH002-FD01_001', -- d_created_user
    NOW(),               -- d_created_datetime
    'JN_SH002-FD01_001', -- d_updated_user
    NOW(),               -- d_updated_datetime
    1                    -- d_version
FROM sales_info_alignment_header_work
ON CONFLICT (shop_cd, register_num, business_date, receipt_num)
DO UPDATE SET
    chit_num = EXCLUDED.chit_num,
    system_datetime = EXCLUDED.system_datetime,
    open_count = EXCLUDED.open_count,
    is_void = EXCLUDED.is_void,
    check_kind = EXCLUDED.check_kind,
    return_kind = EXCLUDED.return_kind,
    sub_check_kind = EXCLUDED.sub_check_kind,
    sales_group_cd = EXCLUDED.sales_group_cd,
    deposit_kind = EXCLUDED.deposit_kind,
    operator_cd = EXCLUDED.operator_cd,
    operator_name = EXCLUDED.operator_name,
    sales_man_cd = EXCLUDED.sales_man_cd,
    sales_man_name = EXCLUDED.sales_man_name,
    return_employee_cd = EXCLUDED.return_employee_cd,
    return_employee_name = EXCLUDED.return_employee_name,
    void_employee_cd = EXCLUDED.void_employee_cd,
    void_employee_name = EXCLUDED.void_employee_name,
    staff_sale_employee_cd = EXCLUDED.staff_sale_employee_cd,
    staff_sale_employee_name = EXCLUDED.staff_sale_employee_name,
    customer_cd = EXCLUDED.customer_cd,
    customer_layer_cd = EXCLUDED.customer_layer_cd,
    customer_layer_cd2 = EXCLUDED.customer_layer_cd2,
    purchase_motive_cd = EXCLUDED.purchase_motive_cd,
    return_reason_cd = EXCLUDED.return_reason_cd,
    void_reason_cd = EXCLUDED.void_reason_cd,
    net_sales_amount_of_outside_tax = EXCLUDED.net_sales_amount_of_outside_tax,
    net_sales_amount_of_inside_tax = EXCLUDED.net_sales_amount_of_inside_tax,
    net_sales_amount_of_tax_free = EXCLUDED.net_sales_amount_of_tax_free,
    net_sales_outside_tax = EXCLUDED.net_sales_outside_tax,
    net_sales_inside_tax = EXCLUDED.net_sales_inside_tax,
    net_sales_quantity = EXCLUDED.net_sales_quantity,
    outside_sales_amount_of_outside_tax = EXCLUDED.outside_sales_amount_of_outside_tax,
    outside_sales_amount_of_inside_tax = EXCLUDED.outside_sales_amount_of_inside_tax,
    outside_sales_amount_of_tax_free = EXCLUDED.outside_sales_amount_of_tax_free,
    outside_sales_outside_tax = EXCLUDED.outside_sales_outside_tax,
    outside_sales_inside_tax = EXCLUDED.outside_sales_inside_tax,
    outside_sales_quantity = EXCLUDED.outside_sales_quantity,
    total_amount = EXCLUDED.total_amount,
    discount_amount = EXCLUDED.discount_amount,
    discount_tax_inclusive = EXCLUDED.discount_tax_inclusive,
    is_revenue_stamp = EXCLUDED.is_revenue_stamp,
    order_line_count = EXCLUDED.order_line_count,
    pay_line_count = EXCLUDED.pay_line_count,
    is_total_display = EXCLUDED.is_total_display,
    is_reduced_tax_rate_trade = EXCLUDED.is_reduced_tax_rate_trade,
    is_tax_free = EXCLUDED.is_tax_free,
    customers_num = EXCLUDED.customers_num,
    deliver_date = EXCLUDED.deliver_date,
    sale_attribute_cd1 = EXCLUDED.sale_attribute_cd1,
    sale_attribute_cd2 = EXCLUDED.sale_attribute_cd2,
    sale_attribute_cd3 = EXCLUDED.sale_attribute_cd3,
    sale_attribute_cd4 = EXCLUDED.sale_attribute_cd4,
    campaign_no = EXCLUDED.campaign_no,
    closing_date = EXCLUDED.closing_date,
    return_date = EXCLUDED.return_date,
    order_number = EXCLUDED.order_number,
    employee_meal = EXCLUDED.employee_meal,
    employee_code = EXCLUDED.employee_code,
    table_no = EXCLUDED.table_no,
    acceptance_time = EXCLUDED.acceptance_time,
    menu_cook_cmp_time = EXCLUDED.menu_cook_cmp_time,
    menu_offer_cmp_time = EXCLUDED.menu_offer_cmp_time,
    service_charge_amount_outside_tax = EXCLUDED.service_charge_amount_outside_tax,
    service_charge_amount_inside_tax = EXCLUDED.service_charge_amount_inside_tax,
    service_charge_tax_exclusive = EXCLUDED.service_charge_tax_exclusive,
    service_charge_tax_inclusive = EXCLUDED.service_charge_tax_inclusive,
    service_charge_amount1 = EXCLUDED.service_charge_amount1,
    service_charge_amount2 = EXCLUDED.service_charge_amount2,
    service_charge_minus_amount = EXCLUDED.service_charge_minus_amount,
    service_charge_minus_tax_inclusive = EXCLUDED.service_charge_minus_tax_inclusive,
    service_charge_target = EXCLUDED.service_charge_target,
    service_charge_button = EXCLUDED.service_charge_button,
    service_charge1_button = EXCLUDED.service_charge1_button,
    service_charge2_button = EXCLUDED.service_charge2_button,
    eat_in_amount = EXCLUDED.eat_in_amount,
    takeout_amount = EXCLUDED.takeout_amount,
    vein_employee_cd = EXCLUDED.vein_employee_cd,
    is_vein_authentication = EXCLUDED.is_vein_authentication,
    out_calc_flg = EXCLUDED.out_calc_flg,
    sale_goods_flg = EXCLUDED.sale_goods_flg,
    point_linkage_kind = EXCLUDED.point_linkage_kind,
    numeric_reserve1 = EXCLUDED.numeric_reserve1,
    numeric_reserve2 = EXCLUDED.numeric_reserve2,
    numeric_reserve3 = EXCLUDED.numeric_reserve3,
    numeric_reserve4 = EXCLUDED.numeric_reserve4,
    numeric_reserve5 = EXCLUDED.numeric_reserve5,
    numeric_reserve6 = EXCLUDED.numeric_reserve6,
    numeric_reserve7 = EXCLUDED.numeric_reserve7,
    numeric_reserve8 = EXCLUDED.numeric_reserve8,
    numeric_reserve9 = EXCLUDED.numeric_reserve9,
    numeric_reserve10 = EXCLUDED.numeric_reserve10,
    string_reserve1 = EXCLUDED.string_reserve1,
    string_reserve2 = EXCLUDED.string_reserve2,
    string_reserve3 = EXCLUDED.string_reserve3,
    string_reserve4 = EXCLUDED.string_reserve4,
    string_reserve5 = EXCLUDED.string_reserve5,
    string_reserve6 = EXCLUDED.string_reserve6,
    string_reserve7 = EXCLUDED.string_reserve7,
    string_reserve8 = EXCLUDED.string_reserve8,
    string_reserve9 = EXCLUDED.string_reserve9,
    string_reserve10 = EXCLUDED.string_reserve10,
    d_updated_user = 'JN_SH002-FD01_001',
    d_updated_datetime = NOW(),
    d_version = sales_info_alignment_header.d_version + 1;