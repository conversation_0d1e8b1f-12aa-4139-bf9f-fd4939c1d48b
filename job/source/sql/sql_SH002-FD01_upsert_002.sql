INSERT INTO sales_info_alignment_detail (
    shop_cd,
    register_num,
    business_date,
    receipt_num,
    line_num,
    line_kind,
    is_in_store_marking,
    brand_cd,
    dept_cd,
    class_cd,
    sub_class_cd,
    item_cd,
    item_name,
    dept_group_cd,
    parent_item_cd,
    jan_cd,
    item_num,
    color_cd,
    size_cd,
    year_cd,
    season_cd,
    attribute_cd1,
    attribute_cd2,
    attribute_cd3,
    attribute_cd4,
    attribute_cd5,
    scan_bar_cd1,
    scan_bar_cd2,
    discount_cd,
    bmplan_cd,
    coupon_num,
    is_include_sales,
    item_kind,
    tax_kind,
    tax_rate,
    is_reduced_tax_rate,
    is_proper_item,
    is_change_price,
    master_unit_price,
    body_price,
    fixed_price,
    unit_price,
    quantity,
    amount,
    tax_inclusive,
    tax_outside,
    tax,
    disc_rate,
    line_minus_amount,
    line_minus_tax_inclusive,
    bmset_minus_amount,
    bmset_minus_tax_inclusive,
    point_minus_amount,
    point_minus_tax_inclusive,
    coupon_minus_amount,
    coupon_minus_tax_inclusive,
    sub_total_minus_amount,
    sub_total_minus_tax_inclusive,
    before_disc_tax_inclusive,
    sales_man_cd,
    stock_kind,
    is_inventory_counted,
    is_promotion_ticket,
    promotion_ticket_bar_cd,
    is_promotion_ticket_allow_combination,
    oes_item_flag,
    oes_slip_no,
    oes_slip_sub_no,
    grand_classification,
    menu_classification,
    oes_line_num,
    oes_quantity,
    oes_minus,
    pos_minus,
    takeout_flag,
    service_charge_minus_amount,
    service_charge_minus_tax_inclusive,
    service_charge_flag,
    service_charge1_flag,
    service_charge2_flag,
    service_charge1_manually_flag,
    service_charge2_manually_flag,
    oes_service_charge1,
    oes_service_charge2,
    cooking_directions_time,
    cooking_complete_time,
    offer_complete_time,
    acceptance_time,
    menu_cook_cmp_time,
    menu_offer_cmp_time,
    disc_amount_limit,
    is_follow_disc,
    is_allow_credit,
    is_employee_acnt_recv,
    employee_cd,
    sub_menu_kind,
    grand_menu_code,
    grand_menu_index,
    select_kind,
    is_quantity_count,
    is_order_entry,
    modifier_kind,
    return_target_line_num,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    numeric_reserve6,
    numeric_reserve7,
    numeric_reserve8,
    numeric_reserve9,
    numeric_reserve10,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    string_reserve6,
    string_reserve7,
    string_reserve8,
    string_reserve9,
    string_reserve10,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_cd,
    register_num,
    business_date,
    receipt_num,
    line_num,
    line_kind,
    is_in_store_marking,
    brand_cd,
    dept_cd,
    class_cd,
    sub_class_cd,
    item_cd,
    item_name,
    dept_group_cd,
    parent_item_cd,
    jan_cd,
    item_num,
    color_cd,
    size_cd,
    year_cd,
    season_cd,
    attribute_cd1,
    attribute_cd2,
    attribute_cd3,
    attribute_cd4,
    attribute_cd5,
    scan_bar_cd1,
    scan_bar_cd2,
    discount_cd,
    bmplan_cd,
    coupon_num,
    is_include_sales,
    item_kind,
    tax_kind,
    tax_rate,
    is_reduced_tax_rate,
    is_proper_item,
    is_change_price,
    master_unit_price,
    body_price,
    fixed_price,
    unit_price,
    quantity,
    amount,
    tax_inclusive,
    tax_outside,
    tax,
    disc_rate,
    line_minus_amount,
    line_minus_tax_inclusive,
    bmset_minus_amount,
    bmset_minus_tax_inclusive,
    point_minus_amount,
    point_minus_tax_inclusive,
    coupon_minus_amount,
    coupon_minus_tax_inclusive,
    sub_total_minus_amount,
    sub_total_minus_tax_inclusive,
    before_disc_tax_inclusive,
    sales_man_cd,
    stock_kind,
    is_inventory_counted,
    is_promotion_ticket,
    promotion_ticket_bar_cd,
    is_promotion_ticket_allow_combination,
    oes_item_flag,
    oes_slip_no,
    oes_slip_sub_no,
    grand_classification,
    menu_classification,
    oes_line_num,
    oes_quantity,
    oes_minus,
    pos_minus,
    takeout_flag,
    service_charge_minus_amount,
    service_charge_minus_tax_inclusive,
    service_charge_flag,
    service_charge1_flag,
    service_charge2_flag,
    service_charge1_manually_flag,
    service_charge2_manually_flag,
    oes_service_charge1,
    oes_service_charge2,
    cooking_directions_time,
    cooking_complete_time,
    offer_complete_time,
    acceptance_time,
    menu_cook_cmp_time,
    menu_offer_cmp_time,
    disc_amount_limit,
    is_follow_disc,
    is_allow_credit,
    is_employee_acnt_recv,
    employee_cd,
    sub_menu_kind,
    grand_menu_code,
    grand_menu_index,
    select_kind,
    is_quantity_count,
    is_order_entry,
    modifier_kind,
    return_target_line_num,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    numeric_reserve6,
    numeric_reserve7,
    numeric_reserve8,
    numeric_reserve9,
    numeric_reserve10,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    string_reserve6,
    string_reserve7,
    string_reserve8,
    string_reserve9,
    string_reserve10,
    'JN_SH002-FD01_001', -- d_created_user
    NOW(),               -- d_created_datetime
    'JN_SH002-FD01_001', -- d_updated_user
    NOW(),               -- d_updated_datetime
    1                    -- d_version
FROM sales_info_alignment_detail_work
ON CONFLICT (shop_cd, register_num, business_date, receipt_num, line_num)
DO UPDATE SET
    line_kind = EXCLUDED.line_kind,
    is_in_store_marking = EXCLUDED.is_in_store_marking,
    brand_cd = EXCLUDED.brand_cd,
    dept_cd = EXCLUDED.dept_cd,
    class_cd = EXCLUDED.class_cd,
    sub_class_cd = EXCLUDED.sub_class_cd,
    item_cd = EXCLUDED.item_cd,
    item_name = EXCLUDED.item_name,
    dept_group_cd = EXCLUDED.dept_group_cd,
    parent_item_cd = EXCLUDED.parent_item_cd,
    jan_cd = EXCLUDED.jan_cd,
    item_num = EXCLUDED.item_num,
    color_cd = EXCLUDED.color_cd,
    size_cd = EXCLUDED.size_cd,
    year_cd = EXCLUDED.year_cd,
    season_cd = EXCLUDED.season_cd,
    attribute_cd1 = EXCLUDED.attribute_cd1,
    attribute_cd2 = EXCLUDED.attribute_cd2,
    attribute_cd3 = EXCLUDED.attribute_cd3,
    attribute_cd4 = EXCLUDED.attribute_cd4,
    attribute_cd5 = EXCLUDED.attribute_cd5,
    scan_bar_cd1 = EXCLUDED.scan_bar_cd1,
    scan_bar_cd2 = EXCLUDED.scan_bar_cd2,
    discount_cd = EXCLUDED.discount_cd,
    bmplan_cd = EXCLUDED.bmplan_cd,
    coupon_num = EXCLUDED.coupon_num,
    is_include_sales = EXCLUDED.is_include_sales,
    item_kind = EXCLUDED.item_kind,
    tax_kind = EXCLUDED.tax_kind,
    tax_rate = EXCLUDED.tax_rate,
    is_reduced_tax_rate = EXCLUDED.is_reduced_tax_rate,
    is_proper_item = EXCLUDED.is_proper_item,
    is_change_price = EXCLUDED.is_change_price,
    master_unit_price = EXCLUDED.master_unit_price,
    body_price = EXCLUDED.body_price,
    fixed_price = EXCLUDED.fixed_price,
    unit_price = EXCLUDED.unit_price,
    quantity = EXCLUDED.quantity,
    amount = EXCLUDED.amount,
    tax_inclusive = EXCLUDED.tax_inclusive,
    tax_outside = EXCLUDED.tax_outside,
    tax = EXCLUDED.tax,
    disc_rate = EXCLUDED.disc_rate,
    line_minus_amount = EXCLUDED.line_minus_amount,
    line_minus_tax_inclusive = EXCLUDED.line_minus_tax_inclusive,
    bmset_minus_amount = EXCLUDED.bmset_minus_amount,
    bmset_minus_tax_inclusive = EXCLUDED.bmset_minus_tax_inclusive,
    point_minus_amount = EXCLUDED.point_minus_amount,
    point_minus_tax_inclusive = EXCLUDED.point_minus_tax_inclusive,
    coupon_minus_amount = EXCLUDED.coupon_minus_amount,
    coupon_minus_tax_inclusive = EXCLUDED.coupon_minus_tax_inclusive,
    sub_total_minus_amount = EXCLUDED.sub_total_minus_amount,
    sub_total_minus_tax_inclusive = EXCLUDED.sub_total_minus_tax_inclusive,
    before_disc_tax_inclusive = EXCLUDED.before_disc_tax_inclusive,
    sales_man_cd = EXCLUDED.sales_man_cd,
    stock_kind = EXCLUDED.stock_kind,
    is_inventory_counted = EXCLUDED.is_inventory_counted,
    is_promotion_ticket = EXCLUDED.is_promotion_ticket,
    promotion_ticket_bar_cd = EXCLUDED.promotion_ticket_bar_cd,
    is_promotion_ticket_allow_combination = EXCLUDED.is_promotion_ticket_allow_combination,
    oes_item_flag = EXCLUDED.oes_item_flag,
    oes_slip_no = EXCLUDED.oes_slip_no,
    oes_slip_sub_no = EXCLUDED.oes_slip_sub_no,
    grand_classification = EXCLUDED.grand_classification,
    menu_classification = EXCLUDED.menu_classification,
    oes_line_num = EXCLUDED.oes_line_num,
    oes_quantity = EXCLUDED.oes_quantity,
    oes_minus = EXCLUDED.oes_minus,
    pos_minus = EXCLUDED.pos_minus,
    takeout_flag = EXCLUDED.takeout_flag,
    service_charge_minus_amount = EXCLUDED.service_charge_minus_amount,
    service_charge_minus_tax_inclusive = EXCLUDED.service_charge_minus_tax_inclusive,
    service_charge_flag = EXCLUDED.service_charge_flag,
    service_charge1_flag = EXCLUDED.service_charge1_flag,
    service_charge2_flag = EXCLUDED.service_charge2_flag,
    service_charge1_manually_flag = EXCLUDED.service_charge1_manually_flag,
    service_charge2_manually_flag = EXCLUDED.service_charge2_manually_flag,
    oes_service_charge1 = EXCLUDED.oes_service_charge1,
    oes_service_charge2 = EXCLUDED.oes_service_charge2,
    cooking_directions_time = EXCLUDED.cooking_directions_time,
    cooking_complete_time = EXCLUDED.cooking_complete_time,
    offer_complete_time = EXCLUDED.offer_complete_time,
    acceptance_time = EXCLUDED.acceptance_time,
    menu_cook_cmp_time = EXCLUDED.menu_cook_cmp_time,
    menu_offer_cmp_time = EXCLUDED.menu_offer_cmp_time,
    disc_amount_limit = EXCLUDED.disc_amount_limit,
    is_follow_disc = EXCLUDED.is_follow_disc,
    is_allow_credit = EXCLUDED.is_allow_credit,
    is_employee_acnt_recv = EXCLUDED.is_employee_acnt_recv,
    employee_cd = EXCLUDED.employee_cd,
    sub_menu_kind = EXCLUDED.sub_menu_kind,
    grand_menu_code = EXCLUDED.grand_menu_code,
    grand_menu_index = EXCLUDED.grand_menu_index,
    select_kind = EXCLUDED.select_kind,
    is_quantity_count = EXCLUDED.is_quantity_count,
    is_order_entry = EXCLUDED.is_order_entry,
    modifier_kind = EXCLUDED.modifier_kind,
    return_target_line_num = EXCLUDED.return_target_line_num,
    numeric_reserve1 = EXCLUDED.numeric_reserve1,
    numeric_reserve2 = EXCLUDED.numeric_reserve2,
    numeric_reserve3 = EXCLUDED.numeric_reserve3,
    numeric_reserve4 = EXCLUDED.numeric_reserve4,
    numeric_reserve5 = EXCLUDED.numeric_reserve5,
    numeric_reserve6 = EXCLUDED.numeric_reserve6,
    numeric_reserve7 = EXCLUDED.numeric_reserve7,
    numeric_reserve8 = EXCLUDED.numeric_reserve8,
    numeric_reserve9 = EXCLUDED.numeric_reserve9,
    numeric_reserve10 = EXCLUDED.numeric_reserve10,
    string_reserve1 = EXCLUDED.string_reserve1,
    string_reserve2 = EXCLUDED.string_reserve2,
    string_reserve3 = EXCLUDED.string_reserve3,
    string_reserve4 = EXCLUDED.string_reserve4,
    string_reserve5 = EXCLUDED.string_reserve5,
    string_reserve6 = EXCLUDED.string_reserve6,
    string_reserve7 = EXCLUDED.string_reserve7,
    string_reserve8 = EXCLUDED.string_reserve8,
    string_reserve9 = EXCLUDED.string_reserve9,
    string_reserve10 = EXCLUDED.string_reserve10,
    d_updated_user = 'JN_SH002-FD01_001',
    d_updated_datetime = NOW(),
    d_version = sales_info_alignment_detail.d_version + 1;