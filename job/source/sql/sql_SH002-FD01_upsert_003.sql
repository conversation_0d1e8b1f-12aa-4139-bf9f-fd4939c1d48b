INSERT INTO sales_info_alignment_payment (
    shop_cd,
    register_num,
    business_date,
    receipt_num,
    line_num,
    pay_kind,
    pay_cd,
    credit_linkage_kind,
    claim_kind,
    pay_amount,
    change_amount,
    surplus_amount,
    coupon_num,
    ticket_quantity,
    ticket_bar_cd,
    is_worn_cash,
    is_emoney_cash_back,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    numeric_reserve6,
    numeric_reserve7,
    numeric_reserve8,
    numeric_reserve9,
    numeric_reserve10,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    string_reserve6,
    string_reserve7,
    string_reserve8,
    string_reserve9,
    string_reserve10,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_cd,
    register_num,
    business_date,
    receipt_num,
    line_num,
    pay_kind,
    pay_cd,
    credit_linkage_kind,
    claim_kind,
    pay_amount,
    change_amount,
    surplus_amount,
    coupon_num,
    ticket_quantity,
    ticket_bar_cd,
    is_worn_cash,
    is_emoney_cash_back,
    numeric_reserve1,
    numeric_reserve2,
    numeric_reserve3,
    numeric_reserve4,
    numeric_reserve5,
    numeric_reserve6,
    numeric_reserve7,
    numeric_reserve8,
    numeric_reserve9,
    numeric_reserve10,
    string_reserve1,
    string_reserve2,
    string_reserve3,
    string_reserve4,
    string_reserve5,
    string_reserve6,
    string_reserve7,
    string_reserve8,
    string_reserve9,
    string_reserve10,
    'JN_SH002-FD01_001', -- d_created_user
    NOW(),               -- d_created_datetime
    'JN_SH002-FD01_001', -- d_updated_user
    NOW(),               -- d_updated_datetime
    1                    -- d_version
FROM sales_info_alignment_payment_work
ON CONFLICT (shop_cd, register_num, business_date, receipt_num, line_num)
DO UPDATE SET
    pay_kind = EXCLUDED.pay_kind,
    pay_cd = EXCLUDED.pay_cd,
    credit_linkage_kind = EXCLUDED.credit_linkage_kind,
    claim_kind = EXCLUDED.claim_kind,
    pay_amount = EXCLUDED.pay_amount,
    change_amount = EXCLUDED.change_amount,
    surplus_amount = EXCLUDED.surplus_amount,
    coupon_num = EXCLUDED.coupon_num,
    ticket_quantity = EXCLUDED.ticket_quantity,
    ticket_bar_cd = EXCLUDED.ticket_bar_cd,
    is_worn_cash = EXCLUDED.is_worn_cash,
    is_emoney_cash_back = EXCLUDED.is_emoney_cash_back,
    numeric_reserve1 = EXCLUDED.numeric_reserve1,
    numeric_reserve2 = EXCLUDED.numeric_reserve2,
    numeric_reserve3 = EXCLUDED.numeric_reserve3,
    numeric_reserve4 = EXCLUDED.numeric_reserve4,
    numeric_reserve5 = EXCLUDED.numeric_reserve5,
    numeric_reserve6 = EXCLUDED.numeric_reserve6,
    numeric_reserve7 = EXCLUDED.numeric_reserve7,
    numeric_reserve8 = EXCLUDED.numeric_reserve8,
    numeric_reserve9 = EXCLUDED.numeric_reserve9,
    numeric_reserve10 = EXCLUDED.numeric_reserve10,
    string_reserve1 = EXCLUDED.string_reserve1,
    string_reserve2 = EXCLUDED.string_reserve2,
    string_reserve3 = EXCLUDED.string_reserve3,
    string_reserve4 = EXCLUDED.string_reserve4,
    string_reserve5 = EXCLUDED.string_reserve5,
    string_reserve6 = EXCLUDED.string_reserve6,
    string_reserve7 = EXCLUDED.string_reserve7,
    string_reserve8 = EXCLUDED.string_reserve8,
    string_reserve9 = EXCLUDED.string_reserve9,
    string_reserve10 = EXCLUDED.string_reserve10,
    d_updated_user = 'JN_SH002-FD01_001',
    d_updated_datetime = NOW(),
    d_version = sales_info_alignment_payment.d_version + 1;