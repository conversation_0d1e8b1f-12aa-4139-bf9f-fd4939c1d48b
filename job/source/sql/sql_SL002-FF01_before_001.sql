-- sync_timestampのtempに基準日時を入れる
UPDATE sync_timestamp 
SET sync_datetime_temp = NOW() 
WHERE job_schedule_id = 'JN_SL002-FF01_001'
  AND file_name = 'txStoreInventory';
-- ワークテーブルに出力データを格納
TRUNCATE TABLE wk_sl002_ff01_storeid;
INSERT INTO wk_sl002_ff01_storeid (store_id,allocation_timestamp)
    SELECT DISTINCT
      CAST(store_cd AS VARCHAR),
      TO_CHAR(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Tokyo', 'YYYY-MM-DD"T"HH24:MI:SS".000+09:00"')
    FROM store_stock_alignment_diff
    WHERE d_updated_datetime > :sync_datetime;
-- ワークテーブルに分割単位を入れる
WITH temp_parallel_num AS (
  SELECT
     store_id
    ,MOD(ROW_NUMBER() OVER (ORDER BY store_id) - 1, :split_num) + 1 AS parallel_num
  FROM (SELECT store_id FROM wk_sl002_ff01_storeid)
)
UPDATE wk_sl002_ff01_storeid AS inv
SET split_num = t.parallel_num
FROM temp_parallel_num AS t
WHERE inv.store_id = t.store_id;
SELECT * FROM
    aws_s3.query_export_to_s3(
        concat(
            ' SELECT
    CAST(store_cd AS VARCHAR) as store_id,
    warehouse_management_no,
    stock_quantity
    FROM
        store_stock_alignment_diff
    WHERE
        d_updated_datetime >''',
            :sync_datetime,
            ''' ORDER BY store_cd, warehouse_management_no;'
        ),
        aws_commons.create_s3_uri(
            :bucket_name,
            -- bucket名
            :object_path,
            -- object名
            'ap-northeast-1' -- region名
        ),
        options := 'format csv, header true, quote ''"'' '
    );