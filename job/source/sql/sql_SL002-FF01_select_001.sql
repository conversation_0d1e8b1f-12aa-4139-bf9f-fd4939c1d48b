select
    *
from
    aws_s3.query_export_to_s3(
        concat(
            ' SELECT
    CAST(store_cd AS VARCHAR) as store_id,
    warehouse_management_no,
    stock_quantity
    FROM
        store_stock_alignment_diff
    WHERE
        d_updated_datetime >''',
            :sync_datetime,
            '''',
            'AND d_updated_datetime <=''',
            :diff_base_timestamp,
            ''';'
        ),
        aws_commons.create_s3_uri(
            :bucket_name,
            -- bucket名
            :object_path,
            -- object名
            'ap-northeast-1' -- region名
        ),
        options := 'format csv, header true, quote ''"'' '
    );