INSERT INTO store_stock_alignment_diff (
    company_cd,
    store_cd,
    warehouse_management_no,
    stock_quantity,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    company_cd,
    store_cd,
    warehouse_management_no,
    stock_quantity,
    'JN_SL002-FF01_001', -- d_created_user
    NOW(),               -- d_created_datetime
    'JN_SL002-FF01_001', -- d_updated_user
    NOW(),               -- d_updated_datetime
    1                    -- d_version
FROM store_stock_alignment_diff_work
ON CONFLICT (company_cd, store_cd, warehouse_management_no, stock_quantity) DO UPDATE SET
    d_updated_user = 'JN_SL002-FF01_001',
    d_updated_datetime = NOW(),
    d_version = store_stock_alignment_diff.d_version + 1;