INSERT INTO out_indicate_header (
        accept_no,
        cust_no,
        record_no,
        cust_name,
        post_no,
        addr1,
        addr2,
        addr3,
        tel_no,
        prefecture_code,
        cust_flg,
        order_date,
        pay_kb,
        total_price,
        delive_cust_name,
        delive_post_no,
        delive_addr1,
        delive_addr2,
        delive_addr3,
        delive_tel_no,
        gift_flg,
        kibou_ymd,
        night_flg,
        cosme_price,
        health_price,
        inner_price,
        update_date,
        chit_print_date,
        yamato_bar_code,
        gyosha_flg,
        status_flg,
        slip_ono,
        order_no,
        clinic_name,
        shipment_date,
        shipment_plan_date,
        pack_cnt,
        store_code,
        period_flg,
        delivery_box_gb,
        air_delivery_yn,
        tax_amt,
        conveni_yn,
        pudo_yn,
        inplan_yn,
        over_stock_yn,
        reserve_order_yn,
        gift_rapping_yn,
        kanshi_yn,
        fusoku_yn,
        airplane_yn,
        satofuru_yn,
        tokusha_yn,
        rakugaki_yn,
        multi_sample_yn,
        slip_size_code,
        wh_code,
        agent_cd,
        import_yn,
        import_date,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT accept_no,
    cust_no,
    record_no,
    cust_name,
    post_no,
    addr1,
    addr2,
    addr3,
    tel_no,
    prefecture_code,
    cust_flg,
    order_date,
    pay_kb,
    total_price,
    delive_cust_name,
    delive_post_no,
    delive_addr1,
    delive_addr2,
    delive_addr3,
    delive_tel_no,
    gift_flg,
    kibou_ymd,
    night_flg,
    cosme_price,
    health_price,
    inner_price,
    update_date,
    chit_print_date,
    yamato_bar_code,
    gyosha_flg,
    status_flg,
    slip_ono,
    order_no,
    clinic_name,
    shipment_date,
    shipment_plan_date,
    pack_cnt,
    store_code,
    period_flg,
    delivery_box_gb,
    air_delivery_yn,
    tax_amt,
    conveni_yn,
    pudo_yn,
    inplan_yn,
    over_stock_yn,
    reserve_order_yn,
    gift_rapping_yn,
    kanshi_yn,
    fusoku_yn,
    airplane_yn,
    satofuru_yn,
    tokusha_yn,
    rakugaki_yn,
    multi_sample_yn,
    slip_size_code,
    wh_code,
    agent_cd,
    import_yn,
    import_date,
    'JN_SP001-FF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_SP001-FF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM out_indicate_header_work ON CONFLICT (accept_no) DO
UPDATE
SET accept_no = EXCLUDED.accept_no,
    cust_no = EXCLUDED.cust_no,
    record_no = EXCLUDED.record_no,
    cust_name = EXCLUDED.cust_name,
    post_no = EXCLUDED.post_no,
    addr1 = EXCLUDED.addr1,
    addr2 = EXCLUDED.addr2,
    addr3 = EXCLUDED.addr3,
    tel_no = EXCLUDED.tel_no,
    prefecture_code = EXCLUDED.prefecture_code,
    cust_flg = EXCLUDED.cust_flg,
    order_date = EXCLUDED.order_date,
    pay_kb = EXCLUDED.pay_kb,
    total_price = EXCLUDED.total_price,
    delive_cust_name = EXCLUDED.delive_cust_name,
    delive_post_no = EXCLUDED.delive_post_no,
    delive_addr1 = EXCLUDED.delive_addr1,
    delive_addr2 = EXCLUDED.delive_addr2,
    delive_addr3 = EXCLUDED.delive_addr3,
    delive_tel_no = EXCLUDED.delive_tel_no,
    gift_flg = EXCLUDED.gift_flg,
    kibou_ymd = EXCLUDED.kibou_ymd,
    night_flg = EXCLUDED.night_flg,
    cosme_price = EXCLUDED.cosme_price,
    health_price = EXCLUDED.health_price,
    inner_price = EXCLUDED.inner_price,
    update_date = EXCLUDED.update_date,
    chit_print_date = EXCLUDED.chit_print_date,
    yamato_bar_code = EXCLUDED.yamato_bar_code,
    gyosha_flg = EXCLUDED.gyosha_flg,
    status_flg = EXCLUDED.status_flg,
    slip_ono = EXCLUDED.slip_ono,
    order_no = EXCLUDED.order_no,
    clinic_name = EXCLUDED.clinic_name,
    shipment_date = EXCLUDED.shipment_date,
    shipment_plan_date = EXCLUDED.shipment_plan_date,
    pack_cnt = EXCLUDED.pack_cnt,
    store_code = EXCLUDED.store_code,
    period_flg = EXCLUDED.period_flg,
    delivery_box_gb = EXCLUDED.delivery_box_gb,
    air_delivery_yn = EXCLUDED.air_delivery_yn,
    tax_amt = EXCLUDED.tax_amt,
    conveni_yn = EXCLUDED.conveni_yn,
    pudo_yn = EXCLUDED.pudo_yn,
    inplan_yn = EXCLUDED.inplan_yn,
    over_stock_yn = EXCLUDED.over_stock_yn,
    reserve_order_yn = EXCLUDED.reserve_order_yn,
    gift_rapping_yn = EXCLUDED.gift_rapping_yn,
    kanshi_yn = EXCLUDED.kanshi_yn,
    fusoku_yn = EXCLUDED.fusoku_yn,
    airplane_yn = EXCLUDED.airplane_yn,
    satofuru_yn = EXCLUDED.satofuru_yn,
    tokusha_yn = EXCLUDED.tokusha_yn,
    rakugaki_yn = EXCLUDED.rakugaki_yn,
    multi_sample_yn = EXCLUDED.multi_sample_yn,
    slip_size_code = EXCLUDED.slip_size_code,
    wh_code = EXCLUDED.wh_code,
    agent_cd = EXCLUDED.agent_cd,
    import_yn = EXCLUDED.import_yn,
    import_date = EXCLUDED.import_date,
    d_updated_user = 'JN_SP001-FF01_001',
    d_updated_datetime = NOW(),
    d_version = out_indicate_header.d_version + 1;
