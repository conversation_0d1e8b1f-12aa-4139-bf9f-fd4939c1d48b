INSERT INTO out_indicate_detail (
        accept_no,
        seq,
        prod_no,
        qty,
        chit_print_date,
        yamato_bar_code,
        gyosha_flg,
        invoice_branch_number,
        bumon_kbn,
        prod_price,
        order_type,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT accept_no,
    seq,
    prod_no,
    qty,
    chit_print_date,
    yamato_bar_code,
    gyosha_flg,
    invoice_branch_number,
    bumon_kbn,
    prod_price,
    order_type,
    'JN_SP001-FF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_SP001-FF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM out_indicate_detail_work ON CONFLICT (accept_no, seq) DO
UPDATE
SET accept_no = EXCLUDED.accept_no,
    seq = EXCLUDED.seq,
    prod_no = EXCLUDED.prod_no,
    qty = EXCLUDED.qty,
    chit_print_date = EXCLUDED.chit_print_date,
    yamato_bar_code = EXCLUDED.yamato_bar_code,
    gyosha_flg = EXCLUDED.gyosha_flg,
    invoice_branch_number = EXCLUDED.invoice_branch_number,
    bumon_kbn = EXCLUDED.bumon_kbn,
    prod_price = EXCLUDED.prod_price,
    order_type = EXCLUDED.order_type,
    d_updated_user = 'JN_SP001-FF01_001',
    d_updated_datetime = NOW(),
    d_version = out_indicate_detail.d_version + 1;
