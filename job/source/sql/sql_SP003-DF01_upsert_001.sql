INSERT INTO out_achievements_mail_order (
    core_out_indicate_no,
    box_no,
    slip_no,
    core_system_regist_flg,
    regist_datetime,
    update_datetime,
    stock_io_slip_no,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    core_out_indicate_no,
    box_no,
    slip_no,
    core_system_regist_flg,
    regist_datetime,
    update_datetime,
    stock_io_slip_no,
    'JN_SP003-DF01_001' AS d_created_user,
    NOW() AS d_created_datetime,
    'JN_SP003-DF01_001' AS d_updated_user,
    NOW() AS d_updated_datetime,
    1 AS d_version
FROM
    out_achievements_mail_order_work
ON CONFLICT (core_out_indicate_no, box_no)
DO UPDATE SET
    slip_no = EXCLUDED.slip_no,
    core_system_regist_flg = EXCLUDED.core_system_regist_flg,
    regist_datetime = EXCLUDED.regist_datetime,
    update_datetime = EXCLUDED.update_datetime,
    stock_io_slip_no = EXCLUDED.stock_io_slip_no,
    d_updated_user = 'JN_SP003-DF01_001',
    d_updated_datetime = NOW(),
    d_version = out_achievements_mail_order.d_version + 1;
