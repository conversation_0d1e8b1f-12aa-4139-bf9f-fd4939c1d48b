SELECT
    core_out_indicate_no AS "ACCEPT_NO",
    box_no AS "PACK_SEQ",
    slip_no AS "YAMATO_BAR_CODE2",
    core_system_regist_flg AS "UPDATE_YN",
    TO_CHAR(regist_datetime, 'YYYY/MM/DD HH24:MI:SS') AS "INSERT_DATE",
    TO_CHAR(update_datetime, 'YYYY/MM/DD HH24:MI:SS') AS "MODIFY_DATE",
    stock_io_slip_no AS "LOGIMANE_SLIP_NO"
FROM out_achievements_mail_order
WHERE d_updated_datetime > :sync_datetime
    AND d_updated_datetime <= :diff_base_timestamp;
