INSERT INTO
    out_achievements_store_sales_issue (
        accept_no,
        close_date,
        wh_code,
        goods_code,
        out_qty,
        logimane_slip_no,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    accept_no,
    close_date,
    wh_code,
    goods_code,
    out_qty,
    logimane_slip_no,
    'JN_SP006-DF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_SP006-FF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM
    out_achievements_store_sales_issue_work ON CONFLICT (
        accept_no,
        goods_code
    ) DO
UPDATE
SET
    accept_no = EXCLUDED.accept_no,
    close_date = EXCLUDED.close_date,
    wh_code = EXCLUDED.wh_code,
    goods_code = EXCLUDED.goods_code,
    out_qty = EXCLUDED.out_qty,
    logimane_slip_no = EXCLUDED.logimane_slip_no,
    d_updated_user = 'JN_SP006-FF01_001',
    d_updated_datetime = NOW(),
    d_version = out_achievements_store_sales_issue.d_version + 1;
