WITH TARGET_DATA AS (
    SELECT 
        B.WH_CODE,
        COALESCE(B.commodity_code, O.commodity_code) AS GOODS_CODE,
        A.shipping_no,
        COALESCE(B.commodity_name, O.commodity_name) AS GOODS_NAME,
        COALESCE(B.DELY_QTY, 0) AS DELY_QTY,
        CASE WHEN A.shipping_status ='3' THEN '1' ELSE '0' END AS OUT_CLOSE_YN, 
        CASE WHEN A.order_no IN (SELECT order_no FROM order_detail WHERE commodity_kind ='11' ) THEN '1' ELSE '0' END AS PERIOD_YN,
        A.shipping_date AS SHIP_DATE
    FROM shipping_header A
    JOIN (SELECT sd.shipping_no,
                  sc.child_commodity_code AS commodity_code, 
                  commodity_name,
                  purchasing_amount * composition_quantity AS DELY_QTY,
                  sd.hasso_souko_cd AS WH_CODE
          FROM shipping_detail sd
          LEFT JOIN shipping_detail_composition sc ON sd.shipping_no = sc.shipping_no
    ) B ON A.shipping_no = B.shipping_no
    JOIN order_detail O ON A.order_no = O.order_no --O:受注(Order)
      WHERE A.shipping_status = '3'
      AND A.shipping_no NOT IN (SELECT shipping_no FROM returns_detail)
      AND B.WH_CODE IN ('000001', '000002', '000003', '000004', '000101', '000051')
      AND A.shipping_date BETWEEN (DATE_TRUNC('month', CURRENT_DATE) - interval '1 month')
                          AND (DATE_TRUNC('day', CURRENT_DATE) + interval '1 day' - interval '1 second')
),
TARGET_RESULT AS (
    SELECT 
        LPAD(A.GOODS_CODE, 10, '0') AS GOODS_CODE, 
        A.GOODS_NAME, 
        A.OUT_CLOSE_YN,
        A.SHIP_DATE,
        A.PERIOD_YN,
        SUM(A.DELY_QTY) AS DELY_QTY
    FROM TARGET_DATA A
    WHERE A.SHIP_DATE BETWEEN (CURRENT_DATE - interval '9 days')
                      AND (CURRENT_DATE + interval '1 day' - interval '1 second')
    GROUP BY A.GOODS_CODE, A.GOODS_NAME, A.OUT_CLOSE_YN, A.SHIP_DATE, A.PERIOD_YN
)
SELECT 
    TO_CHAR(A.SHIP_DATE, 'YYYYMMDD') AS "SYUKKA_DATE",
    CASE WHEN A.PERIOD_YN = '1' THEN '02' ELSE '01' END AS "CHANNEL_CD",
    CASE WHEN A.PERIOD_YN = '1' THEN '定期' ELSE '通販' END AS "CHANNEL_NAME",
    A.GOODS_CODE as "PRODUCT_CD",
    A.GOODS_NAME as "PRODUCT_NAME",
    A.DELY_QTY as "KOSU"
FROM TARGET_RESULT A
WHERE A.OUT_CLOSE_YN = '1'
ORDER BY A.SHIP_DATE, A.PERIOD_YN, A.GOODS_CODE;