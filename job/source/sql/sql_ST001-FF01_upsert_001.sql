INSERT INTO
    center_stock_all (
        center_code,
        stock_kind,
        sh_control_number,
        stock_quantity,
        allocated_quantity,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    center_code,
    stock_kind,
    sh_control_number,
    stock_quantity,
    allocated_quantity,
    'JN_ST001-FF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_ST001-FF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM
    center_stock_all_work ON CONFLICT (
        center_code,
        stock_kind,
        sh_control_number
    ) DO
UPDATE
SET
    stock_quantity = EXCLUDED.stock_quantity,
    allocated_quantity = EXCLUDED.allocated_quantity,
    d_updated_user = 'JN_ST001-FF01_001',
    d_updated_datetime = NOW(),
    d_version = center_stock_all.d_version + 1;