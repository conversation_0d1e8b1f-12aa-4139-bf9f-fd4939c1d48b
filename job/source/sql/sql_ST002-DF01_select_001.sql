SELECT shop_code, sku_code, allocated_warehouse_code, commodity_code, wms_stock_quantity, stock_quantity, allocated_quantity, reserved_quantity, temporary_allocated_quantity, arrival_reserved_quantity, temporary_reserved_quantity, reservation_limit, stock_threshold, stock_arrival_date, arrival_quantity, orm_rowid, created_user, created_datetime, updated_user, updated_datetime
FROM stock
WHERE updated_datetime >= :sync_datetime
  AND updated_datetime <= :diff_base_timestamp;
