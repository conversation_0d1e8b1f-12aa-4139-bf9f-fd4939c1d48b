INSERT INTO stock (
    shop_code,
    sku_code,
    allocated_warehouse_code,
    commodity_code,
    wms_stock_quantity,
    stock_quantity,
    allocated_quantity,
    reserved_quantity,
    temporary_allocated_quantity,
    arrival_reserved_quantity,
    temporary_reserved_quantity,
    reservation_limit,
    stock_threshold,
    stock_arrival_date,
    arrival_quantity,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    d_created_user,
    d_created_datetime,
    d_updated_user,
    d_updated_datetime,
    d_version
)
SELECT
    shop_code,
    sku_code,
    allocated_warehouse_code,
    commodity_code,
    wms_stock_quantity,
    stock_quantity,
    allocated_quantity,
    reserved_quantity,
    temporary_allocated_quantity,
    arrival_reserved_quantity,
    temporary_reserved_quantity,
    reservation_limit,
    stock_threshold,
    stock_arrival_date,
    arrival_quantity,
    orm_rowid,
    created_user,
    created_datetime,
    updated_user,
    updated_datetime,
    'JN_ST002-DF01_001', -- d_created_user
    NOW(),               -- d_created_datetime
    'JN_ST002-DF01_001', -- d_updated_user
    NOW(),               -- d_updated_datetime
    1                    -- d_version
FROM stock_work
ON CONFLICT (shop_code, sku_code, allocated_warehouse_code) DO UPDATE SET
    commodity_code = EXCLUDED.commodity_code,
    wms_stock_quantity = EXCLUDED.wms_stock_quantity,
    stock_quantity = EXCLUDED.stock_quantity,
    allocated_quantity = EXCLUDED.allocated_quantity,
    reserved_quantity = EXCLUDED.reserved_quantity,
    temporary_allocated_quantity = EXCLUDED.temporary_allocated_quantity,
    arrival_reserved_quantity = EXCLUDED.arrival_reserved_quantity,
    temporary_reserved_quantity = EXCLUDED.temporary_reserved_quantity,
    reservation_limit = EXCLUDED.reservation_limit,
    stock_threshold = EXCLUDED.stock_threshold,
    stock_arrival_date = EXCLUDED.stock_arrival_date,
    arrival_quantity = EXCLUDED.arrival_quantity,
    orm_rowid = EXCLUDED.orm_rowid,
    created_user = EXCLUDED.created_user,
    created_datetime = EXCLUDED.created_datetime,
    updated_user = EXCLUDED.updated_user,
    updated_datetime = EXCLUDED.updated_datetime,
    d_updated_user = 'JN_ST002-DF01_001',
    d_updated_datetime = NOW(),
    d_version = stock.d_version + 1;
