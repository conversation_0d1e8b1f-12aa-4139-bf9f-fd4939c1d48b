INSERT INTO
    stock_increase_decrease (
        wms_stock_io_type,
        slip_date,
        slip_no,
        sh_control_number,
        quantity,
        stock_io_type,
        center_code,
        stock_type,
        swh_cmns_target_shop,
        center_code_partner,
        stock_type_partner,
        swh_counter,
        receiving_shipping,
        receiving_shipping_name,
        reason_code_items,
        reason_code_name,
        reason_code_item,
        d_created_user,
        d_created_datetime,
        d_updated_user,
        d_updated_datetime,
        d_version
    )
SELECT
    wms_stock_io_type,
    slip_date,
    slip_no,
    sh_control_number,
    quantity,
    stock_io_type,
    center_code,
    stock_type,
    swh_cmns_target_shop,
    center_code_partner,
    stock_type_partner,
    swh_counter,
    receiving_shipping,
    receiving_shipping_name,
    reason_code_items,
    reason_code_name,
    reason_code_item,
    'JN_ST003-DF01_001',
    -- d_created_user
    NOW(),
    -- d_created_datetime
    'JN_ST003-DF01_001',
    -- d_updated_user
    NOW(),
    -- d_updated_datetime
    1 -- d_version
FROM
    stock_increase_decrease_work ON CONFLICT (slip_no, sh_control_number) DO
UPDATE
SET
    wms_stock_io_type = EXCLUDED.wms_stock_io_type,
    slip_date = EXCLUDED.slip_date,
    quantity = EXCLUDED.quantity,
    stock_io_type = EXCLUDED.stock_io_type,
    center_code = EXCLUDED.center_code,
    stock_type = EXCLUDED.stock_type,
    swh_cmns_target_shop = EXCLUDED.swh_cmns_target_shop,
    center_code_partner = EXCLUDED.center_code_partner,
    stock_type_partner = EXCLUDED.stock_type_partner,
    swh_counter = EXCLUDED.swh_counter,
    receiving_shipping = EXCLUDED.receiving_shipping,
    receiving_shipping_name = EXCLUDED.receiving_shipping_name,
    reason_code_items = EXCLUDED.reason_code_items,
    reason_code_name = EXCLUDED.reason_code_name,
    reason_code_item = EXCLUDED.reason_code_item,
    d_updated_user = 'JN_ST003-DF01_001',
    d_updated_datetime = NOW(),
    d_version = stock_increase_decrease.d_version + 1;