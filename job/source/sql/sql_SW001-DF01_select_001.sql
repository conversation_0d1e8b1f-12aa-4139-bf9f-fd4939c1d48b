WITH RECURSIVE DateSeries AS (
    SELECT
        date_trunc('month', CURRENT_DATE) AS yyyymmdd
    UNION
    ALL
    SELECT
        (yyyymmdd + INTERVAL '1 day') :: date
    FROM
        DateSeries
    WHERE
        yyyymmdd < CURRENT_DATE + INTERVAL '1 year'
)
SELECT
    TO_CHAR(d.yyyymmdd, 'YYYYMMDD') AS "YMD",
    CASE
        WHEN h.holiday IS NOT NULL THEN 0
        ELSE 1
    END "SOUKO_KADOUBI_FLG"
FROM
    DateSeries d
    LEFT JOIN holiday h ON d.yyyymmdd = h.holiday
ORDER BY
    d.yyyymmdd ASC;