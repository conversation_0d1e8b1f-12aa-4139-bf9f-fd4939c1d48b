#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import pytest
import boto3
from unittest.mock import patch
from moto import mock_s3, mock_ssm, mock_secretsmanager
from source.glue_logger import GlueLogger
from source.db_connector import DbConnector


@pytest.fixture(autouse=True)
def mock_get_resource_path():
    """リソースパス解決処理のモック

    テスト実行時はjob/test/resources配下のファイルを参照するように
    source.common_util.get_resource_pathをモック化する
    """

    def mock_path_resolver(resource_type: str, file_name: str):
        """テスト用リソースパスの解決
        Args:
            resource_type: リソースタイプ（config/sql）
            file_name: ファイル名
        Returns:
            Path: リソースファイルのパス
        """
        from test.resources import get_test_resource_path

        return get_test_resource_path(resource_type, file_name)

    with patch("source.common_util.get_resource_path", side_effect=mock_path_resolver):
        yield


class AWSMockHelper:
    """AWS関連のモック処理を提供するヘルパークラス"""

    def __init__(self, region_name="ap-northeast-1"):
        self.region_name = region_name
        self.S3_BUCKET_NAME = "test-bucket"  # デフォルトのモック用バケット名

    def setup_ssm_parameters(self, ssm_client):
        """SSMパラメータの設定"""
        default_env_config = {
            "process": {"TZ": "Asia/Tokyo"},
            "aws": {
                "S3_BUCKET_NAME": self.S3_BUCKET_NAME,  # バケット名を変数から取得
                "S3_RETRY_LIMIT": "3",
                "S3_RETRY_INTERVAL": "1",
            },
        }
        ssm_client.put_parameter(
            Name="/glue/job/environment-config",
            Value=json.dumps(default_env_config),
            Type="String",
        )

    def setup_secrets(self, secrets_client):
        """Secrets Managerのシークレット設定"""
        db_secrets = {
            "username": "dlpf_ope",
            "password": "password",
            "host": "s2105c10703-t1",
            "port": "5432",
            "dbname": "test_db",
        }
        # db_secrets = {
        #     "username": "dlpf_batch",
        #     "password": "password",
        #     "host": "localhost",
        #     "port": "15432",
        #     "dbname": "dlpf-dev",
        # }
        secrets_client.create_secret(
            Name="test-db-secret", SecretString=json.dumps(db_secrets)
        )
        # DLPF_DB_INFOの設定
        secrets_client.create_secret(
            Name="DLPF_DB_INFO", SecretString=json.dumps(db_secrets)
        )

    def setup_s3_bucket(self, s3_client):
        """S3バケットの設定"""
        s3_client.create_bucket(
            Bucket=self.S3_BUCKET_NAME,
            CreateBucketConfiguration={"LocationConstraint": self.region_name},
        )


@pytest.fixture
def aws_helper():
    """AWSモックヘルパーのfixture"""
    os.environ["AWS_DEFAULT_REGION"] = "ap-northeast-1"
    os.environ["LOG_LEVEL"] = "DEBUG"
    return AWSMockHelper()


@pytest.fixture
def mock_aws(aws_helper):
    """AWS各種サービスのモック設定を行うfixture（モック版）

    motoライブラリを使用してAWSサービスをモック化
    S3操作もモック環境で実施
    """
    with mock_ssm():
        ssm = boto3.client("ssm", region_name=aws_helper.region_name)
        aws_helper.setup_ssm_parameters(ssm)

        with mock_secretsmanager():
            secrets = boto3.client("secretsmanager", region_name=aws_helper.region_name)
            aws_helper.setup_secrets(secrets)

            with mock_s3():
                s3 = boto3.client("s3", region_name=aws_helper.region_name)
                aws_helper.setup_s3_bucket(s3)
                yield {"ssm": ssm, "secrets": secrets, "s3": s3}


@pytest.fixture
def real_aws(aws_helper):
    """AWS各種サービスのモック設定を行うfixture（実S3版）

    SSMとSecrets Managerはモック、S3は実環境を使用
    実際のS3バケットにアクセスするため、権限とデータの取り扱いに注意
    """
    with mock_ssm():
        ssm = boto3.client("ssm", region_name=aws_helper.region_name)
        # 実S3バケット名を設定
        aws_helper.S3_BUCKET_NAME = "s3-dev-dlpf-if-886436956581"
        aws_helper.setup_ssm_parameters(ssm)

        with mock_secretsmanager():
            secrets = boto3.client("secretsmanager", region_name=aws_helper.region_name)
            aws_helper.setup_secrets(secrets)

            # 実S3クライアントを使用
            s3 = boto3.client("s3")
            yield {"ssm": ssm, "secrets": secrets, "s3": s3}


@pytest.fixture
def logger():
    """ロガーインスタンス"""
    return GlueLogger("test-jobnet")


@pytest.fixture
def db_connector(logger, mock_aws):
    """DBコネクタインスタンス"""
    db_connector = DbConnector(logger, secret_id="test-db-secret")
    db_connector.connect()
    yield db_connector

    if hasattr(db_connector, "_is_closed") and not db_connector._is_closed:
        try:
            db_connector.rollback()
        except Exception:
            pass
        finally:
            try:
                db_connector.close()
            except Exception:
                pass


@pytest.fixture
def mock_db_timestamp_responses():
    """差分基準時刻用のモックDBレスポンスを提供するフィクスチャー"""
    base_responses = {
        "sync_timestamp": [{"sync_datetime": "2024-01-01 00:00:00"}],
        "diff_base_timestamp": [{"diff_base_timestamp": "2024-02-01 00:00:00"}],
    }

    # 親子関係のテストデータ
    parent_child_data = {
        "product_query": [
            {
                "product_id": "PROD001",
                "product_name": "Parent Product 1",
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
        "bundle_query": [
            {
                "bundle_id": "BUNDLE001",
                "product_id": "PROD001",
                "bundle_quantity": 2,
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
    }

    # 入れ子構造のテストデータ
    nested_data = {
        "product_query": [
            {
                "product_id": "PROD002",
                "product_name": "Nested Product 1",
                "product_price": 1000,
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
        "bundle_query": [
            {
                "bundle_id": "BUNDLE002",
                "product_id": "PROD002",
                "bundle_name": "Bundle 1",
                "bundle_quantity": 1,
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
        "option_query": [
            {
                "option_id": "OPT001",
                "bundle_id": "BUNDLE002",
                "option_name": "Option 1",
                "option_price": 500,
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
    }

    # 同一レベルの複数子要素のテストデータ
    multiple_data = {
        "order_query": [
            {
                "order_id": "ORDER001",
                "order_date": "2024-01-15 10:00:00",
                "total_amount": 3000,
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
        "customer_query": [
            {
                "customer_id": "CUST001",
                "customer_name": "Test Customer",
                "customer_type": "REGULAR",
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
        "item_query": [
            {
                "item_id": "ITEM001",
                "order_id": "ORDER001",
                "product_id": "PROD001",
                "quantity": 2,
                "unit_price": 1500,
                "updated_datetime": "2024-01-15 00:00:00",
            }
        ],
    }

    # エラーケースのテストデータ
    error_data = {
        # 親なし子データのケース
        "error_orphan": {
            "product_query": [],  # 親データが存在しない
            "bundle_query": [
                {
                    "bundle_id": "BUNDLE_ORPHAN",
                    "product_id": "NON_EXISTENT_ID",  # 存在しない親ID
                    "bundle_quantity": 1,
                    "updated_datetime": "2024-01-15 00:00:00",
                }
            ],
        },
        # 不正な階層構造のケース
        "error_structure": {
            "product_query": [
                {
                    "product_id": "PROD_INVALID",
                    "product_name": "Invalid Structure",
                    "updated_datetime": "2024-01-15 00:00:00",
                }
            ],
            "bundle_query": [
                {
                    "bundle_id": "BUNDLE_INVALID",
                    "product_id": "PROD_INVALID",
                    "bundle_quantity": 1,
                    "updated_datetime": "2024-01-15 00:00:00",
                }
            ],
        },
        # 循環参照のケース
        "error_circular": {
            "product_query": [
                {
                    "product_id": "PROD_A",
                    "related_product_id": "PROD_B",  # PROD_Bを参照
                    "product_name": "Product A",
                    "updated_datetime": "2024-01-15 00:00:00",
                }
            ],
            "related_query": [
                {
                    "product_id": "PROD_B",
                    "related_product_id": "PROD_A",  # PROD_Aを参照（循環）
                    "product_name": "Product B",
                    "updated_datetime": "2024-01-15 00:00:00",
                }
            ],
        },
    }

    return {
        **base_responses,
        **parent_child_data,
        **nested_data,
        **multiple_data,
        **error_data,
    }


def mock_db_exec_with_timestamps(mock_db_timestamp_responses):
    """DBのexecメソッドをモック化（差分基準時刻対応版）"""
    return lambda sql, params=None: mock_db_timestamp_responses.get(
        "diff_base_timestamp", []
    )
