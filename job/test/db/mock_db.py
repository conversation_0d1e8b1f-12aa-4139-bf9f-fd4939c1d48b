#!/usr/bin/env python
# -*- coding: utf-8 -*-

from unittest.mock import MagicMock, patch
import json
from source.db_connector import DbConnector
from source.glue_logger import G<PERSON><PERSON><PERSON><PERSON>


def get_mock_db_connector():
    """
    DBコネクタのモックを作成
    Returns:
        DbConnector: モック化されたDBコネクタ
    """
    # モックロガー作成
    mock_logger = MagicMock(spec=GlueLogger)
    
    # SecretsManagerの応答をモック
    mock_secret_response = {
        "SecretString": json.dumps({
            "username": "test_user",
            "password": "test_pass",
            "host": "localhost",
            "port": 5432,
            "dbname": "test_db"
        })
    }
    
    # SecretsManagerクライアントをモック
    with patch("boto3.client") as mock_client:
        mock_secrets = MagicMock()
        mock_secrets.get_secret_value.return_value = mock_secret_response
        mock_client.return_value = mock_secrets
        
        # DBコネクタ作成
        db_connector = DbConnector(mock_logger, "test-secret-id")
        
        # DBセッションをモック化
        mock_db = MagicMock()
        mock_db.execute = MagicMock()
        db_connector.db = mock_db
        
        # トランザクション関連メソッドをモック化
        db_connector.begin = MagicMock()
        db_connector.commit = MagicMock()
        db_connector.rollback = MagicMock()
        db_connector.close = MagicMock()
        
        return db_connector
