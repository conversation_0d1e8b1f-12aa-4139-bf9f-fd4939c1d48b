#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import Mock
import yaml

from source.converter_base import DBSourceConverter
from test.resources import get_test_resource_path


# テストデータの設定
TEST_CONFIG = {
    "etl": {
        "test_query": "SELECT * FROM test_table WHERE id = :id",
        "invalid_query": "INVALID SQL",
    }
}


# テストデータファイルの作成
def setup_module(module):
    """モジュールの初期化"""
    # テストリソースディレクトリの作成
    config_dir = get_test_resource_path("config", "converter/test")
    config_dir.mkdir(parents=True, exist_ok=True)

    # テスト用設定ファイルの作成
    config_path = config_dir / "test_config.yaml"
    with open(config_path, "w", encoding="utf-8") as f:
        yaml.dump(TEST_CONFIG, f)


def teardown_module(module):
    """モジュールの終了処理"""
    # テスト用設定ファイルの削除
    config_path = get_test_resource_path("config", "converter/test/test_config.yaml")
    if config_path.exists():
        config_path.unlink()


# テスト用具象クラス
class TestDBSourceConverter(DBSourceConverter):
    """テスト用DBSourceConverter実装クラス"""

    def __init__(self, jobnet_id: str, etl_config: str, db_connection):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
            db_connection: DBコネクション
        """
        self.converter_type = "test"
        super().__init__(jobnet_id, etl_config, db_connection)

    def execute_conversion(self, output_path: str) -> str:
        """
        データ取得と変換を実行（テスト用ダミー実装）
        Args:
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        return "test_data"

    def convert_from_data(self, data: list, output_path: str) -> str:
        """
        既存データの変換（テスト用ダミー実装）
        Args:
            data: 変換元データ
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        return "test_data"


@pytest.fixture
def mock_db():
    """DBConnectorのモック"""
    return Mock()


@pytest.fixture
def converter(mock_db):
    """テスト用DBSourceConverterインスタンス"""
    return TestDBSourceConverter("test_job", "test_config", mock_db)


def test_init_success(converter):
    """初期化の正常系テスト"""
    assert converter.jobnet_id == "test_job"
    assert converter.etl_config == "test_config"
    assert isinstance(converter.config, dict)
    assert "etl" in converter.config


def test_execute_query_success(converter, mock_db):
    """_execute_queryメソッドの正常系テスト"""
    # 正常系テストではResultProxyのみモック
    mock_result = Mock()
    mock_rows = [
        Mock(_mapping={"id": 1, "name": "test1"}),
        Mock(_mapping={"id": 2, "name": "test2"}),
    ]
    mock_result.fetchall.return_value = mock_rows
    mock_result.close.return_value = None
    mock_db.exec.return_value = mock_result

    # 実行
    params = {"id": 1}
    result = converter._execute_query("test_query", params)

    # 検証
    expected_data = [{"id": 1, "name": "test1"}, {"id": 2, "name": "test2"}]
    assert result == expected_data
    mock_db.exec.assert_called_once_with(TEST_CONFIG["etl"]["test_query"], params)
    mock_result.close.assert_called_once()


def test_execute_query_query_not_found(converter, mock_db):
    """_execute_queryメソッドのクエリ未定義テスト"""
    with pytest.raises(ValueError, match="Query not found in config"):
        converter._execute_query("non_existent_query")

    mock_db.exec.assert_not_called()


def test_execute_query_db_error(converter, mock_db):
    """_execute_queryメソッドのDB実行エラーテスト"""
    mock_db.exec.side_effect = Exception("DB Error")

    with pytest.raises(Exception, match="DB Error"):
        converter._execute_query("test_query")

    mock_db.exec.assert_called_once()


@pytest.mark.parametrize(
    "query_name,params,expected_error",
    [
        ("test_query", None, None),  # 正常系
        ("non_existent", None, ValueError),  # クエリ未定義
        ("test_query", {"invalid": "params"}, Exception),  # DBエラー
    ],
)
def test_execute_query_patterns(converter, mock_db, query_name, params, expected_error):
    """_execute_queryメソッドの各パターンテスト"""
    if expected_error == Exception:
        mock_db.exec.side_effect = Exception("DB Error")
    else:
        # 正常系のモック設定
        mock_result = Mock()
        mock_result.fetchall.return_value = [Mock(_mapping={"result": "ok"})]
        mock_result.close.return_value = None
        mock_db.exec.return_value = mock_result

    if expected_error:
        with pytest.raises(expected_error):
            converter._execute_query(query_name, params)
    else:
        result = converter._execute_query(query_name, params)
        assert result == [{"result": "ok"}]
