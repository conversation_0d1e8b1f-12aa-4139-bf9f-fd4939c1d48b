#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pytest
import yaml
from typing import Dict, List

from source.delimited_file_converter import DelimitedFileConverter
from test.resources import get_test_resource_path


# テストデータ定義
CSV2TSV_CONFIG = {
    "common": {"format": "csv2tsv", "encoding": "utf_8"},
    "input": {"csv_options": {"has_header": True}},
    "output": {"tsv_options": {"has_header": True}},
}

TSV2CSV_CONFIG = {
    "common": {"format": "tsv2csv", "encoding": "ms932"},
    "input": {"tsv_options": {"has_header": True}},
    "output": {"csv_options": {"has_header": True}},
}

# 日本語データ検証用の設定
JAPANESE_CONFIG = {
    "common": {"format": "csv2tsv", "encoding": "ms932"},
    "input": {"csv_options": {"has_header": True}},
    "output": {"tsv_options": {"has_header": True}},
}


def setup_module(module):
    """モジュールの初期化"""
    # テストリソースディレクトリの作成
    config_dir = get_test_resource_path("config", "converter")
    for converter_type in ["csv2tsv", "tsv2csv"]:
        (config_dir / converter_type).mkdir(parents=True, exist_ok=True)

    # テスト用設定ファイルの作成
    with open(config_dir / "csv2tsv/test_config.yaml", "w", encoding="utf-8") as f:
        yaml.dump(CSV2TSV_CONFIG, f)

    with open(config_dir / "tsv2csv/test_config.yaml", "w", encoding="utf-8") as f:
        yaml.dump(TSV2CSV_CONFIG, f)

    with open(config_dir / "csv2tsv/japanese_config.yaml", "w", encoding="utf-8") as f:
        yaml.dump(JAPANESE_CONFIG, f)

    # etl_format_convert_csv_to_tsv.yaml追加
    etl_format_convert_csv_to_tsv_config = {
        "common": {"format": "csv2tsv", "encoding": "utf_8"},
        "input": {"csv_options": {"has_header": True}},
        "output": {"tsv_options": {"has_header": True}},
    }

    with open(
        config_dir / "etl_format_convert_csv_to_tsv.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(etl_format_convert_csv_to_tsv_config, f)

    # エラーケース用のinvalid_config.yaml追加（commonセクション欠落）
    invalid_config = {
        "input": {
            "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
        },
        "output": {
            "tsv_options": {
                "delimiter": "\t",
                "quote_char": "",
                "has_header": True,
                "line_ending": "\r\n",
            }
        },
    }

    with open(config_dir / "csv2tsv/invalid_config.yaml", "w", encoding="utf-8") as f:
        yaml.dump(invalid_config, f)

    # format欠落用のformat_missing_config.yaml追加
    format_missing_config = {
        "common": {"encoding": "utf_8"},
        "input": {
            "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
        },
        "output": {
            "tsv_options": {
                "delimiter": "\t",
                "quote_char": "",
                "has_header": True,
                "line_ending": "\r\n",
            }
        },
    }

    with open(
        config_dir / "csv2tsv/format_missing_config.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(format_missing_config, f)

    # format不正指定用のinvalid_format_config.yaml追加
    invalid_format_config = {
        "common": {"format": "invalid_format", "encoding": "utf_8"},  # 無効な形式を指定
        "input": {
            "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
        },
        "output": {
            "tsv_options": {
                "delimiter": "\t",
                "quote_char": "",
                "has_header": True,
                "line_ending": "\r\n",
            }
        },
    }

    with open(
        config_dir / "csv2tsv/invalid_format_config.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(invalid_format_config, f)

    # encoding欠落用のencoding_missing_config.yaml追加
    encoding_missing_config = {
        "common": {
            "format": "csv2tsv"
            # encodingを意図的に省略
        },
        "input": {
            "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
        },
        "output": {
            "tsv_options": {
                "delimiter": "\t",
                "quote_char": "",
                "has_header": True,
                "line_ending": "\r\n",
            }
        },
    }

    with open(
        config_dir / "csv2tsv/encoding_missing_config.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(encoding_missing_config, f)

    # 不正なencoding指定用のinvalid_encoding_config.yaml追加
    invalid_encoding_config = {
        "common": {
            "format": "csv2tsv",
            "encoding": "invalid_encoding",  # 無効な文字コードを指定
        },
        "input": {
            "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
        },
        "output": {
            "tsv_options": {
                "delimiter": "\t",
                "quote_char": "",
                "has_header": True,
                "line_ending": "\r\n",
            }
        },
    }

    with open(
        config_dir / "csv2tsv/invalid_encoding_config.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(invalid_encoding_config, f)

    # 入力オプション欠落用のmissing_input_options_config.yaml追加
    missing_input_options_config = {
        "common": {"format": "csv2tsv", "encoding": "utf_8"},
        "input": {
            # csv_optionsを意図的に省略
        },
        "output": {
            "tsv_options": {
                "delimiter": "\t",
                "quote_char": "",
                "has_header": True,
                "line_ending": "\r\n",
            }
        },
    }

    with open(
        config_dir / "csv2tsv/missing_input_options_config.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(missing_input_options_config, f)

    # 出力オプション欠落用のmissing_output_options_config.yaml追加
    missing_output_options_config = {
        "common": {"format": "csv2tsv", "encoding": "utf_8"},
        "input": {
            "csv_options": {"delimiter": ",", "quote_char": '"', "has_header": True}
        },
        "output": {
            # tsv_optionsを意図的に省略
        },
    }

    with open(
        config_dir / "csv2tsv/missing_output_options_config.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(missing_output_options_config, f)


def teardown_module(module):
    """モジュールの終了処理"""
    # config_dir = get_test_resource_path("config", "converter")
    # for config_file in config_dir.glob("**/*.yaml"):
    #     os.unlink(config_file)


# テスト用具象クラス
class TestDelimitedFileConverter(DelimitedFileConverter):
    """テスト用DelimitedFileConverter実装クラス"""

    def __init__(self, jobnet_id: str, etl_config: str, converter_type: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
            converter_type: 変換タイプ(csv2tsv/tsv2csv)
        """
        super().__init__(jobnet_id, etl_config, converter_type)

    def convert(self, source_path: str, output_path: str) -> str:
        """テスト用ダミー実装"""
        return "test_data"

    def convert_from_data(self, data: List[Dict], output_path: str) -> str:
        """テスト用ダミー実装"""
        return "test_data"

    def _read_source_file(self, file_path: str) -> List[Dict]:
        """テスト用ダミー実装"""
        return [{"column1": "value1", "column2": "value2"}]

    def get_error_prefix(self):
        """
        テスト用のエラープレフィックスを返すメソッド

        Returns:
            str: エラープレフィックス
        """
        return "TEST_ERROR"


# 正常系テスト
@pytest.mark.parametrize(
    "converter_type,expected_config",
    [
        ("csv2tsv", CSV2TSV_CONFIG),
        ("tsv2csv", TSV2CSV_CONFIG),
    ],
    ids=["csv_to_tsv", "tsv_to_csv"],
)
def test_init_success(converter_type, expected_config):
    """初期化の正常系テスト"""
    converter = TestDelimitedFileConverter("test_job", "test_config", converter_type)
    assert converter.jobnet_id == "test_job"
    assert converter.etl_config == "test_config"
    assert converter.converter_type == converter_type
    # configの内容を検証
    assert converter.config["common"]["format"] == expected_config["common"]["format"]
    assert (
        converter.config["common"]["encoding"] == expected_config["common"]["encoding"]
    )
    assert converter.config["input"] == expected_config["input"]
    assert converter.config["output"] == expected_config["output"]


# 区切り文字取得のテスト
@pytest.mark.parametrize(
    "converter_type,io_type,expected",
    [
        ("csv2tsv", "input", ","),
        ("csv2tsv", "output", "\t"),
        ("tsv2csv", "input", "\t"),
        ("tsv2csv", "output", ","),
    ],
)
def test_get_delimiter(converter_type, io_type, expected):
    """_get_delimiterメソッドのテスト"""
    converter = TestDelimitedFileConverter("test_job", "test_config", converter_type)
    assert converter._get_delimiter(io_type) == expected


# 設定検証のエラーケーステスト
def test_missing_common_section():
    """commonセクション欠落時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter("test_job", "invalid_config", "csv2tsv")
    assert "必須セクション'common'がありません" in str(excinfo.value)


def test_missing_format():
    """format指定欠落時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter("test_job", "format_missing_config", "csv2tsv")
    assert "変換形式(format)が指定されていません" in str(excinfo.value)


def test_invalid_format():
    """不正なformat指定時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter("test_job", "invalid_format_config", "csv2tsv")
    assert "サポートされていない変換形式です" in str(excinfo.value)


def test_missing_encoding():
    """encoding指定欠落時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter("test_job", "encoding_missing_config", "csv2tsv")
    assert "文字コード(encoding)が指定されていません" in str(excinfo.value)


def test_invalid_encoding():
    """不正なencoding指定時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter("test_job", "invalid_encoding_config", "csv2tsv")
    assert "サポートされていない文字コードです" in str(excinfo.value)


def test_missing_input_options():
    """入力オプション欠落時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter(
            "test_job", "missing_input_options_config", "csv2tsv"
        )
    assert "必須サブセクション'input.csv_options'がありません" in str(excinfo.value)


def test_missing_output_options():
    """出力オプション欠落時のエラー"""
    with pytest.raises(ValueError) as excinfo:
        TestDelimitedFileConverter(
            "test_job", "missing_output_options_config", "csv2tsv"
        )
    assert "必須サブセクション'output.tsv_options'がありません" in str(excinfo.value)


# 日本語データと特殊文字のテスト
def test_japanese_data():
    """日本語データの処理テスト"""
    test_data = [
        {"name": "テスト太郎", "address": "東京都新宿区"},
        {"name": "テスト花子", "address": "大阪府大阪市"},
    ]
    converter = TestDelimitedFileConverter("test_job", "japanese_config", "csv2tsv")
    result = converter.convert_from_data(test_data, "output.tsv")
    assert "test_data" in result  # ダミー実装のため、実際の変換は検証しない


def test_special_chars():
    """特殊文字を含むデータの処理テスト"""
    test_data = [
        {"field1": "カンマ,含む", "field2": "タブ\t含む"},
        {"field1": "改行\n含む", "field2": '引用符"含む'},
    ]
    converter = TestDelimitedFileConverter("test_job", "test_config", "csv2tsv")
    result = converter.convert_from_data(test_data, "output.tsv")
    assert "test_data" in result  # ダミー実装のため、実際の変換は検証しない


def test_invalid_io_type():
    """_get_delimiterの不正なio_type指定テスト"""
    converter = TestDelimitedFileConverter("test_job", "test_config", "csv2tsv")
    with pytest.raises(ValueError) as excinfo:
        converter._get_delimiter("invalid")
    assert "io_typeはinputまたはoutputを指定してください" in str(excinfo.value)
