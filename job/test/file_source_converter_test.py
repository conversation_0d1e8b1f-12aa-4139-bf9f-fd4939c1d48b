#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, List
import pytest
import yaml

from source.converter_base import FileSourceConverter
from test.resources import get_test_resource_path


# テストデータの設定
TEST_CONFIG = {
    "input": {
        "encoding": "utf-8",
        "delimiter": ",",
    },
    "output": {
        "encoding": "utf-8",
        "root_element": "root",
        "record_element": "record",
        "indent": 2,
    },
    "transformations": {
        "mappings": [
            {"source_column": "id", "target_element": "ID"},
            {"source_column": "name", "target_element": "Name"},
        ]
    },
}


# テストデータファイルの作成
def setup_module(module):
    """モジュールの初期化"""
    # テストリソースディレクトリの作成
    config_dir = get_test_resource_path("config", "converter/test")
    config_dir.parent.mkdir(parents=True, exist_ok=True)

    # テスト用設定ファイルの作成
    config_path = config_dir / "test_config.yaml"
    with open(config_path, "w", encoding="utf-8") as f:
        yaml.dump(TEST_CONFIG, f)


def teardown_module(module):
    """モジュールの終了処理"""
    # テスト用設定ファイルの削除
    config_path = get_test_resource_path("config", "converter/test/test_config.yaml")
    if config_path.exists():
        config_path.unlink()


# テスト用具象クラス
class TestFileSourceConverter(FileSourceConverter):
    """テスト用FileSourceConverter実装クラス"""

    def __init__(self, jobnet_id: str, etl_config: str):
        """
        初期化処理
        Args:
            jobnet_id: ジョブネットID
            etl_config: ETL設定ファイルID
        """
        self.converter_type = "test"
        super().__init__(jobnet_id, etl_config)

    def execute_conversion(self, output_path: str) -> str:
        """
        データ取得と変換を実行（テスト用ダミー実装）
        Args:
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        return "test_data"

    def convert_from_data(self, data: list, output_path: str) -> str:
        """
        既存データの変換（テスト用ダミー実装）
        Args:
            data: 変換元データ
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        return "test_data"

    def _read_source_file(self, file_path: str) -> List[Dict]:
        """
        テスト用ダミー実装
        Args:
            file_path: ファイルパス
        Returns:
            List[Dict]: 読み込んだデータ
        """
        return [{"id": 1, "name": "test1"}, {"id": 2, "name": "test2"}]

    def convert(self, source_path: str, output_path: str) -> str:
        """
        データ変換を実行（テスト用ダミー実装）
        Args:
            source_path: 入力パス
            output_path: 出力パス
        Returns:
            str: 変換後データ
        """
        return "test_data"


@pytest.fixture
def converter():
    """テスト用FileSourceConverterインスタンス"""
    return TestFileSourceConverter("test_job", "test_config")


def test_init_success(converter):
    """初期化の正常系テスト"""
    assert converter.jobnet_id == "test_job"
    assert converter.etl_config == "test_config"
    assert isinstance(converter.config, dict)
    assert "input" in converter.config
    assert "output" in converter.config
    assert "transformations" in converter.config


def test_read_source_file_success(converter):
    """_read_source_fileメソッドの正常系テスト"""
    # 実行
    result = converter._read_source_file("dummy_path")

    # 検証
    assert result == [{"id": 1, "name": "test1"}, {"id": 2, "name": "test2"}]


def test_convert_success(converter):
    """convertメソッドの正常系テスト"""
    # 実行
    result = converter.convert("dummy_source_path", "dummy_output_path")

    # 検証
    assert result == "test_data"
