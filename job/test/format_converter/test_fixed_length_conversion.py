#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import pytest
import tempfile
import yaml

from source.format_converter_factory import FormatConverterFactory
from test.resources import get_test_resource_path


# 固定⾧→TSV変換の設定
FIXED2TSV_CONFIG = {
    "common": {
        "format": "fixed2tsv",
        "encoding": "shift-jis",  # 固定⾧ファイルはshift-jis固定
    },
    "input": {
        "fixed_options": {
            "total_length": 50,  # フィールド⾧の合計
            "fields": [
                {
                    "name": "name",
                    "length": 20,
                },
                {
                    "name": "age",
                    "length": 10,
                },
                {
                    "name": "city",
                    "length": 20,
                },
            ],
        }
    },
    "output": {
        "tsv_options": {
            "delimiter": "\t",
            "quote_char": "",
            "has_header": True,
            "line_ending": "\r\n",
        }
    },
}


def setup_module(module):
    """モジュールの初期化"""
    # ETL設定ファイル用ディレクトリ作成
    config_dir = get_test_resource_path("config", "converter/fixed2tsv")
    config_dir.mkdir(parents=True, exist_ok=True)

    # ETL設定ファイル作成
    with open(
        config_dir / "etl_format_convert_fixed_to_tsv.yaml", "w", encoding="utf-8"
    ) as f:
        yaml.dump(FIXED2TSV_CONFIG, f)


def teardown_module(module):
    """一時ファイルの削除"""
    # 一時ファイルの削除
    temp_dir = tempfile.gettempdir()
    for filename in os.listdir(temp_dir):
        if filename.endswith((".txt", ".tsv")):
            try:
                os.unlink(os.path.join(temp_dir, filename))
            except Exception:
                pass  # 削除に失敗しても続行

    # ETL設定ファイルの削除（オプション）
    # config_dir = get_test_resource_path("config", "converter")
    # if os.path.exists(config_dir / "etl_format_convert_fixed_to_tsv.yaml"):
    #     os.unlink(config_dir / "etl_format_convert_fixed_to_tsv.yaml")


@pytest.fixture
def create_input_file():
    """入力ファイル用のfixtureを作成"""
    with tempfile.NamedTemporaryFile(
        mode="w", delete=False, encoding="shift-jis", suffix=".txt", newline="\n"
    ) as temp_file:
        return temp_file.name


@pytest.fixture
def create_empty_file():
    """空ファイル用のfixtureを作成"""
    with tempfile.NamedTemporaryFile(
        mode="w", delete=False, encoding="shift-jis", suffix=".txt"
    ) as temp_file:
        return temp_file.name


def test_fixed_to_tsv_basic_conversion(create_input_file):
    """固定⾧→TSV基本変換テスト"""
    jobnet_id = "test_jobnet"
    input_file = create_input_file
    output_file = input_file.replace(".txt", ".tsv")

    # サンプルデータ作成
    data = [
        "山田　太郎          25        東京都　　　　　　　",
        "鈴木　花子          30        大阪府　　　　　　　",
        "佐藤　一郎          35        福岡県　　　　　　　",
    ]
    with open(input_file, "w", encoding="shift-jis", newline="\n") as f:
        f.write("".join(data))

    # コンバーター取得
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )

    # 変換実行
    converter.convert(input_file, output_file)

    # 結果の検証
    assert os.path.exists(output_file)
    with open(output_file, "r", encoding="shift-jis") as f:
        lines = f.readlines()
        assert len(lines) == 4  # ヘッダー + 3データ行
        assert lines[0].strip() == "name\tage\tcity"  # ヘッダー行
        assert "山田　太郎\t25\t東京都" in lines[1]
        assert "鈴木　花子\t30\t大阪府" in lines[2]
        assert "佐藤　一郎\t35\t福岡県" in lines[3]


def test_empty_file_conversion(create_empty_file):
    """空ファイル変換テスト"""
    jobnet_id = "test_jobnet"
    input_file = create_empty_file
    output_file = input_file.replace(".txt", ".tsv")

    # コンバーター取得
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )

    # 空ファイルで変換実行
    converter.convert(input_file, output_file)

    # 結果の検証
    assert os.path.exists(output_file)
    assert os.path.getsize(output_file) == 0  # 空のファイルであることを確認


def test_field_length_validation(create_input_file):
    """フィールド⾧の検証"""
    jobnet_id = "test_jobnet"
    input_file = create_input_file
    output_file = input_file.replace(".txt", ".tsv")

    # フィールド⾧が不足したデータ
    data = ["短すぎるデータ"]
    with open(input_file, "w", encoding="shift-jis", newline="\n") as f:
        f.write("".join(data))

    # コンバーター取得
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )

    # 変換実行（エラーを期待）
    with pytest.raises(ValueError) as excinfo:
        converter.convert(input_file, output_file)
    assert "固定長ファイルのレコード長が不正です" in str(excinfo.value)


def test_shift_jis_character_handling(create_input_file):
    """SJISの文字コード処理検証"""
    jobnet_id = "test_jobnet"
    input_file = create_input_file
    output_file = input_file.replace(".txt", ".tsv")

    # デバッグ用: バイト長確認関数
    def print_byte_info(data):
        for line in data:
            print(f"Line: {line}")
            print(f"Byte length: {len(line.encode('ms932'))}")

    # SJISでエンコード可能な文字のテスト
    data = [
        "漢字かなカナ        25        テスト　　　　　　　",
        "記号！＃＄％        30        いろいろ　　　　　　",
        "外字①②③④        35        ＡＢＣ　　　　　　　",
    ]

    # デバッグ用: バイト長の確認
    print_byte_info(data)

    # バイナリモードで書き込み
    with open(input_file, "wb") as f:
        f.write("".join(data).encode("ms932"))

    # コンバーター取得
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )

    # 変換実行
    converter.convert(input_file, output_file)

    # 結果の検証
    with open(output_file, "r", encoding="ms932") as f:
        content = f.read()
        assert "漢字かな" in content
        assert "記号！＃" in content
        assert "外字①②" in content
        assert "テスト" in content
        assert "ＡＢＣ" in content


def test_multiline_data_handling(create_input_file):
    """複数行データの処理検証"""
    jobnet_id = "test_jobnet"
    input_file = create_input_file
    output_file = input_file.replace(".txt", ".tsv")

    # バイト長確認関数
    def check_byte_length(text, encoding="ms932"):
        return len(text.encode(encoding))

    # 複数行のデータ（10行）
    data = []
    for i in range(10):
        # 名前フィールド: 20バイト固定（全角文字を考慮）
        name = f"テスト太郎{i:02d}".ljust(30)
        name_bytes = name.encode("ms932")
        name = name_bytes[:20].decode("ms932")

        # 年齢フィールド: 10バイト固定（半角数字）
        age = f"{20 + i}".rjust(10)

        # 都市フィールド: 20バイト固定（全角文字を考慮）
        city = f"テスト市{i:02d}".ljust(30)
        city_bytes = city.encode("ms932")
        city = city_bytes[:20].decode("ms932")

        # デバッグ用: バイト長確認
        print(f"名前: {name}, バイト長: {check_byte_length(name)}")
        print(f"年齢: {age}, バイト長: {check_byte_length(age)}")
        print(f"都市: {city}, バイト長: {check_byte_length(city)}")

        # 1レコード50バイトになるように結合
        data.append(f"{name}{age}{city}")

    # バイナリモードで書き込み
    with open(input_file, "wb") as f:
        f.write("".join(data).encode("ms932"))

    # コンバーター取得して変換
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )
    converter.convert(input_file, output_file)

    # 結果の検証
    with open(output_file, "r", encoding="ms932") as f:
        lines = f.readlines()
        assert len(lines) == 11  # ヘッダー + 10データ行
        for i, line in enumerate(lines[1:], 0):
            expected_name = f"テスト太郎{i:02d}"
            expected_age = str(20 + i)
            expected_city = f"テスト市{i:02d}"
            assert expected_name in line
            assert expected_age in line
            assert expected_city in line


def test_whitespace_handling(create_input_file):
    """空白文字の処理検証"""
    jobnet_id = "test_jobnet"
    input_file = create_input_file
    output_file = input_file.replace(".txt", ".tsv")

    # 空白を含むテストデータ
    data = []
    test_cases = ["　前後空白", "途中　空白", "空白なし"]
    ages = [10, 20, 30]
    cities = ["　空白", "空白　途中", "詰める"]

    for name, age, city in zip(test_cases, ages, cities):
        # 名前フィールド: 20バイト固定（全角文字を考慮）
        name = name.ljust(30)
        name_bytes = name.encode("ms932")
        name = name_bytes[:20].decode("ms932")

        # 年齢フィールド: 10バイト固定（半角数字）
        age = f"{age}".rjust(10)

        # 都市フィールド: 20バイト固定（全角文字を考慮）
        city = city.ljust(30)
        city_bytes = city.encode("ms932")
        city = city_bytes[:20].decode("ms932")

        # 1レコード50バイトになるように結合
        data.append(f"{name}{age}{city}")

    # バイナリモードで書き込み
    with open(input_file, "wb") as f:
        f.write("".join(data).encode("ms932"))

    # コンバーター取得して変換
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )
    converter.convert(input_file, output_file)

    # 結果の検証
    with open(output_file, "r", encoding="shift-jis") as f:
        lines = f.readlines()
        assert "前後空白\t10\t空白" in lines[1]  # 前後の空白が除去
        assert "途中　空白\t20\t空白　途中" in lines[2]  # 途中の空白は保持
        assert "空白なし\t30\t詰める" in lines[3]  # 空白なしは変化なし


def test_data_parse_error(create_input_file):
    """
    TC-019: データ解析失敗の検証

    目的:
    - 固定長ファイルのフォーマット不正を検出できることを確認
    - レコード長の不一致エラーの確認

    検証内容:
    1. フィールド定義と一致しないデータ（レコード長不正）を作成
    2. レコード長の不一致を検出することを確認
    3. エラーメッセージにバイト数情報が含まれることを確認
    """
    jobnet_id = "test_jobnet"
    input_file = create_input_file
    output_file = input_file.replace(".txt", ".tsv")

    # フィールド定義と一致しないデータ（レコード長不正）を作成
    data = [
        "山田　太郎            25",  # cityフィールドがない
        "鈴木　花子            30        大阪府　　　　　　　",
    ]
    with open(input_file, "w", encoding="ms932", newline="\n") as f:
        f.write("".join(data))

    # コンバーター取得
    converter = FormatConverterFactory.get_converter(
        jobnet_id=jobnet_id, etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv"
    )

    # 変換実行（エラーを期待）
    with pytest.raises(ValueError) as excinfo:
        converter.convert(input_file, output_file)

    # エラーメッセージの検証
    error_message = str(excinfo.value)
    assert "固定長ファイルのレコード長が不正です" in error_message  # エラーメッセージ
    assert "ファイル長" in error_message  # 実際のファイルサイズ情報
    assert "レコード長" in error_message  # 期待されるレコード長情報
    assert "50バイト" in error_message  # 期待されるレコード長値
