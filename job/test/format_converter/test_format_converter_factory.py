#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pytest
from source.format_converter_factory import FormatConverterFactory
from source.delimited_file_converter import Csv2TsvConverter, Tsv2CsvConverter
from source.fixed_length_converter import Fixed2TsvConverter


def test_get_csv2tsv_converter():
    """CSV→TSV変換のコンバーター取得テスト"""
    converter = FormatConverterFactory.get_converter(
        jobnet_id="test_jobnet",
        etl_config="csv2tsv/test_config",
    )
    assert isinstance(converter, Csv2TsvConverter)
    assert converter.converter_type == "csv2tsv"


def test_get_tsv2csv_converter():
    """TSV→CSV変換のコンバーター取得テスト"""
    converter = FormatConverterFactory.get_converter(
        jobnet_id="test_jobnet",
        etl_config="tsv2csv/test_config",
    )
    assert isinstance(converter, Tsv2CsvConverter)
    assert converter.converter_type == "tsv2csv"


def test_get_fixed2tsv_converter():
    """固定長→TSV変換のコンバーター取得テスト"""
    converter = FormatConverterFactory.get_converter(
        jobnet_id="test_jobnet",
        etl_config="fixed2tsv/etl_format_convert_fixed_to_tsv",
    )
    assert isinstance(converter, Fixed2TsvConverter)
    assert converter.converter_type == "fixed2tsv"


def test_unsupported_input_format():
    """サポート外の入力フォーマット指定時のエラー検証"""
    with pytest.raises(ValueError) as excinfo:
        FormatConverterFactory.get_converter(
            jobnet_id="test_jobnet",
            etl_config="example",
        )
    assert "ETL設定に変換形式(format)が指定されていません。" in str(excinfo.value)
