#!/usr/bin/env python
# -*- coding: utf-8 -*-

from pathlib import Path


def get_test_resource_path(resource_type: str, file_name: str) -> Path:
    """テストリソースファイルのパスを解決する

    Args:
        resource_type: リソースタイプ（'config' or 'sql'）
        file_name: ファイル名

    Returns:
        Path: リソースファイルのパス
    """
    base_path = Path(__file__).parent
    return base_path / "resources" / resource_type / file_name
