database:
  batch_size: 1000
  timeout: 3600

output:
  encoding: shift-jis
  format: fixed-length
  line_ending: "\r\n"

input:
  type: "database"
fixed_length:
  total_length: 40
  padding_char: " "
  fields:
    - name: "id"
      length: 10
      type: "number"
    - name: "name"
      length: 20
      type: "string"
    - name: "value"
      length: 10
      type: "number"

etl:
  main_query: |
    SELECT id, name, value
    FROM ut_test_table
    WHERE id > 0

  mappings:
    - source_column: "id"
      target_field: "id"
      type: "number"
    - source_column: "name"
      target_field: "name"
      type: "string"
    - source_column: "value"
      target_field: "value"
      type: "number"
