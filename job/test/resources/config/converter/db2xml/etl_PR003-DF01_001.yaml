common:
  format: db2xml
  encoding: utf-8

output:
  root_element: catalog
  record_element: product
  indent: 2

etl:
  parameters:
    sync_timestamp:
      type: datetime
      description: 前回同期済時刻
      required: true
    diff_base_timestamp:
      type: datetime
      description: 差分基準時刻
      required: true

  # メインクエリ: 商品基本情報の取得
  product_query: |
    SELECT
      'dhc-m-catalog' AS catalog_id,
      p.mail_order_product_cd,
      1 AS min_order_quantity,
      1 AS step_quantity,
      p.web_product_name,
      'x-default' AS lang,
      p.representative_product_cd,
      p.registration_name,
      p.contents,
      p.core_department,
      p.dep,
      p.jan,
      p.product_segment,
      p.business_segment,
      p.product_cat,
      p.lgroup,
      p.mgroup,
      p.sgroup,
      p.dgroup,
      p.product_type,
      p.web,
      p.order_per_order_max,
      p.weight,
      p.color_name,
      p.color_cd,
      p.size_name,
      p.size_cd,
      p.mail_delivery_flg,
      p.nekoposu_volume_rate,
      p.outside_home_receive_service_flg,
      p.outside_home_volume_rate,
      p.company_sales_buy_flg,
      p.set_composition_flg,
      p.before_renewal_product_no,
      p.shape_name,
      p.shape_cd,
      p.season,
      p.use_point_cnt,
      p.sales_channel_1_sale_start_date,
      p.sales_channel_1_sale_end_date,
      p.product_series,
      p.set_product_flg,
      '' AS txsalemethod,
      '' AS txcumulativeorderlimit,
      p.preferential_product_flg,
      CASE
          WHEN p.mail_order_product_cd != p.warehouse_management_cd THEN p.warehouse_management_cd
          WHEN p.warehouse_management_cd IS NULL THEN NULL
          ELSE NULL
      END AS attribute_id,
      CASE pp.tax
          WHEN 0 THEN 0
          WHEN 1 THEN pp.tax_rate
      END AS tax_class_id
    FROM
      mdm.product_linkage p
    LEFT JOIN
      mdm.period_price_linkage pp ON p.mdm_integration_management_cd = pp.mdm_integration_management_cd
    WHERE
      (p.pms_u_ymd > :sync_timestamp AND p.pms_u_ymd <= :diff_base_timestamp)
      OR (pp.pms_u_ymd > :sync_timestamp AND pp.pms_u_ymd <= :diff_base_timestamp)

  # セット商品構成情報を取得するサブクエリ
  bundled_products_query: |
    SELECT
      child_commodity_code AS product_id,
      composition_quantity AS quantity
    FROM
      set_commodity_composition
    WHERE
      commodity_code = :mail_order_product_cd
      AND (d_updated_datetime > :sync_timestamp AND d_updated_datetime <= :diff_base_timestamp)

  # 定期商品構成情報を取得するサブクエリ
  regular_products_query: |
    SELECT
      commodity_code AS product_id,
      contract_amount AS quantity
    FROM
      regular_sale_cont_detail
    WHERE
      commodity_code = :mail_order_product_cd
      AND next_regular_sale_stop_status != '1'
      AND (d_updated_datetime > :sync_timestamp AND d_updated_datetime <= :diff_base_timestamp)

  mappings:
    - target_element: catalog
      attributes:
        catalog-id: "dhc-m-catalog"
      child_elements:
        - target_element: product
          query: product_query
          attributes:
            product-id: "{mail_order_product_cd}"
          child_elements:
            - target_element: min-order-quantity
              source_column: min_order_quantity
            - target_element: step-quantity
              source_column: step_quantity
              type: number
            - target_element: display-name
              attributes:
                xml:lang: x-default
            - target_element: tax-class-id
              source_column: tax_class_id
              type: string
            - target_element: custom-attributes
              child_elements:
                - target_element: custom-attribute
                  source_column: representative_product_cd
                  attributes:
                    attribute-id: txRepresentativeProductID
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txEntryName
                  source_column: registration_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txCapacity
                  source_column: contents
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txCoreDept
                  source_column: core_department
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txPepartmentInCharge
                  source_column: dep
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txJanCode
                  source_column: jan
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSegment
                  source_column: product_segment
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txBussinessSegment
                  source_column: business_segment
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductClass
                  source_column: product_cat
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductCategory
                  source_column: lgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductSubclass
                  source_column: mgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductDivision
                  source_column: sgroup
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductComp
                  source_column: product_type
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWebSite
                  source_column: web
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txOrderLimit
                  source_column: order_per_order_max
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWeight
                  source_column: weight
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txColorName
                  source_column: color_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txColor
                  source_column: color_cd
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSizeName
                  source_column: size_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSize
                  source_column: size_cd
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txMailDeliveryFlag
                  source_column: mail_delivery_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txVolume
                  source_column: nekoposu_volume_rate
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txTakeStorFlag
                  source_column: outside_home_receive_service_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txOutsideHomeVolumeRate
                  source_column: outside_home_volume_rate
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txEmployeeFlag
                  source_column: company_sales_buy_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSetConfigrationFlag
                  source_column: set_composition_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txOther
                  source_column: before_renewal_product_no
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSharpName
                  source_column: shape_name
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSharpCode
                  source_column: shape_cd
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSeason
                  source_column: season
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txPoint
                  source_column: use_point_cnt
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txProductSeries
                  source_column: product_series
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWebOrderStartDate
                  source_column: sales_channel_1_sale_start_date
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txWebOrderFinishDate
                  source_column: sales_channel_1_sale_end_date
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSetProductFlag
                  source_column: set_product_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txSaleMethod
                  source_column: txSaleMethod
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txCumulativeOrderLimit
                  source_column: txCumulativeOrderLimit
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txPreferentialFlag
                  source_column: preferential_product_flg
                - target_element: custom-attribute
                  attributes:
                    attribute-id: txInventoryProductID
                  source_column: attribute_id

            # バンドル情報（セット商品または定期商品の場合）
            - target_element: bundled-products
              condition: "set_product_flg == '1' or product_type == '11'"
              child_elements:
                # セット商品の場合の構成品
                - target_element: bundled-product
                  condition: "set_product_flg == '1'"
                  sub_query: bundled_products_query
                  attributes:
                    product-id: "{product_id}"
                  child_elements:
                    - target_element: quantity
                      source_column: quantity
                      type: number
                # 定期商品の場合の構成品
                - target_element: bundled-product
                  condition: "product_type == '11'"
                  sub_query: regular_products_query
                  attributes:
                    product-id: "{product_id}"
                  child_elements:
                    - target_element: quantity
                      source_column: quantity
                      type: number
