# 循環参照のエラーケース用YAML
common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "products"
  indent: 2

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # 商品情報取得クエリ
  product_query: |
    SELECT
      p.product_id,
      p.related_product_id,  # 関連商品ID（循環参照用）
      p.product_name,
      p.updated_datetime
    FROM products p
    WHERE p.updated_datetime >= :sync_timestamp
      AND p.updated_datetime < :diff_base_timestamp

  # 関連商品情報取得クエリ（循環参照）
  related_query: |
    SELECT
      p.product_id,
      p.related_product_id,  # 元の商品を参照（循環）
      p.product_name,
      p.updated_datetime
    FROM products p
    WHERE p.product_id = :related_product_id  # 関連商品IDを参照
      AND p.updated_datetime >= :sync_timestamp
      AND p.updated_datetime < :diff_base_timestamp

  # マッピング定義（循環参照構造）
  mappings:
    - target_element: "products"
      child_elements:
        # 商品情報（循環参照の開始点）
        - target_element: "product"
          query: "product_query"
          attributes:
            product-id: "{product_id}"
          child_elements:
            - target_element: "name"
              source_column: "product_name"
            # 関連商品情報（循環参照）
            - target_element: "related-product"
              query: "related_query"
              attributes:
                product-id: "{product_id}"
              child_elements:
                # 元の商品を再度参照（循環）
                - target_element: "product"
                  query: "product_query"
                  attributes:
                    product-id: "{product_id}"
