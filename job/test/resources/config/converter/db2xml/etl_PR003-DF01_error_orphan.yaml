# 親が存在しない子データのエラーケース用YAML
common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "products"
  indent: 2

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # 親商品情報取得クエリ（存在しない商品ID）
  product_query: |
    SELECT
      p.product_id,
      p.product_name,
      p.updated_datetime
    FROM products p
    WHERE p.product_id = 'NON_EXISTENT_ID'  # 存在しない商品ID
      AND p.updated_datetime >= :sync_timestamp
      AND p.updated_datetime < :diff_base_timestamp

  # バンドル商品情報取得クエリ（親なし）
  bundle_query: |
    SELECT
      b.bundle_id,
      b.product_id,  # 親商品のID（存在しない）
      b.bundle_quantity,
      b.updated_datetime
    FROM bundled_products b
    WHERE b.product_id = :product_id  # 存在しない親商品ID
      AND b.updated_datetime >= :sync_timestamp
      AND b.updated_datetime < :diff_base_timestamp

  # マッピング定義
  mappings:
    - target_element: "products"
      child_elements:
        # 商品情報（親要素 - 存在しない）
        - target_element: "product"
          query: "product_query"
          attributes:
            product-id: "{product_id}"
          child_elements:
            - target_element: "name"
              source_column: "product_name"
            # バンドル商品情報（子要素 - 親なし）
            - target_element: "bundled-products"
              child_elements:
                - target_element: "bundled-product"
                  query: "bundle_query" # 親が存在しないためエラーになるはず
                  attributes:
                    bundle-id: "{bundle_id}"
                  child_elements:
                    - target_element: "quantity"
                      source_column: "bundle_quantity"
                      type: "number"
