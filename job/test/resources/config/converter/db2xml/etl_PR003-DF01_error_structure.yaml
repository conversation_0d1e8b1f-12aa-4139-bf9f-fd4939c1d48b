# 不正な階層構造のエラーケース用YAML
common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "products"
  indent: 2

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # 商品情報取得クエリ
  product_query: |
    SELECT
      p.product_id,
      p.product_name,
      p.updated_datetime
    FROM products p
    WHERE p.updated_datetime >= :sync_timestamp
      AND p.updated_datetime < :diff_base_timestamp

  # バンドル商品情報取得クエリ
  bundle_query: |
    SELECT
      b.bundle_id,
      b.product_id,
      b.bundle_quantity,
      b.updated_datetime
    FROM bundled_products b
    WHERE b.updated_datetime >= :sync_timestamp
      AND b.updated_datetime < :diff_base_timestamp

  # マッピング定義（不正な階層構造）
  mappings:
    - target_element: "products"
      child_elements:
        # 不正な階層1：子要素が親要素を参照
        - target_element: "bundled-product"
          query: "bundle_query"
          child_elements:
            - target_element: "product" # 本来は親要素であるべき
              query: "product_query"
              attributes:
                product-id: "{product_id}"

        # 不正な階層2：同じ要素の無限ネスト
        - target_element: "product"
          query: "product_query"
          child_elements:
            - target_element: "product" # 同じ要素の無限ネスト
              query: "product_query"
              child_elements:
                - target_element: "product"
                  query: "product_query"

        # 不正な階層3：未定義の要素を参照
        - target_element: "product"
          query: "product_query"
          child_elements:
            - target_element: "undefined-element" # 存在しない要素
              query: "undefined_query" # 存在しないクエリ
