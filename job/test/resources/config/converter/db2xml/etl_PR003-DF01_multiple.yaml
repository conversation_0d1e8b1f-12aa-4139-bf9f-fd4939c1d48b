# 同一レベルでの複数子要素のテスト用YAML
common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "orders"
  indent: 2

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # 注文情報取得クエリ（親）
  order_query: |
    SELECT
      o.order_id,
      o.order_date,
      o.total_amount,
      o.updated_datetime
    FROM orders o
    WHERE o.updated_datetime >= :sync_timestamp
      AND o.updated_datetime < :diff_base_timestamp

  # 顧客情報取得クエリ（子要素1）
  customer_query: |
    SELECT
      c.customer_id,
      c.customer_name,
      c.customer_type,
      c.updated_datetime
    FROM customers c
    INNER JOIN orders o ON o.customer_id = c.customer_id
    WHERE o.order_id = :order_id  # 親注文IDをパラメータとして使用
      AND c.updated_datetime >= :sync_timestamp
      AND c.updated_datetime < :diff_base_timestamp

  # 注文商品情報取得クエリ（子要素2）
  item_query: |
    SELECT
      i.item_id,
      i.order_id,  # 親注文のID
      i.product_id,
      i.quantity,
      i.unit_price,
      i.updated_datetime
    FROM order_items i
    WHERE i.order_id = :order_id  # 親注文IDをパラメータとして使用
      AND i.updated_datetime >= :sync_timestamp
      AND i.updated_datetime < :diff_base_timestamp

  # マッピング定義
  mappings:
    - target_element: "orders"
      child_elements:
        # 注文情報（親要素）
        - target_element: "order"
          query: "order_query"
          attributes:
            order-id: "{order_id}"
          child_elements:
            - target_element: "order-date"
              source_column: "order_date"
              type: "datetime"
            - target_element: "total-amount"
              source_column: "total_amount"
              type: "number"

            # 顧客情報（子要素1）
            - target_element: "customer"
              query: "customer_query"
              attributes:
                customer-id: "{customer_id}"
              child_elements:
                - target_element: "name"
                  source_column: "customer_name"
                - target_element: "type"
                  source_column: "customer_type"

            # 注文商品情報（子要素2）
            - target_element: "items"
              child_elements:
                - target_element: "item"
                  query: "item_query"
                  attributes:
                    item-id: "{item_id}"
                    product-id: "{product_id}"
                  child_elements:
                    - target_element: "quantity"
                      source_column: "quantity"
                      type: "number"
                    - target_element: "unit-price"
                      source_column: "unit_price"
                      type: "number"

  # データ変換ルール
  transformations:
    # 日時の変換
    - column: "order_date"
      type: "datetime"
      format: "%Y-%m-%dT%H:%M:%S.000%z"

    # 数値の変換
    - column: "total_amount"
      type: "number"
      format: "%.0f"

    - column: "quantity"
      type: "number"
      format: "%.0f"

    - column: "unit_price"
      type: "number"
      format: "%.0f"
