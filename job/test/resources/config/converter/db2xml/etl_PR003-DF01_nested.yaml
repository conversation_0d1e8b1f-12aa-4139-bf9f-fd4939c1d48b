# 複数レベルの入れ子構造のテスト用YAML
common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "products"
  indent: 2

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # 親商品情報取得クエリ（第1階層）
  product_query: |
    SELECT
      p.product_id,
      p.product_name,
      p.product_price,
      p.updated_datetime
    FROM products p
    WHERE p.updated_datetime >= :sync_timestamp
      AND p.updated_datetime < :diff_base_timestamp

  # バンドル商品情報取得クエリ（第2階層）
  bundle_query: |
    SELECT
      b.bundle_id,
      b.product_id,  # 親商品のID
      b.bundle_name,
      b.bundle_quantity,
      b.updated_datetime
    FROM bundled_products b
    WHERE b.product_id = :product_id  # 親商品IDをパラメータとして使用
      AND b.updated_datetime >= :sync_timestamp
      AND b.updated_datetime < :diff_base_timestamp

  # オプション情報取得クエリ（第3階層）
  option_query: |
    SELECT
      o.option_id,
      o.bundle_id,  # 親バンドルのID
      o.option_name,
      o.option_price,
      o.updated_datetime
    FROM product_options o
    WHERE o.bundle_id = :bundle_id  # 親バンドルIDをパラメータとして使用
      AND o.updated_datetime >= :sync_timestamp
      AND o.updated_datetime < :diff_base_timestamp

  # マッピング定義
  mappings:
    - target_element: "products"
      child_elements:
        # 商品情報（第1階層）
        - target_element: "product"
          query: "product_query"
          attributes:
            product-id: "{product_id}"
          child_elements:
            - target_element: "name"
              source_column: "product_name"
            - target_element: "price"
              source_column: "product_price"
              type: "number"

            # バンドル商品情報（第2階層）
            - target_element: "bundled-products"
              child_elements:
                - target_element: "bundled-product"
                  query: "bundle_query"
                  attributes:
                    bundle-id: "{bundle_id}"
                  child_elements:
                    - target_element: "name"
                      source_column: "bundle_name"
                    - target_element: "quantity"
                      source_column: "bundle_quantity"
                      type: "number"

                    # オプション情報（第3階層）
                    - target_element: "options"
                      child_elements:
                        - target_element: "option"
                          query: "option_query"
                          attributes:
                            option-id: "{option_id}"
                          child_elements:
                            - target_element: "name"
                              source_column: "option_name"
                            - target_element: "price"
                              source_column: "option_price"
                              type: "number"

  # データ変換ルール
  transformations:
    # 数値の変換
    - column: "product_price"
      type: "number"
      format: "%.0f"

    - column: "bundle_quantity"
      type: "number"
      format: "%.0f"

    - column: "option_price"
      type: "number"
      format: "%.0f"
