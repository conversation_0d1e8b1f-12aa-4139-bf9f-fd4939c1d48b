# 基本的な親子関係のテスト用YAML
common:
  format: "db2xml"
  encoding: "utf-8"

output:
  root_element: "products"
  indent: 2

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true

  # 親商品情報取得クエリ
  product_query: |
    SELECT
      p.mdm_integration_management_cd as product_id,
      p.web_product_name as product_name,
      p.pms_u_ymd as updated_datetime
    FROM mdm.product_linkage p
    WHERE p.pms_u_ymd >= :sync_timestamp
      AND p.pms_u_ymd < :diff_base_timestamp

  # バンドル商品情報取得クエリ（子要素）
  bundle_query: |
    SELECT
      scc.child_commodity_code as bundle_id,
      scc.commodity_code as product_id,
      1 as bundle_quantity,
      scc.d_updated_datetime as updated_datetime
    FROM set_commodity_composition scc
    WHERE scc.commodity_code = :product_id
      AND scc.d_updated_datetime >= :sync_timestamp
      AND scc.d_updated_datetime < :diff_base_timestamp

  # マッピング定義
  mappings:
    - target_element: "products"
      child_elements:
        # 商品情報（親要素）
        - target_element: "product"
          query: "product_query"
          attributes:
            product-id: "{product_id}"
          child_elements:
            - target_element: "name"
              source_column: "product_name"
            # バンドル商品情報（子要素）
            - target_element: "bundled-products"
              child_elements:
                - target_element: "bundled-product"
                  query: "bundle_query"
                  parameters:
                    product_id: "{product_id}"
                  attributes:
                    bundle-id: "{bundle_id}"
                  child_elements:
                    - target_element: "quantity"
                      source_column: "bundle_quantity"
