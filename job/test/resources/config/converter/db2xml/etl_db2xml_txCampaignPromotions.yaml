# キャンペーン・プロモーションXML変換の設定
database:
  batch_size: 1000
  timeout: 3600

output:
  encoding: utf-8
  root_element: promotions # XMLのルート要素

etl:
  # パラメータ定義
  parameters:
    sync_timestamp:
      type: datetime
      description: "前回同期済時刻"
      required: true
    diff_base_timestamp:
      type: datetime
      description: "差分基準時刻"
      required: true
    user:
      type: string
      description: "更新ユーザー"
      required: true
      default: "batch_user"

  # キャンペーン情報取得クエリ
  campaign_query: |
    WITH target_campaigns AS (
      SELECT
        campaign_instructions_code,
        updated_datetime
      FROM campaign_instructions
      WHERE updated_datetime > :sync_timestamp
        AND updated_datetime <= :diff_base_timestamp
    )
    SELECT
      ci.campaign_instructions_code as campaign_code,
      ci.campaign_instructions_name as campaign_name,
      CASE WHEN ci.delete_flg = 0 THEN 'true' ELSE 'false' END as enabled_flag,
      ci.campaign_start_date,
      ci.campaign_end_date
    FROM campaign_instructions ci
    INNER JOIN target_campaigns tc
      ON ci.campaign_instructions_code = tc.campaign_instructions_code

  # プロモーション情報取得クエリ
  promotion_query: |
    WITH target_campaigns AS (
      SELECT
        campaign_instructions_code,
        updated_datetime
      FROM campaign_instructions
      WHERE updated_datetime > :sync_timestamp
        AND updated_datetime <= :diff_base_timestamp
    )
    SELECT
      cp.campaign_instructions_code as campaign_code,
      cp.promotion_no,
      cp.promotion_type,
      cp.commodity_code,
      cp.commodity_name,
      cp.discount_rate,
      cp.discount_amount,
      ci.oneshot_order_limit as order_limit_quantity,
      ci.campaign_quantity_limit as campaign_limit_quantity,
      ci.campaign_end_date,
      CASE WHEN EXISTS (
        SELECT 1 FROM product_linkage pl
        WHERE pl.MAIL_ORDER_PRODUCT_CD = cp.commodity_code
        AND pl.PREFERENTIAL_PRODUCT_FLG = '1'
      ) THEN 'true' ELSE 'false' END as privileged_flag,
      ci.baitai_code as media_code,
      ci.campaign_priority as priority
    FROM campaign_promotion cp
    INNER JOIN campaign_instructions ci
      ON cp.campaign_instructions_code = ci.campaign_instructions_code
    INNER JOIN target_campaigns tc
      ON cp.campaign_instructions_code = tc.campaign_instructions_code

  # マッピング定義
  mappings:
    # ルート要素：promotions
    - target_element: promotions
      child_elements:
        # 1. campaign要素（promotionsの直接の子要素）
        - target_element: campaign
          source_column: campaign_code
          type: string
          query: campaign_query
          attributes:
            campaign-id: "{campaign_code}"
          child_elements:
            - target_element: description
              source_column: campaign_name
              type: string
            - target_element: enabled-flag
              source_column: enabled_flag
              type: string
            - target_element: campaign-scope
            - target_element: applicable-online
            - target_element: start-date
              source_column: campaign_start_date
              type: datetime
              format: "%Y-%m-%dT%H:%M:%S.000%z"
            - target_element: end-date
              source_column: campaign_end_date
              type: datetime
              format: "%Y-%m-%dT%H:%M:%S.000%z"
            - target_element: customer-groups
              attributes:
                match-mode: "any"
              child_elements:
                - target_element: customer-group
                  attributes:
                    group-id: "txGroupId_{campaign_code}"

        # 2. promotion要素（promotionsの直接の子要素）
        - target_element: promotion
          query: promotion_query
          attributes:
            promotion-id: "{campaign_code}_{promotion_no}"
          child_elements:
            # 子要素として追加する要素群
            - target_element: enabled-flag
              value: "true"
              type: boolean
            - target_element: archived-flag
              value: "false"
              type: boolean
            - target_element: searchable-flag
              value: "false"
              type: boolean
            - target_element: refinable-flag
              value: "false"
              type: boolean
            - target_element: prevent-requalifying-flag
              value: "false"
              type: boolean
            - target_element: prorate-across-eligible-items-flag
              value: "false"
              type: boolean
            - target_element: exclusivity
              value: "no"
              type: string
            - target_element: name
              source_column: commodity_name
              type: string
            - target_element: custom-attributes
              child_elements:
                # プロモーション種別
                - target_element: custom-attribute
                  source_column: promotion_type
                  attributes:
                    attribute-id: "promotion_type"
                # プレゼント商品コード
                - target_element: custom-attribute
                  source_column: commodity_code
                  condition: promotion_type == '01'
                  attributes:
                    attribute-id: "gift_product_code"
                # OFF率
                - target_element: custom-attribute
                  source_column: discount_rate
                  condition: promotion_type == '03'
                  attributes:
                    attribute-id: "off_rate"
                  type: number
                # 上限数量（1注文）
                - target_element: custom-attribute
                  source_column: order_limit_quantity
                  attributes:
                    attribute-id: "order_limit_quantity"
                  type: number
                # 上限数量（同一CP累積）
                - target_element: custom-attribute
                  source_column: campaign_limit_quantity
                  attributes:
                    attribute-id: "campaign_limit_quantity"
                  type: number
                # キャンペーン終了日
                - target_element: custom-attribute
                  source_column: campaign_end_date
                  attributes:
                    attribute-id: "campaign_end_date"
                  type: datetime
                  format: "%Y-%m-%dT%H:%M:%S.000%z"
                # 優待プロモーションフラグ
                - target_element: custom-attribute
                  source_column: privileged_flag
                  attributes:
                    attribute-id: "privileged_flag"
                  type: boolean
                # 媒体コード
                - target_element: custom-attribute
                  source_column: media_code
                  attributes:
                    attribute-id: "media_code"
                # 優先順位
                - target_element: custom-attribute
                  source_column: priority
                  attributes:
                    attribute-id: "priority"
                  type: number

            # 値引の場合のproduct-promotion-rule
            - target_element: product-promotion-rule
              condition: promotion_type == '02'
              child_elements:
                - target_element: discounted-products
                  child_elements:
                    - target_element: included-products
                      child_elements:
                        - target_element: condition-group
                          child_elements:
                            - target_element: product-id-condition
                              attributes:
                                operator: "is-equal"
                              child_elements:
                                - target_element: product-id
                                  source_column: commodity_code
                - target_element: simple-discount
                  child_elements:
                    - target_element: amount
                      source_column: discount_amount
                      type: number

            # 割引の場合のproduct-promotion-rule
            - target_element: product-promotion-rule
              condition: promotion_type == '03'
              child_elements:
                - target_element: discounted-products
                  child_elements:
                    - target_element: included-products
                      child_elements:
                        - target_element: condition-group
                          child_elements:
                            - target_element: product-id-condition
                              attributes:
                                operator: "is-equal"
                              child_elements:
                                - target_element: product-id
                                  source_column: commodity_code
                - target_element: simple-discount
                  child_elements:
                    - target_element: percentage
                      source_column: discount_rate
                      type: number
                      format: "%.2f"

        # 3. promotion-campaign-assignment要素（promotionsの直接の子要素）
        - target_element: promotion-campaign-assignment
          query: promotion_query
          attributes:
            promotion-id: "{campaign_code}_{promotion_no}"
            campaign-id: "{campaign_code}"
          child_elements:
            - target_element: qualifiers
              attributes:
                match-mode: "any"
              child_elements:
                - target_element: customer-groups
                - target_element: source-codes
                - target_element: coupons

  # データ変換ルール
  transformations:
    # NULL値の置換処理
    - column: condition_content
      type: replace_null
      value: "未分類"
      query: promotion_query

    # 真偽値の変換
    - column: privileged_flag
      type: boolean_convert
      true_value: "1"
      false_value: "0"
      query: promotion_query

    # 日時形式の変換
    - column: campaign_start_date
      type: datetime_convert
      format: "%Y-%m-%dT%H:%M:%S.000%z"
      query: campaign_query

    - column: campaign_end_date
      type: datetime_convert
      format: "%Y-%m-%dT%H:%M:%S.000%z"
      query: campaign_query
