common:
  format: db2xml
  encoding: utf-8

output:
  root_element: data
  encoding: utf-8

etl:
  parameters:
    sync_timestamp:
      type: datetime
      required: true
    diff_base_timestamp:
      type: datetime
      required: true

  test_query: |
    SELECT
      id,
      name,
      value,
      created_user,
      created_datetime,
      updated_user,
      updated_datetime,
      d_created_user,
      d_created_datetime,
      d_updated_user,
      d_updated_datetime,
      d_version
    FROM ut_test_table
    ORDER BY id

  mappings:
    - target_element: data
      query: test_query
      child_elements:
        - target_element: record
          attributes:
            record-id: "{id}"
          child_elements:
            - target_element: name
              source_column: name
            - target_element: value
              source_column: value
            - target_element: created_user
              source_column: created_user
            - target_element: created_datetime
              source_column: created_datetime
            - target_element: updated_user
              source_column: updated_user
            - target_element: updated_datetime
              source_column: updated_datetime
            - target_element: d_created_user
              source_column: d_created_user
            - target_element: d_created_datetime
              source_column: d_created_datetime
            - target_element: d_updated_user
              source_column: d_updated_user
            - target_element: d_updated_datetime
              source_column: d_updated_datetime
            - target_element: d_version
              source_column: d_version
