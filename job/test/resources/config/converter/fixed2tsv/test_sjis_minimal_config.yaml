common:
  encoding: shift_jis
  format: fixed2tsv
input:
  fixed_options:
    fields:
    - length: 10
      name: "\u5546\u54C1\u30B3\u30FC\u30C9"
      type: string
    - length: 20
      name: "\u5546\u54C1\u540D"
      type: string
    - length: 10
      name: "\u4FA1\u683C"
      type: string
    - length: 5
      name: "\u5728\u5EAB\u6570"
      type: string
    - length: 8
      name: "\u66F4\u65B0\u65E5"
      type: string
    total_length: 53
output:
  tsv_options:
    delimiter: "\t"
    has_header: false
    line_ending: '

      '
    quote_char: ''
