etl:
  # メインクエリの定義
  main_query: test.sql
  parameters:
    sync_timestamp:
      type: datetime
      required: true
    diff_base_timestamp:
      type: datetime
      required: true

output:
  # 出力ファイル設定
  format: csv
  settings:
    header: true
    quote_char: '"'
    line_ending: "\n"
  columns:
    - id
    - name
    - value
    - created_user
    - created_datetime
    - updated_user
    - updated_datetime
    - d_created_user
    - d_created_datetime
    - d_updated_user
    - d_updated_datetime
    - d_version
