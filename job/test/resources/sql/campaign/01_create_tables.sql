-- キャンペーン指示テーブル
CREATE TABLE IF NOT EXISTS campaign_instructions (
    campaign_instructions_code VARCHAR(10) PRIMARY KEY,
    campaign_instructions_name VARCHAR(100) NOT NULL,
    delete_flg INTEGER NOT NULL DEFAULT 0,
    campaign_start_date TIMESTAMP NOT NULL,
    campaign_end_date TIMESTAMP NOT NULL,
    baitai_code VARCHAR(20) NOT NULL DEFAULT 'ONLINE',
    campaign_priority INTEGER NOT NULL DEFAULT 1,
    oneshot_order_limit INTEGER,
    campaign_quantity_limit INTEGER,
    applicable_online BOOLEAN NOT NULL DEFAULT true,
    match_mode VARCHAR(10) NOT NULL DEFAULT 'any',
    created_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- プロモーション情報テーブル
CREATE TABLE IF NOT EXISTS campaign_promotion (
    promotion_id VARCHAR(20) PRIMARY KEY,
    campaign_instructions_code VARCHAR(10) NOT NULL REFERENCES campaign_instructions(campaign_instructions_code),
    promotion_no VARCHAR(10) NOT NULL,
    promotion_type VARCHAR(2) NOT NULL,
    commodity_code VARCHAR(20) NOT NULL,
    commodity_name VARCHAR(100) NOT NULL,
    discount_rate DECIMAL(5, 2),
    discount_amount INTEGER,
    archived_flag BOOLEAN NOT NULL DEFAULT false,
    searchable_flag BOOLEAN NOT NULL DEFAULT false,
    refinable_flag BOOLEAN NOT NULL DEFAULT false,
    prevent_requalifying_flag BOOLEAN NOT NULL DEFAULT false,
    prorate_across_eligible_items_flag BOOLEAN NOT NULL DEFAULT false,
    exclusivity VARCHAR(10) NOT NULL DEFAULT 'no',
    created_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- キャンペーン順序テーブル
CREATE TABLE IF NOT EXISTS campaign_order (
    campaign_instructions_code VARCHAR(10) PRIMARY KEY REFERENCES campaign_instructions(campaign_instructions_code),
    display_order INTEGER NOT NULL,
    created_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- 商品連携テーブル
CREATE TABLE IF NOT EXISTS product_linkage (
    MAIL_ORDER_PRODUCT_CD VARCHAR(20) NOT NULL,
    PREFERENTIAL_PRODUCT_FLG CHAR(1) NOT NULL DEFAULT '0',
    promotion_id VARCHAR(20) REFERENCES campaign_promotion(promotion_id),
    created_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_datetime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (promotion_id, MAIL_ORDER_PRODUCT_CD)
);