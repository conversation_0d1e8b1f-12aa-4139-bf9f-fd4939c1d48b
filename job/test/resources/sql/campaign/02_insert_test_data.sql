-- キャンペーン指示データ
INSERT INTO campaign_instructions (
        campaign_instructions_code,
        campaign_instructions_name,
        delete_flg,
        campaign_start_date,
        campaign_end_date,
        baitai_code,
        campaign_priority,
        oneshot_order_limit,
        campaign_quantity_limit,
        applicable_online,
        match_mode
    )
VALUES (
        'CAMP001',
        '春の大感謝祭',
        0,
        '2024-04-01 00:00:00',
        '2024-04-30 23:59:59',
        'ONLINE',
        1,
        NULL,
        NULL,
        true,
        'any'
    ) ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET campaign_instructions_name = EXCLUDED.campaign_instructions_name,
    delete_flg = EXCLUDED.delete_flg,
    campaign_start_date = EXCLUDED.campaign_start_date,
    campaign_end_date = EXCLUDED.campaign_end_date,
    baitai_code = EXCLUDED.baitai_code,
    campaign_priority = EXCLUDED.campaign_priority,
    oneshot_order_limit = EXCLUDED.oneshot_order_limit,
    campaign_quantity_limit = EXCLUDED.campaign_quantity_limit,
    applicable_online = EXCLUDED.applicable_online,
    match_mode = EXCLUDED.match_mode;
INSERT INTO campaign_instructions (
        campaign_instructions_code,
        campaign_instructions_name,
        delete_flg,
        campaign_start_date,
        campaign_end_date,
        baitai_code,
        campaign_priority,
        oneshot_order_limit,
        campaign_quantity_limit,
        applicable_online,
        match_mode
    )
VALUES (
        'CAMP002',
        'シンプルキャンペーン',
        0,
        '2024-04-01 00:00:00',
        '2024-12-31 23:59:59',
        'ONLINE',
        2,
        NULL,
        NULL,
        true,
        'any'
    ) ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET campaign_instructions_name = EXCLUDED.campaign_instructions_name,
    delete_flg = EXCLUDED.delete_flg,
    campaign_start_date = EXCLUDED.campaign_start_date,
    campaign_end_date = EXCLUDED.campaign_end_date,
    baitai_code = EXCLUDED.baitai_code,
    campaign_priority = EXCLUDED.campaign_priority,
    oneshot_order_limit = EXCLUDED.oneshot_order_limit,
    campaign_quantity_limit = EXCLUDED.campaign_quantity_limit,
    applicable_online = EXCLUDED.applicable_online,
    match_mode = EXCLUDED.match_mode;
INSERT INTO campaign_instructions (
        campaign_instructions_code,
        campaign_instructions_name,
        delete_flg,
        campaign_start_date,
        campaign_end_date,
        baitai_code,
        campaign_priority,
        oneshot_order_limit,
        campaign_quantity_limit,
        applicable_online,
        match_mode
    )
VALUES (
        'CAMP003',
        'スペシャル&キャンペーン',
        0,
        '2024-04-01 10:00:00',
        '2024-04-30 18:00:00',
        'ONLINE',
        3,
        NULL,
        NULL,
        true,
        'any'
    ) ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET campaign_instructions_name = EXCLUDED.campaign_instructions_name,
    delete_flg = EXCLUDED.delete_flg,
    campaign_start_date = EXCLUDED.campaign_start_date,
    campaign_end_date = EXCLUDED.campaign_end_date,
    baitai_code = EXCLUDED.baitai_code,
    campaign_priority = EXCLUDED.campaign_priority,
    oneshot_order_limit = EXCLUDED.oneshot_order_limit,
    campaign_quantity_limit = EXCLUDED.campaign_quantity_limit,
    applicable_online = EXCLUDED.applicable_online,
    match_mode = EXCLUDED.match_mode;
-- プロモーション情報データ
INSERT INTO campaign_promotion (
        promotion_id,
        campaign_instructions_code,
        promotion_no,
        promotion_type,
        commodity_code,
        commodity_name,
        discount_rate,
        discount_amount,
        archived_flag,
        searchable_flag,
        refinable_flag,
        prevent_requalifying_flag,
        prorate_across_eligible_items_flag,
        exclusivity
    )
VALUES (
        'CAMP001_1',
        'CAMP001',
        '1',
        '01',
        'PROD001',
        'テスト商品1',
        NULL,
        NULL,
        false,
        false,
        false,
        false,
        false,
        'no'
    ) ON CONFLICT (promotion_id) DO
UPDATE
SET campaign_instructions_code = EXCLUDED.campaign_instructions_code,
    promotion_no = EXCLUDED.promotion_no,
    promotion_type = EXCLUDED.promotion_type,
    commodity_code = EXCLUDED.commodity_code,
    commodity_name = EXCLUDED.commodity_name,
    discount_rate = EXCLUDED.discount_rate,
    discount_amount = EXCLUDED.discount_amount,
    archived_flag = EXCLUDED.archived_flag,
    searchable_flag = EXCLUDED.searchable_flag,
    refinable_flag = EXCLUDED.refinable_flag,
    prevent_requalifying_flag = EXCLUDED.prevent_requalifying_flag,
    prorate_across_eligible_items_flag = EXCLUDED.prorate_across_eligible_items_flag,
    exclusivity = EXCLUDED.exclusivity;
INSERT INTO campaign_promotion (
        promotion_id,
        campaign_instructions_code,
        promotion_no,
        promotion_type,
        commodity_code,
        commodity_name,
        discount_rate,
        discount_amount,
        archived_flag,
        searchable_flag,
        refinable_flag,
        prevent_requalifying_flag,
        prorate_across_eligible_items_flag,
        exclusivity
    )
VALUES (
        'CAMP002_1',
        'CAMP002',
        '1',
        '02',
        'PROD002',
        'テスト商品2',
        NULL,
        1000,
        false,
        false,
        false,
        false,
        false,
        'no'
    ) ON CONFLICT (promotion_id) DO
UPDATE
SET campaign_instructions_code = EXCLUDED.campaign_instructions_code,
    promotion_no = EXCLUDED.promotion_no,
    promotion_type = EXCLUDED.promotion_type,
    commodity_code = EXCLUDED.commodity_code,
    commodity_name = EXCLUDED.commodity_name,
    discount_rate = EXCLUDED.discount_rate,
    discount_amount = EXCLUDED.discount_amount,
    archived_flag = EXCLUDED.archived_flag,
    searchable_flag = EXCLUDED.searchable_flag,
    refinable_flag = EXCLUDED.refinable_flag,
    prevent_requalifying_flag = EXCLUDED.prevent_requalifying_flag,
    prorate_across_eligible_items_flag = EXCLUDED.prorate_across_eligible_items_flag,
    exclusivity = EXCLUDED.exclusivity;
INSERT INTO campaign_promotion (
        promotion_id,
        campaign_instructions_code,
        promotion_no,
        promotion_type,
        commodity_code,
        commodity_name,
        discount_rate,
        discount_amount,
        archived_flag,
        searchable_flag,
        refinable_flag,
        prevent_requalifying_flag,
        prorate_across_eligible_items_flag,
        exclusivity
    )
VALUES (
        'CAMP003_1',
        'CAMP003',
        '1',
        '03',
        'PROD003',
        'テスト商品3',
        10.00,
        NULL,
        false,
        false,
        false,
        false,
        false,
        'no'
    ) ON CONFLICT (promotion_id) DO
UPDATE
SET campaign_instructions_code = EXCLUDED.campaign_instructions_code,
    promotion_no = EXCLUDED.promotion_no,
    promotion_type = EXCLUDED.promotion_type,
    commodity_code = EXCLUDED.commodity_code,
    commodity_name = EXCLUDED.commodity_name,
    discount_rate = EXCLUDED.discount_rate,
    discount_amount = EXCLUDED.discount_amount,
    archived_flag = EXCLUDED.archived_flag,
    searchable_flag = EXCLUDED.searchable_flag,
    refinable_flag = EXCLUDED.refinable_flag,
    prevent_requalifying_flag = EXCLUDED.prevent_requalifying_flag,
    prorate_across_eligible_items_flag = EXCLUDED.prorate_across_eligible_items_flag,
    exclusivity = EXCLUDED.exclusivity;
-- キャンペーン順序データ
INSERT INTO campaign_order (
        campaign_instructions_code,
        display_order
    )
VALUES ('CAMP001', 1) ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET display_order = EXCLUDED.display_order;
INSERT INTO campaign_order (
        campaign_instructions_code,
        display_order
    )
VALUES ('CAMP002', 2) ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET display_order = EXCLUDED.display_order;
INSERT INTO campaign_order (
        campaign_instructions_code,
        display_order
    )
VALUES ('CAMP003', 3) ON CONFLICT (campaign_instructions_code) DO
UPDATE
SET display_order = EXCLUDED.display_order;