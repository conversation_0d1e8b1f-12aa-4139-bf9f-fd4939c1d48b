#!/usr/bin/env python
# -*- coding: utf-8 -*-

import html
import sys
import pytest
from datetime import datetime, timedelta
from xml.etree import ElementTree as ET

from source.glue_job_db_to_file import GlueJobDbToFile, main
from test.test_utils import GlueTestUtils, get_test_file_path


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    target_name = "JN_PR003-DF01_001_EC_test.xml"
    # より古いタイムスタンプを設定（差分基準時刻2025-02-21より前の日付）
    one_week_ago = datetime(
        2025, 2, 1, 0, 0, 0
    )  # 2025-02-01 00:00:00 (差分基準時刻より十分に古い)
    old_timestamp = one_week_ago.strftime("%Y-%m-%d %H:%M:%S")

    insert_sync_sql = """
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, :sync_timestamp,
         :user, CURRENT_TIMESTAMP, :user, CURRENT_TIMESTAMP, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = :sync_timestamp,
        d_updated_user = :user,
        d_updated_datetime = CURRENT_TIMESTAMP,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_PR003-DF01_001",
        "file_name": target_name,
        "sync_timestamp": old_timestamp,
        "user": "test_user",
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


class TestPR003:
    """商品マスタXML変換シナリオテスト"""

    @pytest.fixture
    def setup_product_tables(self, db_connector):
        """テストデータのセットアップと同期タイムスタンプの初期化"""
        try:
            # 同期タイムスタンプテーブルのデータ更新のみを行う
            # テーブル作成は行わない（権限問題を回避するため）
            setup_sync_timestamp_table(db_connector)
            yield
        except Exception as e:
            # エラー時はロールバック
            db_connector.rollback()
            raise e

    def test_main_db2xml_normal(
        self, monkeypatch, mock_aws, db_connector, setup_product_tables, capsys
    ):
        """main関数の正常系テスト（DBからXMLファイル出力）"""
        try:
            # 出力ファイルの設定
            output_filename = "JN_PR003-DF01_001_EC_test.xml"
            output_dir = "test/output"
            output_path = f"{output_dir}/{output_filename}"

            # sys.argvのモック
            test_args = [
                "glue_job_db_to_file.py",
                "--secret_name",
                "test-db-secret",
                "--execute_query",
                "etl_PR003-DF01_001",
                "--output_file_dir",
                output_dir,
                "--file_name",
                output_filename,
                "--file_id",
                output_filename,
                "--file_type",
                "xml",
                "--diff_base_timestamp_query",
                "sql_PR003-DF01_timestamp_001",
                "--jobnet_id",
                "JN_PR003-DF01_001",
            ]
            monkeypatch.setattr(sys, "argv", test_args)

            # sys.exitの呼び出しを無視するようにモック
            with patch("sys.exit") as mock_exit:
                # テスト実行
                main()
                # sys.exitが呼ばれたかを確認（警告として表示）
                if mock_exit.called:
                    print(f"警告: sys.exitが呼ばれました: {mock_exit.call_args}")

            # 標準出力をキャプチャ
            captured_out, _ = capsys.readouterr()
            # キャプチャした出力を再度標準出力に書き戻す
            sys.stdout.write(captured_out)

            # 出力ファイルの検証
            s3_client = mock_aws["s3"]
            response = s3_client.get_object(Bucket="test-bucket", Key=output_path)
            content = response["Body"].read().decode("utf-8")

            # XML内容の検証
            tree = ET.ElementTree(ET.fromstring(content))
            root = tree.getroot()

            # 基本構造の検証
            assert root.tag == "catalog", "ルート要素名が不正"
            assert len(root.findall("product")) > 0, "product要素が存在しません"

            # 各商品タイプの検証
            self._verify_set_products(root)
            self._verify_regular_products(root)
            self._verify_normal_products(root)

            # ログ検証
            assert (
                "[INFO][JN_PR003-DF01_001][I_job_db_to_file_001]ジョブを開始しました。"
                in captured_out
            ), "開始ログが不正"
            assert (
                "[INFO][JN_PR003-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。"
                in captured_out
            ), "終了ログが不正"

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

    def _verify_set_products(self, root):
        """セット商品の検証（set_product_flg = '1'）"""
        # カスタム属性でセット商品を検索
        set_products = []
        for product in root.findall("product"):
            for custom_attr in product.findall("./custom-attributes/custom-attribute"):
                if (
                    custom_attr.get("attribute-id") == "txSetProductFlag"
                    and custom_attr.text == "1"
                ):
                    set_products.append(product)

        if len(set_products) == 0:
            print("警告: セット商品が見つかりません")
            return

        print(f"セット商品数: {len(set_products)}")

        # セット商品のバンドル情報検証
        for set_product in set_products:
            product_id = set_product.get("product-id")
            bundled_products = set_product.find("bundled-products")

            # bundled-products要素の存在を確認
            if bundled_products is not None:
                bundled_items = bundled_products.findall("bundled-product")
                print(f"セット商品 {product_id} のバンドル商品数: {len(bundled_items)}")

                # 少なくとも1つのバンドル商品が必要
                assert (
                    len(bundled_items) > 0
                ), f"セット商品 {product_id} にバンドル商品がありません"

                # バンドル商品の属性と子要素を検証
                for item in bundled_items:
                    assert (
                        item.get("product-id") is not None
                    ), "バンドル商品にproduct-id属性がありません"
                    quantity = item.find("quantity")
                    assert (
                        quantity is not None
                    ), "バンドル商品にquantity要素がありません"
                    # quantityは数値型であることを確認
                    try:
                        int(quantity.text)
                    except (ValueError, TypeError):
                        assert False, f"quantity値が数値ではありません: {quantity.text}"
            else:
                print(
                    f"警告: セット商品 {product_id} にbundled-products要素がありません"
                )

    def _verify_regular_products(self, root):
        """定期商品の検証（product_type = '11'）"""
        # カスタム属性で定期商品を検索
        regular_products = []
        for product in root.findall("product"):
            for custom_attr in product.findall("./custom-attributes/custom-attribute"):
                if (
                    custom_attr.get("attribute-id") == "txProductComp"
                    and custom_attr.text == "11"
                ):
                    regular_products.append(product)

        if len(regular_products) == 0:
            print("警告: 定期商品が見つかりません")
            return

        print(f"定期商品数: {len(regular_products)}")

        # 定期商品のバンドル情報検証
        for regular_product in regular_products:
            product_id = regular_product.get("product-id")
            bundled_products = regular_product.find("bundled-products")

            # bundled-products要素の存在を確認
            if bundled_products is not None:
                bundled_items = bundled_products.findall("bundled-product")
                print(f"定期商品 {product_id} のバンドル商品数: {len(bundled_items)}")

                # バンドル商品の属性と子要素を検証
                for item in bundled_items:
                    assert (
                        item.get("product-id") is not None
                    ), "バンドル商品にproduct-id属性がありません"
                    quantity = item.find("quantity")
                    assert (
                        quantity is not None
                    ), "バンドル商品にquantity要素がありません"
                    # quantityは数値型であることを確認
                    try:
                        int(quantity.text)
                    except (ValueError, TypeError):
                        assert False, f"quantity値が数値ではありません: {quantity.text}"
            else:
                print(f"警告: 定期商品 {product_id} にbundled-products要素がありません")

    def _verify_normal_products(self, root):
        """通常商品の検証と共通検証項目"""
        # 基本的なXML構造と要素の検証
        for product in root.findall("product"):
            product_id = product.get("product-id")
            assert product_id is not None, "product-id属性がありません"

            # 必須の子要素を確認
            min_order = product.find("min-order-quantity")
            assert (
                min_order is not None
            ), f"商品 {product_id} にmin-order-quantity要素がありません"

            step_qty = product.find("step-quantity")
            assert (
                step_qty is not None
            ), f"商品 {product_id} にstep-quantity要素がありません"
            # step-quantityは数値型であることを確認
            try:
                int(step_qty.text)
            except (ValueError, TypeError):
                assert False, f"step-quantity値が数値ではありません: {step_qty.text}"

            # カスタム属性の検証
            custom_attrs = product.find("custom-attributes")
            assert (
                custom_attrs is not None
            ), f"商品 {product_id} にcustom-attributes要素がありません"
            assert (
                len(custom_attrs.findall("custom-attribute")) > 0
            ), f"商品 {product_id} のカスタム属性が空です"

            # tax-class-idの検証（存在する場合）
            tax_class = product.find("tax-class-id")
            if tax_class is not None:
                # tax-class-idは数値型であることを確認
                try:
                    int(tax_class.text)
                except (ValueError, TypeError):
                    assert (
                        False
                    ), f"tax-class-id値が数値ではありません: {tax_class.text}"


# patchをインポート
from unittest.mock import patch
