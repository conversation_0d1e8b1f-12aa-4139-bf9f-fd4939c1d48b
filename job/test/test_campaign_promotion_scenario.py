import html
import sys
import pytest
from datetime import datetime, timedelta
from xml.etree import ElementTree as ET

from source.glue_job_db_to_file import GlueJobDbToFile, main
from test.test_utils import GlueTestUtils, get_test_file_path


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    target_name = "JN_CP001-DF01_001_EC_test.xml"
    # 現在時刻から3時間前の時刻を設定（テストデータの作成時刻より前）
    three_hours_ago = datetime.now() - timedelta(hours=3)
    old_timestamp = three_hours_ago.strftime("%Y-%m-%d %H:%M:%S")
    insert_sync_sql = """
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, :sync_timestamp,
         :user, CURRENT_TIMESTAMP, :user, CURRENT_TIMESTAMP, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = :sync_timestamp,
        d_updated_user = :user,
        d_updated_datetime = CURRENT_TIMESTAMP,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_CP001-DF01_001",
        "file_name": target_name,
        "sync_timestamp": old_timestamp,
        "user": "test_user",
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


class TestCampaignPromotionScenario:
    """キャンペーン・プロモーションの変換シナリオテスト"""

    @pytest.fixture
    def setup_campaign_tables(self, db_connector):
        """テストテーブルとデータのセットアップ"""
        try:
            # トランザクション開始
            db_connector.begin()

            # テスト用テーブルのクリーンアップ
            GlueTestUtils.cleanup_test_resources(db_connector)

            # 既存のテーブルをクリーンアップ
            try:
                cleanup_sql_path = get_test_file_path("sql/campaign/99_cleanup.sql")
                with open(cleanup_sql_path, "r", encoding="utf-8") as f:
                    cleanup_sql = f.read()
                db_connector.exec(cleanup_sql)
            except Exception:
                # クリーンアップ失敗は無視（テーブルが存在しない場合など）
                pass
            db_connector.commit()  # クリーンアップ結果をコミット

            # SQLファイルの実行
            for filename in ["01_create_tables.sql", "02_insert_test_data.sql"]:
                sql_path = get_test_file_path(f"sql/campaign/{filename}")
                with open(sql_path, "r", encoding="utf-8") as f:
                    sql = f.read()
                    db_connector.exec(sql)

            # created_datetime, updated_datetimeの更新
            update_timestamp_sql = """
            UPDATE campaign_instructions
            SET created_datetime = :created_dt,
                updated_datetime = :updated_dt;

            UPDATE campaign_promotion
            SET created_datetime = :created_dt,
                updated_datetime = :updated_dt;

            UPDATE campaign_order
            SET created_datetime = :created_dt,
                updated_datetime = :updated_dt;

            UPDATE product_linkage
            SET created_datetime = :created_dt,
                updated_datetime = :updated_dt;
            """

            # テスト用のタイムスタンプを設定
            now = datetime.now()
            two_hours_ago = now - timedelta(hours=2)  # データ作成日時
            one_hour_ago = now - timedelta(hours=1)  # データ更新日時
            # 更新日時を1時間前に設定することで、差分基準時刻（現在時刻）よりも前になり、
            # かつ前回同期済時刻（2024-01-01 00:00:00）よりも後になるようにする
            params = {"created_dt": two_hours_ago, "updated_dt": one_hour_ago}

            # タイムスタンプ更新SQLの実行
            db_connector.exec(update_timestamp_sql, params)

            # コミット
            db_connector.commit()

            # 同期タイムスタンプテーブルのセットアップ
            GlueTestUtils.setup_test_db_tables(db_connector)
            setup_sync_timestamp_table(db_connector)

            yield

        except Exception as e:
            # エラー時はロールバック
            db_connector.rollback()
            raise e

        finally:
            # クリーンアップ
            try:
                cleanup_sql_path = get_test_file_path("sql/campaign/99_cleanup.sql")
                with open(cleanup_sql_path, "r", encoding="utf-8") as f:
                    cleanup_sql = f.read()

                # トランザクション開始
                try:
                    db_connector.begin()
                except Exception:
                    # トランザクションがすでに開始している場合は無視
                    pass

                # クリーンアップSQL実行
                db_connector.exec(cleanup_sql)

                # コミット
                db_connector.commit()

            except Exception:
                # エラー時はロールバック
                try:
                    db_connector.rollback()
                except Exception:
                    pass

            # テスト用テーブルのクリーンアップ
            GlueTestUtils.cleanup_test_resources(db_connector)

    def test_main_db2xml_normal(
        self, monkeypatch, mock_aws, db_connector, setup_campaign_tables, capsys
    ):
        """main関数の正常系テスト（DBからXMLファイル出力）
        テストケース番号：DB2XML-018,DB2XML-019,DB2XML-020
        """
        try:
            # sys.argvのモック
            test_args = [
                "glue_job_db_to_file.py",
                "--secret_name",
                "test-db-secret",
                "--execute_query",
                "etl_db2xml_txCampaignPromotions",
                "--output_file_dir",
                "test/output",
                "--file_name",
                "JN_CP001-DF01_001_EC_test.xml",  # ファイル名
                "--file_id",
                "JN_CP001-DF01_001_EC_test.xml",  # ファイルID（同期タイムスタンプテーブル用）
                "--file_type",
                "xml",
                "--diff_base_timestamp_query",
                "campaign/test_campaign_timestamp_query",  # キャンペーン用の差分基準時刻取得クエリ
                "--jobnet_id",
                "JN_CP001-DF01_001",
            ]
            monkeypatch.setattr(sys, "argv", test_args)

            # テスト実行
            main()
            captured_out, _ = capsys.readouterr()
            # キャプチャした出力を再度標準出力に書き戻す
            sys.stdout.write(captured_out)

            # 出力ファイルの検証
            s3_client = mock_aws["s3"]
            response = s3_client.get_object(
                Bucket="test-bucket", Key="test/output/JN_CP001-DF01_001_EC_test.xml"
            )
            content = response["Body"].read().decode("utf-8")

            # XML内容の検証
            tree = ET.ElementTree(ET.fromstring(content))
            root = tree.getroot()

            # 基本構造の検証
            assert root.tag == "promotions", "ルート要素名が不正"
            assert len(root.findall(".//campaign")) == 3, "キャンペーン数が不正"

            # 各キャンペーンの検証
            self._verify_normal_campaign(root)
            self._verify_minimal_campaign(root)
            self._verify_special_campaign(root)

            # ログ検証
            assert (
                "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。"
                in captured_out
            ), "開始ログが不正"
            assert (
                "[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。"
                in captured_out
            ), "終了ログが不正"

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

        finally:
            # クリーンアップ処理
            GlueTestUtils.cleanup_test_resources(db_connector)

    def test_db2xml_error_case(self, mock_aws, db_connector, setup_campaign_tables):
        """DB2XMLConverterのエラーケーステスト
        テストケース番号：DB2XML-008
        """
        try:
            # 不正なパラメータでジョブを実行
            params = {
                "secret_name": "test-db-secret",
                "execute_query": "non_existent_query",  # 存在しないクエリ
                "output_file_dir": "test/output",
                "file_name": "error_test.xml",
                "file_id": "error_test.xml",  # ファイルID追加
                "diff_base_timestamp_query": "campaign/test_campaign_timestamp_query",  # キャンペーン用の差分基準時刻取得クエリ
                "file_type": "xml",
                "jobnet_id": "TEST_JOB",
            }

            # エラーが発生することを確認
            with pytest.raises(FileNotFoundError) as exc_info:
                job = GlueJobDbToFile(params["jobnet_id"])
                job.execute(params)

            # エラーメッセージの検証
            assert "No such file or directory" in str(
                exc_info.value
            ), "エラーメッセージが不正"

        finally:
            # クリーンアップ処理
            GlueTestUtils.cleanup_test_resources(db_connector)

    def _verify_normal_campaign(self, root):
        """通常キャンペーン（CAMP001）の検証"""
        camp001 = root.find(".//campaign[@campaign-id='CAMP001']")
        assert camp001 is not None, "CAMP001が存在しない"

        # 基本情報の検証
        description = camp001.find("description")
        assert (
            description is not None and description.text == "春の大感謝祭"
        ), "キャンペーン名が不正"

        enabled_flag = camp001.find("enabled-flag")
        assert (
            enabled_flag is not None and enabled_flag.text == "true"
        ), "enabled_flagが不正"

        # 日付フォーマット検証
        start_date = camp001.find("start-date")
        assert (
            start_date is not None and start_date.text == "2024-04-01T00:00:00.000+0900"
        ), "開始日が不正"

        end_date = camp001.find("end-date")
        assert (
            end_date is not None and end_date.text == "2024-04-30T23:59:59.000+0900"
        ), "終了日が不正"

        # customer-groups検証
        customer_groups = camp001.find("customer-groups")
        assert customer_groups is not None, "customer-groups要素が存在しない"
        assert customer_groups.get("match-mode") == "any", "match-modeが不正"

        # プロモーション関連の基本検証
        promotions = root.findall(".//promotion[@promotion-id='CAMP001_1']")
        assert len(promotions) == 1, "CAMP001_1のプロモーション数が不正"

        for promo in promotions:
            # 子要素として追加された共通プロモーション属性の検証
            archived_flag = promo.find("archived-flag")
            assert (
                archived_flag is not None and archived_flag.text == "false"
            ), "archived-flagが不正"

            searchable_flag = promo.find("searchable-flag")
            assert (
                searchable_flag is not None and searchable_flag.text == "false"
            ), "searchable-flagが不正"

            refinable_flag = promo.find("refinable-flag")
            assert (
                refinable_flag is not None and refinable_flag.text == "false"
            ), "refinable-flagが不正"

            prevent_requalifying_flag = promo.find("prevent-requalifying-flag")
            assert (
                prevent_requalifying_flag is not None
                and prevent_requalifying_flag.text == "false"
            ), "prevent-requalifying-flagが不正"

            prorate_across_eligible_items_flag = promo.find(
                "prorate-across-eligible-items-flag"
            )
            assert (
                prorate_across_eligible_items_flag is not None
                and prorate_across_eligible_items_flag.text == "false"
            ), "prorate-across-eligible-items-flagが不正"

            exclusivity = promo.find("exclusivity")
            assert (
                exclusivity is not None and exclusivity.text == "no"
            ), "exclusivityが不正"

    def _verify_minimal_campaign(self, root):
        """最小項目キャンペーン（CAMP002）の検証"""
        camp002 = root.find(".//campaign[@campaign-id='CAMP002']")
        assert camp002 is not None, "CAMP002が存在しない"

        # 必須項目の検証
        description = camp002.find("description")
        assert (
            description is not None and description.text == "シンプルキャンペーン"
        ), "キャンペーン名が不正"

        enabled_flag = camp002.find("enabled-flag")
        assert (
            enabled_flag is not None and enabled_flag.text == "true"
        ), "enabled_flagが不正"

        # 日付フォーマット検証
        start_date = camp002.find("start-date")
        assert (
            start_date is not None and start_date.text == "2024-04-01T00:00:00.000+0900"
        ), "開始日が不正"

        end_date = camp002.find("end-date")
        assert (
            end_date is not None and end_date.text == "2024-12-31T23:59:59.000+0900"
        ), "終了日が不正"

        # customer-groups検証
        customer_groups = camp002.find("customer-groups")
        assert customer_groups is not None, "customer-groups要素が存在しない"
        assert customer_groups.get("match-mode") == "any", "match-modeが不正"

        # プロモーション関連の基本検証
        promotions = root.findall(".//promotion[@promotion-id='CAMP002_1']")
        assert len(promotions) == 1, "CAMP002_1のプロモーション数が不正"

        for promo in promotions:
            # 子要素として追加された共通プロモーション属性の検証
            archived_flag = promo.find("archived-flag")
            assert (
                archived_flag is not None and archived_flag.text == "false"
            ), "archived-flagが不正"

            searchable_flag = promo.find("searchable-flag")
            assert (
                searchable_flag is not None and searchable_flag.text == "false"
            ), "searchable-flagが不正"

            refinable_flag = promo.find("refinable-flag")
            assert (
                refinable_flag is not None and refinable_flag.text == "false"
            ), "refinable-flagが不正"

            prevent_requalifying_flag = promo.find("prevent-requalifying-flag")
            assert (
                prevent_requalifying_flag is not None
                and prevent_requalifying_flag.text == "false"
            ), "prevent-requalifying-flagが不正"

            prorate_across_eligible_items_flag = promo.find(
                "prorate-across-eligible-items-flag"
            )
            assert (
                prorate_across_eligible_items_flag is not None
                and prorate_across_eligible_items_flag.text == "false"
            ), "prorate-across-eligible-items-flagが不正"

            exclusivity = promo.find("exclusivity")
            assert (
                exclusivity is not None and exclusivity.text == "no"
            ), "exclusivityが不正"

    def _verify_special_campaign(self, root):
        """特殊文字キャンペーン（CAMP003）の検証"""
        camp003 = root.find(".//campaign[@campaign-id='CAMP003']")
        assert camp003 is not None, "CAMP003が存在しない"

        # 特殊文字の検証
        description = camp003.find("description")
        assert description is not None, "descriptionが存在しない"
        decoded_text = html.unescape(description.text)
        assert (
            decoded_text == "スペシャル&キャンペーン"
        ), f"特殊文字の変換が不正: {decoded_text}"

        enabled_flag = camp003.find("enabled-flag")
        assert (
            enabled_flag is not None and enabled_flag.text == "true"
        ), "enabled_flagが不正"

        # 日付フォーマット検証
        start_date = camp003.find("start-date")
        assert (
            start_date is not None and start_date.text == "2024-04-01T10:00:00.000+0900"
        ), "開始日が不正"

        end_date = camp003.find("end-date")
        assert (
            end_date is not None and end_date.text == "2024-04-30T18:00:00.000+0900"
        ), "終了日が不正"

        # customer-groups検証
        customer_groups = camp003.find("customer-groups")
        assert customer_groups is not None, "customer-groups要素が存在しない"
        assert customer_groups.get("match-mode") == "any", "match-modeが不正"

        # プロモーション関連の基本検証
        promotions = root.findall(".//promotion[@promotion-id='CAMP003_1']")
        assert len(promotions) == 1, "CAMP003_1のプロモーション数が不正"

        for promo in promotions:
            # 子要素として追加された共通プロモーション属性の検証
            archived_flag = promo.find("archived-flag")
            assert (
                archived_flag is not None and archived_flag.text == "false"
            ), "archived-flagが不正"

            searchable_flag = promo.find("searchable-flag")
            assert (
                searchable_flag is not None and searchable_flag.text == "false"
            ), "searchable-flagが不正"

            refinable_flag = promo.find("refinable-flag")
            assert (
                refinable_flag is not None and refinable_flag.text == "false"
            ), "refinable-flagが不正"

            prevent_requalifying_flag = promo.find("prevent-requalifying-flag")
            assert (
                prevent_requalifying_flag is not None
                and prevent_requalifying_flag.text == "false"
            ), "prevent-requalifying-flagが不正"

            prorate_across_eligible_items_flag = promo.find(
                "prorate-across-eligible-items-flag"
            )
            assert (
                prorate_across_eligible_items_flag is not None
                and prorate_across_eligible_items_flag.text == "false"
            ), "prorate-across-eligible-items-flagが不正"

            exclusivity = promo.find("exclusivity")
            assert (
                exclusivity is not None and exclusivity.text == "no"
            ), "exclusivityが不正"
