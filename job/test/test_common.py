import os
import json
import boto3
import pytest
import sys
from unittest.mock import patch, MagicMock
from source.common import (
    backup_input_file,
    create_s3_client,
    delete_input_file,
    find_s3_prefix,
    get_s3_client,
    initialize_env,
    get_job_params,
    move_file_to_error_dir,
    put_s3_file,
    retry_function,
    get_s3_file,
)
from source.glue_logger import <PERSON><PERSON><PERSON>og<PERSON>
from botocore.exceptions import ClientError


# initialize_env関数が正常に環境変数を設定できることをテスト
@patch("boto3.client")
def test_initialize_env_success(mock_boto3_client):
    mock_ssm_client = MagicMock()
    mock_boto3_client.return_value = mock_ssm_client
    mock_ssm_client.get_parameter.return_value = {
        "Parameter": {
            "Value": json.dumps(
                {
                    "process": {"TZ": "Asia/Tokyo"},
                    "aws": {"S3_BUCKET_NAME": "my-bucket-name"},
                }
            )
        }
    }

    initialize_env()
    assert os.environ.get("TZ") == "Asia/Tokyo"
    assert os.environ.get("S3_BUCKET_NAME") == "my-bucket-name"


# initialize_env関数がJSONデコードエラーを正しくハンドルできることをテスト
@patch("boto3.client")
def test_initialize_env_json_decode_error(mock_boto3_client):
    mock_ssm_client = MagicMock()
    mock_boto3_client.return_value = mock_ssm_client
    mock_ssm_client.get_parameter.return_value = {
        "Parameter": {"Value": "invalid json"}
    }
    with pytest.raises(json.JSONDecodeError):
        initialize_env()


# initialize_env関数が例外を正しくハンドルできることをテスト
@patch("boto3.client")
def test_initialize_env_exception(mock_boto3_client):
    mock_ssm_client = MagicMock()
    mock_boto3_client.return_value = mock_ssm_client
    mock_ssm_client.get_parameter.side_effect = Exception("Test Exception")
    with pytest.raises(Exception):
        initialize_env()


# get_job_params関数が正常にジョブパラメータを取得できることをテスト
def test_get_job_params_success():
    sys.argv = [
        "script_name",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "scriptLocation",
        "s3://aws-glue-assets-730335353052-ap-northeast-1/scripts/env_varible_test.py",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "TempDir",
        "s3://aws-glue-assets-730335353052-ap-northeast-1/temporary/",
    ]
    params = get_job_params()
    assert params == {
        "TZ": "Asia/Tokyo",
        "enable-job-insights": "false",
        "enable-glue-datacatalog": "true",
        "library-set": "analytics",
        "scriptLocation": "s3://aws-glue-assets-730335353052-ap-northeast-1/scripts/env_varible_test.py",
        "python-version": "3.9",
        "job-language": "python",
        "TempDir": "s3://aws-glue-assets-730335353052-ap-northeast-1/temporary/",
    }


# get_job_params関数が無効なフォーマットの引数を正しくハンドルできることをテスト
def test_get_job_params_invalid_format():
    sys.argv = ["script_name", "TZ", "Asia/Tokyo", "invalid_format"]
    with pytest.raises(ValueError):
        get_job_params()


# retry_function関数が正常に関数をリトライして成功することをテスト
def test_retry_function_success():
    mock_func = MagicMock(return_value="success")
    logger = GlueLogger("test_jobnet_id")
    result = retry_function(
        mock_func, logger, "test_process", "I_test_001", "E_test_001"
    )
    assert result == "success"
    mock_func.assert_called_once()


# retry_function関数が例外をハンドルしてリトライ後に成功することをテスト
def test_retry_function_retry_success():
    mock_func = MagicMock(side_effect=[Exception("Test Exception"), "success"])
    logger = GlueLogger("test_jobnet_id")
    result = retry_function(
        mock_func,
        logger,
        "test_process",
        "I_job_db_to_file_002",
        "E_job_db_to_file_002",
        retry_limit=2,
    )
    assert result == "success"
    assert mock_func.call_count == 2


# retry_function関数がすべてのリトライで失敗することをテスト
def test_retry_function_all_retries_fail():
    mock_func = MagicMock(side_effect=Exception("Test Exception"))
    logger = GlueLogger("test_jobnet_id")
    with pytest.raises(Exception):
        retry_function(
            mock_func,
            logger,
            "test_process",
            "I_job_db_to_file_002",
            "E_job_db_to_file_002",
            retry_limit=2,
        )
    assert mock_func.call_count == 2


# retry_function関数が引数を正しくハンドルできることをテスト
def test_retry_function_with_args():
    mock_func = MagicMock(return_value="success")
    logger = GlueLogger("test_jobnet_id")
    result = retry_function(
        mock_func,
        logger,
        "test_process",
        "I_test_001",
        "E_test_001",
        args=("arg1", "arg2"),
    )
    assert result == "success"
    mock_func.assert_called_once_with("arg1", "arg2")


# retry_function関数がロガーを使用してログを記録できることをテスト
def test_retry_function_with_logger():
    mock_func = MagicMock(side_effect=Exception("Test Exception"))
    logger = GlueLogger("test_jobnet_id")
    with pytest.raises(Exception):
        retry_function(
            mock_func,
            logger,
            "test_process",
            "I_job_db_to_file_002",
            "E_job_db_to_file_002",
            retry_limit=2,
        )
    assert mock_func.call_count == 2


# retry_function関数が例外をハンドルしてNoneを返すことをテスト
def test_retry_function_returns_none():
    mock_func = MagicMock(side_effect=Exception("Test Exception"))
    logger = GlueLogger("test_jobnet_id")
    with pytest.raises(Exception):
        retry_function(
            mock_func,
            logger,
            "test_process",
            "I_job_db_to_file_002",
            "E_job_db_to_file_002",
            retry_limit=2,
        )
    assert mock_func.call_count == 2


@patch("boto3.client")
def test_create_s3_client_default_parameters(mock_boto3_client):
    mock_s3_client = MagicMock()
    mock_boto3_client.return_value = mock_s3_client

    result = create_s3_client()

    mock_boto3_client.assert_called_once()
    assert result == mock_s3_client


@patch("boto3.client")
def test_create_s3_client_invalid_mode(mock_boto3_client):
    with pytest.raises(ValueError) as exc_info:
        create_s3_client(mode="invalid_mode")
    assert (
        str(exc_info.value)
        == "無効なモードです。'legacy'、'standard'、または'adaptive'を指定してください。"
    )
    mock_boto3_client.assert_not_called()


@patch("os.environ", {})
@patch("source.common.create_s3_client")
def test_get_s3_client_default_env(mock_create_s3_client):
    mock_s3_client = MagicMock()
    mock_create_s3_client.return_value = mock_s3_client

    result = get_s3_client()

    mock_create_s3_client.assert_called_once_with(
        region_name="ap-northeast-1", max_attempts=3, mode="standard"
    )
    assert result == mock_s3_client


@patch(
    "os.environ",
    {"REGION_NAME": "us-west-2", "S3_RETRY_LIMIT": "5", "S3_RETRY_MODE": "adaptive"},
)
@patch("source.common.create_s3_client")
def test_get_s3_client_custom_env(mock_create_s3_client):
    mock_s3_client = MagicMock()
    mock_create_s3_client.return_value = mock_s3_client

    result = get_s3_client()

    mock_create_s3_client.assert_called_once_with(
        region_name="us-west-2", max_attempts=5, mode="adaptive"
    )
    assert result == mock_s3_client


@patch("source.common.get_s3_client")
def test_get_s3_file_success(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client

    bucket = "test-bucket"
    key = "test-key"
    local_path = "/tmp/test-file"

    get_s3_file(bucket, key, local_path)

    mock_s3_client.download_file.assert_called_once_with(bucket, key, local_path)


@patch("source.common.get_s3_client")
def test_get_s3_file_client_error(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.download_file.side_effect = ClientError(
        {"Error": {"Code": "TestException", "Message": "Test error message"}},
        "download_file",
    )

    bucket = "test-bucket"
    key = "test-key"
    local_path = "/tmp/test-file"

    with pytest.raises(ClientError):
        get_s3_file(bucket, key, local_path)

    mock_s3_client.download_file.assert_called_once_with(bucket, key, local_path)


@patch("source.common.get_s3_client")
@patch("logging.error")
def test_get_s3_file_logs_error_on_failure(mock_logging_error, mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.download_file.side_effect = ClientError(
        {"Error": {"Code": "TestException", "Message": "Test error message"}},
        "download_file",
    )

    bucket = "test-bucket"
    key = "test-key"
    local_path = "/tmp/test-file"

    with pytest.raises(ClientError):
        get_s3_file(bucket, key, local_path)

    mock_logging_error.assert_called_once_with(
        "Failed to download file from S3: An error occurred (TestException) when calling the download_file operation: Test error message"
    )
    mock_s3_client.download_file.assert_called_once_with(bucket, key, local_path)


def test_get_s3_file_with_real_s3_client():
    # Assume AWS credentials are configured
    s3_client = boto3.client("s3")

    # Test bucket and file
    bucket = "s3-dev-dlpf-if-886436956581"
    key = "o.iketsu/test-file.txt"
    local_path = "/tmp/test-file.txt"

    # Create a test file in S3
    s3_client.put_object(Bucket=bucket, Key=key, Body=b"Test content")

    try:
        # Call the function to test
        get_s3_file(bucket, key, local_path)

        # Check if the file was downloaded successfully
        assert os.path.exists(local_path)

        # Verify the content of the downloaded file
        with open(local_path, "rb") as f:
            content = f.read()
        assert content == b"Test content"

    except ClientError as e:
        pytest.fail(f"Failed to download file from S3: {str(e)}")

    finally:
        # Clean up: delete the test file from S3 and local
        s3_client.delete_object(Bucket=bucket, Key=key)
        if os.path.exists(local_path):
            os.remove(local_path)


def test_put_s3_file_with_real_s3_client():
    # Assume AWS credentials are configured
    s3_client = boto3.client("s3")

    # Test bucket and file
    bucket = "s3-dev-dlpf-if-886436956581"
    key = "o.iketsu/test-file.txt"
    local_path = "/tmp/test-file.txt"

    # Create a test file locally
    with open(local_path, "w") as f:
        f.write("Test content")

    try:
        # Call the function to test
        put_s3_file(local_path, bucket, key)

        # Verify the file was uploaded successfully
        response = s3_client.get_object(Bucket=bucket, Key=key)
        content = response["Body"].read().decode("utf-8")
        assert content == "Test content"

    except ClientError as e:
        pytest.fail(f"Failed to upload file to S3: {str(e)}")

    finally:
        # Clean up: delete the test file from S3 and local
        s3_client.delete_object(Bucket=bucket, Key=key)
        if os.path.exists(local_path):
            os.remove(local_path)


@patch("source.common.get_s3_client")
@patch("logging.error")
def test_put_s3_file_client_error(mock_logging_error, mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.upload_file.side_effect = ClientError(
        {"Error": {"Code": "TestException", "Message": "Test error message"}},
        "upload_file",
    )

    local_path = "/tmp/test-file"
    bucket = "test-bucket"
    key = "test-key"

    with pytest.raises(ClientError):
        put_s3_file(local_path, bucket, key)

    mock_logging_error.assert_called_once_with(
        "Failed to upload file to S3: An error occurred (TestException) when calling the upload_file operation: Test error message"
    )
    mock_s3_client.upload_file.assert_called_once_with(local_path, bucket, key)


def test_backup_input_file_with_real_s3_client():
    # Assume AWS credentials are configured
    s3_client = boto3.client("s3")

    # Test bucket and files
    bucket = "s3-dev-dlpf-if-886436956581"
    source_key = "o.iketsu/test-source-file.txt"
    backup_key = "o.iketsu/test-backup-file.txt"

    # Create a test file in S3
    s3_client.put_object(Bucket=bucket, Key=source_key, Body=b"Test content")

    try:
        # Call the function to test
        backup_input_file(bucket, source_key, backup_key)

        # Check if the backup file was created successfully
        response = s3_client.get_object(Bucket=bucket, Key=backup_key)
        content = response["Body"].read()
        assert content == b"Test content"

    except ClientError as e:
        pytest.fail(f"Failed to backup file in S3: {str(e)}")

    finally:
        # Clean up: delete test files
        s3_client.delete_object(Bucket=bucket, Key=source_key)
        s3_client.delete_object(Bucket=bucket, Key=backup_key)


@patch("source.common.get_s3_client")
def test_backup_input_file_source_not_exist(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.copy_object.side_effect = ClientError(
        {
            "Error": {
                "Code": "NoSuchKey",
                "Message": "The specified key does not exist.",
            }
        },
        "CopyObject",
    )

    bucket = "test-bucket"
    source_key = "non-existent-key"
    backup_key = "backup-key"

    with pytest.raises(ClientError):
        backup_input_file(bucket, source_key, backup_key)

    mock_s3_client.copy_object.assert_called_once_with(
        CopySource={"Bucket": bucket, "Key": source_key}, Bucket=bucket, Key=backup_key
    )


@patch("source.common.get_s3_client")
def test_backup_input_file_destination_bucket_not_exist(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.copy_object.side_effect = ClientError(
        {
            "Error": {
                "Code": "NoSuchBucket",
                "Message": "The specified bucket does not exist",
            }
        },
        "CopyObject",
    )

    bucket = "non-existent-bucket"
    source_key = "source/file.txt"
    backup_key = "backup/file.txt"

    with pytest.raises(ClientError):
        backup_input_file(bucket, source_key, backup_key)

    mock_s3_client.copy_object.assert_called_once_with(
        CopySource={"Bucket": bucket, "Key": source_key}, Bucket=bucket, Key=backup_key
    )


@patch("source.common.get_s3_client")
@patch("logging.error")
def test_delete_input_file_client_error(mock_logging_error, mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.delete_object.side_effect = ClientError(
        {"Error": {"Code": "TestException", "Message": "Test error message"}},
        "delete_object",
    )

    bucket = "test-bucket"
    key = "test-key"

    with pytest.raises(ClientError):
        delete_input_file(bucket, key)

    mock_logging_error.assert_called_once_with(
        "Failed to delete input file: An error occurred (TestException) when calling the delete_object operation: Test error message"
    )
    mock_s3_client.delete_object.assert_called_once_with(Bucket=bucket, Key=key)


def test_delete_input_file_with_real_s3_client():
    # Assume AWS credentials are configured
    s3_client = boto3.client("s3")

    # Test bucket and file
    bucket = "s3-dev-dlpf-if-886436956581"
    key = "test-delete-file.txt"

    # Create a test file in S3
    s3_client.put_object(Bucket=bucket, Key=key, Body=b"Test content")

    try:
        # Call the function to test
        delete_input_file(bucket, key)

        # Verify that the file has been deleted
        with pytest.raises(ClientError) as exc_info:
            s3_client.head_object(Bucket=bucket, Key=key)
        assert exc_info.value.response["Error"]["Code"] == "404"

    except ClientError as e:
        pytest.fail(f"Failed to delete file from S3: {str(e)}")

    finally:
        # Clean up: make sure the test file is deleted from S3
        try:
            s3_client.delete_object(Bucket=bucket, Key=key)
        except Exception as e:
            raise e


def test_find_s3_prefix_with_matching_prefixes():
    s3_client = boto3.client("s3")
    bucket_name = "s3-dev-dlpf-if-886436956581"
    prefix = "prefix/"

    # Ensure the bucket and prefixes exist for the test
    s3_client.put_object(
        Bucket=bucket_name, Key="prefix_20240101/test1.txt", Body=b"Test content"
    )
    s3_client.put_object(
        Bucket=bucket_name, Key="prefix_20240102/test2.txt", Body=b"Test content"
    )
    s3_client.put_object(
        Bucket=bucket_name, Key="prefix_20250101/test3.txt", Body=b"Test content"
    )

    prefix_path, date_str = find_s3_prefix(prefix, bucket_name)
    assert prefix_path == "prefix_20240101/"
    assert date_str == "20240101"

    # Clean up the test data
    s3_client.delete_object(Bucket=bucket_name, Key="prefix_20240101/test1.txt")
    s3_client.delete_object(Bucket=bucket_name, Key="prefix_20240102/test2.txt")
    s3_client.delete_object(Bucket=bucket_name, Key="prefix_20250101/test3.txt")


@patch("source.common.get_s3_client")
def test_find_s3_prefix_no_matching_prefixes(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.list_objects_v2.return_value = {"CommonPrefixes": []}

    aws_s3_dir = "test_dir/"
    bucket_name = "test_bucket"

    with pytest.raises(ValueError) as exc_info:
        find_s3_prefix(aws_s3_dir, bucket_name)

    assert (
        str(exc_info.value)
        == f"指定されたディレクトリに一致するプレフィックスが見つかりません: {aws_s3_dir}"
    )

@patch("source.common.get_s3_client")
def test_find_s3_prefix_empty_response(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.list_objects_v2.return_value = {}

    with pytest.raises(ValueError) as exc_info:
        find_s3_prefix("test/", "test-bucket")

    assert (
        str(exc_info.value)
        == "指定されたディレクトリに一致するプレフィックスが見つかりません: test/"
    )


@patch("source.common.get_s3_client")
def test_find_s3_prefix_with_valid_date(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_get_s3_client.return_value = mock_s3_client
    mock_s3_client.list_objects_v2.return_value = {
        "CommonPrefixes": [{"Prefix": "test_20240321/"}, {"Prefix": "test_20240322/"}]
    }

    prefix_path, date_str = find_s3_prefix("test/", "test-bucket")

    assert prefix_path == "test_20240321/"
    assert date_str == "20240321"


def test_move_file_to_error_dir_correct_error_directory():
    s3_client = get_s3_client()
    bucket_name = "s3-dev-dlpf-if-886436956581"
    input_dir = "o.iketsu/OMS_DMS/prefix_20240101/test.csv"
    # Upload a test file to the input directory
    s3_client.put_object(Bucket=bucket_name, Key=input_dir, Body=b"test")

    move_file_to_error_dir(input_dir, bucket_name)

    # Check if the file is copied to the error directory
    response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix="error/")
    assert "Contents" in response
    assert any(
        obj["Key"] == "error/OMS_DMS/prefix_20240101/test.csv"
        for obj in response["Contents"]
    )

    # Check if the file is deleted from the input directory
    response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=input_dir)
    assert "Contents" not in response

    # Clean up the test files
    s3_client.delete_object(Bucket=bucket_name, Key="error/path/to/file.txt")


@patch("source.common.get_s3_client")
def test_move_file_to_error_dir_client_error(mock_get_s3_client):
    mock_s3_client = MagicMock()
    mock_s3_client.copy_object.side_effect = ClientError(
        {"Error": {"Code": "NoSuchKey", "Message": "Test Error"}}, "copy_object"
    )
    mock_get_s3_client.return_value = mock_s3_client

    with pytest.raises(ClientError):
        move_file_to_error_dir("input/dir/file.txt", "test-bucket")


@patch("source.common.get_s3_client")
def test_find_s3_prefix_with_dated_prefix(mock_get_s3_client):
    """
    正常系テスト
    日付サフィックス付きのプレフィックスが正しく見つかることを確認
    最も古い日付のプレフィックスが選択されることを確認
    """
    mock_s3 = MagicMock()
    mock_get_s3_client.return_value = mock_s3
    mock_s3.list_objects_v2.return_value = {
        'CommonPrefixes': [
            {'Prefix': 'takahashi/input-output/CRM_IN_20240321/'},
            {'Prefix': 'takahashi/input-output/CRM_IN_20240320/'}
        ]
    }

    directory = "takahashi/input-output/CRM_IN/"
    bucket_name = "test-bucket"
    prefix_path, date_str = find_s3_prefix(directory, bucket_name)
    assert prefix_path == "takahashi/input-output/CRM_IN_20240320/"
    assert date_str == "20240320"

    mock_s3.list_objects_v2.assert_called_once_with(
        Bucket=bucket_name,
        Delimiter='/',
        Prefix=directory.rstrip('/')
    )


@patch("source.common.get_s3_client")
def test_find_s3_prefix_without_date(mock_get_s3_client):
    """
    正常系テスト
    日付サフィックスなしのプレフィックスが正しく見つかることを確認
    """
    mock_s3 = MagicMock()
    mock_get_s3_client.return_value = mock_s3
    mock_s3.list_objects_v2.return_value = {
        'CommonPrefixes': [
            {'Prefix': 'takahashi/input-output/CRM_IN/'}
        ]
    }

    directory = "takahashi/input-output/CRM_IN/"
    bucket_name = "test-bucket"
    prefix_path, date_str = find_s3_prefix(directory, bucket_name)
    assert prefix_path == "takahashi/input-output/CRM_IN/"
    assert date_str == ""

    mock_s3.list_objects_v2.assert_called_once_with(
        Bucket=bucket_name,
        Delimiter='/',
        Prefix=directory.rstrip('/')
    )


@patch("source.common.get_s3_client")
def test_find_s3_prefix_no_matching_prefixes(mock_get_s3_client):
    """
    異常系テスト
    プレフィックスが見つからない場合にValueErrorが発生することを確認
    """
    mock_s3 = MagicMock()
    mock_get_s3_client.return_value = mock_s3
    mock_s3.list_objects_v2.return_value = {
        'CommonPrefixes': []
    }

    directory = "invalid/prefix/path/"
    bucket_name = "test-bucket"
    with pytest.raises(ValueError) as exc_info:
        find_s3_prefix(directory, bucket_name)
    
    assert "指定されたディレクトリに一致するプレフィックスが見つかりません" in str(exc_info.value)

    mock_s3.list_objects_v2.assert_called_once_with(
        Bucket=bucket_name,
        Delimiter='/',
        Prefix=directory.rstrip('/')
    )


@patch("source.common.get_s3_client")
def test_find_s3_prefix_multiple_dates(mock_get_s3_client):
    """
    正常系テスト
    複数の日付サフィックスが存在する場合に最も古い日付が選択されることを確認
    """
    mock_s3 = MagicMock()
    mock_get_s3_client.return_value = mock_s3
    mock_s3.list_objects_v2.return_value = {
        'CommonPrefixes': [
            {'Prefix': 'takahashi/input-output/CRM_IN_20240321/'},
            {'Prefix': 'takahashi/input-output/CRM_IN_20240320/'},
            {'Prefix': 'takahashi/input-output/CRM_IN_20240319/'}
        ]
    }

    directory = "takahashi/input-output/CRM_IN/"
    bucket_name = "test-bucket"
    prefix_path, date_str = find_s3_prefix(directory, bucket_name)
    assert prefix_path == "takahashi/input-output/CRM_IN_20240319/"
    assert date_str == "20240319"

    mock_s3.list_objects_v2.assert_called_once_with(
        Bucket=bucket_name,
        Delimiter='/',
        Prefix=directory.rstrip('/')
    )


@patch("source.common.get_s3_client")
def test_find_s3_prefix_s3_error(mock_get_s3_client):
    """
    異常系テスト
    S3操作でエラーが発生した場合の処理を確認
    """
    mock_s3 = MagicMock()
    mock_get_s3_client.return_value = mock_s3
    mock_s3.list_objects_v2.side_effect = ClientError(
        {'Error': {'Code': 'NoSuchBucket', 'Message': 'The specified bucket does not exist'}},
        'ListObjectsV2'
    )

    directory = "takahashi/input-output/CRM_IN/"
    bucket_name = "test-bucket"
    with pytest.raises(ClientError) as exc_info:
        find_s3_prefix(directory, bucket_name)
    
    assert exc_info.value.response['Error']['Code'] == 'NoSuchBucket'

    mock_s3.list_objects_v2.assert_called_once_with(
        Bucket=bucket_name,
        Delimiter='/',
        Prefix=directory.rstrip('/')
    )
