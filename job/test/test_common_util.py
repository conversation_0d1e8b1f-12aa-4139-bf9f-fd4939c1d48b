from pathlib import Path
from configparser import ConfigParser
from source.common_util import (
    get_resource_path,
    load_log_config,
    load_yaml_config,
    load_sql_config,
)


def test_get_resource_path():
    result = get_resource_path("config", "log_message.config")
    expected = Path(__file__).parent.parent / "source" / "config" / "log_message.config"
    assert result == expected


def test_load_log_config():
    config = load_log_config("log_message.config")
    assert isinstance(config, ConfigParser)
    assert len(config.sections()) > 0


def test_load_yaml_config():
    config = load_yaml_config("example.yaml")
    assert isinstance(config, dict)
    assert "key" in config


def test_load_sql_config():
    sql = load_sql_config("sql_common_select_001.sql")
    assert isinstance(sql, str)
    assert "SELECT" in sql
