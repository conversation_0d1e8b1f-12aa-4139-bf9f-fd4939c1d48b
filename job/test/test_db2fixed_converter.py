#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import Mock
import yaml
from datetime import datetime
from source.db2fixed_converter import DB2FixedConverter
from test.resources import get_test_resource_path
from sqlalchemy.exc import SQLAlchemyError


TEST_CONFIG = {
    "database": {"batch_size": 1000, "timeout": 3600},
    "output": {
        "encoding": "ms932",
        "format": "fixed-length",
        "line_ending": "\r\n",
    },
    "fixed_length": {
        "total_length": 50,
        "padding_char": " ",
        "fields": [
            {
                "name": "id",
                "start": 1,
                "length": 10,
                "align": "right",
                "type": "number",
                "padding_char": "0",
            },
            {"name": "name", "start": 11, "length": 40, "type": "string"},
        ],
    },
    "etl": {
        "main_query": """
            SELECT id, name, amount, created_at
            FROM test_table
            WHERE id = :id
        """,
        "sql_common_update_001": "UPDATE sync_table SET timestamp = :timestamp",
    },
}


def setup_module(module):
    """モジュールの初期化"""
    config_dir = get_test_resource_path("config", "converter/db2fixed")
    config_dir.parent.mkdir(parents=True, exist_ok=True)

    config_path = config_dir / "test_etl_config.yaml"
    with open(config_path, "w", encoding="utf-8") as f:
        yaml.dump(TEST_CONFIG, f)


def teardown_module(module):
    """モジュールの終了処理"""
    config_path = get_test_resource_path(
        "config", "converter/db2fixed/test_etl_config.yaml"
    )
    if config_path.exists():
        config_path.unlink()


@pytest.fixture
def mock_db():
    """DBConnectorのモック"""
    mock = Mock()
    mock.exec.return_value = True
    mock.fetch.return_value = [
        {"id": 1, "name": "テスト", "amount": 1000, "created_at": datetime(2023, 1, 15)}
    ]
    return mock


@pytest.fixture
def converter(mock_db):
    """テスト用DB2FixedConverterインスタンス"""
    converter = DB2FixedConverter(
        "test_jobnet",
        "test_etl_config",
        mock_db,
    )
    converter.config = TEST_CONFIG
    converter.params = {
        "cooperation_dest_system": "test_system",
        "file_name": "test_file.txt",
    }
    return converter


def test_init(converter):
    """初期化の正常系テスト
    テストケース番号：DB2FIX-003
    """
    assert converter.converter_type == "db2fixed"
    assert converter.jobnet_id == "test_jobnet"
    assert converter.etl_config == "test_etl_config"
    assert isinstance(converter.config, dict)


def test_convert_from_data(converter):
    """
    テストケース番号：DB2FIX-006,DB2FIX-010
    固定長データ変換のテスト
    - テストデータ2レコードを用意
    - 各レコードのバイト数を正しく検証
    - レコード位置をバイト数で計算して内容を確認
    """
    test_data = [
        {
            "id": 1,
            "name": "テスト1",  # 全角文字
            "amount": 1000,
            "created_at": datetime(2023, 1, 15),
        },
        {
            "id": 2,
            "name": "テスト2",  # 全角文字
            "amount": 2000,
            "created_at": datetime(2023, 2, 20),
        },
    ]

    # データ変換実行
    result = converter.convert_from_data(test_data, "test_output.txt")

    # shift-jisでエンコードしてバイト列を取得
    result_bytes = result.encode("shift-jis")

    # 1レコードのバイト数を取得
    record_bytes = 50  # 設定ファイルのtotal_lengthと同じ

    # 全体のバイト数を検証
    expected_total_bytes = record_bytes * len(test_data)
    assert len(result_bytes) == expected_total_bytes

    # 最初のレコードを検証（バイト位置で切り出し）
    first_record = result_bytes[0:record_bytes].decode("shift-jis")
    assert first_record.startswith("0000000001")  # IDは右寄せ、0埋め

    # 2番目のレコードを検証
    second_record = result_bytes[record_bytes : record_bytes * 2].decode("shift-jis")
    assert second_record.startswith("0000000002")  # IDは右寄せ、0埋め


def test_error_session_creation(converter, mock_db):
    """セッション生成失敗のテスト

    テストケース番号：DB2FIX-009
    不正なエンジン設定でのセッション生成失敗を確認
    """
    mock_db.begin_transaction.side_effect = SQLAlchemyError(
        "Failed to create database session"
    )
    mock_db.exec.side_effect = SQLAlchemyError("Failed to create database session")

    # 実行と検証
    with pytest.raises(SQLAlchemyError) as exc_info:
        converter._execute_query("main_query", {"param": "value"})

    # エラーメッセージの検証
    assert "Failed to create database session" in str(exc_info.value)

    # モックの呼び出し確認
    assert mock_db.exec.called or mock_db.begin_transaction.called


def test_number_truncate(converter):
    """数値型変換の桁数超過テスト

    テストケース番号：DB2FIX-011
    フィールド長を超える数値を指定した場合に切り捨てられることを確認
    """
    # テスト用の設定を作成
    converter.config["fixed_length"]["fields"] = [
        {
            "name": "number_field",
            "length": 3,  # 3バイトの固定長
            "type": "number",
            "align": "right",  # 右寄せ
            "padding_char": "0",  # 0埋め
        }
    ]

    # バイト数超過のテストデータ
    test_data = [{"number_field": 1234}]  # 4桁の数値

    # 実行
    result = converter.convert_from_data(test_data, "test_output.txt")

    # バイト長の検証
    result_bytes = result.encode("shift-jis")
    assert len(result_bytes) == 3  # 3バイトの固定長

    # 切り捨て結果の検証
    # 4桁(1234)が先頭から3桁(123)に切り捨てられる
    expected = "123"  # 先頭3桁
    assert result == expected


def test_normal_zero_padding(converter):
    """数値型変換の0埋め処理テスト

    テストケース番号：DB2FIX-012
    右寄せ指定の数値フィールドで0埋めされることを確認
    """
    # テスト用の設定を作成
    converter.config["fixed_length"]["fields"] = [
        {
            "name": "number_field",
            "length": 5,
            "type": "number",
            "align": "right",
            "padding_char": "0",
        }
    ]

    # テストケース
    test_cases = [(123, "00123"), (1, "00001"), (0, "00000"), (99999, "99999")]

    for value, expected in test_cases:
        test_data = [{"number_field": value}]
        result = converter.convert_from_data(test_data, "test.txt")
        # 文字列全体を比較
        assert (
            result == expected
        ), f"Value {value}: expected {expected}, but got {result}"

        # バイト長も検証
        result_bytes = result.encode("shift-jis")
        assert (
            len(result_bytes) == 5
        ), f"Expected 5 bytes, but got {len(result_bytes)} bytes for value {value}"


def test_date_format(converter):
    """日付フォーマットテスト

    テストケース番号：DB2FIX-013
    異なる日付形式でも文字列として処理されることを確認
    """
    # テスト用の設定を作成
    converter.config["fixed_length"]["fields"] = [
        {
            "name": "date_field",
            "length": 8,
            "type": "date",
            "format": "%Y%m%d",  # YYYYMMDD形式
            "align": "left",  # 左寄せ
            "padding_char": " ",  # 空白埋め
        }
    ]

    # 異なる日付形式のテストデータ
    test_data = [{"date_field": "2024-01-06"}]  # YYYY-MM-DD形式

    # 実行
    result = converter.convert_from_data(test_data, "test.txt")

    # バイト長の検証
    result_bytes = result.encode("shift-jis")
    assert len(result_bytes) == 8  # 8バイトの固定長

    # 文字列としての結果を検証
    # 入力文字列が8バイトなので、そのまま出力される
    expected = "2024-01-"  # 8バイトに切り詰められる
    assert result == expected


def test_normal_date_format_conversion(converter):
    """日付フォーマット変換の正常系テスト

    テストケース番号：DB2FIX-014
    datetimeオブジェクトが文字列として処理されることを確認
    """
    test_date = datetime(2024, 1, 6)
    test_cases = [
        # フォーマット, フィールド長, 期待値
        ("%Y%m%d", 8, "2024-01-"),  # str(datetime)の結果が切り詰められる
        ("%y%m%d", 6, "2024-0"),  # str(datetime)の結果が切り詰められる
        ("%Y-%m-%d", 10, "2024-01-06"),  # str(datetime)の結果がそのまま使用
    ]

    for date_format, field_length, expected in test_cases:
        # テスト用の設定を作成
        converter.config["fixed_length"]["fields"] = [
            {
                "name": "date_field",
                "length": field_length,
                "type": "date",
                "format": date_format,  # 現状は無視される
                "align": "left",  # 左寄せ
                "padding_char": " ",  # 空白埋め
            }
        ]

        # テスト実行
        test_data = [{"date_field": test_date}]
        result = converter.convert_from_data(test_data, "test.txt")

        # バイト長の検証
        result_bytes = result.encode("shift-jis")
        assert (
            len(result_bytes) == field_length
        ), f"Expected {field_length} bytes, but got {len(result_bytes)} bytes for format {date_format}"

        # 変換結果の検証
        assert (
            result == expected
        ), f"Format {date_format}: Expected {expected}, but got {result}"


def test_multibyte_truncate(converter):
    """マルチバイト文字のバイト長超過時の切り捨てテスト

    テストケース番号：DB2FIX-015
    マルチバイト文字でバイト長超過した場合の切り捨て処理を確認
    """
    # テスト用の設定を作成（4バイトのフィールド）
    converter.config["fixed_length"]["fields"] = [
        {
            "name": "text_field",
            "length": 4,
            "type": "string",
            "align": "left",
            "padding_char": " ",
        }
    ]

    test_cases = [
        # 入力値, 期待値（4バイトに切り捨て、パディング込み）
        ("テスト中", "テス"),  # 全角2文字(4バイト)
        ("あ123", "あ12"),  # 全角1文字(2バイト) + 半角2文字(2バイト)
    ]

    for input_value, expected in test_cases:
        # テストデータ
        test_data = [{"text_field": input_value}]

        # 実行
        result = converter.convert_from_data(test_data, "test.txt")

        # バイト長の検証
        result_bytes = result.encode("shift-jis")
        assert len(result_bytes) == 4  # 4バイトに切り捨てられていることを確認

        # 切り捨て結果の検証
        assert result == expected


def test_normal_string_padding(converter):
    """文字列型のパディング処理テスト

    テストケース番号：DB2FIX-016
    左寄せ指定の文字列フィールドで指定文字でパディングされることを確認
    """
    test_cases = [
        # 値, フィールド長, パディング文字, 期待値
        ("テスト", 12, " ", "テスト      "),  # 全角文字(8バイト)+半角スペース(4バイト)
        ("ABC", 5, "_", "ABC__"),  # 半角文字(3バイト)+アンダースコア(2バイト)
        ("", 3, "x", "xxx"),  # 空文字列+x3バイト
        ("漢字", 8, "-", "漢字----"),  # 全角文字(4バイト)+ハイフン(4バイト)
    ]

    for value, length, pad_char, expected in test_cases:
        # テスト用の設定を作成
        converter.config["fixed_length"]["fields"] = [
            {
                "name": "text_field",
                "length": length,
                "type": "string",
                "align": "left",
                "padding_char": pad_char,
            }
        ]

        test_data = [{"text_field": value}]
        result = converter.convert_from_data(test_data, "test.txt")

        # shift-jisでエンコードしてバイト長を検証
        result_bytes = result.encode("shift-jis")
        assert (
            len(result_bytes) == length
        ), f"Value '{value}': Expected {length} bytes, but got {len(result_bytes)} bytes"

        # 結果を検証
        assert (
            result == expected
        ), f"Value '{value}': Expected '{expected}', but got '{result}'"

        # パディング文字が正しく適用されていることを確認
        if value:  # 空文字列以外の場合
            # 元の値で始まることを確認
            assert result.startswith(
                value
            ), f"Result '{result}' should start with '{value}'"

            # パディングが正しく適用されていることを確認
            padding = result[len(value) :]
            expected_padding = pad_char * (len(padding))
            assert (
                padding == expected_padding
            ), f"Padding mismatch. Expected '{expected_padding}', but got '{padding}'"


def test_config_file_not_exists(converter, mock_db):
    """設定ファイルが存在しない場合のテスト

    テストケース番号：DB2FIX-001
    シナリオ：存在しない設定ファイルを指定した場合のエラー処理を確認
    """
    # 存在しない設定ファイルを指定
    converter.etl_config = "non_existent_config"

    # FileNotFoundErrorが発生することを確認
    with pytest.raises(FileNotFoundError) as exc_info:
        converter._load_config()

    # エラーメッセージの検証
    assert "No such file or directory" in str(exc_info.value)


def test_invalid_yaml_format(converter, mock_db):
    """不正なYAML形式の設定ファイルを読み込んだ場合のテスト

    テストケース番号：DB2FIX-002
    シナリオ：構文エラーを含むYAMLファイルを読み込んだ場合のエラー処理を確認
    """
    # テスト用の設定ファイルを作成
    config_dir = get_test_resource_path("config", "converter/db2fixed")
    config_path = config_dir / "invalid_yaml_config.yaml"

    # 不正なYAML形式の内容を書き込み
    with open(config_path, "w", encoding="utf-8") as f:
        f.write(
            """
        fixed_length:
            total_length: 50
            fields:
                - name: id
                  length: 10
                - invalid:
                      format: here
                    - wrong:
                      indentation
        """
        )

    # コンバーターの設定を更新
    converter.etl_config = "invalid_yaml_config"

    # YAMLErrorが発生することを確認
    with pytest.raises(yaml.parser.ParserError) as exc_info:
        converter._load_config()

    # エラーメッセージの検証
    error_msg = str(exc_info.value)
    assert "while parsing a block mapping" in error_msg

    # テストファイルのクリーンアップ
    if config_path.exists():
        config_path.unlink()


def test_total_length_mismatch(mock_aws, db_connector, logger, monkeypatch):
    """total_lengthと実際の長さの不一致テスト

    テストケース番号：DB2FIX-004
    シナリオ：フィールド長の合計がtotal_lengthと異なる場合のエラー処理を確認
    """
    from source.db2fixed_converter import DB2FixedConverter
    from source.converter_base import ConverterBase

    # 不正な設定を作成（total_length = 50だが、実際のフィールド長合計は60）
    invalid_config = {
        "database": {"batch_size": 1000, "timeout": 3600},
        "output": {
            "encoding": "utf-8",
            "root_element": "test",
            "record_element": "record",
            "indent": 2,
        },
        "fixed_length": {
            "total_length": 50,  # total_length=50
            "padding_char": " ",
            "fields": [
                {
                    "name": "id",
                    "start": 1,
                    "length": 20,  # 20バイト
                    "type": "number",
                    "align": "right",
                    "padding_char": "0",
                },
                {
                    "name": "name",
                    "start": 21,
                    "length": 40,  # +40バイト = 合計60バイト
                    "type": "string",
                },
            ],
        },
        "etl": {"main_query": "SELECT 1 as id, 'test' as name"},
    }

    # _load_configのモック
    def mock_load_config(self):
        return invalid_config

    # モックを適用
    monkeypatch.setattr(ConverterBase, "_load_config", mock_load_config)

    # ValidationErrorが発生することを確認
    with pytest.raises(ValueError) as exc_info:
        DB2FixedConverter(
            jobnet_id="test",
            etl_config="dummy_config.yaml",  # 実際のファイルは不要
            db_connection=db_connector,
            last_sync_timestamp=None,
        )

    # エラーメッセージの検証
    assert "フィールド長の合計 (60) が total_length (50) と一致しません" in str(
        exc_info.value
    )


if __name__ == "__main__":
    pytest.main()
