#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import Mock
import yaml
import xml.etree.ElementTree as ET
from datetime import datetime
from source.db2xml_converter import DB2XMLConverter
from test.resources import get_test_resource_path

# テストデータの設定 - 複数カラムマッピングを削除
TEST_CONFIG = {
    "etl": {
        "test_query": """
            SELECT
                id,
                name,
                amount,
                created_at
            FROM test_table
            WHERE id = :id
        """,
        # シンプルなマッピングに修正
        "mappings": [
            {
                "target_element": "root",
                "child_elements": [
                    {
                        "target_element": "record",
                        "query": "test_query",
                        "child_elements": [
                            {"target_element": "id", "source_column": "id"},
                            {"target_element": "name", "source_column": "name"},
                            {"target_element": "amount", "source_column": "amount"},
                        ],
                        "attributes": {"created": "{created_at}"},
                    }
                ],
            }
        ],
    },
    "transformations": {
        "null_value": "",
    },
    "output": {
        "encoding": "utf-8",
        "root_element": "root",
        "record_element": "record",
        "indent": 2,
    },
}


def setup_module(module):
    """モジュールの初期化"""
    # テストリソースディレクトリの作成
    config_dir = get_test_resource_path("config", "converter/db2xml")
    config_dir.parent.mkdir(parents=True, exist_ok=True)

    # テスト用設定ファイルの作成
    config_path = config_dir / "test_etl_config.yaml"
    with open(config_path, "w", encoding="utf-8") as f:
        yaml.dump(TEST_CONFIG, f)


def teardown_module(module):
    """モジュールの終了処理"""
    # テスト用設定ファイルの削除
    config_path = get_test_resource_path(
        "config", "converter/db2xml/test_etl_config.yaml"
    )
    if config_path.exists():
        config_path.unlink()


@pytest.fixture
def mock_db():
    """DBConnectorのモック"""
    return Mock()


@pytest.fixture
def converter(mock_db):
    """テスト用DB2XMLConverterインスタンス"""
    converter = DB2XMLConverter(
        "test_jobnet",
        "test_etl_config",
        mock_db,
    )
    # 事前に定義された設定を使用
    converter.config = TEST_CONFIG
    return converter


def test_init(converter):
    """初期化の正常系テスト
    テストケース番号：DB2XML-003
    """
    assert converter.converter_type == "db2xml", "コンバータータイプが正しくありません"
    assert converter.jobnet_id == "test_jobnet", "ジョブネットIDが正しくありません"
    assert converter.etl_config == "test_etl_config", "ETL設定IDが正しくありません"

    # マッピングの存在を確認
    assert "etl" in converter.config, "ETL設定が存在しません"
    assert "mappings" in converter.config["etl"], "マッピング定義が存在しません"

    # マッピングの基本的な構造を検証
    mappings = converter.config["etl"]["mappings"]
    assert isinstance(mappings, list), "マッピングはリスト形式である必要があります"
    assert len(mappings) > 0, "マッピングが空です"

    # マッピングの各要素に対する基本的な検証
    for mapping in mappings:
        assert "target_element" in mapping, "各マッピングには target_element が必要です"


def test_escape_xml_chars(converter):
    """XML特殊文字のエスケープ処理テスト"""
    test_cases = [
        ("Hello & World", "Hello &amp; World"),
        ("<tag>", "&lt;tag&gt;"),
        ("'quote'", "&apos;quote&apos;"),
        ('"double"', "&quot;double&quot;"),
    ]

    for input_text, expected in test_cases:
        result = converter._escape_xml_chars(input_text)
        assert result == expected


def test_data_type_conversion(converter):
    """データ型変換のテスト
    テストケース番号：DB2XML-014,DB2XML-015
    """
    test_cases = [
        ("123", "number", None, "123"),  # 整数として変換
        (True, "boolean", None, "true"),
        (datetime(2023, 1, 15, 10, 30), "datetime", "%Y-%m-%d", "2023-01-15"),
    ]

    for value, type_, format_, expected in test_cases:
        result = converter._convert_value(value, type_, format_)
        assert result == expected


def test_convert_from_data(converter):
    """convert_from_dataメソッドのテスト"""
    # テストデータ
    test_data = [
        {
            "id": "1",
            "name": "test1",
            "amount": "1000",
            "created_at": "2023-01-15 10:30:00",
        }
    ]

    # モックの戻り値を設定
    converter.db_connection.exec.return_value = test_data

    # 実行
    result = converter.convert_from_data(test_data, "output_path")

    # XML文字列のパース
    root = ET.fromstring(result)

    # 基本構造の検証
    assert root.tag == "root"
    records = root.findall(".//record")
    assert len(records) == 1, "レコードの数が不正"

    # レコードの内容を検証
    record = records[0]
    assert record.find("id").text == "1", "IDが不正"
    assert record.find("name").text == "test1", "名前が不正"
    assert record.find("amount").text == "1000", "金額が不正"


def test_build_xml_element_with_condition(converter):
    """条件付きXML要素構築のテスト"""
    # テスト用のマッピング定義
    mapping = {
        "source_column": "id",
        "target_element": "test_element",
        "condition": "amount > 500",
        "query": "test_query",
    }

    # テストデータ
    test_data = {
        "test_query": [
            {"id": 1, "amount": 1000},  # 条件を満たす
            {"id": 2, "amount": 100},  # 条件を満たさない
        ]
    }

    # ルート要素を作成
    root = ET.Element("root")

    # 実行
    converter._build_xml_element(test_data, mapping, root)

    # 検証
    assert len(root.findall(".//test_element")) == 1  # 条件を満たす要素のみ生成される


def test_create_element_with_source_column(converter):
    """
    ソースカラムを持つ要素の作成テスト

    テストケース番号：DB2XML-011

    _create_element メソッドの基本的な要素生成機能を検証する

    検証観点:
    1. 要素が正しく生成されること
    2. 要素の値が正確であること
    3. 要素に子要素がないこと
    """
    # テスト用のマッピング定義
    mapping = {"source_column": "name", "target_element": "simple_element"}

    # ルート要素を作成
    parent = ET.Element("root")

    # テストデータ
    record = {
        "name": "test_value",
    }

    # 要素生成の実行
    result = converter._build_xml_element(
        query_results={}, mapping=mapping, target_element=parent, parent_record=record
    )
    element = parent.find("simple_element")

    # 検証
    assert element is not None, "要素が生成されていません"
    assert element.tag == "simple_element", "要素のタグが正しくありません"
    assert element.text == "test_value", "要素のテキスト値が正しくありません"
    assert len(element) == 0, "子要素が存在しません"


def test_create_element_with_attributes(converter):
    """
    属性を持つ要素の作成テスト

    テストケース番号：DB2XML-013

    _create_element メソッドの属性設定機能を検証する

    検証観点:
    1. 要素が正しく生成されること
    2. 属性が正しく設定されること
    """
    # テスト用のマッピング定義
    mapping = {
        "source_column": "name",
        "target_element": "element_with_attrs",
        "attributes": {"type": "name_type", "length": "name_length"},
    }

    # テストデータ
    record = {"name": "test_value", "name_type": "custom", "name_length": "5"}

    # ルート要素を作成
    parent = ET.Element("root")

    # 要素の生成
    result = converter._build_xml_element(
        query_results={}, mapping=mapping, target_element=parent, parent_record=record
    )
    element = parent.find("element_with_attrs")

    # 検証
    assert element is not None, "要素が生成されていません"
    assert element.tag == "element_with_attrs", "要素のタグが正しくありません"
    assert element.text == "test_value", "要素のテキスト値が正しくありません"
    assert element.get("type") == "custom", "属性 type が正しく設定されていません"
    assert element.get("length") == "5", "属性 length が正しく設定されていません"
    assert len(element) == 0, "子要素が存在しません"


def test_create_element_with_fixed_value(converter):
    """
    固定値を持つ要素の作成テスト

    _create_element メソッドの固定値要素生成機能を検証する

    検証観点:
    1. 固定値の要素が正しく生成されること
    2. 値が正確であること
    """
    # テスト用のマッピング定義
    mapping = {"target_element": "fixed_element", "value": "constant_value"}

    # ルート要素を作成
    parent = ET.Element("root")

    # 要素の生成
    result = converter._build_xml_element(
        query_results={}, mapping=mapping, target_element=parent, parent_record={}
    )
    element = parent.find("fixed_element")

    # 検証
    assert element is not None, "固定値要素が生成されていません"
    assert element.tag == "fixed_element", "固定値要素のタグが正しくありません"
    assert element.text == "constant_value", "固定値要素の値が正しくありません"
    assert len(element) == 0, "子要素が存在しません"


def test_format_xml(converter):
    """XML整形のテスト"""
    # テスト用のXML要素
    root = ET.Element("root")
    child = ET.SubElement(root, "child")
    child.text = "test"

    # 実行
    result = converter._format_xml(root)

    # 検証
    assert "<?xml" in result  # XMLヘッダーが含まれる
    assert "<root" in result  # ルート要素が含まれる
    assert "<child>test</child>" in result.replace(" ", "")  # 子要素が含まれる


def test_config_file_not_exists(converter, mock_db):
    """設定ファイルが存在しない場合のテスト

    テストケース番号：DB2XML-001
    シナリオ：存在しない設定ファイルを指定した場合のエラー処理を確認
    """
    # 存在しないファイルパスを設定
    converter.etl_config = "non_existent_config"

    # FileNotFoundErrorが発生することを確認
    with pytest.raises(FileNotFoundError) as exc_info:
        # _load_configを直接呼び出し
        converter._load_config()

    # エラーメッセージの検証
    assert "No such file or directory" in str(exc_info.value)


def test_invalid_yaml_format(converter, mock_db):
    """不正なYAML形式の設定ファイルを読み込んだ場合のテスト

    テストケース番号：DB2XML-002
    シナリオ：構文エラーを含むYAMLファイルを読み込んだ場合のエラー処理を確認
    """
    # テスト用の設定ファイルを作成
    config_dir = get_test_resource_path("config", "converter/db2xml")
    config_path = config_dir / "invalid_yaml_config.yaml"

    # 不正なYAML形式の内容を書き込み
    with open(config_path, "w", encoding="utf-8") as f:
        f.write(
            """
        etl:
          - invalid:
              yaml: format
            - missing:
              indentation
        """
        )

    # コンバーターの設定を更新
    converter.etl_config = "invalid_yaml_config"

    # YAMLErrorが発生することを確認
    with pytest.raises(yaml.parser.ParserError) as exc_info:
        converter._load_config()

    # エラーメッセージの検証 - 実際のYAMLエラーメッセージに合わせる
    assert "while parsing a block mapping" in str(exc_info.value)
    assert "expected <block end>, but found '-'" in str(exc_info.value)

    # テストファイルのクリーンアップ
    if config_path.exists():
        config_path.unlink()


def test_invalid_element_name(converter):
    """不正な要素名指定のテスト

    テストケース番号：DB2XML-010
    シナリオ：XMLで使用できない文字を含む要素名を指定した場合のエラー処理を確認
    """
    # テスト用のマッピング定義（不正な要素名を含む）
    mapping = {
        "source_column": "id",
        "target_element": "invalid<element>name",  # XMLで使用できない文字を含む
        "type": "string",
    }

    # テストデータ
    record = {"id": "test_value"}

    # ルート要素を作成
    parent = ET.Element("root")

    # ValueError（または適切な例外）が発生することを確認
    with pytest.raises(ValueError) as exc_info:
        converter._build_xml_element(
            query_results={},
            mapping=mapping,
            target_element=parent,
            parent_record=record,
        )

    # エラーメッセージの検証


def test_invalid_attribute_value(converter):
    """不正な属性値のテスト

    テストケース番号：DB2XML-012
    シナリオ：制御文字を含む属性値を設定した場合のエラー処理を確認
    """
    # 制御文字（NUL、US）を含む文字列を作成
    control_chars = chr(0x00) + chr(0x1F)
    invalid_value = "test" + control_chars + "value"

    # テスト用のマッピング定義（制御文字を含む属性値）
    mapping = {
        "target_element": "test_element",
        "attributes": {"control_char": chr(0)},  # NUL文字
    }

    # テストデータ
    record = {}

    # ルート要素を作成
    parent = ET.Element("root")

    # ValueError（または適切な例外）が発生することを確認
    with pytest.raises(ValueError) as exc_info:
        converter._build_xml_element(
            query_results={},
            mapping=mapping,
            target_element=parent,
            parent_record=record,
        )

    # エラーメッセージの検証
    assert "属性値に不正な文字が含まれています" in str(exc_info.value)


if __name__ == "__main__":
    pytest.main()
