#!/usr/bin/env python
# -*- coding: utf-8 -*-

import html
import sys
import pytest
from unittest.mock import MagicM<PERSON>, patch, Mo<PERSON>, Mock
from datetime import datetime, timedelta
from xml.etree import ElementTree as ET

from source.db2xml_converter import DB2XMLConverter
from source.glue_job_db_to_file import GlueJobDbToFile, main
from test.test_utils import GlueTestUtils, get_test_file_path


class TestDb2XMLConverter2:
    """変換シナリオテスト"""

    @patch("source.converter_base.ConverterBase._load_config")
    @patch("source.db_connector.DbConnector.exec")
    def test_basic_xml_conversion(
        self, mock_db_exec: Mock, mock_load_config: Mock, db_connector
    ):
        """基本的なXML変換のテスト

        目的:
        - 単一のSubTreeにおける親クエリと子要素の関係を検証
        - 親クエリ（campaign_query）の結果から子要素へのデータ受け渡しを確認
        - 子要素が親要素のデータを正しく参照できることを確認
        """
        # XMLコンバーター設定
        config = {
            "output": {"encoding": "utf-8", "root_element": "promotions"},
            "etl": {
                "parameters": {
                    "sync_timestamp": {"type": "datetime", "required": True}
                },
                "campaign_query": "SELECT * FROM campaign_instructions",
                "mappings": [
                    {
                        "target_element": "campaign",
                        "query": "campaign_query",
                        "attributes": {"campaign-id": "{campaign_code}"},
                        "child_elements": [
                            {
                                "target_element": "description",
                                "source_column": "campaign_name",
                            }
                        ],
                    }
                ],
            },
        }
        mock_load_config.return_value = config

        # DBの戻り値をモック
        mock_result = MagicMock()
        mock_result.return_value = [
            {  # campaign_queryの結果
                "campaign_code": "CP001",
                "campaign_name": "テストキャンペーン",
            }
        ]
        # クエリ実行の戻り値を設定
        mock_db_exec.return_value = mock_result.return_value

        # コンバーターの実行
        now = datetime.now()
        converter = DB2XMLConverter(
            jobnet_id="test_job",
            etl_config=config,
            db_connection=db_connector,
            last_sync_timestamp=now - timedelta(hours=1),
            diff_base_timestamp=now,
        )
        result = converter.execute_conversion("")

        # 結果の検証
        assert "<?xml" in result  # XMLヘッダーの存在確認
        assert "<promotions>" in result
        assert 'campaign-id="CP001"' in result
        assert "<description>テストキャンペーン</description>" in result

    @patch("source.converter_base.ConverterBase._load_config")
    def test_parent_child_relationship(
        self, mock_load_config: Mock, monkeypatch, mock_aws, db_connector, capsys
    ):
        """基本的な親子関係のテスト

        目的：
        1. SubTreeにおけるMainQueryの使用を検証
           - 親要素でのQueryの定義
           - 子要素は親のデータを参照

        2. 親子要素間のデータ伝播を確認
           - 親のデータが子要素で正しく参照できること
           - 属性値が正しく設定されること"""
        mock_load_config.return_value = {"output": {"root_element": "products"}}
        try:
            # MagicMockを使用したテストデータ
            product_result = MagicMock()
            product_result._mapping = {
                "id": "MDM001",
                "name": "Test Product",
                "updated_datetime": "2024-01-15 00:00:00",
            }

            bundle_data = {
                "id": "BUNDLE001",
                "parent_id": "MDM001",
                "bundle_quantity": 2,
                "updated_datetime": "2024-01-15 00:00:00",
            }

            # テスト用の設定
            config = {
                "output": {"root_element": "products"},
                "etl": {
                    "product_query": "SELECT * FROM product",
                    "bundle_query": "SELECT * FROM bundle WHERE parent_id = :id",
                    "mappings": [
                        {
                            "target_element": "product",
                            "query": "product_query",  # MainQuery
                            "attributes": {"id": "id", "name": "name"},
                            "child_elements": [
                                {
                                    "target_element": "bundle",
                                    "sub_query": "bundle_query",  # sub_queryを使用
                                    "attributes": {
                                        "id": "id",
                                        "quantity": "bundle_quantity",
                                    },
                                }
                            ],
                        }
                    ],
                },
            }

            # 設定ファイルの読み込みをモック
            mock_load_config.return_value = config

            # モックのDB接続を設定
            db_mock = MagicMock()

            def mock_exec(sql, params=None):
                if "product" in str(sql) and "bundle" not in str(sql):
                    return [product_result._mapping]
                elif "bundle" in str(sql):
                    print(
                        f"Bundle query execution with params: {params}"
                    )  # デバッグ出力
                    return [bundle_data]
                return []

            db_mock.exec = mock_exec

            # コンバーターの実行
            converter = DB2XMLConverter(
                jobnet_id="test_job", etl_config=config, db_connection=db_mock
            )

            # 変換を実行
            result = converter.execute_conversion("")

            # XMLの解析
            root = ET.fromstring(result)

            # 親子関係の検証
            products = root.findall("product")
            for product in products:
                # 親要素の検証
                assert "id" in product.attrib
                assert product.get("id") == "MDM001"

                bundles = product.findall("bundle")
                for bundle in bundles:
                    assert bundle.get("id") == "BUNDLE001"
                    assert bundle.get("quantity") == "2"

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

    @patch("source.converter_base.ConverterBase._load_config")
    def test_query_hierarchy_validation(
        self, mock_load_config: Mock, monkeypatch, mock_aws, db_connector, capsys
    ):
        """クエリ階層のバリデーションテスト

        目的：
        1. SubTree内でのquery使用を検出して適切なエラーを発生させる
        2. sub_queryの正しい使用を確認する"""

        # 不正なクエリ階層の設定
        invalid_config = {
            "output": {"root_element": "products"},
            "etl": {
                "product_query": "SELECT * FROM product",
                "bundle_query": "SELECT * FROM bundle",
                "mappings": [
                    {
                        "target_element": "product",
                        "query": "product_query",
                        "child_elements": [
                            {
                                "target_element": "bundle",
                                "query": "bundle_query",  # 不正: SubTree内でqueryを使用
                            }
                        ],
                    }
                ],
            },
        }

        # 正しいクエリ階層の設定
        valid_config = {
            "output": {"root_element": "products"},
            "etl": {
                "product_query": "SELECT * FROM product",
                "bundle_query": "SELECT * FROM bundle",
                "mappings": [
                    {
                        "target_element": "product",
                        "query": "product_query",
                        "child_elements": [
                            {
                                "target_element": "bundle",
                                "sub_query": "bundle_query",  # 正しい: sub_queryを使用
                            }
                        ],
                    }
                ],
            },
        }

        # 不正なケースのテスト
        mock_load_config.return_value = invalid_config
        with pytest.raises(
            ValueError, match="SubTree内でのquery使用は禁止されています"
        ):
            DB2XMLConverter(
                jobnet_id="test_job",
                etl_config=invalid_config,
                db_connection=db_connector,
            )

        # 正しいケースのテスト
        mock_load_config.return_value = valid_config
        try:
            converter = DB2XMLConverter(
                jobnet_id="test_job",
                etl_config=valid_config,
                db_connection=db_connector,
            )
        except ValueError as e:
            pytest.fail(f"正しい設定でエラーが発生: {str(e)}")

    @patch("source.converter_base.ConverterBase._load_config")
    def test_nested_structure(
        self, mock_load_config: Mock, monkeypatch, mock_aws, db_connector, capsys
    ):
        """複数レベルの入れ子構造のテスト

        目的：
        1. 複数レベルのSubTreeにおけるQuery伝播を検証
           - 各階層でのMainQuery定義
           - 上位階層から下位階層へのデータ伝播

        2. 深い階層でのデータ参照を確認
           - product → bundle → optionの3階層構造
           - 各階層での属性値の設定
           - 最下層での要素値（price）の設定"""
        try:
            # テストデータ
            product_data = {"id": "P001", "name": "Product"}
            bundle_data = {"id": "B001", "product_id": "P001", "name": "Bundle"}
            option_data = {"id": "O001", "bundle_id": "B001", "price": 1000}

            # テスト用の設定
            config = {
                "output": {"root_element": "products"},
                "etl": {
                    "product_query": "SELECT * FROM product",
                    "bundle_query": "SELECT * FROM bundle WHERE product_id = :id",
                    "option_query": "SELECT * FROM option WHERE bundle_id = :id",
                    "mappings": [
                        {
                            "target_element": "product",
                            "query": "product_query",  # MainQuery
                            "attributes": {"id": "id", "name": "name"},
                            "child_elements": [
                                {
                                    "target_element": "bundle",
                                    "sub_query": "bundle_query",  # sub_queryを使用
                                    "attributes": {"id": "id", "name": "name"},
                                    "child_elements": [
                                        {
                                            "target_element": "option",
                                            "sub_query": "option_query",  # sub_queryを使用
                                            "attributes": {"id": "id"},
                                            "source_column": "price",
                                        }
                                    ],
                                }
                            ],
                        }
                    ],
                },
            }

            # 設定ファイルの読み込みをモック
            mock_load_config.return_value = config

            # モックのDB接続を設定
            db_mock = MagicMock()

            def mock_exec(sql, params=None):
                if "product" in str(sql) and "bundle" not in str(sql):
                    return [product_data]
                elif "bundle" in str(sql) and "option" not in str(sql):
                    return [bundle_data]
                elif "option" in str(sql):
                    return [option_data]
                return []

            db_mock.exec = mock_exec

            # コンバーターの実行
            converter = DB2XMLConverter(
                jobnet_id="test_job", etl_config=config, db_connection=db_mock
            )

            # 変換を実行
            result = converter.execute_conversion("")
            root = ET.fromstring(result)

            # 階層構造の検証
            product = root.find("product")
            assert product is not None, "製品要素が存在しない"
            assert product.get("id") == "P001"

            bundle = product.find("bundle")
            assert bundle is not None, "バンドル要素が存在しない"
            assert bundle.get("id") == "B001"

            option = bundle.find("option")
            assert option is not None, "オプション要素が存在しない"
            assert option.get("id") == "O001"
            assert option.text == "1000"

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

    @patch("source.converter_base.ConverterBase._load_config")
    def test_multiple_children(
        self, mock_load_config: Mock, monkeypatch, mock_aws, db_connector, capsys
    ):
        """同一レベルでの複数の子要素のテスト

        目的：
        1. 同一レベルでの複数の子要素の処理を検証
           - order → customer, items という構造
           - 各子要素が独立して正しく生成されること
        """
        # YAML設定のモック
        config = {
            "output": {"root_element": "orders", "encoding": "utf-8"},
            "etl": {
                "order_query": "SELECT * FROM orders",
                "customer_query": "SELECT * FROM customers WHERE order_id = :id",
                "item_query": "SELECT * FROM items WHERE order_id = :id",
                "mappings": [
                    {
                        "target_element": "orders",
                        "child_elements": [
                            {
                                "target_element": "order",
                                "query": "order_query",
                                "attributes": {"order-id": "id"},
                                "child_elements": [
                                    {
                                        "target_element": "customer",
                                        "sub_query": "customer_query",
                                        "attributes": {"customer-id": "id"},
                                    },
                                    {
                                        "target_element": "items",
                                        "child_elements": [
                                            {
                                                "target_element": "item",
                                                "sub_query": "item_query",
                                                "attributes": {"item-id": "id"},
                                            }
                                        ],
                                    },
                                ],
                            }
                        ],
                    }
                ],
            },
        }
        mock_load_config.return_value = config

        # テストデータの準備
        order_data = {"id": "ORD001", "order_date": "2024-01-15 10:00:00"}
        customer_data = {"id": "CUST001", "order_id": "ORD001", "name": "Test Customer"}
        item_data = [
            {"id": "ITEM001", "order_id": "ORD001", "name": "Product 1"},
            {"id": "ITEM002", "order_id": "ORD001", "name": "Product 2"},
        ]

        # DBコネクタのモック化
        db_mock = MagicMock()

        def mock_exec(sql, params=None):
            if "orders" in str(sql):
                return [order_data]
            elif "customers" in str(sql):
                return [customer_data]
            elif "items" in str(sql):
                return item_data
            return []

        db_mock.exec = mock_exec

        try:
            # コンバーターの実行
            converter = DB2XMLConverter(
                jobnet_id="test_job", etl_config=config, db_connection=db_mock
            )

            # 変換を実行
            result = converter.execute_conversion("")
            root = ET.fromstring(result)

            # XML構造の検証
            # 同一レベルの複数子要素の検証
            orders = root.findall(".//order")
            for order in orders:
                assert order.get("order-id") == "ORD001"
                assert "order-id" in order.attrib

                # 顧客情報（子要素1）
                customer = order.find("customer")
                assert customer is not None
                assert customer.get("customer-id") == "CUST001"

                # 商品情報（子要素2）
                items = order.find("items")
                assert items is not None
                item_list = items.findall("item")
                assert len(item_list) == 2
                for item in item_list:
                    assert "item-id" in item.attrib

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

    @patch("source.converter_base.ConverterBase._load_config")
    def test_parameter_inheritance(self, mock_load_config: Mock, db_connector):
        """パラメータの継承処理のテスト

        目的：
        1. 親子要素間のパラメータ継承を検証
           - 親レコードのデータが子クエリのパラメータとして使用されること
           - 親のparent_idが子クエリのWHERE句で使用されること

        2. データの整合性を確認
           - 親子関係が正しく維持されること
           - 継承されたパラメータ値が期待通りであること"""
        try:
            # テスト用の設定
            config = {
                "output": {"root_element": "root"},
                "etl": {
                    "query1": "SELECT * FROM table1",
                    "query2": "SELECT * FROM table2 WHERE parent_id = :parent_id",
                    "mappings": [
                        {
                            "target_element": "parent",
                            "query": "query1",
                            "child_elements": [
                                {"target_element": "child", "sub_query": "query2"}
                            ],
                        }
                    ],
                },
            }

            # 設定ファイルの読み込みをモック
            mock_load_config.return_value = config

            # テストデータ
            parent_data = {"parent_id": "PARENT001", "name": "Parent Name"}
            child_data = {"child_id": "CHILD001", "parent_id": "PARENT001"}

            # モックDBコネクタ
            def mock_exec(sql, params):
                if "table1" in str(sql):
                    return [parent_data]
                elif "table2" in str(sql):
                    assert "parent_id" in params, "親パラメータが継承されていない"
                    assert params["parent_id"] == "PARENT001", "親パラメータの値が不正"
                    return [child_data]
                return []

            db_connector.exec = mock_exec

            # 変換処理の実行
            converter = DB2XMLConverter(
                jobnet_id="test_job",
                etl_config=config,
                db_connection=db_connector,
            )

            result = converter.execute_conversion("")
            assert result is not None, "変換結果がNULL"

            # 生成されたXMLの検証
            root = ET.fromstring(result)
            parent = root.find(".//parent")
            assert parent is not None, "親要素が生成されていない"
            child = parent.find(".//child")
            assert child is not None, "子要素が生成されていない"

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

    @patch("source.converter_base.ConverterBase._load_config")
    def test_direct_converter(self, mock_load_config: Mock, db_connector):
        """DB2XMLConverterの直接テスト（DBアクセスなし）

        目的：
        1. コンバーターの基本機能を独立して検証
           - 設定ファイルの読み込み
           - モックデータを使用した変換処理
           - XML要素の生成

        2. DBアクセスを分離したユニットテスト環境の確認"""
        try:
            # テスト用の設定
            config = {
                "output": {"root_element": "root"},
                "etl": {
                    "query1": "SELECT * FROM table1",
                    "query2": "SELECT * FROM table2",
                    "mappings": [
                        {
                            "target_element": "parent",
                            "query": "query1",
                            "child_elements": [
                                {"target_element": "child", "sub_query": "query2"}
                            ],
                        }
                    ],
                },
            }

            # 設定ファイルの読み込みをモック
            mock_load_config.return_value = config

            # モックデータ
            parent_data = {"id": "P001", "name": "Parent"}
            child_data = {"id": "C001", "parent_id": "P001", "name": "Child"}

            # モックのDB接続を設定
            db_mock = MagicMock()

            def mock_exec(sql, params=None):
                if "table1" in str(sql):
                    return [parent_data]
                elif "table2" in str(sql):
                    return [child_data]
                return []

            db_mock.exec = mock_exec

            # コンバーターの実行
            converter = DB2XMLConverter(
                jobnet_id="test_job", etl_config=config, db_connection=db_mock
            )

            # 変換を実行
            result = converter.execute_conversion("")
            root = ET.fromstring(result)

            # 結果の検証
            parent = root.find(".//parent")
            assert parent is not None, "親要素が存在しない"
            child = parent.find(".//child")
            assert child is not None, "子要素が存在しない"

        except Exception as e:
            print(f"Test error: {str(e)}")
            raise

    @patch("source.converter_base.ConverterBase._load_config")
    def test_validation_errors(self, mock_load_config, db_connector):
        """パラメータバリデーションのテスト

        目的：
        1. XML要素名のバリデーションを検証
           - 不正な文字を含む要素名
           - XMLの命名規則に違反する要素名

        2. 設定ファイルの必須項目チェック
           - マッピング定義の存在確認
           - 適切なエラーメッセージの生成"""
        error_cases = [
            {
                "name": "無効な要素名",
                "config": {
                    "output": {"root_element": "invalid<name>"},
                    "etl": {
                        "query1": "SELECT * FROM table1",
                        "mappings": [{"target_element": "element", "query": "query1"}],
                    },
                },
                "expected_error": "無効なルート要素名",
            },
            {
                "name": "マッピング定義なし",
                "config": {
                    "output": {"root_element": "root"},
                    "etl": {"query1": "SELECT * FROM table1"},
                },
                "expected_error": "マッピング定義が未定義",
            },
        ]

        for case in error_cases:
            mock_load_config.return_value = case["config"]
            with pytest.raises(ValueError, match=case["expected_error"]):
                DB2XMLConverter(
                    jobnet_id="test_job",
                    etl_config=case["config"],
                    db_connection=db_connector,
                )

    @patch("source.converter_base.ConverterBase._load_config")
    def test_error_cases(
        self, mock_load_config: Mock, monkeypatch, mock_aws, db_connector, capsys
    ):
        """エラーケースのテスト

        目的：
        1. 循環参照の検出機能を検証
           - SubTree内でのクエリの循環参照
           - 適切なエラーメッセージの生成

        2. クエリ参照の整合性チェック
           - 未定義クエリの参照検出
           - エラー処理の適切な動作
           - 例外発生時の状態管理"""
        error_cases = [
            {
                "name": "循環参照検出",
                "config": {
                    "output": {"root_element": "root"},
                    "etl": {
                        "query1": "SELECT * FROM table1",
                        "query2": "SELECT * FROM table2",
                        "mappings": [
                            {
                                "target_element": "parent",
                                "query": "query1",
                                "child_elements": [
                                    {
                                        "target_element": "child",
                                        "sub_query": "query2",
                                        "child_elements": [
                                            {
                                                "target_element": "circular",
                                                "sub_query": "query1",
                                            }
                                        ],
                                    }
                                ],
                            }
                        ],
                    },
                },
                "expected_error": "循環参照を検出",
            },
            {
                "name": "未定義クエリ参照",
                "config": {
                    "output": {"root_element": "root"},
                    "etl": {
                        "query1": "SELECT * FROM table1",
                        "mappings": [
                            {"target_element": "element", "query": "undefined_query"}
                        ],
                    },
                },
                "expected_error": "未定義のクエリを参照",
            },
        ]

        # DB2XMLConverterの直接テスト
        for case in error_cases:
            mock_load_config.return_value = case["config"]
            with pytest.raises(ValueError, match=case["expected_error"]):
                DB2XMLConverter(
                    jobnet_id="test_job",
                    etl_config=case["config"],
                    db_connection=db_connector,
                )

    @patch("source.db_connector.DbConnector.exec")
    @patch("source.converter_base.ConverterBase._load_config")
    def test_glue_error_cases(
        self, mock_db_exec: Mock, mock_load_config: Mock, db_connector
    ):
        """Glueジョブのエラーケースのテスト"""
        glue_error_cases = [
            {
                "name": "親が存在しない子データ",
                "query": "etl_PR003-DF01_error_orphan",
                "expected_error": "Parent record not found",
            },
            {
                "name": "不正な階層構造",
                "query": "etl_PR003-DF01_error_structure",
                "expected_error": "Invalid hierarchy structure",
            },
            {
                "name": "循環参照",
                "query": "etl_PR003-DF01_error_circular",
                "expected_error": "Circular reference detected",
            },
        ]

        for case in glue_error_cases:
            # 基本設定
            config = {
                "output": {"root_element": "products"},
                "etl": {
                    "product_query": "SELECT * FROM product",
                    "bundle_query": "SELECT * FROM bundle WHERE parent_id = :id",
                    "mappings": [
                        {
                            "target_element": "product",
                            "query": "product_query",
                            "child_elements": [
                                {
                                    "target_element": "bundle",
                                    "sub_query": "bundle_query",
                                }
                            ],
                        }
                    ],
                },
            }
            mock_load_config.return_value = config

            if "orphan" in case["query"].lower():
                # 親レコードが存在しないケース
                def mock_exec_orphan(sql, params=None):
                    if "product" in str(sql):
                        return [{"id": "P001", "name": "Product"}]
                    elif "bundle" in str(sql):
                        raise ValueError("Parent record not found")
                    return []

                mock_db_exec.side_effect = mock_exec_orphan

            elif "structure" in case["query"].lower():

                def mock_exec_structure(sql, params=None):
                    if "product" in str(sql):
                        return [{"id": "P001", "name": "Product"}]
                    elif "bundle" in str(sql):
                        raise ValueError("Invalid hierarchy structure")
                    return []

                mock_db_exec.side_effect = mock_exec_structure

            elif "circular" in case["query"].lower():

                def mock_exec_circular(sql, params=None):
                    raise ValueError("Circular reference detected")

                mock_db_exec.side_effect = mock_exec_circular

            with pytest.raises(Exception) as excinfo:
                converter = DB2XMLConverter(
                    jobnet_id="test_job",
                    etl_config=config,
                    db_connection=db_connector,
                )
                converter.execute_conversion("")
                # エラーメッセージの検証
                assert case["expected_error"] in str(excinfo.value)

            db_mock = MagicMock()
