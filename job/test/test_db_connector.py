import pytest
from unittest.mock import patch, MagicMock
from source.db_connector import <PERSON><PERSON><PERSON>onnector
from source.glue_logger import G<PERSON><PERSON>ogger
import json
from botocore.exceptions import ClientError
from sqlalchemy.exc import OperationalError


@pytest.fixture
def mock_logger():
    return MagicMock(spec=GlueLogger)


@pytest.fixture
def mock_boto3_client():
    with patch('boto3.client') as mock_client:
        yield mock_client


def test_db_connector_initialization_success(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)

    assert db_connector.secret_id == secret_id
    assert db_connector.username == 'test_user'
    assert db_connector.password == 'test_password'
    assert db_connector.dbname == 'test_db'
    assert db_connector.host == 'test_host'
    assert db_connector.port == '5432'
    assert db_connector.logger == mock_logger

    mock_boto3_client.assert_called_once_with('secretsmanager')
    mock_secrets_client.get_secret_value.assert_called_once_with(SecretId=secret_id)
    mock_logger.debug.assert_called_with("db_host_name: [test_host], db_port: [5432], db_name: [test_db]")


def test_db_connector_initialization_empty_secret_string(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': ''
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    with pytest.raises(json.JSONDecodeError):
        DbConnector(mock_logger, secret_id)

    mock_boto3_client.assert_called_once_with('secretsmanager')
    mock_secrets_client.get_secret_value.assert_called_once_with(SecretId=secret_id)
    mock_logger.error_common.assert_any_call("DBコネクション初期化が失敗しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")


def test_db_connector_initialization_failure(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.side_effect = Exception("Test exception")
    mock_boto3_client.return_value = mock_secrets_client

    with pytest.raises(Exception) as excinfo:
        DbConnector(mock_logger, secret_id)

    assert "Test exception" in str(excinfo.value)
    mock_logger.error_common.assert_any_call("DBコネクション初期化が失敗しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    mock_boto3_client.assert_called_once_with('secretsmanager')
    mock_secrets_client.get_secret_value.assert_called_once_with(SecretId=secret_id)


def test_db_connector_password_masking(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'secret_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    DbConnector(mock_logger, secret_id)

    mock_logger.debug.assert_any_call("db_user_name: [test_user], db_password: [**********]")
    for call in mock_logger.debug.call_args_list:
        assert "db_password: [secret_password]" not in call[0][0]


def test_db_connector_initialization_missing_fields(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            # 'dbname' is missing
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)

    assert db_connector.secret_id == secret_id
    assert db_connector.username == 'test_user'
    assert db_connector.password == 'test_password'
    assert db_connector.dbname is None
    assert db_connector.host == 'test_host'
    assert db_connector.port == '5432'
    assert db_connector.logger == mock_logger

    mock_boto3_client.assert_called_once_with('secretsmanager')
    mock_secrets_client.get_secret_value.assert_called_once_with(SecretId=secret_id)
    mock_logger.debug.assert_any_call("db_user_name: [test_user], db_password: [**********]")
    mock_logger.debug.assert_any_call("db_host_name: [test_host], db_port: [5432], db_name: [None]")


def test_db_connector_parse_and_store_connection_params(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)

    assert db_connector.secret_id == secret_id
    assert db_connector.username == 'test_user'
    assert db_connector.password == 'test_password'
    assert db_connector.dbname == 'test_db'
    assert db_connector.host == 'test_host'
    assert db_connector.port == '5432'
    assert db_connector.logger == mock_logger

    mock_boto3_client.assert_called_once_with('secretsmanager')
    mock_secrets_client.get_secret_value.assert_called_once_with(SecretId=secret_id)
    mock_logger.debug.assert_any_call("db_user_name: [test_user], db_password: [**********]")
    mock_logger.debug.assert_any_call("db_host_name: [test_host], db_port: [5432], db_name: [test_db]")


def test_db_connector_creates_correct_boto3_client(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_boto3_client.return_value = mock_secrets_client

    DbConnector(mock_logger, secret_id)

    mock_boto3_client.assert_called_once_with('secretsmanager')


def test_db_connector_initialization_network_error(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.side_effect = ClientError(
        {'Error': {'Code': 'NetworkError', 'Message': 'A network error occurred'}},
        'GetSecretValue'
    )
    mock_boto3_client.return_value = mock_secrets_client

    with pytest.raises(ClientError) as excinfo:
        DbConnector(mock_logger, secret_id)

    assert "A network error occurred" in str(excinfo.value)
    mock_logger.error_common.assert_any_call("DBコネクション初期化が失敗しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    mock_boto3_client.assert_called_once_with('secretsmanager')
    mock_secrets_client.get_secret_value.assert_called_once_with(SecretId=secret_id)


def test_db_connector_sets_logger(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)

    assert db_connector.logger == mock_logger


def test_db_connector_connect_success(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    with patch('source.db_connector.create_engine') as mock_create_engine, \
         patch('source.db_connector.scoped_session') as mock_scoped_session:
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        mock_session = MagicMock()
        mock_scoped_session.return_value = mock_session

        db_connector = DbConnector(mock_logger, secret_id)
        db_connector.connect()

        expected_database_url = "***************************************************/test_db"
        mock_create_engine.assert_called_once_with(
            expected_database_url,
            echo=True,
            connect_args={"connect_timeout": -1, 'client_encoding': 'utf8'}
        )
        mock_scoped_session.assert_called_once()
        assert db_connector.db == mock_session


def test_db_connector_connect_unreachable_database(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'unreachable_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    with patch('source.db_connector.create_engine') as mock_create_engine:
        error = OperationalError(
            statement="SELECT 1",
            params={},
            orig=Exception("Could not connect to the database")
        )
        mock_create_engine.side_effect = error
        db_connector = DbConnector(mock_logger, secret_id)

        with pytest.raises(OperationalError) as excinfo:
            db_connector.connect()

        assert "Could not connect to the database" in str(excinfo.value)

    expected_database_url = "**********************************************************/test_db"
    mock_create_engine.assert_called_once_with(
        expected_database_url,
        echo=True,
        connect_args={"connect_timeout": -1, 'client_encoding': 'utf8'}
    )


def test_db_connector_connect_cleanup_on_exception(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    with patch('source.db_connector.create_engine') as mock_create_engine, \
         patch('source.db_connector.scoped_session') as mock_scoped_session:
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        mock_scoped_session.side_effect = Exception("Test exception during connection")

        db_connector = DbConnector(mock_logger, secret_id)

        with pytest.raises(Exception) as excinfo:
            db_connector.connect()

        assert "Test exception during connection" in str(excinfo.value)
        mock_create_engine.assert_called_once()
        mock_scoped_session.assert_called_once()
        assert not hasattr(db_connector, 'db')


def test_db_connector_exec_invalid_sql(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.execute.side_effect = Exception("Invalid SQL syntax")

    invalid_sql = "INVALID SQL STATEMENT"

    with pytest.raises(Exception) as excinfo:
        db_connector.exec(invalid_sql)

    assert "Invalid SQL syntax" in str(excinfo.value)
    mock_logger.debug.assert_called_with("sql: [INVALID SQL STATEMENT]")
    mock_logger.error_common.assert_any_call("SQL文の実行が失敗しました。sql:[INVALID SQL STATEMENT]")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.execute.assert_called_once()


def test_db_connector_close_success(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()

    db_connector.close()


def test_db_connector_close_exception(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.close.side_effect = Exception("Test close exception")

    with pytest.raises(Exception) as excinfo:
        db_connector.close()

    assert "Test close exception" in str(excinfo.value)
    mock_logger.error_common.assert_any_call("DBコネクションを閉じる時にエラーが発生しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.close.assert_called_once()


def test_db_connector_exec_with_valid_sql_no_values(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()

    sql_template = "SELECT * FROM test_table"
    expected_result = MagicMock()
    db_connector.db.execute.return_value = expected_result

    result = db_connector.exec(sql_template)

    assert result == expected_result
    mock_logger.debug.assert_called_with("sql: [%s]" % sql_template)
    db_connector.db.execute.assert_called_once()


def test_db_connector_exec_with_valid_sql_and_values(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()

    sql_template = "SELECT * FROM users WHERE id = :id"
    values = {"id": 1}

    mock_result = MagicMock()
    db_connector.db.execute.return_value = mock_result

    result = db_connector.exec(sql_template, values)

    assert result == mock_result
    db_connector.db.execute.assert_called_once()
    mock_logger.debug.assert_any_call("sql: [SELECT * FROM users WHERE id = :id]")
    mock_logger.debug.assert_any_call("params: {'id': 1}")


def test_db_connector_exec_invalid_sql_2(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.execute.side_effect = Exception("Invalid SQL syntax")

    invalid_sql = "INVALID SQL STATEMENT"

    with pytest.raises(Exception) as excinfo:
        db_connector.exec(invalid_sql)

    assert "Invalid SQL syntax" in str(excinfo.value)
    mock_logger.debug.assert_called_with("sql: [INVALID SQL STATEMENT]")
    mock_logger.error_common.assert_any_call("SQL文の実行が失敗しました。sql:[INVALID SQL STATEMENT]")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.execute.assert_called_once()


def test_db_connector_begin_success(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    with patch('source.db_connector.create_engine') as mock_create_engine, \
         patch('source.db_connector.scoped_session') as mock_scoped_session:
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        mock_session = MagicMock()
        mock_scoped_session.return_value = mock_session

        db_connector = DbConnector(mock_logger, secret_id)
        db_connector.connect()
        db_connector.begin()

        mock_session.begin.assert_called_once()


def test_db_connector_begin_exception_handling(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.begin.side_effect = Exception("Test exception during begin")

    with pytest.raises(Exception) as excinfo:
        db_connector.begin()

    assert "Test exception during begin" in str(excinfo.value)
    mock_logger.error_common.assert_any_call("DBコネクションを開始時にエラーが発生しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.begin.assert_called_once()


def test_rollback_success(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()

    db_connector.rollback()

    db_connector.db.rollback.assert_called_once()
    mock_logger.error_common.assert_not_called()


def test_rollback_failure(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.rollback.side_effect = Exception("Rollback failed")

    with pytest.raises(Exception) as excinfo:
        db_connector.rollback()

    assert "Rollback failed" in str(excinfo.value)
    mock_logger.error_common.assert_any_call("DBコネクションをロールバックする時にエラーが発生しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.rollback.assert_called_once()


def test_db_connector_rollback_failure(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.rollback.side_effect = Exception("Rollback failed")

    with pytest.raises(Exception) as excinfo:
        db_connector.rollback()

    assert str(excinfo.value) == "Rollback failed"
    mock_logger.error_common.assert_any_call("DBコネクションをロールバックする時にエラーが発生しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.rollback.assert_called_once()


def test_db_connector_commit_success(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()

    db_connector.commit()

    db_connector.db.commit.assert_called_once()
    mock_logger.error_common.assert_not_called()


def test_db_connector_commit_exception(mock_logger, mock_boto3_client):
    secret_id = "test_secret_id"
    mock_secret_response = {
        'SecretString': json.dumps({
            'username': 'test_user',
            'password': 'test_password',
            'dbname': 'test_db',
            'host': 'test_host',
            'port': '5432'
        })
    }
    mock_secrets_client = MagicMock()
    mock_secrets_client.get_secret_value.return_value = mock_secret_response
    mock_boto3_client.return_value = mock_secrets_client

    db_connector = DbConnector(mock_logger, secret_id)
    db_connector.db = MagicMock()
    db_connector.db.commit.side_effect = Exception("Commit failed")

    with pytest.raises(Exception) as excinfo:
        db_connector.commit()

    assert "Commit failed" in str(excinfo.value)
    mock_logger.error_common.assert_any_call("DBコネクションをコミットする時にエラーが発生しました。")
    mock_logger.error_common.assert_any_call("共通処理DbConnectorで異常終了しました。")
    db_connector.db.commit.assert_called_once()
