import pytest
import os
import sys
import json
from source.glue_job_convert_character_encoding import (
    GlueJobConvertCharacterEncoding,
    get_params,
    main,
)
from unittest.mock import patch, MagicMock


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]


@pytest.fixture
def glue_job():
    """GlueJobConvertCharacterEncodingのインスタンス生成"""
    return GlueJobConvertCharacterEncoding("JN_PR002-DD01_001")


def set_data_utf8_to_sjis(glue_job: GlueJobConvertCharacterEncoding, backup_flag: int):
    """
    テスト用データ・パラメータを初期設定（UTF8→SJIS用）
    Args:
        glue_job: ジョブ
    """
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    if backup_flag == 1:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            input_character_encoding,
            "output_character_encoding",
            output_character_encoding,
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_dir",
            output_file_dir,
            "backup_flag",
            True,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    elif backup_flag == 0:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            input_character_encoding,
            "output_character_encoding",
            output_character_encoding,
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_dir",
            output_file_dir,
            "backup_flag",
            False,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    else:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            input_character_encoding,
            "output_character_encoding",
            output_character_encoding,
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_dir",
            output_file_dir,  # backup_flg指定なし
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]


def set_data_sjis_to_utf8(glue_job: GlueJobConvertCharacterEncoding, backup_flag: int):
    """
    テスト用データ・パラメータを初期設定（UTF8→SJIS用）
    Args:
        glue_job: ジョブ
    """
    input_character_encoding = "ms932"
    output_character_encoding = "utf-8"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test2.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    if backup_flag == 1:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            input_character_encoding,
            "output_character_encoding",
            output_character_encoding,
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_dir",
            output_file_dir,
            "backup_flag",
            True,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    elif backup_flag == 0:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            input_character_encoding,
            "output_character_encoding",
            output_character_encoding,
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_dir",
            output_file_dir,
            "backup_flag",
            False,
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    else:
        sys.argv = [
            "glue_job_convert_character_encoding",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "input_character_encoding",
            input_character_encoding,
            "output_character_encoding",
            output_character_encoding,
            "input_file_dir",
            input_file_dir,
            "file_name",
            file_name,
            "output_file_dir",
            output_file_dir,  # backup_flg指定なし
            "backup_file_dir",
            backup_file_dir,
            "jobnet_id",
            glue_job.jobnet_id,
        ]


def set_data_utf8bom_to_sjis(glue_job: GlueJobConvertCharacterEncoding):
    """
    テスト用データ・パラメータを初期設定（UTF8→SJIS用）
    Args:
        glue_job: ジョブ
    """
    input_character_encoding = "utf_8_sig"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test3.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]


def test_1_get_s3_file_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    正常系テスト
    S3ファイル取得が正常に完了することを確認
    """
    input_character_encoding = "utf-8"
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    input_data_encode_expected = "a1,b1,c1\nd1,e1"

    # テスト実行
    file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert (
        file_data["Body"].read().decode(input_character_encoding)
        == input_data_encode_expected
    )


def test_2_get_s3_file_error(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding, capsys
):
    """
    異常系テスト
    S3ファイル取得が失敗（リトライ超過）することを確認
    """
    set_data_utf8_to_sjis(glue_job, 0)

    # テスト実行
    with patch.object(
        glue_job.s3_client, "get_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            # メイン処理実行
            params = get_params()
            glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "S3ファイル取得", "test1.csv")


def test_3_get_s3_file_retry_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    input_character_encoding = "utf-8"
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    input_data_encode_expected = "a1,b1,c1\nd1,e1"

    # テスト実行
    response = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
    )
    with patch.object(
        glue_job.s3_client,
        "get_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回目で成功
        ],
    ):
        file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 確認
    assert (
        file_data["Body"].read().decode(input_character_encoding)
        == input_data_encode_expected
    )


def assert_error(captured, method_name: str, file_name: str):
    assert (
        "[INFO][JN_PR002-DD01_001][I_job_convert_character_encoding_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][JN_PR002-DD01_001][E_job_convert_character_encoding_002]処理で異常が発生しました。(処理名="
        + method_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][JN_PR002-DD01_001][E_job_convert_character_encoding_003]例外発生しました。"
        in captured.out
    )
    assert (
        "[ERROR][JN_PR002-DD01_001][E_job_convert_character_encoding_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][JN_PR002-DD01_001][I_job_convert_character_encoding_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )


def test_4_put_s3_file_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    正常系テスト
    文字コード変換、S3ファイル配置が正常に完了することを確認（UTF-8→SJIS）
    """
    # テストデータ準備
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"
    output_file_expected = "test\ndata"

    # 文字コード変換
    input_data_encode = glue_job.convert_character_encoding(
        input_character_encoding, output_character_encoding, input_file_data
    )
    assert input_data_encode == input_file_data

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir, file_name, input_data_encode)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(output_character_encoding)
            == output_file_expected
        )

    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_5_put_s3_file_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    正常系テスト
    文字コード変換、S3ファイル配置が正常に完了することを確認（SJIS→UTF-8）
    """
    # テストデータ準備
    input_character_encoding = "ms932"
    output_character_encoding = "utf-8"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"
    output_file_expected = "test\ndata"

    # 文字コード変換
    input_data_encode = glue_job.convert_character_encoding(
        input_character_encoding, output_character_encoding, input_file_data
    )
    assert input_data_encode == input_file_data

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir, file_name, input_data_encode)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(output_character_encoding)
            == output_file_expected
        )

    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_6_convert_error(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding, capsys
):
    """
    異常系テスト
    文字コード変換が失敗することを確認
    """
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1_error.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        False,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]

    # テスト実行
    with pytest.raises(SystemExit):
        # メイン処理実行
        params = get_params()
        glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "文字コード変換", file_name)


def test_6_convert_error_sjis_unicode(
    s3_bucket: None,
    glue_job: GlueJobConvertCharacterEncoding,
):
    """
    異常系テスト
    文字コード変換が失敗することを確認
    """
    input_character_encoding = "ms932"
    output_character_encoding = "utf-8"
    input_file_data = b"\x81\xff"

    # テスト実行
    with pytest.raises(UnicodeDecodeError):
        glue_job.convert_character_encoding(
            input_character_encoding, output_character_encoding, input_file_data
        )


def test_6_convert_error_utf8_unicode(
    s3_bucket: None,
    glue_job: GlueJobConvertCharacterEncoding,
):
    """
    異常系テスト
    文字コード変換が失敗することを確認
    """
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_data = b"\xe3\x81"

    # テスト実行
    with pytest.raises(UnicodeDecodeError):
        glue_job.convert_character_encoding(
            input_character_encoding, output_character_encoding, input_file_data
        )


def test_7_put_s3_file_error(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding, capsys
):
    """
    異常系テスト
    S3ファイル配置が失敗（リトライ超過）することを確認
    """
    set_data_utf8_to_sjis(glue_job, 0)

    # テスト実行
    with patch.object(
        glue_job.s3_client, "put_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            # メイン処理実行
            params = get_params()
            glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "S3ファイル配置", "test1.csv")


def test_8_put_s3_file_retry_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding, capsys
):
    """
    準正常系テスト
    S3ファイル配置がリトライして成功することを確認
    """
    output_character_encoding = "ms932"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test.csv"
    input_file_data = b"test\ndata"
    output_file_expected = "test\ndata"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "put_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.put_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=output_file_dir + file_name,
                    Body=input_file_data,
                ),  # 3回目で成功
            ],
        ):
            glue_job.put_s3_file(output_file_dir, file_name, input_file_data)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(output_character_encoding)
            == output_file_expected
        )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_PR002-DD01_001][I_job_convert_character_encoding_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_9_backup_input_file_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    正常系テスト
    インプットファイルバックアップが正常に完了することを確認
    """
    # テストデータ準備
    intput_character_encoding = "utf-8"
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    input_data_expected = "a1,b1,c1\nd1,e1"

    try:
        # S3ファイル配置
        glue_job.backup_input_file(input_file_dir, backup_file_dir, file_name)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(intput_character_encoding)
            == input_data_expected
        )

    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )


def test_9_13_main_backup_true(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグTrueで正常終了することを確認
    """
    execute(glue_job, 1)


def test_10_14_main_backup_false(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグFalseで正常終了することを確認
    """
    execute(glue_job, 0)


def test_11_backup_error(
    glue_job: GlueJobConvertCharacterEncoding, s3_bucket: None, capsys
):
    """
    異常系テスト
    バックアップで異常終了することを確認
    """
    set_data_utf8_to_sjis(glue_job, 1)
    with patch.object(
        glue_job.s3_client, "copy_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            params = get_params()
            glue_job.execute(params)

    # バックアップされていないことを確認
    with pytest.raises(Exception):
        glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=params["backup_file_dir"] + params["file_name"],
        )

    # S3ファイル配置したものを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=params["output_file_dir"] + params["file_name"],
    )

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイルバックアップ", "test1.csv")


def test_12_backup_retry_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding, capsys
):
    """
    準正常系テスト
    インプットファイルバックアップがリトライして成功することを確認
    """
    input_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test2.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    output_file_expected = "あ,い,う\r\nえ,髙"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "copy_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.copy_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + file_name,
                    CopySource={
                        "Bucket": os.environ["S3_BUCKET_NAME"],
                        "Key": input_file_dir + file_name,
                    },
                ),  # 3回目で成功
            ],
        ):
            glue_job.backup_input_file(input_file_dir, backup_file_dir, file_name)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(input_character_encoding)
            == output_file_expected
        )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_PR002-DD01_001][I_job_convert_character_encoding_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )


def test_15_delete_error(
    glue_job: GlueJobConvertCharacterEncoding, s3_bucket: None, capsys
):
    """
    異常系テスト
    インプットファイル削除で異常終了することを確認
    """
    set_data_sjis_to_utf8(glue_job, 1)
    with patch.object(
        glue_job.s3_client, "delete_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            params = get_params()
            glue_job.execute(params)

    # インプットファイル削除されていないことを確認
    input_character_encoding = params["input_character_encoding"]
    input_file_dir = params["input_file_dir"]
    file_name = params["file_name"]
    output_file_expected = "あ,い,う\r\nえ,髙"
    response = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=input_file_dir + file_name,
    )
    assert (
        response["Body"].read().decode(input_character_encoding) == output_file_expected
    )

    # S3ファイル配置したものを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"],
        Key=params["output_file_dir"] + file_name,
    )
    # バックアップしたファイルを削除
    glue_job.s3_client.delete_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=params["backup_file_dir"] + file_name
    )

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイル削除", "test2.csv")


def test_16_delete_retry_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding, capsys
):
    """
    準正常系テスト
    インプットファイル削除がリトライして成功することを確認
    """
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "delete_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.delete_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=input_file_dir + file_name,
                ),  # 3回目で成功
            ],
        ):
            glue_job.delete_input_file(input_file_dir, file_name)

        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_PR002-DD01_001][I_job_convert_character_encoding_003]処理をリトライしました。(処理名=インプットファイル削除)"
            in captured.out
        )
    finally:
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file_dir + file_name,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": "saito/back-up/" + file_name,
            },
        )


def test_17_main_backup_none(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグ指定なしで正常終了することを確認
    """
    execute(glue_job, 2)


def test_17_main_backup_none(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグ指定なしで正常終了することを確認
    """
    execute(glue_job, 2)


def test_17_main_backup_none2(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    バックアップフラグ指定なしで正常終了することを確認（main呼び出し）
    """
    set_data_utf8_to_sjis(glue_job, 2)
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    backup_file_dir = "saito/back-up/OMS_IN/"
    file_name = "test1.csv"

    try:
        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client

            main()

            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
                )
    finally:
        # S3ファイル配置したものを削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file_dir + file_name,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": "saito/back-up/" + file_name,
            },
        )


def test_18_put_s3_file_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    正常系テスト
    文字コード変換、S3ファイル配置が正常に完了することを確認（UTF-8BOM→SJIS）
    """
    # テストデータ準備
    input_character_encoding = "utf_8_sig"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test3.csv"
    output_file_expected = "a1,b1,c1\nd1,e1"

    # S3ファイル取得
    file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 文字コード変換
    input_data_encode = glue_job.convert_character_encoding(
        input_character_encoding, output_character_encoding, file_data["Body"].read()
    )

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir, file_name, input_data_encode)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(output_character_encoding)
            == output_file_expected
        )

    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def test_19_put_s3_file_success(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    """
    正常系テスト
    文字コード変換、S3ファイル配置が正常に完了することを確認（SJIS→UTF-8BOM）
    """
    # テストデータ準備
    input_character_encoding = "ms932"
    output_character_encoding = "utf_8_sig"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test2.csv"
    output_file_expected = "あ,い,う\r\nえ,髙"

    # S3ファイル取得
    file_data = glue_job.get_s3_file(input_file_dir, file_name)

    # 文字コード変換
    input_data_encode = glue_job.convert_character_encoding(
        input_character_encoding, output_character_encoding, file_data["Body"].read()
    )

    try:
        # S3ファイル配置
        glue_job.put_s3_file(output_file_dir, file_name, input_data_encode)

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )
        assert (
            response["Body"].read().decode(output_character_encoding)
            == output_file_expected
        )

    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
        )


def execute(glue_job: GlueJobConvertCharacterEncoding, backup_flag_mode: int, output_file_name =""):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data_utf8_to_sjis(glue_job, backup_flag_mode)
    output_data_expected = "a1,b1,c1\nd1,e1"
    put_done = False
    backup_done = False
    input_file_dir = None
    file_name = None
    backup_file_dir = None
    output_file_name = None
    try:
        # メイン処理実行
        params = get_params()
        input_character_encoding = params["input_character_encoding"]
        output_character_encoding = params["output_character_encoding"]
        input_file_dir = params["input_file_dir"]
        output_file_dir = params["output_file_dir"]
        file_name = params["file_name"]
        output_file_name = params["output_file_name"]
        backup_file_dir = params["backup_file_dir"]
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        glue_job.execute(params)

        # S3ファイル配置されたか確認
        if output_file_name:
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + output_file_name
            )
        else:
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
            )
            put_done = True
        assert (
            response["Body"].read().decode(output_character_encoding)
            == output_data_expected
        )

        if backup_flag:
            # バックアップされたか確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
            backup_done = True
            assert (
                response["Body"].read().decode(input_character_encoding)
                == output_data_expected
            )
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
                )

        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
    finally:
        if put_done:
            # S3ファイル配置したものを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=output_file_dir + file_name
            )
        if backup_done:
            # バックアップしたファイルをインプットディレクトリに複製（次のテストで利用できるように）
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": backup_file_dir + file_name,
                },
            )
            # バックアップしたファイルを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
        else:
            # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": "saito/back-up/" + file_name,
                },
            )


def test_20_value_error_input_character_encoding(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    # input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        # "input_character_encoding",
        # input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert (
            str(exc_info.value)
            == "Required parameter 'input_character_encoding' is missing"
        )


def test_21_value_error_output_character_encoding(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    input_character_encoding = "utf-8"
    # output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        # "output_character_encoding",
        # output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert (
            str(exc_info.value)
            == "Required parameter 'output_character_encoding' is missing"
        )


def test_22_value_error_input_file_dir(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    # input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        # "input_file_dir",
        # input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'input_file_dir' is missing"


def test_23_value_error_file_name(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    # file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        # "file_name",
        # file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'file_name' is missing"


def test_24_value_error_output_file_dir(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    # output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        # "output_file_dir",
        # output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'output_file_dir' is missing"


def test_25_value_error_backup_file_dir(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    # backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        # "backup_file_dir",
        # backup_file_dir,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'backup_file_dir' is missing"


def test_26_value_error_jobnet_id(
    s3_bucket: None, glue_job: GlueJobConvertCharacterEncoding
):
    input_character_encoding = "utf-8"
    output_character_encoding = "ms932"
    input_file_dir = "saito/input-output/OMS_IN/"
    output_file_dir = "saito/input-output/WMS_OUT/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "input_character_encoding",
        input_character_encoding,
        "output_character_encoding",
        output_character_encoding,
        "input_file_dir",
        input_file_dir,
        "file_name",
        file_name,
        "output_file_dir",
        output_file_dir,
        "backup_flag",
        True,
        "backup_file_dir",
        backup_file_dir,
        # "jobnet_id",
        # glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'jobnet_id' is missing"


def test_27_output_file_indicate(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    アウトプットファイル名を指定した場合に出力ファイル名が変更されることを確認
    """
    execute(glue_job, 1, "encoded.csv")


def test_28_output_file_empty(
    glue_job: GlueJobConvertCharacterEncoding,
    s3_bucket: None,
):
    """
    正常系テスト
    アウトプットファイル名を空で指定した場合に出力ファイル名が変更されないことを確認
    """
    execute(glue_job, 1, "")