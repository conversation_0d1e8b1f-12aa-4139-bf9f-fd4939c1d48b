#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
import json
import sys
from unittest.mock import MagicMock
import pytest
from source.glue_job_db_to_file import main
from test.conftest import AWSMockHelper
from test.test_utils import GlueTestUtils
from sqlalchemy.exc import SQLAlchemyError


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    # 同期データの初期設定
    base_time = datetime(
        2025, 2, 14, 14, 0, 0
    )  # 2025-02-14 14:00:00 (テストデータより1時間前)
    target_id = "JN_CP001-DF01_001_test.csv"
    insert_sync_sql = f"""
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, :sync_time,
         :user, :sync_time, :user, :sync_time, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = :sync_time,
        d_updated_user = :user,
        d_updated_datetime = :sync_time,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_CP001-DF01_001",
        "file_name": target_id,  # sync_timestampのfile_nameはfile_idとして使用
        "user": "test_user",
        "sync_time": base_time,  # 前回同期タイムスタンプ
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


def test_main_error_missing_params(monkeypatch, mock_aws, capsys):
    """
    TD-1, TD-18: 必須パラメータの検証

    テスト内容:
    TD-1: 必須パラメータ未設定
    - diff_base_timestamp_queryが未設定の場合のエラー処理
    TD-18: file_id未指定
    - 必須パラメータfile_idが未指定の場合のエラー処理
    """
    # 必須パラメータの一覧
    required_params = [
        "secret_name",
        "execute_query",
        "output_file_dir",
        "file_name",
        "file_type",
        "jobnet_id",
        "diff_base_timestamp_query",
        "file_id",
    ]

    # 各パラメータが欠けているケースをテスト
    for missing_param in required_params:
        # 全パラメータを含むリストを作成
        test_args = ["glue_job_db_to_file.py"]
        for param in required_params:
            if param != missing_param:  # テスト対象のパラメータを除外
                test_args.extend([f"--{param}", "test_value"])

        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured = capsys.readouterr()
        expected_error = (
            f"Error occurred: Required parameter '{missing_param}' is missing"
        )
        assert expected_error in captured.out
        sys.stdout.write(captured.out)  # 出力を再度標準出力に書き戻す


def test_non_existent_secret(monkeypatch, mock_aws, capsys):
    """
    テストケース番号: DB2CSV-001
    シナリオ: 存在しない秘密情報を指定した場合のエラー処理を確認
    """
    try:
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.csv"
        test_file_name = f"test_{timestamp}.csv"
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "non-existent-secret-name",  # 存在しないシークレット名を指定
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured = capsys.readouterr()
        assert (
            f"[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名={test_file_name})"
            in captured.out.split("\n")[
                0
            ]  # タイムスタンプを除外するため、最初の行のみを検証
        )
        assert (
            "[ERROR][JN_CP001-DF01_001]DBコネクション初期化が失敗しました。"
            in captured.out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001]共通処理DbConnectorで異常終了しました。"
            in captured.out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001]DB接続エラー: An error occurred (ResourceNotFoundException)"
            in captured.out
        )
        assert "Secrets Manager can't find the specified secret" in captured.out
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。"
            in captured.out
        )
        assert (
            f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名={test_file_name})"
            in captured.out
        )

    finally:
        pass  # motoのモックは自動的にクリーンアップされる


def test_sync_timestamp_fetch_failure(monkeypatch, mock_aws, db_connector, capsys):
    """
    テストケース番号：DB2CSV-005
    シナリオ：連携先の「前回同期済タイムスタンプ」取得に失敗した場合のエラー処理を確認
    """

    try:
        # DbConnectorクラスのモック
        mock_db = MagicMock()
        mock_db.exec.side_effect = SQLAlchemyError(
            "前回同期済タイムスタンプの取得に失敗しました。"
        )
        mock_db.connect.return_value = None
        # DbConnectorのモックを注入
        monkeypatch.setattr(
            "source.glue_job_db_to_file.DbConnector", lambda *args, **kwargs: mock_db
        )

        # sys.argvのモック
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.csv"
        test_file_name = f"test_{timestamp}.csv"
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)
        assert "[ERROR][JN_CP001-DF01_001]差分基準時刻取得エラー" in captured_out
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。"
            in captured_out
        )
        assert (
            f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名={test_file_name})"
            in captured_out
        )

    finally:
        pass


def test_main_query_execution_failure(monkeypatch, mock_aws, db_connector, capsys):
    """
    テストケース番号：DB2CSV-007
    シナリオ：メインSQLの実行に失敗した場合のエラー処理を確認
    """

    try:
        # DbConnectorクラスのモック
        mock_db = MagicMock()
        mock_db.exec.side_effect = [
            MagicMock(
                fetchone=lambda: [datetime(2025, 2, 14, 15, 0, 0)]
            ),  # 差分基準時刻の取得
            MagicMock(
                fetchone=lambda: [datetime(2025, 2, 14, 14, 0, 0)]
            ),  # 前回同期タイムスタンプの取得
            MagicMock(),  # 1回目は成功
            SQLAlchemyError("メインクエリの実行に失敗しました。"),  # 2回目は失敗
        ]
        monkeypatch.setattr(
            "source.glue_job_db_to_file.DbConnector", lambda *args, **kwargs: mock_db
        )

        # sys.argvのモック
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.csv"
        test_file_name = f"test_{timestamp}.csv"
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        # エラーログの検証
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_002]処理で異常が発生しました。(処理名=同期タイムスタンプ更新)"
            in captured_out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。同期タイムスタンプの更新に失敗しました。"
            in captured_out
        )
        assert "[ERROR][JN_CP001-DF01_001]DBの更新処理に失敗しました。" in captured_out
        error_msg = f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名={test_file_name})"
        assert error_msg in captured_out
    finally:
        pass


def test_sync_timestamp_update_failure(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-14: タイムスタンプ更新処理のDB更新失敗

    テスト内容:
    - DBへの書き込み権限なしを想定
    - 更新用SQLの実行権限なしを想定
    - エラーログが出力されることを確認
    """

    try:
        # DbConnectorクラスのモック
        mock_db = MagicMock()
        # タイムスタンプの設定
        diff_time = datetime(2025, 2, 14, 15, 0, 0)
        sync_time = datetime(2025, 2, 14, 14, 0, 0)

        # モックレスポンスの設定
        mock_diff = MagicMock()
        mock_diff.fetchone.return_value = (diff_time,)
        mock_sync = MagicMock()
        mock_sync.fetchone.return_value = (sync_time,)

        # DBの操作をシミュレート
        mock_db.exec.side_effect = [
            mock_diff,  # 差分基準時刻の取得
            mock_sync,  # 前回同期タイムスタンプの取得
            MagicMock(),  # メインクエリの実行
            SQLAlchemyError(
                "同期タイムスタンプの更新に失敗しました。"
            ),  # タイムスタンプ更新（失敗）
        ]
        mock_db.connect.return_value = None
        monkeypatch.setattr(
            "source.glue_job_db_to_file.DbConnector", lambda *args, **kwargs: mock_db
        )

        # sys.argvのモック
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.csv"
        test_file_name = f"test_{timestamp}.csv"
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_002]処理で異常が発生しました。(処理名=クエリ実行)"
            in captured_out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。"
            in captured_out
        )
        error_msg = f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名={test_file_name})"
        assert error_msg in captured_out

    finally:
        pass


def test_invalid_file_format(monkeypatch, mock_aws, db_connector, capsys):
    """
    テストケース番号：DB2CSV-009
    シナリオ：サポートされていないファイル形式を指定した場合のエラー処理を確認
    """
    try:
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.dat"
        test_file_name = f"test_{timestamp}.dat"
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "invalid_format",  # 不正なフォーマット
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # テスト実行
        from source.glue_job_db_to_file import main

        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        expected_error = "例外発生しました。不正なファイル形式が指定されました。指定可能な形式: csv, tsv, xml, fixed"
        assert (
            f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]{expected_error}"
            in captured_out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。"
            in captured_out
        )
        error_msg = f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名={test_file_name})"
        assert error_msg in captured_out
    finally:
        pass


def test_s3_upload_retry_exceeded(monkeypatch, mock_aws, db_connector, capsys):
    """
    テストケース番号：DB2CSV-014
    シナリオ：異常（S3リトライ回数超過）
    """
    try:
        # モック環境の設定
        monkeypatch.setenv("AWS_DEFAULT_REGION", "ap-northeast-1")

        # Secrets Managerのモック
        mock_secrets_client = MagicMock()
        mock_secrets_client.get_secret_value.return_value = {
            "SecretString": json.dumps(
                {
                    "username": "dlpf_ope",
                    "password": "password",
                    "host": "s2105c10703-t1",
                    "port": "5432",
                    "dbname": "test_db",
                }
            )
        }

        # boto3.clientのモック化
        def mock_boto3_client(service, **kwargs):
            if service == "ssm":
                mock_ssm_client = MagicMock()
                mock_ssm_client.get_parameter.return_value = {
                    "Parameter": {
                        "Value": json.dumps(
                            {
                                "process": {"TZ": "Asia/Tokyo"},
                                "aws": {
                                    "S3_BUCKET_NAME": "test-bucket",
                                    "REGION_NAME": "ap-northeast-1",
                                    "S3_RETRY_LIMIT": "3",
                                    "S3_RETRY_MODE": "standard",
                                },
                            }
                        )
                    }
                }
                return mock_ssm_client
            if service == "secretsmanager":
                return mock_secrets_client
            if service == "s3":
                mock_s3_client = MagicMock()
                mock_s3_client.put_object.side_effect = [
                    Exception("S3アップロードエラー"),  # 1回目は失敗
                    Exception("S3アップロードエラー"),  # 2回目も失敗
                    Exception("S3アップロードエラー"),  # 3回目も失敗
                ]
                return mock_s3_client
            return MagicMock()

        monkeypatch.setattr("boto3.client", mock_boto3_client)

        # テスト引数の設定
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.csv"
        test_file_name = f"test_{timestamp}.csv"
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--file_setting",
            '{"header": true, "quote_char": "\\"", "line_ending": "\\n"}',
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        from source.glue_job_db_to_file import main

        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        # リトライのログが3回出力されていることを確認
        retry_logs = [
            line
            for line in captured_out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_db_to_file_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in line
        ]  # リトライは2回まで（3回目で失敗）
        assert len(retry_logs) == 2

        # エラーログの確認
        assert "[ERROR][JN_CP001-DF01_001]S3ファイル配置エラー" in captured_out

    finally:
        # DBのクリーンアップ
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_s3_upload_retry_success(monkeypatch, mock_aws, db_connector, capsys):
    """
    テストケース番号：DB2CSV-015
    シナリオ：準正常（リトライ後に成功）
    """
    try:
        # モック環境の設定
        monkeypatch.setenv("AWS_DEFAULT_REGION", "ap-northeast-1")

        # Secrets Managerのモック
        mock_secrets_client = MagicMock()
        mock_secrets_client.get_secret_value.return_value = {
            "SecretString": json.dumps(
                {
                    "username": "dlpf_ope",
                    "password": "password",
                    "host": "s2105c10703-t1",
                    "port": "5432",
                    "dbname": "test_db",
                }
            )
        }

        # boto3.clientのモック化
        def mock_boto3_client(service, **kwargs):
            if service == "ssm":
                mock_ssm_client = MagicMock()
                mock_ssm_client.get_parameter.return_value = {
                    "Parameter": {
                        "Value": json.dumps(
                            {
                                "process": {"TZ": "Asia/Tokyo"},
                                "aws": {
                                    "S3_BUCKET_NAME": "test-bucket",
                                    "REGION_NAME": "ap-northeast-1",
                                    "S3_RETRY_LIMIT": "3",
                                    "S3_RETRY_MODE": "standard",
                                },
                            }
                        )
                    }
                }
                return mock_ssm_client
            if service == "secretsmanager":
                return mock_secrets_client
            if service == "s3":
                mock_s3_client = MagicMock()
                mock_s3_client.put_object.side_effect = [
                    Exception("S3アップロードエラー"),  # 1回目は失敗
                    Exception("S3アップロードエラー"),  # 2回目も失敗
                    None,  # 3回目は成功
                ]
                return mock_s3_client
            return MagicMock()

        monkeypatch.setattr("boto3.client", mock_boto3_client)

        # テスト引数の設定
        # テストファイル名とIDの設定
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "test.csv"
        test_file_name = f"test_{timestamp}.csv"
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--file_setting",
            '{"header": true, "quote_char": "\\"", "line_ending": "\\n"}',
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        from source.glue_job_db_to_file import main

        main()  # 今回は例外を期待しないのでSystemExitは使わない

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        # リトライのログが2回出力されていることを確認
        retry_logs = [
            line
            for line in captured_out.split("\n")
            if "[INFO][JN_CP001-DF01_001][I_job_db_to_file_003]処理をリトライしました。(処理名=S3ファイル配置)"
            in line
        ]
        assert len(retry_logs) == 2

        # 正常終了ログの確認
        success_msg = f"[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名={test_file_name})"
        assert success_msg in captured_out

    finally:
        # DBのクリーンアップ
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_error_s3_bucket_not_exists(monkeypatch, mock_aws, db_connector, capsys):
    """S3バケット不存在時のテスト

    テストケース番号：DB2XML-017,DB2CSV-015
    シナリオ：存在しないS3バケットを指定した場合のエラー処理の確認
    """
    try:
        # 既存のS3バケットを削除
        s3_client = mock_aws["s3"]
        s3_client.delete_bucket(Bucket="test-bucket")

        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "test.xml",
            "--file_type",
            "xml",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--file_id",
            "test.xml",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        assert (
            "[ERROR][JN_CP001-DF01_001]S3ファイル配置エラー: An error occurred (NoSuchBucket)"
            in captured_out
        )
        assert "The specified bucket does not exist" in captured_out
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。"
            in captured_out
        )
        error_msg = f"[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名=test.xml)"
        assert error_msg in captured_out

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)
        # S3バケットを再作成（他のテストに影響を与えないため）
        aws_helper = AWSMockHelper()
        aws_helper.setup_s3_bucket(s3_client)
