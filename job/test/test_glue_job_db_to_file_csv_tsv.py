#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
import json
import sys
from unittest.mock import MagicMock
import pytest
from source.glue_job_db_to_file import main
from test.conftest import AWSMockHelper
from test.test_utils import GlueTestUtils
from sqlalchemy.exc import SQLAlchemyError


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    # 同期データの初期設定
    base_time = datetime(
        2025, 2, 14, 14, 0, 0
    )  # 2025-02-14 14:00:00 (テストデータの15:00:00より1時間前)
    target_id = "JN_CP001-DF01_001_test.csv"
    insert_sync_sql = f"""
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, :sync_time,
         :user, :sync_time, :user, :sync_time, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = :sync_time,
        d_updated_user = :user,
        d_updated_datetime = :sync_time,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_CP001-DF01_001",
        "file_name": target_id,  # sync_timestampのfile_nameはfile_idとして使用
        "user": "test_user",
        "sync_time": base_time,  # 前回同期タイムスタンプ
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


def test_main_db2csv_normal(monkeypatch, mock_aws, db_connector, capsys):
    """main関数の正常系テスト（DBからCSVファイル出力）- モック環境版
    テストケース番号：DB2CSV-007,DB2CSV-010,DB2CSV-011,DB2CSV-012,DB2CSV-015,DB2CSV-016,DB2CSV-017,DB2CSV-018

    検証項目：
    1. DBデータの取得と変換
    2. CSVファイルの形式（ヘッダー、区切り文字、引用符）
    3. デ連項目（タイムスタンプ、バージョン）の出力
    4. 差分基準時刻による出力データの制御
    5. タイムスタンプの更新処理
    """
    try:
        # テスト用の一意なファイル名を生成（タイムスタンプ付き）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file_id = "JN_CP001-DF01_001_test.csv"  # sync_timestampテーブルと同じ値
        test_file_name = (
            f"test_db2csv_{timestamp}.csv"  # 出力ファイル名（動的な部分を含む）
        )
        test_output_dir = "test/output"

        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            test_output_dir,
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--file_setting",
            '{"header": true, "quote_char": "\\"", "line_ending": "\\n"}',
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)  # 引数なし

        # テスト実行
        main()
        captured = capsys.readouterr()

        # S3出力の検証
        s3_client = mock_aws["s3"]
        try:
            response = s3_client.get_object(
                Bucket="test-bucket",
                Key=f"{test_output_dir}/{test_file_name}",
            )
            content = response["Body"].read()
            content_str = content.decode("utf-8")
            lines = content_str.splitlines()

            # 1. レコード件数の確認（ヘッダー + 3件のデータ）
            assert len(lines) == 4

            # 2. ヘッダーの検証
            # CSV形式で期待値を設定（ヘッダーとデータ行）
            expected_headers = [
                "id",
                "name",
                "value",
                "created_user",
                "created_datetime",
                "updated_user",
                "updated_datetime",
                "d_created_user",
                "d_created_datetime",
                "d_updated_user",
                "d_updated_datetime",
                "d_version",
            ]
            header_line = lines[0]
            for header in expected_headers:
                assert f'"{header}"' in header_line

            # 3. データ行の検証
            # test1のデータ
            assert '"1","test1","100","test_user"' in lines[1]
            assert '"1"' in lines[1]

            # test2のデータ
            assert '"2","test2","200","test_user"' in lines[2]
            assert '"1"' in lines[2]

            # test3のデータ（タイムスタンプが5分前であることを確認）
            assert '"3","test3","300","test_user"' in lines[3]
            assert '"1"' in lines[3]

            # 4. タイムスタンプの形式確認
            for line in lines[1:]:  # ヘッダーを除く
                # タイムスタンプの形式チェック（YYYY-MM-DD HH:MM:SS.nnnnnn）
                assert all(
                    '"20' in field and "." in field  # タイムスタンプフィールドの特徴
                    for field in [col for col in line.split(",") if "datetime" in line]
                )

            # ファイルサイズの検証（0バイトでないことを確認）
            assert len(content) > 0

            # ログ検証
            assert (
                f"[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名={test_file_name})"
                in captured.out
            )
            assert (
                f"[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名={test_file_name})"
                in captured.out
            )
        finally:
            # テストファイルのクリーンアップ
            try:
                s3_client.delete_object(
                    Bucket="test-bucket",
                    Key=f"{test_output_dir}/{test_file_name}",
                )
            except Exception as e:
                print(f"Warning: Failed to cleanup test file: {e}")

    finally:
        # DBのクリーンアップ
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_main_db2tsv_normal(monkeypatch, mock_aws, db_connector, capsys):
    """main関数の正常系テスト（DBからTSVファイル出力）
    テストケース番号：DB2CSV-007,DB2CSV-011,DB2CSV-018
    """
    try:
        # テストファイル名の生成
        test_file_name = "JN_CP001-DF01_001_test.tsv"
        test_output_dir = "test/output"
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            test_output_dir,
            "--file_name",
            test_file_name,
            "--file_id",
            "JN_CP001-DF01_001_test.csv",  # 同期タイムスタンプテーブルの既存データに合わせる
            "--file_type",
            "tsv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--file_setting",
            '{"header": true, "quote_char": "\\"", "line_ending": "\\n"}',
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        main()
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        # 出力ファイルの検証
        s3_client = mock_aws["s3"]
        response = s3_client.get_object(
            Bucket="test-bucket", Key=f"{test_output_dir}/{test_file_name}"
        )
        content = response["Body"].read().decode("utf-8")
        print("\n=== Content ===\n")
        print(content)
        print("\n=== End of Content ===\n")
        print("\n=== Debug Info ===")
        print("Raw content split by lines:")
        for i, line in enumerate(content.split("\n")):
            print(f"Line {i}: {line}")
        lines = [line for line in content.split("\n") if line.strip()]

        # 1. レコード件数の確認（ヘッダー + 3件のデータ）
        assert len(lines) == 4

        # 2. ヘッダーの検証
        expected_headers = [
            "id",
            "name",
            "value",
            "created_user",
            "created_datetime",
            "updated_user",
            "updated_datetime",
            "d_created_user",
            "d_created_datetime",
            "d_updated_user",
            "d_updated_datetime",
            "d_version",
        ]
        header_fields = lines[0].split("\t")
        assert len(header_fields) == len(expected_headers)
        # ヘッダーもクォートされていることを確認
        for expected, actual in zip(expected_headers, header_fields):
            expected_quoted = f'"{expected}"'
            assert expected_quoted == actual

        # 3. データ行の検証
        # test1のデータ
        line1_fields = lines[1].split("\t")
        assert '"1"' == line1_fields[0]  # id（数値もクォート）
        assert '"test1"' == line1_fields[1]  # name
        assert '"100"' == line1_fields[2]  # value（数値もクォート）

        # test2のデータ
        line2_fields = lines[2].split("\t")
        assert '"2"' == line2_fields[0]  # id（数値もクォート）
        assert '"test2"' == line2_fields[1]  # name
        assert '"200"' == line2_fields[2]  # value（数値もクォート）

        # test3のデータ
        line3_fields = lines[3].split("\t")
        assert '"3"' == line3_fields[0]  # id（数値もクォート）
        assert '"test3"' == line3_fields[1]  # name
        assert '"300"' == line3_fields[2]  # value（数値もクォート）

        # 4. タイムスタンプの形式確認
        for line in lines[1:]:  # ヘッダーを除く
            fields = line.split("\t")
            datetime_fields = []
            for f in fields:
                if '"20' in f and "datetime" in f:
                    datetime_fields.append(f.strip('"'))
            # マイクロ秒部分を含むことを確認
            for field in datetime_fields:
                print(f"Checking datetime field: {field}")
                assert "." in field, f"Field does not contain microseconds: {field}"

        # ログ検証
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=JN_CP001-DF01_001_test.tsv)"
            in captured_out
        )
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名=JN_CP001-DF01_001_test.tsv)"
            in captured_out
        )

    finally:
        # クリーンアップ処理
        GlueTestUtils.cleanup_test_resources(db_connector)
