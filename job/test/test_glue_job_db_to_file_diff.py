#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
import sys
import pytest
from sqlalchemy.exc import SQLAlchemyError
from unittest.mock import MagicMock, create_autospec
from source.common_util import load_sql_config
from source.glue_job_db_to_file import main
from test.test_utils import setup_sync_timestamp_table
from test.test_utils import GlueTestUtils


def test_diff_base_timestamp_null_handling(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-11: 差分基準時刻のSQLクエリ実行（データなし）

    テスト内容:
    - 差分基準時刻取得クエリを指定
    - 対象テーブルが空の場合
    - NULLが返却されることを確認
    - デフォルト時刻（1900/01/01）が使用されることを確認
    """
    try:
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "test.csv",
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--file_id",
            "test.csv",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # load_sql_configをモック化
        def mock_load_sql_config(file_name: str) -> str:
            if file_name == "test_timestamp_query.sql":
                return "SELECT NULL as diff_base_timestamp"
            return load_sql_config(file_name)

        monkeypatch.setattr(
            "source.glue_job_db_to_file.load_sql_config", mock_load_sql_config
        )

        # SQLAlchemyの結果セットをより詳細にモック
        mock_result = MagicMock()
        mock_result.fetchone.return_value = None
        mock_result.fetchall.return_value = []

        original_exec = db_connector.exec
        sql_count = 0

        def mock_exec(sql, params=None):
            nonlocal sql_count
            sql_count += 1
            if sql_count == 1:  # 最初のクエリ（差分基準時刻の取得）
                return mock_result
            return original_exec(sql, params)

        monkeypatch.setattr(db_connector, "exec", mock_exec)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        main()

        # ログ出力の検証
        captured = capsys.readouterr()
        assert "差分基準時刻がありません。デフォルト値を使用します。" in captured.out
        assert "ジョブが正常終了しました。" in captured.out

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_diff_base_timestamp_query_error(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-3, TD-5, TD-12: 差分基準時刻のエラー系テスト

    テスト内容:
    TD-3: 不正なパラメータ
    - diff_base_timestamp_queryに無効なクエリIDを指定
    TD-5: 差分基準時刻取得エラー
    - 差分基準時刻取得クエリの実行に失敗
    TD-12: 不正なクエリ
    - 存在しないクエリIDを使用
    """
    try:
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "test.csv",
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "invalid_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--file_id",
            "test.csv",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # load_sql_configをモック化
        def mock_load_sql_config(file_name: str) -> str:
            if file_name == "invalid_query.sql":
                return "SELECT diff_base_timestamp FROM error_table"  # エラーを発生させるためのクエリ
            return load_sql_config(file_name)

        monkeypatch.setattr(
            "source.glue_job_db_to_file.load_sql_config", mock_load_sql_config
        )

        # エラーを発生させるクエリの準備
        def mock_exec(*args, **kwargs):
            raise SQLAlchemyError("差分基準時刻の取得に失敗しました。")

        monkeypatch.setattr(db_connector, "exec", mock_exec)

        # テストデータの準備は不要（エラーが発生するため）

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured = capsys.readouterr()
        assert "[ERROR][JN_CP001-DF01_001]差分基準時刻取得エラー" in captured.out
        assert "ジョブが異常終了しました。" in captured.out

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_existing_file_id_sync_timestamp(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-16: 既存file_id指定での前回同期時刻取得

    テスト内容:
    - sync_timestampテーブルに対象のfile_idのレコードが存在
    - 前回同期時刻が設定済み
    - sync_timestampテーブルから正しい前回同期時刻が取得されることを確認
    """
    try:
        # テストデータの準備
        base_time = datetime(2025, 2, 14, 14, 0, 0)  # 2025-02-14 14:00:00
        test_file_id = "test_existing.csv"

        # sync_timestampテーブルにテストデータを投入
        insert_sync_sql = """
        INSERT INTO sync_timestamp
            (job_schedule_id, file_name, sync_datetime,
             d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
        VALUES
            (:job_schedule_id, :file_name, :sync_time,
             :user, :sync_time, :user, :sync_time, 1)
        """
        params = {
            "job_schedule_id": "JN_CP001-DF01_001",
            "file_name": test_file_id,
            "user": "test_user",
            "sync_time": base_time,
        }
        db_connector.exec(insert_sync_sql, params)
        db_connector.commit()

        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "output.csv",
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)

        # テスト実行
        main()

        # ログ出力の検証
        captured = capsys.readouterr()
        assert "前回同期タイムスタンプ: 2025-02-14 14:00:00" in captured.out
        assert "ジョブが正常終了しました。" in captured.out

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_new_file_id_sync_timestamp(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-17: 新規file_id指定での前回同期時刻取得

    テスト内容:
    - sync_timestampテーブルに対象のfile_idのレコードが未存在
    - デフォルト時刻（1900/01/01 00:00:00）が使用されることを確認
    """
    try:
        # テストファイル名とIDの設定
        test_file_id = "test_new.csv"  # 存在しないfile_id

        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "output.csv",
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)

        # sync_timestampテーブルの初期状態を確認
        check_sql = """
        SELECT COUNT(*)
        FROM sync_timestamp
        WHERE job_schedule_id = :job_schedule_id
        AND file_name = :file_name
        """
        params = {
            "job_schedule_id": "JN_CP001-DF01_001",
            "file_name": test_file_id,
        }
        result = db_connector.exec(check_sql, params)
        row = result.fetchone()
        assert row[0] == 0, "テスト前にsync_timestampテーブルにレコードが存在しています"

        # テスト実行
        main()

        # ログ出力の検証
        captured = capsys.readouterr()
        assert (
            "前回同期タイムスタンプがありません。デフォルト値を使用します。"
            in captured.out
        )
        assert "前回同期タイムスタンプ: 1900-01-01 00:00:00" in captured.out
        assert "ジョブが正常終了しました。" in captured.out

        # sync_timestampテーブルの更新を確認
        result = db_connector.exec(check_sql, params)
        row = result.fetchone()
        assert row[0] == 1, "sync_timestampテーブルにレコードが作成されていません"

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_timestamp_inconsistency(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-9: 時刻不整合のエラーケース

    テスト内容:
    - 差分基準時刻が前回同期済時刻より前の場合
    - エラーログが出力されることを確認
    """
    try:
        # テストデータの準備
        base_time = datetime(2025, 2, 14, 15, 0, 0)  # 2025-02-14 15:00:00
        test_file_id = "test_inconsistent.csv"

        # sync_timestampテーブルに前回同期時刻を設定（15:00:00）
        insert_sync_sql = """
        INSERT INTO sync_timestamp
            (job_schedule_id, file_name, sync_datetime,
             d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
        VALUES
            (:job_schedule_id, :file_name, :sync_time,
             :user, :sync_time, :user, :sync_time, 1)
        """
        params = {
            "job_schedule_id": "JN_CP001-DF01_001",
            "file_name": test_file_id,
            "user": "test_user",
            "sync_time": base_time,
        }
        db_connector.exec(insert_sync_sql, params)
        db_connector.commit()

        # 差分基準時刻を前回同期時刻より前に設定（14:00:00）
        def mock_load_sql_config(file_name: str) -> str:
            if file_name == "test_timestamp_query.sql":
                return """
                SELECT TIMESTAMP '2025-02-14 14:00:00' as diff_base_timestamp
                """
            return load_sql_config(file_name)

        monkeypatch.setattr(
            "source.glue_job_db_to_file.load_sql_config", mock_load_sql_config
        )

        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "output.csv",
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # ログ出力の検証
        captured = capsys.readouterr()
        assert (
            "差分基準時刻（2025-02-14 14:00:00）が前回同期時刻（2025-02-14 15:00:00）より前です"
            in captured.out
        )
        assert "ジョブが異常終了しました。" in captured.out

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_multiple_executions(monkeypatch, mock_aws, db_connector, capsys):
    """
    TD-15: タイムスタンプ更新の複数回実行

    テスト内容:
    - 同一条件で複数回実行
    - 前回実行が正常終了
    - 最新の差分基準時刻で更新されることを確認
    """
    try:
        # テストデータの準備
        test_file_id = "test_multiple.csv"
        initial_time = datetime(2025, 2, 14, 14, 0, 0)  # 2025-02-14 14:00:00

        # sync_timestampテーブルに初期データを投入
        insert_sync_sql = """
        INSERT INTO sync_timestamp
            (job_schedule_id, file_name, sync_datetime,
             d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
        VALUES
            (:job_schedule_id, :file_name, :sync_time,
             :user, :sync_time, :user, :sync_time, 1)
        """
        params = {
            "job_schedule_id": "JN_CP001-DF01_001",
            "file_name": test_file_id,
            "user": "test_user",
            "sync_time": initial_time,
        }
        db_connector.exec(insert_sync_sql, params)
        db_connector.commit()

        # 1回目の実行: 差分基準時刻を15:00:00に設定
        first_time = datetime(2025, 2, 14, 15, 0, 0)

        def mock_load_sql_config_first(file_name: str) -> str:
            if file_name == "test_timestamp_query.sql":
                return f"""
                SELECT TIMESTAMP '{first_time}' as diff_base_timestamp
                """
            return load_sql_config(file_name)

        monkeypatch.setattr(
            "source.glue_job_db_to_file.load_sql_config", mock_load_sql_config_first
        )

        # sys.argvのモック（両方の実行で同じ引数を使用）
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "output.csv",
            "--file_id",
            test_file_id,
            "--file_type",
            "csv",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr("sys.argv", test_args)

        # 1回目の実行
        main()

        # sync_timestampテーブルの更新を確認
        check_sql = """
        SELECT sync_datetime
        FROM sync_timestamp
        WHERE job_schedule_id = :job_schedule_id
        AND file_name = :file_name
        """
        result = db_connector.exec(
            check_sql,
            {"job_schedule_id": "JN_CP001-DF01_001", "file_name": test_file_id},
        )
        row = result.fetchone()
        assert (
            row[0] == first_time
        ), "1回目の実行後のタイムスタンプが正しく更新されていません"

        # 2回目の実行: 差分基準時刻を16:00:00に設定
        second_time = datetime(2025, 2, 14, 16, 0, 0)

        def mock_load_sql_config_second(file_name: str) -> str:
            if file_name == "test_timestamp_query.sql":
                return f"""
                SELECT TIMESTAMP '{second_time}' as diff_base_timestamp
                """
            return load_sql_config(file_name)

        monkeypatch.setattr(
            "source.glue_job_db_to_file.load_sql_config", mock_load_sql_config_second
        )

        # 2回目の実行
        main()

        # sync_timestampテーブルの更新を確認
        result = db_connector.exec(
            check_sql,
            {"job_schedule_id": "JN_CP001-DF01_001", "file_name": test_file_id},
        )
        row = result.fetchone()
        assert (
            row[0] == second_time
        ), "2回目の実行後のタイムスタンプが正しく更新されていません"

        # ログ出力の検証
        captured = capsys.readouterr()
        assert "ジョブが正常終了しました。" in captured.out

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)


if __name__ == "__main__":
    pytest.main(["-v", __file__])
