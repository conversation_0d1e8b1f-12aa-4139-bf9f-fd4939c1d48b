#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
import json
import sys
from unittest.mock import MagicMock
import pytest
from source.glue_job_db_to_file import main
from test.conftest import AWSMockHelper
from test.test_utils import GlueTestUtils
from sqlalchemy.exc import SQLAlchemyError


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    # 同期データの初期設定
    base_time = datetime(
        2025, 2, 14, 14, 0, 0
    )  # 2025-02-14 14:00:00 (テストデータより1時間前)
    target_id = "JN_CP001-DF01_001_test.csv"
    insert_sync_sql = f"""
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, :sync_time,
         :user, :sync_time, :user, :sync_time, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = :sync_time,
        d_updated_user = :user,
        d_updated_datetime = :sync_time,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_CP001-DF01_001",
        "file_name": target_id,  # sync_timestampのfile_nameはfile_idとして使用
        "user": "test_user",
        "sync_time": base_time,  # 前回同期タイムスタンプ
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


def test_main_db2fixed_normal(monkeypatch, mock_aws, db_connector, capsys):
    """main関数の正常系テスト（DBから固定長ファイル出力）
    テストケース番号：DB2FIX-018,DB2CSV-013
    """
    try:
        # sys.argvのモック（固定長出力用のパラメータ）
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "JN_CP001-DF01_001_test.txt",
            "--file_id",
            "JN_CP001-DF01_001_test.txt",
            "--file_type",
            "fixed",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        main()
        captured = capsys.readouterr()

        # 出力ファイルの検証
        s3_client = mock_aws["s3"]
        response = s3_client.get_object(
            Bucket="test-bucket", Key="test/output/JN_CP001-DF01_001_test.txt"
        )
        content = response["Body"].read()
        now = datetime.now()
        old_time = now - timedelta(minutes=5)

        # 出力内容の詳細表示
        print("\n=== Fixed Length File Analysis ===")
        print(f"Content (hex): {content.hex()}")
        print(f"Content: {content}")
        print(f"Content length: {len(content)}")

        # test.yamlの設定に基づく固定長の期待値（id:10桁, name:20桁, value:10桁）
        record1 = (
            b"0000000001"  # id: 10桁
            b"test1               "  # name: 20桁（右側スペース埋め）
            b"0000000100"  # value: 10桁
        )
        record2 = b"0000000002" b"test2               " b"0000000200"
        record3 = b"0000000003" b"test3               " b"0000000300"
        expected_content = record1 + record2 + record3

        # 期待値の詳細表示
        print("\n=== Expected Content ===")
        print(f"Record1: {record1}")
        print(f"Record2: {record2}")
        print(f"Record3: {record3}")
        print(f"Total length: {len(expected_content)}\n")
        assert content == expected_content

        # ログ検証
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=JN_CP001-DF01_001_test.txt)"
            in captured.out
        )
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名=JN_CP001-DF01_001_test.txt)"
            in captured.out
        )

    finally:
        # クリーンアップ処理
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_error_secret_manager_auth_fixed(monkeypatch, mock_aws, capsys):
    """Secrets Manager認証情報取得失敗のテスト（固定長出力時）

    テストケース番号：DB2FIX-007
    シナリオ：不正なSecretARNを指定した場合のエラー処理の確認
    """
    try:
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "invalid-secret-arn",  # 存在しないシークレット名を指定
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "test.txt",
            "--file_type",
            "fixed",
            "--cooperation_dest_system",
            "EC",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--file_id",
            "test.txt",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=test.txt)"
            in captured.out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001]DBコネクション初期化が失敗しました。"
            in captured.out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001]共通処理DbConnectorで異常終了しました。"
            in captured.out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001]DB接続エラー: An error occurred (ResourceNotFoundException)"
            in captured.out
        )
        assert "Secrets Manager can't find the specified secret" in captured.out
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。"
            in captured.out
        )
        assert (
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名=test.txt)"
            in captured.out
        )

    finally:
        pass  # motoのモックは自動的にクリーンアップされる


def test_normal_secret_manager_auth_fixed(monkeypatch, mock_aws, db_connector, capsys):
    """Secrets Manager認証情報取得成功のテスト（固定長出力時）

    テストケース番号：DB2FIX-008
    シナリオ：有効なSecretARNを指定した場合の正常処理の確認
    """
    try:
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",  # conftest.pyで設定済みの有効なシークレット
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "test.txt",
            "--file_type",
            "fixed",
            "--cooperation_dest_system",
            "EC",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--file_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        main()

        # ログ出力の検証
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=test.txt)"
            in captured.out
        )
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名=test.txt)"
            in captured.out
        )

        # 出力ファイルの検証
        s3_client = mock_aws["s3"]
        response = s3_client.get_object(
            Bucket="test-bucket", Key="test/output/test.txt"
        )
        content = response["Body"].read()
        assert content is not None
        assert len(content) > 0

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)
