#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
import json
import sys
from unittest.mock import MagicMock
import pytest
from source.glue_job_db_to_file import main
from test.conftest import AWSMockHelper
from test.test_utils import GlueTestUtils
from sqlalchemy.exc import SQLAlchemyError


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    # 同期データの初期設定
    base_time = datetime(
        2025, 2, 14, 14, 0, 0
    )  # 2025-02-14 14:00:00 (テストデータより1時間前)
    target_id = "JN_CP001-DF01_001_test.csv"
    insert_sync_sql = f"""
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, :sync_time,
         :user, :sync_time, :user, :sync_time, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = :sync_time,
        d_updated_user = :user,
        d_updated_datetime = :sync_time,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_CP001-DF01_001",
        "file_name": target_id,  # sync_timestampのfile_nameはfile_idとして使用
        "user": "test_user",
        "sync_time": base_time,  # 前回同期タイムスタンプ
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


def test_main_db2xml_normal(monkeypatch, mock_aws, db_connector, capsys):
    """main関数の正常系テスト（DBからXMLファイル出力）
    テストケース番号：DB2XML-017,DB2CSV-012
    """
    try:
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "JN_CP001-DF01_001_test.xml",
            "--file_id",
            "JN_CP001-DF01_001_test.xml",
            "--file_type",
            "xml",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        main()
        captured_out, _ = capsys.readouterr()
        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured_out)

        # 出力ファイルの検証
        s3_client = mock_aws["s3"]
        response = s3_client.get_object(
            Bucket="test-bucket", Key="test/output/JN_CP001-DF01_001_test.xml"
        )
        content = response["Body"].read().decode("utf-8")
        lines = content.splitlines()

        print("\n=== Generated XML Content ===")
        print(content)
        print("=== End of XML Content ===\n")

        # 1. XMLの基本構造の検証
        assert lines[0].strip() == '<?xml version="1.0" encoding="utf-8"?>'
        assert "<data>" in content
        assert "</data>" in content

        # 2. recordタグの数の確認（3件のデータ）
        assert content.count("<record") == 3

        # 3. 各レコードのキー項目と構造の検証
        # test1のデータ
        assert '<record record-id="1">' in content
        assert "<name>test1</name>" in content
        assert "<value>100</value>" in content

        # test2のデータ
        assert '<record record-id="2">' in content
        assert "<name>test2</name>" in content
        assert "<value>200</value>" in content

        # test3のデータ
        assert '<record record-id="3">' in content
        assert "<name>test3</name>" in content
        assert "<value>300</value>" in content

        # 4. 共通項目の存在確認
        common_elements = [
            "created_user",
            "created_datetime",
            "updated_user",
            "updated_datetime",
            "d_created_user",
            "d_created_datetime",
            "d_updated_user",
            "d_updated_datetime",
            "d_version",
        ]
        for element in common_elements:
            print(f"\nChecking element: {element}")
            print(f"Looking for: <{element}> and </{element}>")
            assert (
                f"<{element}>" in content
            ), f"Element <{element}> not found in XML:\n{content}"
            assert (
                f"</{element}>" in content
            ), f"Closing tag </{element}> not found in XML:\n{content}"

        # 5. タイムスタンプの検証
        # タイムスタンプの基本要素を確認
        timestamp_elements = [
            "<created_datetime>",
            "<updated_datetime>",
            "<d_created_datetime>",
            "<d_updated_datetime>",
        ]
        for element in timestamp_elements:
            # タイムスタンプフィールドが存在し、適切な形式であることを確認
            assert element in content
            assert "2025-" in content  # 年
            assert ":" in content and "." in content  # 時刻とマイクロ秒の区切り

        # ログ検証
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=JN_CP001-DF01_001_test.xml)"
            in captured_out
        )
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名=JN_CP001-DF01_001_test.xml)"
            in captured_out
        )

    finally:
        # クリーンアップ処理
        GlueTestUtils.cleanup_test_resources(db_connector)


def test_error_secret_manager_auth(monkeypatch, mock_aws, capsys):
    """Secrets Manager認証情報取得失敗のテスト（XML出力時）
    DBからXMLファイル出力時にSecretsManagerの認証に失敗するケースを検証

    テストケース番号：DB2XML-004,DB2CSV-004
    シナリオ：不正なSecretARNを指定した場合のエラー処理の確認
    """
    try:
        # テストファイル名とIDの設定
        test_file_id = "test.xml"
        test_file_name = "test.xml"
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "invalid-secret-arn",  # 不正なシークレット名
            "--execute_query",  # 必須パラメータ
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            test_file_name,
            "--file_id",
            test_file_id,
            "--file_type",
            "xml",
            "--cooperation_dest_system",
            "EC",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--jobnet_id",
            "JN_CP001-DF01_001",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テスト実行
        with pytest.raises(SystemExit) as exc_info:
            main()

        # 終了コードの検証
        assert exc_info.value.code == 1

        # ログ出力の検証
        captured = capsys.readouterr()

        # 期待されるログメッセージ
        expected_logs = [
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=test.xml)",
            "[ERROR][JN_CP001-DF01_001]DBコネクション初期化が失敗しました。",
            "[ERROR][JN_CP001-DF01_001]共通処理DbConnectorで異常終了しました。",
            "[ERROR][JN_CP001-DF01_001]DB接続エラー: An error occurred (ResourceNotFoundException)",
            "Secrets Manager can't find the specified secret",
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_003]例外発生しました。",
            "[ERROR][JN_CP001-DF01_001][E_job_db_to_file_001]ジョブが異常終了しました。(ファイル名=test.xml)",
        ]

        # キャプチャした出力を再度標準出力に書き戻す
        sys.stdout.write(captured.out)

        print("\n=== Expected Log Messages ===")
        for msg in expected_logs:
            print(f"Checking: {msg}")
            assert msg in captured.out, f"Expected log message not found: {msg}"

        # 終了コードが1（エラー）であることを確認
        assert exc_info.value.code == 1
        print("\nAll expected error messages were found.")

    finally:
        pass  # motoのモックは自動的にクリーンアップされる


def test_normal_secret_manager_auth(monkeypatch, mock_aws, db_connector, capsys):
    """Secrets Manager認証情報取得成功のテスト（XML出力時）

    テストケース番号：DB2XML-005,DB2CSV-005
    シナリオ：有効なSecretARNを指定した場合の正常処理の確認
    """
    try:
        # sys.argvのモック
        test_args = [
            "glue_job_db_to_file.py",
            "--secret_name",
            "test-db-secret",  # conftest.pyで設定済みの有効なシークレット
            "--execute_query",
            "test",
            "--output_file_dir",
            "test/output",
            "--file_name",
            "test.xml",
            "--file_type",
            "xml",
            "--jobnet_id",
            "JN_CP001-DF01_001",
            "--diff_base_timestamp_query",
            "test_timestamp_query",
            "--file_id",
            "test.xml",
        ]
        monkeypatch.setattr(sys, "argv", test_args)

        # テストデータの準備
        GlueTestUtils.setup_test_db_tables(db_connector)
        setup_sync_timestamp_table(db_connector)

        # テスト実行
        main()

        # ログ出力の検証
        captured = capsys.readouterr()
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_001]ジョブを開始しました。(ファイル名=test.xml)"
            in captured.out
        )
        assert (
            "[INFO][JN_CP001-DF01_001][I_job_db_to_file_002]ジョブが正常終了しました。(ファイル名=test.xml)"
            in captured.out
        )

        # 出力ファイルの検証
        s3_client = mock_aws["s3"]
        response = s3_client.get_object(
            Bucket="test-bucket", Key="test/output/test.xml"
        )
        content = response["Body"].read()
        assert content is not None
        assert len(content) > 0

    finally:
        GlueTestUtils.cleanup_test_resources(db_connector)
