#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

# from unittest import mock
import boto3
from typing import Any
import pytest
from unittest.mock import patch, MagicMock
from source.glue_job_execute_sql import (
    GlueJobExecuteSql,
    get_params,
    main,
)

from source.common_util import get_resource_path
import json
from moto import mock_secretsmanager, mock_ssm


@pytest.fixture
def aws_credentials():
    """Mocked AWS Credentials for moto"""
    # os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    # os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    # os.environ["AWS_SECURITY_TOKEN"] = "testing"
    # os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "ap-northeast-1"
    os.environ["TZ"] = "Asia/Tokyo"


@pytest.fixture
def mock_secrets():
    """Secrets Managerのmock - 実際のDB接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        db_secret = {
            # "username": "dlpf_batch",
            "username": "dlpf_ope",
            "password": "password",
            "host": "************",
            "port": "5432",
            "dbname": "test_db",
        }
        secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))
        yield secrets

@pytest.fixture
def glue_job():
    """GlueJobConvertCharacterEncodingのインスタンス生成"""
    return GlueJobExecuteSql("JN-XXXXX-XXXX")


def test_execute_truncate_empty(mock_secrets,aws_credentials):
    sys.argv = [
    "glue_job_convert_character_encoding",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "secret_name",
        "test-db-secret",
        "sql_info",
        '{"sql_info":[{"query_id":"test_execute_sql_3","params":{"d_updated_datetime":"20250502","d_version":"2","d_updated_user":"job_execute_sql_2"}}]}',
        "jobnet_id",
        "JN-XXXXX-XXXX",
    ]
    main()