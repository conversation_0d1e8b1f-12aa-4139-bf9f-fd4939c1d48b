# from unittest.mock import patch
import pytest
import boto3
import json
from moto import mock_secretsmanager

# from moto import mock_aws
from source.db_connector import DbConnector
from source.glue_logger import GlueLogger
from source.glue_job_internal_db_clear import GlueJobDbDelete


@pytest.fixture
def glue_job():
    """GlueJobDbDeleteのインスタンス生成"""
    return GlueJobDbDelete("JN_TS999-XX01_001")


@pytest.fixture
def mock_secrets():
    """Secrets Managerのmock - 実際のDB接続情報を使用"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際のDB接続情報を設定
        db_secret = {
            "username": "dlpf_ope",
            "password": "password",
            "host": "************",
            "port": "5432",
            "dbname": "test_db",
        }
        secrets.create_secret(Name="test-db-secret", SecretString=json.dumps(db_secret))
        yield secrets


@pytest.fixture
def db_connector():
    """実際のDbConnectorインスタンスを作成"""
    logger = GlueLogger("test-jobnet")
    connector = DbConnector(logger, "test-db-secret")
    connector.connect()
    return connector


def test_execute_truncate_fail(mock_secrets, glue_job: GlueJobDbDelete):
    """テーブル全件削除失敗 test_case5"""
    query_truncate = "test_truncate"
    with pytest.raises(AttributeError):
        # メイン処理実行
        glue_job.execute_query("", query_truncate)


def test_execute_truncate_none(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル全件削除 test_case6"""
    db_connector.begin()
    query_truncate = "test_truncate"
    glue_job.execute_query(db_connector, query_truncate)

    db_connector.commit()
    db_connector.close()


def test_execute_truncate_empty(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル全件削除 test_case7"""
    db_connector.begin()
    query_truncate = "test_truncate"
    glue_job.execute_query(db_connector, query_truncate, "", "")

    db_connector.commit()
    db_connector.close()


def test_execute_delete_fail(mock_secrets, glue_job: GlueJobDbDelete):
    """テーブル条件付き削除失敗 test_case8"""
    query_truncate = "test_delete_001"
    delete_start = "2024/01/01"
    delete_end = "2024/12/31"
    with pytest.raises(AttributeError):
        # メイン処理実行
        glue_job.execute_query("", query_truncate, delete_start, delete_end)


def test_execute_delete_001(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル条件付き削除 test_case9"""
    db_connector.begin()
    query_truncate = "test_delete_001"
    delete_start = "2024/01/01"
    delete_end = "2024/12/31"
    glue_job.execute_query(db_connector, query_truncate, delete_start, delete_end)

    db_connector.commit()
    db_connector.close()


def test_execute_delete_002(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル条件付き削除 test_case10"""
    db_connector.begin()
    query_truncate = "test_delete_002"
    delete_start = "2027/01/01"
    delete_end = None
    glue_job.execute_query(db_connector, query_truncate, delete_start, delete_end)

    db_connector.commit()
    db_connector.close()


def test_execute_delete_003(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル条件付き削除 test_case11"""
    db_connector.begin()
    query_truncate = "test_delete_003"
    delete_start = None
    delete_end = "2024/12/31"
    glue_job.execute_query(db_connector, query_truncate, delete_start, delete_end)

    db_connector.commit()
    db_connector.close()


def test_execute_delete_004(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル条件付き削除 test_case12"""
    db_connector.begin()
    query_truncate = "test_delete_002"
    delete_start = "2027/01/01"
    glue_job.execute_query(db_connector, query_truncate, delete_start, "")

    db_connector.commit()
    db_connector.close()


def test_execute_delete_005(mock_secrets, db_connector, glue_job: GlueJobDbDelete):
    """テーブル条件付き削除 test_case13"""
    db_connector.begin()
    query_truncate = "test_delete_003"
    delete_end = "2024/12/31"
    glue_job.execute_query(db_connector, query_truncate, "", delete_end)

    db_connector.commit()
    db_connector.close()
