import json
import pytest
import os
import boto3
import sys
from moto import mock_secretsmanager
from source.glue_job_send_file import GlueJobSendFile, get_params, main
from unittest.mock import patch, MagicMock


@pytest.fixture
def s3_bucket():
    """テスト用バケット名を環境変数に設定"""
    os.environ["S3_BUCKET_NAME"] = "s3-dev-dlpf-if-886436956581"
    os.environ["S3_RETRY_LIMIT"] = "3"
    os.environ["S3_RETRY_INTERVAL"] = "1"
    yield
    if "S3_BUCKET_NAME" in os.environ:
        del os.environ["S3_BUCKET_NAME"]
    if "S3_RETRY_LIMIT" in os.environ:
        del os.environ["S3_RETRY_LIMIT"]
    if "S3_RETRY_INTERVAL" in os.environ:
        del os.environ["S3_RETRY_INTERVAL"]


@pytest.fixture
def mock_secrets():
    """Secrets Manager"""
    with mock_secretsmanager():
        secrets = boto3.client("secretsmanager")
        # 実際の接続情報を設定
        secret_data = {
            "protocol": "https",
            "site_url": "https://example.sharepoint.com/sites/TestSite",
            "username": "<EMAIL>",
            "password": "securepassword",
        }
        secrets.create_secret(Name="test-secret", SecretString=json.dumps(secret_data))
        yield secrets


@pytest.fixture
def glue_job():
    """GlueJobSendFileのインスタンスを返すフィクスチャ"""
    return GlueJobSendFile(jobnet_id="test-jobnet")


def test_1_get_connection_info(mock_secrets, glue_job):
    """
    正常系テスト
    外部システム接続情報取得確認
    """
    # シークレット情報を取得する
    connection_info = glue_job.get_connection_info("test-secret")
    # 期待される結果と比較
    expect_data = {
        "protocol": "https",
        "site_url": "https://example.sharepoint.com/sites/TestSite",
        "username": "<EMAIL>",
        "password": "securepassword",
    }
    assert connection_info == expect_data


def test_2_get_connection_info_failure(mock_secrets, glue_job, capsys):
    """
    異常系テスト
    get_connection_infoが失敗することを確認し、ログを確認
    """
    with pytest.raises(Exception):  # 例外がスローされることを確認
        # テスト対象メソッドの実行
        glue_job.get_connection_info("test")

        # 標準出力のキャプチャ
        captured = capsys.readouterr()

        # ログの確認
        expected_log_message = "[ERROR][test-jobnet][E_job_send_file_002]処理で異常が発生しました。(処理名=接続情報取得)"
        assert expected_log_message in captured.out


def test_3_get_s3_file_success(s3_bucket: None, glue_job):
    """
    正常系テスト
    S3ファイル取得が正常に完了することを確認
    """
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"

    # テスト実行
    file_data = glue_job.get_s3_file(s3_full_path)

    # 確認
    # バイト配列であることの確認
    file_content = file_data["Body"].read()
    assert isinstance(file_content, bytes)


def set_data(glue_job, backup_flag: int):
    """
    テスト用データ・パラメータを初期設定（ファイル送信用）
    Args:
        glue_job: ジョブ
    """
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    secret_name = "test-secret"
    external_system_destination_directory = "dummy/OMS_OUT/"
    external_system_transmission_file_name = "test1.csv"
    backup_s3_directory = "saito/back-up/OMS_IN/"
    if backup_flag == 1:
        sys.argv = [
            "glue_job_send_file",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "s3_full_path",
            s3_full_path,
            "secret_name",
            secret_name,
            "external_system_destination_directory",
            external_system_destination_directory,
            "external_system_transmission_file_name",
            external_system_transmission_file_name,
            "backup_s3_directory",
            backup_s3_directory,
            "backup_flag",
            True,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    elif backup_flag == 0:
        sys.argv = [
            "glue_job_send_file",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "s3_full_path",
            s3_full_path,
            "secret_name",
            secret_name,
            "external_system_destination_directory",
            external_system_destination_directory,
            "external_system_transmission_file_name",
            external_system_transmission_file_name,
            "backup_s3_directory",
            backup_s3_directory,
            "backup_flag",
            False,
            "jobnet_id",
            glue_job.jobnet_id,
        ]
    else:
        sys.argv = [
            "glue_job_send_file",
            "TZ",
            "Asia/Tokyo",
            "enable-job-insights",
            "false",
            "enable-glue-datacatalog",
            "true",
            "library-set",
            "analytics",
            "python-version",
            "3.9",
            "job-language",
            "python",
            "s3_full_path",
            s3_full_path,
            "secret_name",
            secret_name,
            "external_system_destination_directory",
            external_system_destination_directory,
            "external_system_transmission_file_name",
            external_system_transmission_file_name,
            "backup_s3_directory",
            backup_s3_directory,
            "jobnet_id",
            glue_job.jobnet_id,
        ]


def assert_error(captured, method_name: str, file_name: str):
    assert (
        "[INFO][test-jobnet][I_job_send_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_send_file_002]処理で異常が発生しました。(処理名="
        + method_name
        + ")"
        in captured.out
    )
    assert "[ERROR][test-jobnet][E_job_send_file_003]例外発生しました。" in captured.out
    assert (
        "[ERROR][test-jobnet][E_job_send_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_send_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )


def test_4_get_s3_file_retry_success(s3_bucket: None, glue_job, capsys):
    """
    準正常系テスト
    S3ファイル取得がリトライして成功することを確認
    """
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"

    # テスト実行
    response = glue_job.s3_client.get_object(
        Bucket=os.environ["S3_BUCKET_NAME"], Key=s3_full_path
    )
    with patch.object(
        glue_job.s3_client,
        "get_object",
        side_effect=[
            Exception("Test Exception"),
            Exception("Test Exception"),
            response,  # 3回目で成功
        ],
    ):
        file_data = glue_job.get_s3_file(s3_full_path)

    # 確認
    file_content = file_data["Body"].read()
    assert isinstance(file_content, bytes)

    # ログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_send_file_003]処理をリトライしました。(処理名=S3ファイル取得)"
        in captured.out
    )


def test_5_get_s3_file_error(s3_bucket: None, mock_secrets, glue_job, capsys):
    """
    異常系テスト
    S3ファイル取得が失敗（リトライ超過）することを確認
    """
    set_data(glue_job, 1)
    # テスト実行
    with patch.object(
        glue_job.s3_client, "get_object", side_effect=Exception("Test Exception")
    ):
        with pytest.raises(SystemExit):
            # メイン処理実行
            params = get_params()
            glue_job.execute(params)

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "S3ファイル取得", "test1.csv")


def test_6_7_send_file_ftp_failure(glue_job, capsys):
    """send_fileメソッドの失敗パターンのテスト"""

    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "ftp",
            "host": "fake_host",
            "username": "fake_user",
            "password": "fake_pass",
        }
        with patch.object(
            GlueJobSendFile, "get_connection_info", return_value=mock_connection_info
        ):
            with pytest.raises(SystemExit):
                main()

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "FTPファイル送信", "test1.csv")


def test_8_9_send_file_sftp_failure(glue_job, capsys):
    """send_fileメソッドの失敗パターンのテスト"""

    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "sftp",
            "host": "sftp.example.com",
            "username": "sftp_user",
            "password": "sftp_password",
            "private_key": "xxx",
        }
        with patch.object(
            GlueJobSendFile, "get_connection_info", return_value=mock_connection_info
        ):
            with pytest.raises(SystemExit):
                main()

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "SFTPファイル送信", "test1.csv")


def test_10_11_send_file_https_failure(glue_job, capsys):
    """send_fileメソッドの失敗パターンのテスト"""

    set_data(glue_job, 2)

    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        mock_connection_info = {
            "protocol": "https",
            "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
            "username": "https_user",
            "password": "https_password",
        }
        with patch.object(
            GlueJobSendFile, "get_connection_info", return_value=mock_connection_info
        ):
            with pytest.raises(SystemExit):
                main()

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "HTTPSファイル送信", "test1.csv")


def test_12_16_main_backup_true(s3_bucket: None, glue_job, capsys):
    """
    正常系テスト
    インプットファイルバックアップ、インプットファイル削除が正常に完了することを確認
    """
    file_name = "test1.csv"

    # バックアップフラグTrueのとき、インプットファイルバックアップおよびインプットファイル削除されるか確認
    execute_main(glue_job, 1, "ftp", 0, "")

    # ケース12のログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_send_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_send_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )


def test_13_17_main_backup_false(s3_bucket: None, glue_job, capsys):
    """
    正常系テスト
    バックアップフラグFalseで正常終了することを確認
    """
    file_name = "test1.csv"

    # テスト実行
    # バックアップフラグFalseのときバックアップされないか確認
    execute_main(glue_job, 0, "ftp", 0, "")
    # ケース13のログ確認
    captured = capsys.readouterr()
    assert (
        "[INFO][test-jobnet][I_job_send_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_send_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )


def test_14_backup_error(s3_bucket: None, mock_secrets, glue_job, capsys):
    """
    異常系テスト
    バックアップで異常終了することを確認
    """
    # テストデータ準備
    file_name = "test1.csv"

    with patch.object(
        glue_job.s3_client, "copy_object", side_effect=Exception("Test Exception")
    ):
        with patch.object(glue_job, "get_s3_file", return_value=b"a1,b1,c1\nd1,e1"):
            execute_main(glue_job, 1, "ftp", 1, "backup_input_file")

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイルバックアップ", "test1.csv")


def test_15_backup_retry_success(s3_bucket: None, glue_job, capsys):
    """
    準正常系テスト
    インプットファイルバックアップがリトライして成功することを確認
    """
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"
    backup_file_dir = "saito/back-up/OMS_IN/"
    byte_data = b"a1,b1,c1\nd1,e1"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "copy_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.copy_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=backup_file_dir + file_name,
                    CopySource={
                        "Bucket": os.environ["S3_BUCKET_NAME"],
                        "Key": input_file_dir + file_name,
                    },
                ),  # 3回目で成功
            ],
        ):
            glue_job.backup_input_file(
                input_file_dir + file_name, backup_file_dir, file_name
            )

        # 確認
        response = glue_job.s3_client.get_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )
        assert response["Body"].read() == byte_data

        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_send_file_003]処理をリトライしました。(処理名=インプットファイルバックアップ)"
            in captured.out
        )
    finally:
        # テストファイル削除
        glue_job.s3_client.delete_object(
            Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
        )


def test_18_delete_error(s3_bucket: None, mock_secrets, glue_job, capsys):
    """
    異常系テスト
    バックアップで異常終了することを確認
    """
    # テストデータ準備
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    byte_data = b"a1,b1,c1\nd1,e1"

    with patch.object(
        glue_job.s3_client, "delete_object", side_effect=Exception("Test Exception")
    ):
        with patch.object(glue_job, "get_s3_file", return_value=b"a1,b1,c1\nd1,e1"):
            execute_main(glue_job, 0, "ftp", 1, "delete_input_file")

    # ログ確認
    captured = capsys.readouterr()
    assert_error(captured, "インプットファイル削除", "test1.csv")


def test_19_delete_retry_success(s3_bucket: None, glue_job, capsys):
    """
    準正常系テスト
    インプットファイル削除がリトライして成功することを確認
    """
    input_file_dir = "saito/input-output/OMS_IN/"
    file_name = "test1.csv"

    # テスト実行
    try:
        with patch.object(
            glue_job.s3_client,
            "delete_object",
            side_effect=[
                Exception("Test Exception"),
                Exception("Test Exception"),
                glue_job.s3_client.delete_object(
                    Bucket=os.environ["S3_BUCKET_NAME"],
                    Key=input_file_dir + file_name,
                ),  # 3回目で成功
            ],
        ):
            glue_job.delete_input_file(input_file_dir + file_name)

        # インプットファイル削除されたか確認
        with pytest.raises(Exception):
            glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
            )
        # ログ確認
        captured = capsys.readouterr()
        assert (
            "[INFO][test-jobnet][I_job_send_file_003]処理をリトライしました。(処理名=インプットファイル削除)"
            in captured.out
        )
    finally:
        # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
        glue_job.s3_client.copy_object(
            Bucket=os.environ["S3_BUCKET_NAME"],
            Key=input_file_dir + file_name,
            CopySource={
                "Bucket": os.environ["S3_BUCKET_NAME"],
                "Key": "saito/back-up/" + file_name,
            },
        )


def test_20_21_main(s3_bucket: None, glue_job, capsys):
    # バックアップフラグ指定なし
    execute_main(glue_job, 2, "ftp", 0, "")

    # ログ確認
    captured = capsys.readouterr()
    file_name = "test1.csv"
    assert (
        "[INFO][test-jobnet][I_job_send_file_001]ジョブを開始しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_send_file_002]処理で異常が発生しました。(処理名="
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_send_file_003]例外発生しました。"
        not in captured.out
    )
    assert (
        "[ERROR][test-jobnet][E_job_send_file_001]ジョブが異常終了しました。(ファイル名="
        + file_name
        + ")"
        not in captured.out
    )
    assert (
        "[INFO][test-jobnet][I_job_send_file_002]ジョブが正常終了しました。(ファイル名="
        + file_name
        + ")"
        in captured.out
    )


def test_22_value_error_s3_full_path(s3_bucket: None, glue_job: GlueJobSendFile):
    # s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    secret_name = "test"
    external_system_destination_directory = "dummy/OMS_OUT/"
    external_system_transmission_file_name = "file.txt"
    backup_s3_directory = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        # "s3_full_path",
        # s3_full_path,
        "secret_name",
        secret_name,
        "external_system_destination_directory",
        external_system_destination_directory,
        "external_system_transmission_file_name",
        external_system_transmission_file_name,
        "backup_flag",
        True,
        "backup_s3_directory",
        backup_s3_directory,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 's3_full_path' is missing"


def test_23_value_error_secret_name(s3_bucket: None, glue_job: GlueJobSendFile):
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    # secret_name = "test"
    external_system_destination_directory = "dummy/OMS_OUT/"
    external_system_transmission_file_name = "file.txt"
    backup_s3_directory = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "s3_full_path",
        s3_full_path,
        # "secret_name",
        # secret_name,
        "external_system_destination_directory",
        external_system_destination_directory,
        "external_system_transmission_file_name",
        external_system_transmission_file_name,
        "backup_flag",
        True,
        "backup_s3_directory",
        backup_s3_directory,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'secret_name' is missing"


def test_24_value_error_external_system_destination_directory(
    s3_bucket: None, glue_job: GlueJobSendFile
):
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    secret_name = "test"
    # external_system_destination_directory = "dummy/OMS_OUT/"
    external_system_transmission_file_name = "file.txt"
    backup_s3_directory = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "s3_full_path",
        s3_full_path,
        "secret_name",
        secret_name,
        # "external_system_destination_directory",
        # external_system_destination_directory,
        "external_system_transmission_file_name",
        external_system_transmission_file_name,
        "backup_flag",
        True,
        "backup_s3_directory",
        backup_s3_directory,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert (
            str(exc_info.value)
            == "Required parameter 'external_system_destination_directory' is missing"
        )


def test_25_value_error_external_system_transmission_file_name(
    s3_bucket: None, glue_job: GlueJobSendFile
):
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    secret_name = "test"
    external_system_destination_directory = "dummy/OMS_OUT/"
    # external_system_transmission_file_name = "file.txt"
    backup_s3_directory = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "s3_full_path",
        s3_full_path,
        "secret_name",
        secret_name,
        "external_system_destination_directory",
        external_system_destination_directory,
        # "external_system_transmission_file_name",
        # external_system_transmission_file_name,
        "backup_flag",
        True,
        "backup_s3_directory",
        backup_s3_directory,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert (
            str(exc_info.value)
            == "Required parameter 'external_system_transmission_file_name' is missing"
        )


def test_26_value_error_backup_s3_directory(s3_bucket: None, glue_job: GlueJobSendFile):
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    secret_name = "test"
    external_system_destination_directory = "dummy/OMS_OUT/"
    external_system_transmission_file_name = "file.txt"
    # backup_s3_directory = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "s3_full_path",
        s3_full_path,
        "secret_name",
        secret_name,
        "external_system_destination_directory",
        external_system_destination_directory,
        "external_system_transmission_file_name",
        external_system_transmission_file_name,
        "backup_flag",
        True,
        # "backup_s3_directory",
        # backup_s3_directory,
        "jobnet_id",
        glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert (
            str(exc_info.value) == "Required parameter 'backup_s3_directory' is missing"
        )


def test_27_value_error_jobnet_id(s3_bucket: None, glue_job: GlueJobSendFile):
    s3_full_path = "saito/input-output/OMS_IN/test1.csv"
    secret_name = "test"
    external_system_destination_directory = "dummy/OMS_OUT/"
    external_system_transmission_file_name = "file.txt"
    backup_s3_directory = "saito/back-up/OMS_IN/"
    sys.argv = [
        "glue_job_send_file",
        "TZ",
        "Asia/Tokyo",
        "enable-job-insights",
        "false",
        "enable-glue-datacatalog",
        "true",
        "library-set",
        "analytics",
        "python-version",
        "3.9",
        "job-language",
        "python",
        "s3_full_path",
        s3_full_path,
        "secret_name",
        secret_name,
        "external_system_destination_directory",
        external_system_destination_directory,
        "external_system_transmission_file_name",
        external_system_transmission_file_name,
        "backup_flag",
        True,
        "backup_s3_directory",
        backup_s3_directory,
        # "jobnet_id",
        # glue_job.jobnet_id,
    ]
    with patch("boto3.client") as mock_boto_client:
        mock_ssm_client = MagicMock()
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "process": {"TZ": "Asia/Tokyo"},
                        "aws": {
                            "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                            "REGION_NAME": "ap-northeast-1",
                            "S3_RETRY_LIMIT": "3",
                            "S3_RETRY_INTERVAL": "1",
                            "S3_RETRY_MODE": "standard",
                        },
                    }
                )
            }
        }
        mock_boto_client.return_value = mock_ssm_client

        with pytest.raises(ValueError) as exc_info:
            main()
        assert str(exc_info.value) == "Required parameter 'jobnet_id' is missing"


def execute_main(
    glue_job: GlueJobSendFile,
    backup_flag_mode: int,
    protocol: str,
    exit_mode: int,
    error_method: str,
):
    """
    正常系テスト
    バックアップフラグをTrueに指定してメイン処理を実行して、ジョブ完了後の状態を確認
    """
    set_data(glue_job, backup_flag_mode)
    params = get_params()
    file_name = "test1.csv"
    input_file_dir = "saito/input-output/OMS_IN/"
    backup_file_dir = params["backup_s3_directory"]
    byte_data = b"a1,b1,c1\nd1,e1"
    backup_done = False

    try:
        # メイン処理実行
        backup_flag = params["backup_flag"]
        if backup_flag_mode == 1:
            assert backup_flag
        else:
            assert not backup_flag

        with patch("boto3.client") as mock_boto_client:
            mock_ssm_client = MagicMock()
            mock_ssm_client.get_parameter.return_value = {
                "Parameter": {
                    "Value": json.dumps(
                        {
                            "process": {"TZ": "Asia/Tokyo"},
                            "aws": {
                                "S3_BUCKET_NAME": "s3-dev-dlpf-if-886436956581",
                                "REGION_NAME": "ap-northeast-1",
                                "S3_RETRY_LIMIT": "3",
                                "S3_RETRY_INTERVAL": "1",
                                "S3_RETRY_MODE": "standard",
                            },
                        }
                    )
                }
            }
            mock_boto_client.return_value = mock_ssm_client

            if protocol == "ftp":
                mock_connection_info = {
                    "protocol": "ftp",
                    "host": "fake_host",
                    "username": "fake_user",
                    "password": "fake_pass",
                }
            elif protocol == "sftp":
                mock_connection_info = {
                    "protocol": "sftp",
                    "host": "sftp.example.com",
                    "username": "sftp_user",
                    "password": "sftp_password",
                    "private_key": "xxx",
                }
            elif protocol == "https":
                mock_connection_info = {
                    "protocol": "https",
                    "site_url": "https://localhost/sites/mysite/_layouts/15/download.aspx?SourceUrl=/sites/mysite/documents/file.txt",
                    "username": "https_user",
                    "password": "https_password",
                    "private_key": "xxxyyy",
                }

            with patch.object(
                glue_job,
                "get_connection_info",
                return_value=mock_connection_info,
            ):
                with patch.object(glue_job, "send_file", return_value=None):
                    if exit_mode == 1:
                        with pytest.raises(SystemExit):
                            glue_job.execute(params)
                    else:
                        glue_job.execute(params)

        if backup_flag and error_method != "backup_input_file":
            # バックアップされたかを確認
            response = glue_job.s3_client.get_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
            backup_done = True
            assert response["Body"].read() == byte_data
        else:
            # バックアップされていないことを確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
                )

        if exit_mode != 1:
            # インプットファイル削除されたか確認
            with pytest.raises(Exception):
                glue_job.s3_client.get_object(
                    Bucket=os.environ["S3_BUCKET_NAME"], Key=input_file_dir + file_name
                )
    finally:
        if backup_done and error_method != "backup_input_file":
            # バックアップしたファイルを削除
            glue_job.s3_client.delete_object(
                Bucket=os.environ["S3_BUCKET_NAME"], Key=backup_file_dir + file_name
            )
        if exit_mode != 1:
            # 別場所にあるファイルをインプットディレクトリに複製（次のテストで利用できるように）
            glue_job.s3_client.copy_object(
                Bucket=os.environ["S3_BUCKET_NAME"],
                Key=input_file_dir + file_name,
                CopySource={
                    "Bucket": os.environ["S3_BUCKET_NAME"],
                    "Key": "saito/back-up/" + file_name,
                },
            )
