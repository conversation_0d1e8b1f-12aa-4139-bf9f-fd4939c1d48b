import configparser
import os
import logging
from configparser import Config<PERSON>ars<PERSON>
from unittest import mock

import pytest
from source.glue_logger import G<PERSON><PERSON>ogger


def test_initialization():
    jobnet_id = "test_jobnet_id"
    logger = GlueLogger(jobnet_id)

    assert logger.jobnet_id == jobnet_id
    assert logger.msg_format == "[%s][%s][%s][%s]%s"
    assert logger.static_msg_format == "[%s][%s][%s]%s"
    assert logger.log_level == logging.INFO
    assert isinstance(logger.msg_file, ConfigParser)


def test_initialization_with_empty_jobnet_id():
    logger = GlueLogger("")

    assert logger.jobnet_id == ""
    assert logger.msg_format == "[%s][%s][%s][%s]%s"
    assert logger.static_msg_format == "[%s][%s][%s]%s"
    assert logger.log_level == logging.INFO
    assert isinstance(logger.msg_file, ConfigParser)


def test_default_log_level():
    with mock.patch.dict(os.environ, {}, clear=True):  # Clear environment variables
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.INFO


def test_custom_log_level():
    with mock.patch.dict(os.environ, {"LOG_LEVEL": "DEBUG"}):
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.DEBUG

    with mock.patch.dict(os.environ, {"LOG_LEVEL": "ERROR"}):
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.ERROR

    with mock.patch.dict(os.environ, {"LOG_LEVEL": "INVALID"}):
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.INFO  # Default to INFO for invalid log level


def test_log_level_case_insensitivity():
    with mock.patch.dict(os.environ, {"LOG_LEVEL": "debug"}):
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.DEBUG

    with mock.patch.dict(os.environ, {"LOG_LEVEL": "ERROR"}):
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.ERROR

    with mock.patch.dict(os.environ, {"LOG_LEVEL": "WaRnInG"}):
        logger = GlueLogger("test_jobnet_id")
        assert logger.log_level == logging.WARNING


def test_load_log_message_config_success():
    with mock.patch('source.glue_logger.load_log_config') as mock_load_config:
        mock_config = mock.MagicMock(spec=ConfigParser)
        mock_load_config.return_value = mock_config

        logger = GlueLogger("test_jobnet_id")

        mock_load_config.assert_called_once_with("log_message.config")
        assert logger.msg_file == mock_config


def test_init_with_failed_log_config_load():
    with mock.patch('source.glue_logger.load_log_config') as mock_load_config:
        mock_load_config.side_effect = Exception("Failed to load config")

        with mock.patch('builtins.print') as mock_print:
            logger = GlueLogger("test_jobnet_id")

            mock_print.assert_called_once_with("Warning: Failed to load log message config: Failed to load config")
            assert isinstance(logger.msg_file, ConfigParser)
            assert len(logger.msg_file.sections()) == 0


def test_format_message_with_different_log_levels():
    logger = GlueLogger("test_jobnet_id")
    log_levels = ["INFO", "DEBUG", "ERROR", "WARN", "TRACE"]

    for level in log_levels:
        msg_id = f"{level[0]}_001"
        message = f"Test {level} message"

        formatted_msg = logger._format_message(level, msg_id, message)

        assert level in formatted_msg
        assert msg_id in formatted_msg
        assert message in formatted_msg
        assert logger.jobnet_id in formatted_msg
        assert logger.get_log_time() in formatted_msg

    # Test without msg_id
    for level in log_levels:
        message = f"Test {level} message without ID"

        formatted_msg = logger._format_message(level, None, message)

        assert level in formatted_msg
        assert message in formatted_msg
        assert logger.jobnet_id in formatted_msg
        assert logger.get_log_time() in formatted_msg
        assert formatted_msg.count('[') == 3  # Only 3 brackets for date, level, and jobnet_id


def test_format_message_without_msg_id():
    logger = GlueLogger("test_jobnet_id")
    level = "INFO"
    message = "Test message"

    with mock.patch.object(GlueLogger, 'get_log_time', return_value="2023/01/01 12:00:00"):
        formatted_message = logger._format_message(level, None, message)

    expected_message = "[2023/01/01 12:00:00][INFO][test_jobnet_id]Test message"
    assert formatted_message == expected_message


def test_get_log_time_format():
    logger = GlueLogger("test_jobnet_id")
    log_time = logger.get_log_time()
    assert isinstance(log_time, str)
    assert len(log_time) == 19  # YYYY/MM/DD HH:MM:SS is 19 characters long
    assert log_time[4] == '/' and log_time[7] == '/'  # Check for slashes in date
    assert log_time[10] == ' '  # Check for space between date and time
    assert log_time[13] == ':' and log_time[16] == ':'  # Check for colons in time


def test_get_log_time():
    logger = GlueLogger("test_jobnet_id")
    with mock.patch('datetime.datetime') as mock_datetime:
        mock_now = mock.Mock()
        mock_now.strftime.return_value = "2023/05/15 10:30:45"
        mock_datetime.now.return_value = mock_now

        result = logger.get_log_time()

        assert result == "2023/05/15 10:30:45"
        mock_datetime.now.assert_called_once()
        mock_now.strftime.assert_called_once_with("%Y/%m/%d %H:%M:%S")


def test_format_message_with_valid_inputs():
    logger = GlueLogger("test_jobnet_id")
    logger.get_log_time = lambda: "2023/05/01 12:00:00"  # Mock the get_log_time method

    level = "INFO"
    msg_id = "I001"
    message = "Test message"

    formatted_message = logger._format_message(level, msg_id, message)
    expected_message = "[2023/05/01 12:00:00][INFO][test_jobnet_id][I001]Test message"

    assert formatted_message == expected_message


def test_get_message_content_valid_input():
    logger = GlueLogger("test_jobnet_id")
    logger.msg_file = mock.MagicMock(spec=ConfigParser)
    logger.msg_file.get.return_value = "Hello, %s!"

    result = logger._get_message_content("INFO", "I001", ("World",))

    logger.msg_file.get.assert_called_once_with("INFO", "I001")
    assert result == "Hello, World!"


def test_get_message_content_valid_section_and_msg_id():
    logger = GlueLogger("test_jobnet_id")
    logger.msg_file = mock.MagicMock(spec=ConfigParser)
    logger.msg_file.get.return_value = "Test message content"

    result = logger._get_message_content("INFO", "I_TEST_001")

    assert result == "Test message content"
    logger.msg_file.get.assert_called_once_with("INFO", "I_TEST_001")


def test_get_message_content_section_not_found():
    logger = GlueLogger("test_jobnet_id")
    logger.msg_file = mock.MagicMock(spec=configparser.ConfigParser)
    logger.msg_file.get.side_effect = configparser.NoSectionError("NonexistentSection")

    with pytest.raises(configparser.NoSectionError) as excinfo:
        logger._get_message_content("NonexistentSection", "MSG_001")

    assert "NonexistentSection" in str(excinfo.value)


def test_logger_by_msg_id_empty():
    logger = GlueLogger("test_jobnet_id")
    with mock.patch.object(logger, 'trace') as mock_trace, \
         mock.patch.object(logger, 'debug') as mock_debug, \
         mock.patch.object(logger, 'info') as mock_info, \
         mock.patch.object(logger, 'warning') as mock_warning, \
         mock.patch.object(logger, 'error') as mock_error:

        logger.loggerByMsgId("")

        mock_trace.assert_not_called()
        mock_debug.assert_not_called()
        mock_info.assert_not_called()
        mock_warning.assert_not_called()
        mock_error.assert_not_called()


def test_logger_by_msg_id_trace():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.TRACE

    with mock.patch.object(logger, 'trace') as mock_trace:
        logger.loggerByMsgId("T001")
        mock_trace.assert_called_once_with("T001")

    with mock.patch.object(logger, 'trace') as mock_trace:
        logger.loggerByMsgId("T002", ("param1", "param2"))
        mock_trace.assert_called_once_with("T002")


@mock.patch.object(GlueLogger, 'debug')
def test_logger_by_msg_id_debug(mock_debug):
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.DEBUG

    logger.loggerByMsgId("D_TEST_001")

    mock_debug.assert_called_once_with("D_TEST_001")


@mock.patch('source.glue_logger.GlueLogger.info')
def test_logger_by_msg_id_info_with_values(mock_info):
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    msg_id = "I_TEST_001"
    msg_values = ("param1", "param2")

    logger.loggerByMsgId(msg_id, msg_values)

    mock_info.assert_called_once_with(msg_id, msg_values)


@mock.patch('builtins.print')
def test_loggerByMsgId_warn_with_msg_values(mock_print):
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.WARN

    with mock.patch.object(logger, '_get_message_content') as mock_get_message:
        mock_get_message.return_value = "Warning message with value: test_value"

        logger.loggerByMsgId("W_TEST_001", ("test_value",))

        mock_get_message.assert_called_once_with("WARN", "W_TEST_001", ("test_value",))
        mock_print.assert_called_once()

        printed_message = mock_print.call_args[0][0]
        assert "[WARN][test_jobnet_id][W_TEST_001]Warning message with value: test_value" in printed_message


@mock.patch('builtins.print')
def test_logger_by_msg_id_error_with_values(mock_print):
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content:
        mock_get_content.return_value = "Error message: Test error"
        logger.loggerByMsgId("E_TEST_001", ("Test error",))

    mock_get_content.assert_called_once_with("ERROR", "E_TEST_001", ("Test error",))
    mock_print.assert_called_once()
    printed_message = mock_print.call_args[0][0]
    assert "ERROR" in printed_message
    assert "E_TEST_001" in printed_message
    assert "Error message: Test error" in printed_message


def test_logger_by_msg_id_unrecognized_level():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    with mock.patch.object(logger, 'trace') as mock_trace, \
         mock.patch.object(logger, 'debug') as mock_debug, \
         mock.patch.object(logger, 'info') as mock_info, \
         mock.patch.object(logger, 'warning') as mock_warning, \
         mock.patch.object(logger, 'error') as mock_error:

        logger.loggerByMsgId("X_TEST_001")

        mock_trace.assert_not_called()
        mock_debug.assert_not_called()
        mock_info.assert_not_called()
        mock_warning.assert_not_called()
        mock_error.assert_not_called()


def test_logger_by_msg_id_higher_log_level():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, 'trace') as mock_trace, \
         mock.patch.object(logger, 'debug') as mock_debug, \
         mock.patch.object(logger, 'info') as mock_info, \
         mock.patch.object(logger, 'warning') as mock_warning, \
         mock.patch.object(logger, 'error') as mock_error:

        logger.loggerByMsgId("T001")
        logger.loggerByMsgId("D001")
        logger.loggerByMsgId("I001")
        logger.loggerByMsgId("W001")
        logger.loggerByMsgId("E001")

        mock_trace.assert_not_called()
        mock_debug.assert_not_called()
        mock_info.assert_not_called()
        mock_warning.assert_not_called()
        mock_error.assert_called_once_with("E001", None)


def test_loggerByMsgId_with_invalid_msg_values():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    with mock.patch.object(logger, 'info') as mock_info:
        # Call loggerByMsgId with an invalid msg_values type (string instead of tuple)
        logger.loggerByMsgId("I_TEST_001", "invalid_value")

        # Check that the info method was called with the correct arguments
        mock_info.assert_called_once_with("I_TEST_001", "invalid_value")

    # Verify that no exception was raised and the method completed
    assert True


def test_trace_no_output_when_log_level_higher():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    with mock.patch('builtins.print') as mock_print:
        logger.trace("Test trace message")
        mock_print.assert_not_called()


def test_trace_output_when_log_level_equal_to_trace():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.TRACE

    with mock.patch('builtins.print') as mock_print:
        with mock.patch.object(logger, '_format_message', return_value="Formatted TRACE message") as mock_format:
            logger.trace("Test trace message")

            mock_format.assert_called_once_with("TRACE", None, "Test trace message")
            mock_print.assert_called_once_with("Formatted TRACE message")


def test_trace_with_empty_message():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.TRACE

    with mock.patch('builtins.print') as mock_print:
        logger.trace("")

    mock_print.assert_called_once()
    printed_message = mock_print.call_args[0][0]
    assert "[TRACE][test_jobnet_id]" in printed_message
    assert printed_message.endswith("]")  # Empty message at the end


def test_trace_with_non_string_input():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.TRACE

    with mock.patch('builtins.print') as mock_print:
        logger.trace(42)
        logger.trace(True)
        logger.trace(3.14)

    assert mock_print.call_count == 3
    for call in mock_print.call_args_list:
        formatted_message = call[0][0]
        assert "[TRACE][test_jobnet_id]" in formatted_message
    assert "42" in mock_print.call_args_list[0][0][0]
    assert "True" in mock_print.call_args_list[1][0][0]
    assert "3.14" in mock_print.call_args_list[2][0][0]


def test_debug_no_print_when_log_level_higher():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    with mock.patch('builtins.print') as mock_print:
        logger.debug("This is a debug message")
        mock_print.assert_not_called()


def test_debug_message_printed_when_log_level_is_debug():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.DEBUG

    with mock.patch('builtins.print') as mock_print:
        logger.debug("Test debug message")

        mock_print.assert_called_once()
        printed_message = mock_print.call_args[0][0]
        assert "[DEBUG][test_jobnet_id]Test debug message" in printed_message


def test_info_method_when_log_level_is_info():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO
    msg_id = "I_TEST_001"
    msg_values = ("test_param",)

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Test info message with param: %s"
        mock_format_message.return_value = "[formatted_message]"

        logger.info(msg_id, msg_values)

        mock_get_content.assert_called_once_with("INFO", msg_id, msg_values)
        mock_format_message.assert_called_once_with("INFO", msg_id, "Test info message with param: %s")
        mock_print.assert_called_once_with("[formatted_message]")


def test_info_method_when_log_level_higher_than_info():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch('builtins.print') as mock_print:

        logger.info("I_TEST_001", ("test_value",))

        mock_get_content.assert_not_called()
        mock_print.assert_not_called()


def test_info_with_msg_values():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Test message with value: %s"
        mock_format_message.return_value = "[formatted message]"

        logger.info("I_TEST_001", ("test_value",))

        mock_get_content.assert_called_once_with("INFO", "I_TEST_001", ("test_value",))
        mock_format_message.assert_called_once_with("INFO", "I_TEST_001", "Test message with value: %s")
        mock_print.assert_called_once_with("[formatted message]")


def test_info_with_mismatched_msg_values():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Info message with %s placeholder"
        mock_format_message.return_value = "[formatted message]"

        logger.info("I_TEST_001", ("param1", "param2"))  # More parameters than placeholders

        mock_get_content.assert_called_once_with("INFO", "I_TEST_001", ("param1", "param2"))
        mock_format_message.assert_called_once_with("INFO", "I_TEST_001", "Info message with %s placeholder")
        mock_print.assert_called_once_with("[formatted message]")


def test_warning_when_log_level_is_warn():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.WARN

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Test warning message"
        mock_format_message.return_value = "[FORMATTED] Test warning message"

        logger.warning("W_TEST_001", ("param1", "param2"))

        mock_get_content.assert_called_once_with("WARN", "W_TEST_001", ("param1", "param2"))
        mock_format_message.assert_called_once_with("WARN", "W_TEST_001", "Test warning message")
        mock_print.assert_called_once_with("[FORMATTED] Test warning message")


def test_warning_method_when_log_level_higher_than_warn():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch('builtins.print') as mock_print:

        logger.warning("W_TEST_001", ("test_param",))

        mock_get_content.assert_not_called()
        mock_print.assert_not_called()


def test_warning_with_msg_values():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.WARN

    with mock.patch.object(logger, '_get_message_content') as mock_get_message, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_message.return_value = "Warning: Test value is %s"
        mock_format_message.return_value = "[2023/05/15 10:30:45][WARN][test_jobnet_id][W_TEST_001]Warning: Test value is 42"

        logger.warning("W_TEST_001", (42,))

        mock_get_message.assert_called_once_with("WARN", "W_TEST_001", (42,))
        mock_format_message.assert_called_once_with("WARN", "W_TEST_001", "Warning: Test value is %s")
        mock_print.assert_called_once_with("[2023/05/15 10:30:45][WARN][test_jobnet_id][W_TEST_001]Warning: Test value is 42")


def test_warning_with_mismatched_msg_values():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.WARN

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Warning message with %s and %s"
        mock_format_message.return_value = "Formatted warning message"

        logger.warning("W_TEST_001", ("one_value",))

        mock_get_content.assert_called_once_with("WARN", "W_TEST_001", ("one_value",))
        mock_format_message.assert_called_once_with("WARN", "W_TEST_001", "Warning message with %s and %s")
        mock_print.assert_called_once_with("Formatted warning message")


def test_error_method_with_error_log_level():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Test error message"
        mock_format_message.return_value = "Formatted error message"

        logger.error("E_TEST_001", ("param1", "param2"))

        mock_get_content.assert_called_once_with("ERROR", "E_TEST_001", ("param1", "param2"))
        mock_format_message.assert_called_once_with("ERROR", "E_TEST_001", "Test error message")
        mock_print.assert_called_once_with("Formatted error message")


def test_error_method_when_log_level_higher():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR + 1  # Set log level higher than ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch('builtins.print') as mock_print:

        logger.error("E_TEST_001", ("Test error",))

        mock_get_content.assert_not_called()
        mock_print.assert_not_called()


@mock.patch('builtins.print')
def test_error_with_special_characters(mock_print):
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content:
        mock_get_content.return_value = "Error message: Special@#$%^&* and Chars!@#$%^&*"
        logger.error("E_TEST_002", ("Special@#$%^&*", "Chars!@#$%^&*"))

    mock_get_content.assert_called_once_with("ERROR", "E_TEST_002", ("Special@#$%^&*", "Chars!@#$%^&*"))
    mock_print.assert_called_once()
    printed_message = mock_print.call_args[0][0]
    assert "ERROR" in printed_message
    assert "E_TEST_002" in printed_message
    assert "Error message: Special@#$%^&* and Chars!@#$%^&*" in printed_message


def test_error_with_empty_msg_id():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch.object(logger, '_get_message_content') as mock_get_content, \
         mock.patch.object(logger, '_format_message') as mock_format_message, \
         mock.patch('builtins.print') as mock_print:

        mock_get_content.return_value = "Error message"
        mock_format_message.return_value = "Formatted error message"

        logger.error("")

        mock_get_content.assert_called_once_with("ERROR", "", None)
        mock_format_message.assert_called_once_with("ERROR", "", "Error message")
        mock_print.assert_called_once_with("Formatted error message")


def test_error_common_when_log_level_is_error():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR

    with mock.patch('builtins.print') as mock_print:
        logger.error_common("Test error message")

        mock_print.assert_called_once()
        printed_message = mock_print.call_args[0][0]
        assert "[ERROR][test_jobnet_id]Test error message" in printed_message


def test_error_common_higher_log_level():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR + 1  # Set log level higher than ERROR

    with mock.patch('builtins.print') as mock_print:
        logger.error_common("Test error message")

        mock_print.assert_not_called()


def test_exception_with_error_level_and_args():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR
    test_exception = Exception("Test exception message")

    with mock.patch('builtins.print') as mock_print:
        logger.exception(test_exception)

    mock_print.assert_called_once()
    printed_message = mock_print.call_args[0][0]
    assert "ERROR" in printed_message
    assert "test_jobnet_id" in printed_message
    assert "Test exception message" in printed_message


def test_exception_with_higher_log_level():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR + 1  # Set log level higher than ERROR

    test_exception = Exception("Test exception message")

    with mock.patch('builtins.print') as mock_print:
        logger.exception(test_exception)
        mock_print.assert_not_called()


def test_is_debug_returns_true_when_log_level_is_debug():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.DEBUG

    assert logger.isDebug() is True


def test_is_debug_method():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.INFO
    assert logger.isDebug() is False

    logger.log_level = GlueLogger.DEBUG
    assert logger.isDebug() is True


def test_is_debug_returns_false_when_log_level_is_warn():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.WARN
    assert logger.isDebug() is False


def test_isDebug_returns_false_when_log_level_is_error():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.ERROR
    assert not logger.isDebug()


def test_is_debug_returns_true_for_trace():
    logger = GlueLogger("test_jobnet_id")
    logger.log_level = GlueLogger.TRACE
    assert logger.isDebug() is True
