from datetime import datetime, <PERSON><PERSON><PERSON>
import os
from sqlalchemy.exc import SQLAlchemyError


def get_test_file_path(relative_path):
    """テスト用ファイルの絶対パスを取得"""
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(base_dir, "test", "resources", relative_path)


def setup_sync_timestamp_table(db_connector):
    """同期タイムスタンプテーブルのデータ投入"""
    # 同期データの初期設定
    target_name = "JN_CP001-DF01_001_test.csv"
    insert_sync_sql = """
    INSERT INTO sync_timestamp
        (job_schedule_id, file_name, sync_datetime,
         d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
    VALUES
        (:job_schedule_id, :file_name, NOW(),
         :user, CURRENT_TIMESTAMP, :user, CURRENT_TIMESTAMP, 1)
    ON CONFLICT (job_schedule_id, file_name)
    DO UPDATE SET
        sync_datetime = NOW(),
        d_updated_user = :user,
        d_updated_datetime = CURRENT_TIMESTAMP,
        d_version = sync_timestamp.d_version + 1
    """
    params = {
        "job_schedule_id": "JN_CP001-DF01_001",
        "file_name": target_name,
        "user": "test_user",
    }
    db_connector.exec(insert_sync_sql, params)
    db_connector.commit()


class GlueTestUtils:
    @staticmethod
    def setup_test_db_tables(db_connector):
        """テスト用DBテーブルの作成"""
        try:
            # sync_timestampテーブルの作成
            create_sync_table_sql = """
            CREATE TABLE IF NOT EXISTS sync_timestamp (
                -- ジョブスケジュールID
                job_schedule_id VARCHAR(50) NOT NULL,
                -- 出力ファイル名(識別子)
                file_name VARCHAR(200) NOT NULL,
                -- 前回同期済タイムスタンプ
                sync_datetime TIMESTAMP NOT NULL,
                -- データ連携登録ユーザ
                d_created_user VARCHAR(100) NOT NULL,
                -- データ連携登録日時
                d_created_datetime TIMESTAMP NOT NULL,
                -- データ連携更新ユーザ
                d_updated_user VARCHAR(100) NOT NULL,
                -- データ連携更新日時
                d_updated_datetime TIMESTAMP NOT NULL,
                -- データ連携バージョン
                d_version NUMERIC(8,0) NOT NULL,
                -- 主キー制約
                CONSTRAINT pk_sync_timestamp PRIMARY KEY (job_schedule_id, file_name)
            )
            """
            db_connector.exec(create_sync_table_sql)

            # テーブルとカラムのコメントを追加
            comments_sql = """
            COMMENT ON TABLE sync_timestamp IS '同期タイムスタンプ(差分データ管理用)';
            COMMENT ON COLUMN sync_timestamp.job_schedule_id IS '同期対象のジョブスケジュールID';
            COMMENT ON COLUMN sync_timestamp.file_name IS '出力ファイル名の識別子';
            COMMENT ON COLUMN sync_timestamp.sync_datetime IS '前回同期済時点のタイムスタンプ';
            COMMENT ON COLUMN sync_timestamp.d_created_user IS 'データ連携登録ユーザ';
            COMMENT ON COLUMN sync_timestamp.d_created_datetime IS 'データ連携登録日時';
            COMMENT ON COLUMN sync_timestamp.d_updated_user IS 'データ連携更新ユーザ';
            COMMENT ON COLUMN sync_timestamp.d_updated_datetime IS 'データ連携更新日時';
            COMMENT ON COLUMN sync_timestamp.d_version IS 'データ連携バージョン';
            """
            db_connector.exec(comments_sql)

            # テストテーブルのクリーンアップ
            db_connector.exec("DROP TABLE IF EXISTS ut_test_table")

            # ut_test_tableの作成
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS ut_test_table (
                id SERIAL PRIMARY KEY,
                name varchar(100),
                value integer,
                created_user varchar(100),
                created_datetime timestamp DEFAULT CURRENT_TIMESTAMP,
                updated_user varchar(100),
                updated_datetime timestamp DEFAULT CURRENT_TIMESTAMP,
                d_created_user varchar(100),
                d_created_datetime timestamp DEFAULT CURRENT_TIMESTAMP,
                d_updated_user varchar(100),
                d_updated_datetime timestamp DEFAULT CURRENT_TIMESTAMP,
                d_version integer DEFAULT 1
            )
            """
            db_connector.exec(create_table_sql)

            # テストデータの日時を固定値で設定
            base_time = datetime(2025, 2, 14, 15, 0, 0)  # 2025-02-14 15:00:00
            time1 = base_time + timedelta(minutes=1)  # 15:01:00
            time2 = base_time + timedelta(minutes=2)  # 15:02:00
            time3 = base_time + timedelta(minutes=3)  # 15:03:00

            # テストデータの投入（差分テスト用のタイムスタンプ込み）
            insert_data_sql = """
            INSERT INTO ut_test_table
                (name, value,
                 created_user, created_datetime, updated_user, updated_datetime,
                 d_created_user, d_created_datetime, d_updated_user, d_updated_datetime, d_version)
            VALUES
                ('test1', 100,
                 'test_user', :time1, 'test_user', :time1,
                 'test_user', :time1, 'test_user', :time1, 1),
                ('test2', 200,
                 'test_user', :time2, 'test_user', :time2,
                 'test_user', :time2, 'test_user', :time2, 1),
                ('test3', 300,
                 'test_user', :time3, 'test_user', :time3,
                 'test_user', :time3, 'test_user', :time3, 1)
            """

            db_connector.exec(
                insert_data_sql,
                {"time1": time1, "time2": time2, "time3": time3},
            )

            # シーケンスのリセット
            db_connector.exec(
                "SELECT setval('ut_test_table_id_seq', (SELECT MAX(id) FROM ut_test_table))"
            )
            db_connector.commit()
        except SQLAlchemyError as e:
            if (
                hasattr(db_connector, "_is_in_transaction")
                and db_connector._is_in_transaction
            ):
                db_connector.rollback()
            raise

    @staticmethod
    def cleanup_test_resources(db_connector):
        """テストリソースのクリーンアップ"""
        try:
            # テストテーブルの削除
            # db_connector.exec("DROP TABLE IF EXISTS ut_test_table")
            # db_connector.exec("DROP TABLE IF EXISTS sync_timestamp")
            db_connector.commit()
        except SQLAlchemyError as e:
            db_connector.rollback()
            raise
