import pytest
import unittest
from unittest.mock import MagicMock, patch
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional
import sys
import os
import logging
import io

# 以下のパスは実際の環境に合わせて変更してください
sys.path.append("/home/<USER>/workspaces/tis-dlpf-app/job/source")
from source.db2xml.xml_builder import XMLBuilderMixin


class MockDBConnection:
    """DBコネクションのモック"""

    def __init__(self, query_results=None):
        self.query_results = query_results or {}

    def exec(self, query, params):
        """クエリ実行をモック"""
        # クエリ文字列をキーとして結果を返す
        # 実際のDBアクセスは行わず、事前に設定した結果を返す
        return self.query_results.get(query, [])


class TestXMLBuilder(unittest.TestCase):
    """XMLビルダーのテストクラス"""

    def setUp(self):
        """テスト前の準備"""

        # XMLBuilderMixinを継承したテスト用クラスを作成
        class TestBuilder(XMLBuilderMixin):
            def __init__(self, config, db_connection):
                self.config = config
                self.db_connection = db_connection
                # ロガーの設定
                self.logger = logging.getLogger("test_logger")
                self.logger.setLevel(logging.DEBUG)
                # ログを文字列としてキャプチャするためのハンドラ
                self.log_capture = io.StringIO()
                handler = logging.StreamHandler(self.log_capture)
                self.logger.addHandler(handler)

            def error_common(self, message):
                """共通エラーログ処理"""
                self.logger.error(message)

            def _get_query_params(self):
                """クエリパラメータ取得のモック"""
                return {"param1": "value1", "param2": "value2"}

            def _execute_query(self, query_name, params):
                """クエリ実行のモック"""
                return self.db_connection.exec(query_name, params)

            def _execute_sub_query(self, query_name, params, parent_record):
                """サブクエリ実行のモック"""
                return self.db_connection.exec(query_name, params)

            def _convert_value(self, value, type_name, format_str=None):
                """値変換のモック"""
                return str(value)

        # 基本設定
        self.config = {
            "output": {"root_element": "root", "indent": 2, "encoding": "utf-8"},
            "etl": {
                "main_query": "SELECT * FROM table",
                "sub_query": "SELECT * FROM sub_table WHERE parent_id = :parent_id",
            },
        }

        # デフォルトは空のDBコネクション
        self.db_connection = MockDBConnection()

        # テスト対象のインスタンスを作成
        self.builder = TestBuilder(self.config, self.db_connection)

    def test_main_query_zero_records(self):
        """MainQueryが0件の場合のテスト"""
        # 設定
        mapping = {
            "target_element": "product",
            "query": "main_query",  # 0件を返すクエリ
            "attributes": {"product-id": "mail_order_product_cd"},  # 必須属性
            "child_elements": [{"target_element": "child", "value": "固定値"}],
        }

        # 空のクエリ結果を事前に設定
        query_results = {"main_query": []}

        # XMLビルダーの実行
        root = ET.Element("root")
        result = self.builder._build_xml_element(query_results, mapping, root)

        # 検証: 親要素がそのまま返されること
        self.assertEqual(result, root)

        # 検証: ログに0件の情報が出力されていること
        log_output = self.builder.log_capture.getvalue()
        self.assertIn("クエリ結果が0件のため要素作成をスキップ", log_output)

        # 検証: 子要素が作成されていないこと
        self.assertEqual(len(root), 0)

    def test_sub_query_zero_records(self):
        """SubQueryが0件の場合のテスト"""
        # 親要素のデータ
        parent_data = [{"parent_id": 1, "parent_name": "親要素"}]

        # 親要素のマッピング
        parent_mapping = {
            "target_element": "parent",
            "query": "parent_query",
            "attributes": {"id": "parent_id", "name": "parent_name"},
            "child_elements": [
                {
                    "target_element": "child",
                    "sub_query": "child_query",  # 0件を返すサブクエリ
                    "attributes": {"id": "child_id", "name": "child_name"},
                }
            ],
        }

        # クエリ結果を事前に設定
        query_results = {
            "parent_query": parent_data,
            "child_query": [],  # 子要素のクエリ結果は0件
        }

        # XMLビルダーの実行
        root = ET.Element("root")
        result = self.builder._build_xml_element(query_results, parent_mapping, root)

        # 検証: 親要素が作成されていること
        self.assertEqual(len(root), 1)
        parent_element = root[0]
        self.assertEqual(parent_element.tag, "parent")

        # 検証: 親要素の属性が正しいこと
        self.assertEqual(parent_element.get("id"), "1")
        self.assertEqual(parent_element.get("name"), "親要素")

        # 検証: 子要素が作成されていないこと
        self.assertEqual(len(parent_element), 0)

        # 検証: ログにサブクエリが0件の情報が出力されていること
        log_output = self.builder.log_capture.getvalue()
        self.assertIn("サブクエリ", log_output)
        self.assertIn("の結果が0件のため", log_output)

    def test_normal_case(self):
        """正常系のテスト（データあり）"""
        # 親要素のデータ
        parent_data = [{"parent_id": 1, "parent_name": "親要素"}]

        # 子要素のデータ
        child_data = [
            {"child_id": 101, "child_name": "子要素1"},
            {"child_id": 102, "child_name": "子要素2"},
        ]

        # 親要素のマッピング
        parent_mapping = {
            "target_element": "parent",
            "query": "parent_query",
            "attributes": {"id": "parent_id", "name": "parent_name"},
            "child_elements": [
                {
                    "target_element": "child",
                    "sub_query": "child_query",
                    "attributes": {"id": "child_id", "name": "child_name"},
                }
            ],
        }

        # クエリ結果を事前に設定
        query_results = {"parent_query": parent_data, "child_query": child_data}

        # XMLビルダーの実行
        root = ET.Element("root")
        result = self.builder._build_xml_element(query_results, parent_mapping, root)

        # 検証: 親要素が作成されていること
        self.assertEqual(len(root), 1)
        parent_element = root[0]
        self.assertEqual(parent_element.tag, "parent")

        # 検証: 親要素の属性が正しいこと
        self.assertEqual(parent_element.get("id"), "1")
        self.assertEqual(parent_element.get("name"), "親要素")

        # 検証: 子要素が作成されていること
        self.assertEqual(len(parent_element), 2)

        # 検証: 子要素の属性が正しいこと
        self.assertEqual(parent_element[0].tag, "child")
        self.assertEqual(parent_element[0].get("id"), "101")
        self.assertEqual(parent_element[0].get("name"), "子要素1")

        self.assertEqual(parent_element[1].tag, "child")
        self.assertEqual(parent_element[1].get("id"), "102")
        self.assertEqual(parent_element[1].get("name"), "子要素2")

    def test_empty_records_early_return(self):
        """空のレコードセットに対する早期リターンのテスト"""
        # MainQueryが0件を返すように設定
        empty_query_results = {}

        # XMLビルダーの実行
        mapping = {
            "target_element": "products",
            "query": "product_query",
            "child_elements": [
                {
                    "target_element": "product",
                    "attributes": {
                        "product-id": "mail_order_product_cd"  # 存在しない属性
                    },
                }
            ],
        }

        # ルート要素作成
        root = ET.Element("root")

        # XMLビルダーの実行
        result = self.builder._build_xml_element(empty_query_results, mapping, root)

        # 検証: 親要素がそのまま返されること
        self.assertEqual(result, root)

        # 検証: ログには適切なメッセージが含まれていること
        log_output = self.builder.log_capture.getvalue()
        self.assertIn("クエリからデータを取得", log_output)

        # 検証: 子要素が作成されていないこと
        self.assertEqual(len(root), 0)

    def test_mail_order_product_cd_missing(self):
        """mail_order_product_cdが存在しない場合のテスト"""
        # mail_order_product_cdが存在しないデータ
        invalid_data = [{"some_other_field": "value"}]

        # マッピング
        mapping = {
            "target_element": "product",
            "query": "product_query",
            "attributes": {"product-id": "mail_order_product_cd"},  # 存在しない属性
        }

        # クエリ結果を事前に設定
        query_results = {"product_query": invalid_data}

        # XMLビルダーの実行
        root = ET.Element("root")
        result = self.builder._build_xml_element(query_results, mapping, root)

        # 検証: 親要素がそのまま返されないこと (エラーが発生しないこと)
        self.assertNotEqual(result, root)

        # 検証: 子要素が作成されていること (属性が設定されていなくても要素自体は作成される)
        self.assertEqual(len(root), 1)

        # 検証: ログにエラーや警告が出力されていること
        log_output = self.builder.log_capture.getvalue()
        # ここではエラーログを詳細に検証する必要はないが、
        # 少なくともクラッシュしないことを確認している


if __name__ == "__main__":
    unittest.main()
