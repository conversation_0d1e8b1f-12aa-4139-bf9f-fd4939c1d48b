#!/bin/bash

# デバッグ情報の出力
echo "Current directory: $(pwd)"

# スクリプトのあるディレクトリに移動
script_dir="$(dirname "$0")"
echo "Moving to script directory: $script_dir"
cd "$script_dir"
echo "Current directory after first cd: $(pwd)"

# プロジェクトルートディレクトリに移動
echo "Moving to source directory"
cd ../source
echo "Current directory after second cd: $(pwd)"

# 既存のresources.zipがあれば削除
if [ -f "../tool/resources.zip" ]; then
    echo "Removing existing resources.zip"
    rm -f "../tool/resources.zip"
fi

# 必要なディレクトリの存在確認
echo "Checking directories..."
if [ ! -d "config" ]; then
    echo "Error: config directory not found"
    ls -la
    exit 1
fi

if [ ! -d "sql" ]; then
    echo "Error: sql directory not found"
    ls -la
    exit 1
fi

# resources.zipの作成
echo "Creating resources.zip..."
zip -v -r ../tool/resources.zip config/ sql/

zip_result=$?
echo "Zip command exit code: $zip_result"

if [ $zip_result -eq 0 ]; then
    echo "Successfully created resources.zip in tool directory"
    ls -l ../tool/resources.zip
else
    echo "Error: Failed to create resources.zip"
    exit 1
fi

