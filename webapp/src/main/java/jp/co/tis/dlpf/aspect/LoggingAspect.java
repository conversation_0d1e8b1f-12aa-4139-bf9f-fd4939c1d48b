package jp.co.tis.dlpf.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import jp.co.tis.dlpf.util.LoggerUtils;

@Aspect
@Component
public class LoggingAspect {

    @Around("execution(* jp.co.tis.dlpf.controller..*.*(..))")
    public Object setLoggingContext(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            LoggerUtils.setLogContext("TRX" + System.currentTimeMillis());
            return joinPoint.proceed();
        } finally {
            LoggerUtils.clearLogContext();
        }
    }
}
