package jp.co.tis.dlpf.aspect;

import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import jp.co.tis.dlpf.util.LoggerUtils;

@Aspect
@Component
public class ServiceLoggingAspect {

    private static final Logger logger = LoggerUtils.getLogger();

    @Around("execution(* jp.co.tis.dlpf.service..*.*(..))")
    public Object logServiceMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();

        logger.info("Begin service execution: {}.{}", className, methodName);

        try {
            Object result = joinPoint.proceed();
            logger.info("End service execution: {}.{}", className, methodName);
            return result;
        } catch (Exception e) {
            logger.error("Error in service execution: {}.{} - Error: {}",
                    className, methodName, e.getMessage(), e);
            throw e;
        }
    }
}