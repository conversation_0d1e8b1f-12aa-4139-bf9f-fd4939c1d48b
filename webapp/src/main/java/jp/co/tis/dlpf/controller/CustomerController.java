package jp.co.tis.dlpf.controller;

import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jp.co.tis.dlpf.dto.CustomerRequest;
import jp.co.tis.dlpf.dto.ErrorResponse;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.exception.InvalidEncodingException;
import jp.co.tis.dlpf.service.CustomerService;
import jp.co.tis.dlpf.util.LoggerUtils;

@RestController
@RequestMapping("/api/customers")
@Validated
public class CustomerController {
    private static final Logger logger = LoggerUtils.getLogger();
    private final CustomerService customerService;

    public CustomerController(CustomerService customerService) {
        this.customerService = customerService;
    }

    @PostMapping
    public ResponseEntity<ErrorResponse> createCustomer(
            @RequestBody @Validated CustomerRequest request,
            @RequestHeader(value = "Content-Type", required = false) String contentType) {

        if (contentType != null && !contentType.toLowerCase().contains("utf-8")) {
            throw new InvalidEncodingException("文字エンコーディングが不正です。UTF-8を指定してください。");
        }

        try {
            logger.info("Creating customer with ID: {}", request.getCustomerId());
            customerService.createCustomer(request);

            return ResponseEntity.ok(ErrorResponse.builder()
                    .code("200")
                    .message("OK")
                    .build());
        } catch (BusinessException e) {
            logger.error("Business error occurred while creating customer", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ErrorResponse.builder()
                            .code("500")
                            .message(e.getMessage())
                            .build());
        }
    }
}
