package jp.co.tis.dlpf.controller;

import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jp.co.tis.dlpf.dto.HealthCheckResponse;
import jp.co.tis.dlpf.service.HealthCheckService;
import jp.co.tis.dlpf.util.LoggerUtils;

@RestController
@RequestMapping("/api/healthcheck")
public class HealthCheckController {
    private static final Logger logger = LoggerUtils.getLogger();
    private final HealthCheckService healthCheckService;

    @Autowired
    public HealthCheckController(HealthCheckService healthCheckService) {
        this.healthCheckService = healthCheckService;
    }

    @GetMapping
    public ResponseEntity<HealthCheckResponse> checkHealth() {
        logger.info("Health check requested");

        try {
            boolean isDatabaseHealthy = healthCheckService.isDatabaseHealthy();

            if (isDatabaseHealthy) {
                logger.info("Health check successful");
                return ResponseEntity.ok(HealthCheckResponse.builder()
                        .status("UP")
                        .message("Service is healthy")
                        .build());
            } else {
                logger.error("Health check failed: Database check failed");
                return ResponseEntity.status(500)
                        .body(HealthCheckResponse.builder()
                                .status("DOWN")
                                .message("Database check failed")
                                .build());
            }
        } catch (Exception e) {
            logger.error("Health check failed", e);
            return ResponseEntity.status(500)
                    .body(HealthCheckResponse.builder()
                            .status("DOWN")
                            .message("Service is unhealthy: " + e.getMessage())
                            .build());
        }
    }
}