package jp.co.tis.dlpf.controller;

import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jp.co.tis.dlpf.dto.PointAndRankOrderRequest;
import jp.co.tis.dlpf.dto.PointAndRankOrderResponse;
import jp.co.tis.dlpf.exception.InvalidEncodingException;
import jp.co.tis.dlpf.service.PointAndRankOrderService;
import jp.co.tis.dlpf.util.LoggerUtils;

@RestController
@RequestMapping("/api")
@Validated
public class PointAndRankOrderController {
    private static final Logger logger = LoggerUtils.getLogger();
    private final PointAndRankOrderService pointAndRankOrderService;

    public PointAndRankOrderController(PointAndRankOrderService pointAndRankOrderService) {
        this.pointAndRankOrderService = pointAndRankOrderService;
    }

    /**
     * ポイント・ランク注文情報を登録するAPI
     * 
     * @param request     リクエスト情報
     * @param contentType コンテントタイプ
     * @return レスポンス情報
     */
    @PostMapping("/point-and-rank-orders")
    public PointAndRankOrderResponse registerPointAndRankOrder(
            @RequestBody @Validated PointAndRankOrderRequest request,
            @RequestHeader(value = "Content-Type", required = false) String contentType) {

        validateRequest(contentType);

        logger.info("Registering point and rank order: {}", request.getOrder_no());

        pointAndRankOrderService.registerPointAndRankOrder(request.getOrder_no());

        return createSuccessResponse();
    }

    private void validateRequest(String contentType) {
        if (contentType != null && !contentType.toLowerCase().contains("utf-8")) {
            throw new InvalidEncodingException("文字エンコーディングが不正です。UTF-8を指定してください。");
        }
    }

    private PointAndRankOrderResponse createSuccessResponse() {
        PointAndRankOrderResponse response = new PointAndRankOrderResponse();
        response.setCode(String.valueOf(HttpStatus.OK.value()));
        response.setMessage("OK");
        return response;
    }
}