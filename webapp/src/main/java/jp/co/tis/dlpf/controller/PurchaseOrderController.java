package jp.co.tis.dlpf.controller;

import java.time.LocalDate;
import java.time.LocalDateTime;

import org.apache.logging.log4j.Logger;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jp.co.tis.dlpf.dto.PurchaseOrderResponse;
import jp.co.tis.dlpf.exception.ApiValidationException;
import jp.co.tis.dlpf.exception.InvalidEncodingException;
import jp.co.tis.dlpf.service.PurchaseOrderService;
import jp.co.tis.dlpf.util.LoggerUtils;

@RestController
@RequestMapping("/api/purchase-orders")
@Validated
public class PurchaseOrderController {
    private static final Logger logger = LoggerUtils.getLogger();
    private final PurchaseOrderService purchaseOrderService;

    public PurchaseOrderController(PurchaseOrderService purchaseOrderService) {
        this.purchaseOrderService = purchaseOrderService;
    }

    @GetMapping("/headers")
    public PurchaseOrderResponse getHeaders(
            @RequestParam(name = "customer_code") String customerCode,
            @RequestParam(name = "search_from", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate from,
            @RequestParam(name = "search_to", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate to,
            @RequestHeader(value = "Content-Type", required = false) String contentType) {

        validateRequest(customerCode, from, to, contentType);

        LocalDateTime fromDateTime = from != null ? from.atStartOfDay() : null;
        LocalDateTime toDateTime = to != null ? to.plusDays(1).atStartOfDay() : null;

        logger.info("Searching purchase orders for customer: {}, from: {}, to: {}",
                customerCode, fromDateTime, toDateTime);

        return purchaseOrderService.getPurchaseOrders(customerCode, fromDateTime, toDateTime);
    }

    private void validateRequest(String customerCode, LocalDate from, LocalDate to, String contentType) {
        if (contentType != null && !contentType.toLowerCase().contains("utf-8")) {
            throw new InvalidEncodingException("文字エンコーディングが不正です。UTF-8を指定してください。");
        }

        if (customerCode == null || customerCode.trim().isEmpty()) {
            throw new ApiValidationException("顧客番号は必須です。");
        } else if (!isValidCustomerCode(customerCode)) {
            throw new ApiValidationException("顧客番号は10桁の数字で入力してください。");
        }

        if (from != null && to != null && from.isAfter(to)) {
            throw new ApiValidationException("検索開始日時は検索終了日時より前の日付を指定してください。");
        }
    }

    private boolean isValidCustomerCode(String customerCode) {
        return customerCode.matches("^\\d{10}$");
    }
}
