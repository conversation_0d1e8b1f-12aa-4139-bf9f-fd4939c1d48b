package jp.co.tis.dlpf.controller;

import org.apache.logging.log4j.Logger;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jp.co.tis.dlpf.dto.PurchaseOrderDetailResponse;
import jp.co.tis.dlpf.exception.ApiValidationException;
import jp.co.tis.dlpf.exception.InvalidEncodingException;
import jp.co.tis.dlpf.service.PurchaseOrderDetailService;
import jp.co.tis.dlpf.util.LoggerUtils;

@RestController
@RequestMapping("/api/purchase-orders")
@Validated
public class PurchaseOrderDetailController {
    private static final Logger logger = LoggerUtils.getLogger();
    private final PurchaseOrderDetailService purchaseOrderDetailService;

    public PurchaseOrderDetailController(PurchaseOrderDetailService purchaseOrderDetailService) {
        this.purchaseOrderDetailService = purchaseOrderDetailService;
    }

    @GetMapping("/details")
    public PurchaseOrderDetailResponse getDetails(
            @RequestParam(name = "order_no") String orderNo,
            @RequestParam(name = "customer_code", required = false) String customerCode,
            @RequestHeader(value = "Content-Type", required = false) String contentType) {

        validateRequest(orderNo, contentType);

        logger.info("Searching purchase order details for order: {}, customer: {}",
                   orderNo, customerCode);

        return purchaseOrderDetailService.getPurchaseOrderDetails(orderNo, customerCode);
    }

    private void validateRequest(String orderNo, String contentType) {
        if (contentType != null && !contentType.toLowerCase().contains("utf-8")) {
            throw new InvalidEncodingException("文字エンコーディングが不正です。UTF-8を指定してください。");
        }

        if (orderNo == null || orderNo.trim().isEmpty()) {
            throw new ApiValidationException("注文番号は必須です。");
        }
    }
}
