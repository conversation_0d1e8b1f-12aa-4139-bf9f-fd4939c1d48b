package jp.co.tis.dlpf.controller;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jp.co.tis.dlpf.dto.SubscriptionHistoryResponse;
import jp.co.tis.dlpf.exception.InvalidEncodingException;
import jp.co.tis.dlpf.service.SubscriptionHistoryService;

@RestController
@RequestMapping("/api/subscription-histories")
@Validated
public class SubscriptionHistoryController {
    private final SubscriptionHistoryService subscriptionHistoryService;

    public SubscriptionHistoryController(SubscriptionHistoryService subscriptionHistoryService) {
        this.subscriptionHistoryService = subscriptionHistoryService;
    }

    @GetMapping
    public SubscriptionHistoryResponse getSubscriptionHistories(
            @RequestParam(name = "customer_code") String customerCode,
            @RequestHeader(value = "Content-Type", required = false) String contentType) {

        // Add encoding validation
        if (contentType != null && !contentType.toLowerCase().contains("utf-8")) {
            throw new InvalidEncodingException("文字エンコーディングが不正です。UTF-8を指定してください。");
        }

        return subscriptionHistoryService.getSubscriptionHistories(customerCode);
    }
}
