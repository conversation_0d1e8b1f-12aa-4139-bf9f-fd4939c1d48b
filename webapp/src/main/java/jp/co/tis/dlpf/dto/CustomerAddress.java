package jp.co.tis.dlpf.dto;

import lombok.Data;

@Data
public class CustomerAddress {
    private String address_id;
    private String first_name;
    private String last_name;
    private String c_ad_firstNameKana;
    private String c_ad_lastNameKana;
    private String address1;
    private String address2;
    private String address3;
    private String address4;
    private String postal_code;
    private String phone;
    private Boolean c_mainAddressFlag;
    private String c_SFSCaddress_id;
    private String corporation_post_name;
} 