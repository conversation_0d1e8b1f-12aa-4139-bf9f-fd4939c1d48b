package jp.co.tis.dlpf.dto;

import java.time.LocalDate;
import java.util.List;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class CustomerRequest {
    @NotBlank
    private String customerId;

    @NotBlank
    private String customerNo;

    @NotBlank
    private String lastName;

    @NotBlank
    private String firstName;

    @Size(max = 40)
    private String c_lastNameKana;

    @Size(max = 40)
    private String c_firstNameKana;

    @NotBlank
    @Email
    @Size(max = 256)
    private String email;

    @NotNull
    private LocalDate birthday;

    @NotNull
    @Min(1)
    @Max(2)
    private Integer gender;

    private String member_kbn;
    private Boolean c_mailMagazineFlag;
    private String mail_advisability_flg;
    private String bd_advisability_flg;
    private String shipped_mail_flg;
    private Integer niyose_flg;
    private String shipping_method_kbn;
    private String customer_valid_sts;
    private String create_date;

    private List<CustomerAddress> addresses;
    private List<CustomerCreditCard> creditcard;
}