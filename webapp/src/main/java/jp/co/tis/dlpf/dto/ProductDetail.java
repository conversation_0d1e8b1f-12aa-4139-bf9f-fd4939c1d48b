package jp.co.tis.dlpf.dto;

import lombok.Data;

@Data
public class ProductDetail {
    private String commodity_code;                 // 商品コード
    private String commodity_name;                 // 商品名
    private Integer purchasing_amount;             // 購入数量
    private Integer unit_price;                    // 単価
    private Integer commodity_tax;                 // 商品税額
    private Integer retail_price;                  // キャンペーン単価
    private Integer retail_tax;                    // キャンペーン単価参考税額
    private Integer total_with_tax;                // キャンペーン単価参考税込×注文数量
}
