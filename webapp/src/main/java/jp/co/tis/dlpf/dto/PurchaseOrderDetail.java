package jp.co.tis.dlpf.dto;

import java.util.List;

import lombok.Data;

@Data
public class PurchaseOrderDetail {
    private String order_number; // 注文番号
    private String order_datetime; // 注文日時
    private String order_status; // ステータス
    private String order_method; // 注文方法
    private String delivery_slip_number; // 配送伝票番号
    private String delivery_company; // 配送会社
    private String scheduled_delivery_date; // 配送予定日
    private String subscription_contract_number; // 定期契約番号
    private String last_name; // 姓
    private String first_name; // 名
    private String address1; // 住所1
    private String address2; // 住所2
    private String city; // 市町村
    private String postal_code; // 郵便番号
    private String prefecture; // 都道府県
    private String country; // 国
    private String phone_number; // 電話番号
    private String store_name; // 店舗名
    private String payment_method; // 支払方法
    private String card_number; // カード番号
    private String payment_installments; // 支払回数
    private String preferred_delivery_date; // 配送希望日
    private String preferred_delivery_time_start; // 配送希望時間帯開始
    private String preferred_delivery_time_end; // 配送希望時間帯終了
    private List<ProductInfo> product_info_list; // 商品情報リスト
    private Integer order_subtotal_amount; // 注文小計金額
    private Integer order_total_amount; // 注文合計金額
    private Integer shipping_fee; // 送料
    private String coupon_code; // クーポンコード
    private String coupon_name; // クーポン名
    private List<CampaignInfo> campaign_list; // キャンペーン情報
    private Integer earned_points; // 獲得ポイント
    private Integer used_points; // 使用ポイント
    private String customer_number; // 顧客番号
    private String customer_name; // 顧客名
    private String customer_name_kana; // 顧客名カナ
    private String order_receive_datetime; // 受注日時
    private String order_no_2; // 注文番号2
    private String ext_order_status; // 受注ステータス
    private String marketing_channel; // 販売経路
    private String regular_contract_no; // 定期契約番号
    private String delivery_recipient_name; // 配送先氏名(宛名)
    private String delivery_recipient_address; // 配送先住所
    private String delivery_recipient_tel; // 配送先電話番号
    private String ext_payment_method_type; // 支払方法
    private Integer bill_price; // 請求金額
    private Integer shipping_charge; // 送料
    private Integer total_before_campaign; // 割引前金額
    private String campaign_detail; // キャンペーン詳細
    private String order_datetime_2; // 受注日時
    private String order_user; // 受注者
    private String order_update_datetime; // 更新日時
    private String change_user; // 変更者
    private String shipping_date; // 出荷予定日
    private String arrival_date; // 到着日
    private String delivery_appointed_datetime; // 配送予定日時
    private String delivery_memo; // 配送メモ
    private String shipping_status; // 外部注文ステータス
    private String delivery_slip_no; // 配送伝票番号
    private String shipping_method; // 配送方法
    private List<ProductDetail> product_list; // 商品リスト
}
