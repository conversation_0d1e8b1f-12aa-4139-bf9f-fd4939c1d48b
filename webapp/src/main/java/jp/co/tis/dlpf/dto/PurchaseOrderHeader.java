package jp.co.tis.dlpf.dto;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class PurchaseOrderHeader {
    private String order_no;                  // 注文番号
    private LocalDateTime order_date;         // 注文日時
    private String order_status;              // 注文ステータス
    private String product_id;                // 商品ID
    private String product_name;              // 商品名
    private Integer product_count;            // 購入数量
    private String customer_no;               // 顧客番号
    private String order_date_2;              // 注文受付日
    private String order_no_2;                // 注文番号2
    private String sales_channel;             // 販売経路
    private String regular_delivery;          // 定期便
    private String shipping_status;           // 出荷ステータス
    private Integer invoice_amount;           // 請求金額
    private String payment_method;            // 支払方法
    private String prefecture_code;           // 都道府県
    private String scheduled_delivery_date;   // お届け予定日
}
