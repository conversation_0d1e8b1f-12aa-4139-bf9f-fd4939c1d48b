package jp.co.tis.dlpf.dto;

import lombok.Data;

@Data
public class SubscriptionHistoryDetail {
    private String account_number;
    private String subscription_number;
    private String subscription_status;
    private String next_shipment_date;
    private String sales_channel;
    private String product_code;
    private String product_name;
    private Integer quantity;
    private Integer product_price;
    private Integer total_amount;
    private String payment_method;
    private String cycle;
    private String shipping_name;
    private String shipping_address;
    private String shipping_tel;
    private String create_at;
    private String create_by;
    private String update_at;
    private String update_by;
    private String delivery_request;
}
