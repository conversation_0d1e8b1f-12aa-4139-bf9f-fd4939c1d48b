package jp.co.tis.dlpf.entity;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class PointAndRankBatchOrderInfo {
    private String order_no; // 注文番号
    private String d_created_user; // 作成者
    private LocalDateTime d_created_datetime; // 作成日時
    private String d_updated_user; // 更新者
    private LocalDateTime d_updated_datetime; // 更新日時
    private Integer d_version; // バージョン
}