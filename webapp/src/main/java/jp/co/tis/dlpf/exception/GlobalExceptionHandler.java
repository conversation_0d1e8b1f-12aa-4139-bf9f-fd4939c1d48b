package jp.co.tis.dlpf.exception;

import org.apache.logging.log4j.Logger;
import org.springframework.context.NoSuchMessageException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import jp.co.tis.dlpf.dto.ErrorResponse;
import jp.co.tis.dlpf.util.LoggerUtils;
import lombok.extern.slf4j.Slf4j;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class GlobalExceptionHandler {

        private static final Logger logger = LoggerUtils.getLogger();

        @ExceptionHandler(NoHandlerFoundException.class)
        public ResponseEntity<ErrorResponse> handleNoHandlerFoundException(NoHandlerFoundException ex) {
                logger.error("No handler found for " + ex.getRequestURL(), ex);
                return ResponseEntity
                                .status(HttpStatus.NOT_FOUND)
                                .body(ErrorResponse.builder()
                                                .code("404")
                                                .message("リクエストされたリソースが見つかりません。")
                                                .build());
        }

        @ExceptionHandler(RuntimeException.class)
        public ResponseEntity<ErrorResponse> handleRuntimeException(RuntimeException ex) {
                logger.error("Internal server error", ex);
                return ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(ErrorResponse.builder()
                                                .code("500")
                                                .message("内部サーバーエラーが発生しました。")
                                                .build());
        }

        @ExceptionHandler(ApiValidationException.class)
        public ResponseEntity<ErrorResponse> handleApiValidationException(ApiValidationException ex) {
                String message = ex.getMessage() != null ? ex.getMessage() : "入力パラメータが不正です。";
                logger.error(message, ex);
                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(DuplicateKeyException.class)
        public ResponseEntity<ErrorResponse> handleDuplicateKeyException(DuplicateKeyException ex) {
                String message = ex.getMessage() != null ? ex.getMessage() : "キー重複エラーが発生しています。";
                logger.error(message, ex);
                return ResponseEntity
                                .status(HttpStatus.CONFLICT)
                                .body(ErrorResponse.builder()
                                                .code("409")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(JsonParseException.class)
        public ResponseEntity<ErrorResponse> handleJsonParseException(JsonParseException ex) {
                logger.error("JSON parse error", ex);
                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message("JSONパースエラーが発生しました。")
                                                .build());
        }

        @ExceptionHandler(BusinessException.class)
        public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException ex) {
                String message = ex.getMessage();
                logger.error(message, ex);
                return ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(ErrorResponse.builder()
                                                .code("500")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(DataAccessException.class)
        public ResponseEntity<ErrorResponse> handleDataAccessException(DataAccessException ex) {
                String message = "内部サーバーエラーが発生しました。";
                logger.error(message, ex);
                return ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(ErrorResponse.builder()
                                                .code("500")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(TransactionTimeoutException.class)
        public ResponseEntity<ErrorResponse> handleTransactionTimeoutException(TransactionTimeoutException ex) {
                logger.error("Transaction timeout", ex);
                return ResponseEntity
                                .status(HttpStatus.REQUEST_TIMEOUT)
                                .body(ErrorResponse.builder()
                                                .code("408")
                                                .message("トランザクションタイムアウトしました。")
                                                .build());
        }

        @ExceptionHandler(NoSuchMessageException.class)
        public ResponseEntity<ErrorResponse> handleNoSuchMessageException(NoSuchMessageException ex) {
                logger.error("Message not found", ex);
                return ResponseEntity
                                .status(HttpStatus.SERVICE_UNAVAILABLE)
                                .body(ErrorResponse.builder()
                                                .code("503")
                                                .message("メッセージが見つかりません。")
                                                .build());
        }

        @ExceptionHandler(InvalidEncodingException.class)
        public ResponseEntity<ErrorResponse> handleInvalidEncodingException(InvalidEncodingException ex) {
                String message = "文字エンコーディングが不正です。UTF-8を指定してください。";
                logger.error(message, ex);
                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(MethodArgumentNotValidException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public ResponseEntity<ErrorResponse> handleValidationExceptions(MethodArgumentNotValidException ex) {
                String errorMessage = ex.getBindingResult().getFieldErrors().stream()
                                .map(error -> {
                                        if ("gender".equals(error.getField())) {
                                                return "性別は1または2を指定してください。";
                                        }
                                        return error.getField() + ": " + error.getDefaultMessage();
                                })
                                .findFirst()
                                .orElse("入力パラメータが不正です。");

                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message(errorMessage)
                                                .build());
        }

        @ExceptionHandler(HttpMessageNotReadableException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public ResponseEntity<ErrorResponse> handleHttpMessageNotReadable(HttpMessageNotReadableException ex) {
                String message = "リクエストの形式が不正です。";
                if (ex.getMessage() != null && ex.getMessage().contains("Required request body is missing")) {
                        message = "リクエストボディが空です。";
                }

                logger.error(message, ex);
                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(Exception.class)
        @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
        public ResponseEntity<ErrorResponse> handleAllExceptions(Exception ex) {
                logger.error("Unexpected error occurred", ex);
                return ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(ErrorResponse.builder()
                                                .code("500")
                                                .message("予期せぬエラーが発生しました。")
                                                .build());
        }

        @ExceptionHandler(MissingServletRequestParameterException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public ResponseEntity<ErrorResponse> handleMissingParameter(MissingServletRequestParameterException ex) {
                String paramName = ex.getParameterName();
                String message = String.format("%sは必須です。", paramName);
                logger.error("Required parameter missing: " + paramName);
                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message(message)
                                                .build());
        }

        @ExceptionHandler(MethodArgumentTypeMismatchException.class)
        @ResponseStatus(HttpStatus.BAD_REQUEST)
        public ResponseEntity<ErrorResponse> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException ex) {
                String paramName = ex.getName();
                String message = String.format("%sで入力バリデーションエラーが発生しました。", paramName);
                logger.error(message + " Value: " + ex.getValue(), ex);

                return ResponseEntity
                                .status(HttpStatus.BAD_REQUEST)
                                .body(ErrorResponse.builder()
                                                .code("400")
                                                .message(message)
                                                .build());
        }
}
