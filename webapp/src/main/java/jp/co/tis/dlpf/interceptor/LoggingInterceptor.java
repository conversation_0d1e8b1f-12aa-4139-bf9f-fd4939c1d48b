package jp.co.tis.dlpf.interceptor;

import org.apache.logging.log4j.Logger;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jp.co.tis.dlpf.util.LoggerUtils;

@Component
public class LoggingInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerUtils.getLogger();

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            logger.info("Begin executing {} - Method: {} {}",
                    handlerMethod.getBeanType().getSimpleName(),
                    request.getMethod(),
                    request.getRequestURI());
        }
        return true;
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler, @Nullable ModelAndView modelAndView) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            logger.info("End executing {} - Method: {} {} - Status: {}",
                    handlerMethod.getBeanType().getSimpleName(),
                    request.getMethod(),
                    request.getRequestURI(),
                    response.getStatus());
        }
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler, @Nullable Exception ex) throws Exception {
        if (ex != null) {
            logger.error("Error during request processing: {}", ex.getMessage(), ex);
        }
    }
}
