package jp.co.tis.dlpf.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import jp.co.tis.dlpf.entity.PointAndRankBatchOrderInfo;

@Mapper
public interface PointAndRankOrderMapper {
    /**
     * ポイント・ランク注文情報を登録する
     * 
     * @param orderInfo 登録するポイント・ランク注文情報
     * @return 登録件数
     */
    int insertPointAndRankOrder(@Param("orderInfo") PointAndRankBatchOrderInfo orderInfo);
}