package jp.co.tis.dlpf.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import jp.co.tis.dlpf.dto.CampaignInfo;
import jp.co.tis.dlpf.dto.ProductDetail;
import jp.co.tis.dlpf.dto.ProductInfo;
import jp.co.tis.dlpf.dto.PurchaseOrderDetail;

@Mapper
public interface PurchaseOrderDetailMapper {
    List<PurchaseOrderDetail> findDetailsFromOMS(
            @Param("orderNo") String orderNo,
            @Param("customerCode") String customerCode);

    List<PurchaseOrderDetail> findDetailsFromPOS(
            @Param("orderNo") String orderNo,
            @Param("customerCode") String customerCode);

    List<ProductInfo> findProductInfoFromOMS(
            @Param("orderNo") String orderNo);

    List<CampaignInfo> findCampaignInfoFromOMS(
            @Param("orderNo") String orderNo);

    List<ProductDetail> findProductDetailFromOMS(
            @Param("orderNo") String orderNo);

    List<ProductInfo> findProductInfoFromPOS(
            @Param("orderNo") String orderNo);

    List<ProductDetail> findProductDetailFromPOS(
            @Param("orderNo") String orderNo);
}
