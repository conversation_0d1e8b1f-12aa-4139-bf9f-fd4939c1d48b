package jp.co.tis.dlpf.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import jp.co.tis.dlpf.dto.PurchaseOrderHeader;

@Mapper
public interface PurchaseOrderMapper {
    List<PurchaseOrderHeader> findHeadersFromOMS(
        @Param("customerCode") String customerCode,
        @Param("searchFrom") LocalDateTime searchFrom,
        @Param("searchTo") LocalDateTime searchTo
    );

    List<PurchaseOrderHeader> findHeadersFromPOS(
        @Param("customerCode") String customerCode,
        @Param("searchFrom") LocalDateTime searchFrom,
        @Param("searchTo") LocalDateTime searchTo
    );
}
