package jp.co.tis.dlpf.service;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jp.co.tis.dlpf.dto.CustomerRequest;
import jp.co.tis.dlpf.dto.ErrorResponse;
import jp.co.tis.dlpf.exception.BusinessException;

@Service
public class CustomerService {
    private final RestTemplate restTemplate;
    private static final String CUSTOMER_API_URL = "/front/api/ext/customer/info/renewal";

    public CustomerService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public void createCustomer(CustomerRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CustomerRequest> entity = new HttpEntity<>(request, headers);

        try {
            ResponseEntity<ErrorResponse> response = restTemplate.postForEntity(
                    CUSTOMER_API_URL,
                    entity,
                    ErrorResponse.class);

            ErrorResponse error = response.getBody();
            if (error == null) {
                throw new BusinessException("予期せぬエラーが発生しました。");
            }

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new BusinessException(error.getMessage() != null && !error.getMessage().isEmpty()
                        ? error.getMessage()
                        : "顧客情報の更新に失敗しました。");
            }

            if (!"200".equals(error.getCode())) {
                throw new BusinessException(error.getMessage() != null && !error.getMessage().isEmpty()
                        ? error.getMessage()
                        : "顧客情報の更新に失敗しました。");
            }
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("顧客情報の更新に失敗しました。", e);
        }
    }
}
