package jp.co.tis.dlpf.service;

import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jp.co.tis.dlpf.mapper.HealthCheckMapper;
import jp.co.tis.dlpf.util.LoggerUtils;

@Service
public class HealthCheckService {
    private static final Logger logger = LoggerUtils.getLogger();
    private final HealthCheckMapper healthCheckMapper;

    @Autowired
    public HealthCheckService(HealthCheckMapper healthCheckMapper) {
        this.healthCheckMapper = healthCheckMapper;
    }

    /**
     * Checks the database connection by executing a simple query
     * 
     * @return true if the database is accessible, false otherwise
     */
    public boolean isDatabaseHealthy() {
        try {
            logger.debug("Checking database connection");
            Integer result = healthCheckMapper.checkDatabaseConnection();
            return result != null && result == 1;
        } catch (Exception e) {
            logger.error("Database health check failed", e);
            return false;
        }
    }
}