package jp.co.tis.dlpf.service;

import java.time.LocalDateTime;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jp.co.tis.dlpf.entity.PointAndRankBatchOrderInfo;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.PointAndRankOrderMapper;

@Service
public class PointAndRankOrderService {
    private static final Logger logger = LoggerFactory.getLogger(PointAndRankOrderService.class);
    private static final String CREATED_USER = "API-PT002-FA01";

    private final PointAndRankOrderMapper pointAndRankOrderMapper;

    public PointAndRankOrderService(PointAndRankOrderMapper pointAndRankOrderMapper) {
        this.pointAndRankOrderMapper = pointAndRankOrderMapper;
    }

    /**
     * ポイント・ランク注文情報を登録する
     * 
     * @param orderNo 注文番号
     * @return 登録件数
     * @throws BusinessException データベースアクセスエラーが発生した場合
     */
    @Transactional
    public int registerPointAndRankOrder(String orderNo) {
        try {
            PointAndRankBatchOrderInfo orderInfo = new PointAndRankBatchOrderInfo();
            orderInfo.setOrder_no(orderNo);
            orderInfo.setD_created_user(CREATED_USER);
            orderInfo.setD_created_datetime(LocalDateTime.now());
            orderInfo.setD_updated_user(CREATED_USER);
            orderInfo.setD_updated_datetime(LocalDateTime.now());
            orderInfo.setD_version(1);

            logger.info("Registering point and rank order: {}", orderNo);
            return pointAndRankOrderMapper.insertPointAndRankOrder(orderInfo);
        } catch (DataAccessException e) {
            logger.error("Failed to register point and rank order", e);
            throw new BusinessException("内部サーバーエラーが発生しました。", e);
        }
    }
}