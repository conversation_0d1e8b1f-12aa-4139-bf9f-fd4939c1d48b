package jp.co.tis.dlpf.service;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import jp.co.tis.dlpf.dto.PurchaseOrderDetail;
import jp.co.tis.dlpf.dto.PurchaseOrderDetailResponse;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.PurchaseOrderDetailMapper;

@Service
public class PurchaseOrderDetailService {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderDetailService.class);

    private final PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    public PurchaseOrderDetailService(PurchaseOrderDetailMapper purchaseOrderDetailMapper) {
        this.purchaseOrderDetailMapper = purchaseOrderDetailMapper;
    }

    public PurchaseOrderDetailResponse getPurchaseOrderDetails(String orderNo, String customerCode) {
        try {
            List<PurchaseOrderDetail> mergedResults = new ArrayList<>();

            // Process OMS results
            List<PurchaseOrderDetail> omsResults = purchaseOrderDetailMapper.findDetailsFromOMS(orderNo, customerCode);
            if (!omsResults.isEmpty()) {
                for (PurchaseOrderDetail detail : omsResults) {
                    // Populate OMS specific lists
                    detail.setProduct_info_list(purchaseOrderDetailMapper.findProductInfoFromOMS(orderNo));
                    detail.setCampaign_list(purchaseOrderDetailMapper.findCampaignInfoFromOMS(orderNo));
                    detail.setProduct_list(purchaseOrderDetailMapper.findProductDetailFromOMS(orderNo));
                }
                mergedResults.addAll(omsResults);
            }

            // Process POS results
            List<PurchaseOrderDetail> posResults = purchaseOrderDetailMapper.findDetailsFromPOS(orderNo, customerCode);
            if (!posResults.isEmpty()) {
                for (PurchaseOrderDetail detail : posResults) {
                    // Populate POS specific lists
                    detail.setProduct_info_list(purchaseOrderDetailMapper.findProductInfoFromPOS(orderNo));
                    detail.setCampaign_list(null); // POS doesn't have campaign information
                    detail.setProduct_list(purchaseOrderDetailMapper.findProductDetailFromPOS(orderNo));
                }
                mergedResults.addAll(posResults);
            }

            return createSuccessResponse(mergedResults);
        } catch (DataAccessException e) {
            logger.error("Failed to retrieve purchase order details", e);
            throw new BusinessException("購入履歴明細の取得に失敗しました。");
        }
    }

    private PurchaseOrderDetailResponse createSuccessResponse(List<PurchaseOrderDetail> detailList) {
        PurchaseOrderDetailResponse response = new PurchaseOrderDetailResponse();
        response.setCode("1");
        response.setErr_message("");
        response.setErr_code("");
        response.setDetail_list(detailList);
        return response;
    }
}
