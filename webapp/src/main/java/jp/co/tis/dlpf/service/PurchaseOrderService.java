package jp.co.tis.dlpf.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import jp.co.tis.dlpf.dto.PurchaseOrderHeader;
import jp.co.tis.dlpf.dto.PurchaseOrderResponse;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.PurchaseOrderMapper;

@Service
public class PurchaseOrderService {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderService.class);

    private final PurchaseOrderMapper purchaseOrderMapper;

    public PurchaseOrderService(PurchaseOrderMapper purchaseOrderMapper) {
        this.purchaseOrderMapper = purchaseOrderMapper;
    }

    public PurchaseOrderResponse getPurchaseOrders(String customerCode, LocalDateTime fromDateTime,
            LocalDateTime toDateTime) {
        try {
            List<PurchaseOrderHeader> omsResults = purchaseOrderMapper.findHeadersFromOMS(customerCode, fromDateTime,
                    toDateTime);
            List<PurchaseOrderHeader> posResults = purchaseOrderMapper.findHeadersFromPOS(customerCode, fromDateTime,
                    toDateTime);

            List<PurchaseOrderHeader> mergedResults = new ArrayList<>();
            if (omsResults != null && !omsResults.isEmpty()) {
                mergedResults.addAll(omsResults);
            }
            if (posResults != null && !posResults.isEmpty()) {
                mergedResults.addAll(posResults);
            }
            mergedResults.sort((a, b) -> b.getOrder_no().compareTo(a.getOrder_no()));

            return createSuccessResponse(mergedResults);
        } catch (DataAccessException e) {
            logger.error("Failed to retrieve purchase order headers", e);
            throw new BusinessException("購入履歴ヘッダの取得に失敗しました。");
        }
    }

    private PurchaseOrderResponse createSuccessResponse(List<PurchaseOrderHeader> orderList) {
        PurchaseOrderResponse response = new PurchaseOrderResponse();
        response.setCode("1");
        response.setErr_message("");
        response.setErr_code("");
        response.setOrder_list(orderList);
        return response;
    }
}
