package jp.co.tis.dlpf.service;

import java.util.List;

import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import jp.co.tis.dlpf.dto.SubscriptionHistoryDetail;
import jp.co.tis.dlpf.dto.SubscriptionHistoryResponse;
import jp.co.tis.dlpf.exception.ApiValidationException;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.SubscriptionHistoryMapper;
import jp.co.tis.dlpf.util.LoggerUtils;

@Service
public class SubscriptionHistoryService {
    private static final Logger logger = LoggerUtils.getLogger();
    private final SubscriptionHistoryMapper subscriptionHistoryMapper;

    public SubscriptionHistoryService(SubscriptionHistoryMapper subscriptionHistoryMapper) {
        this.subscriptionHistoryMapper = subscriptionHistoryMapper;
    }

    public SubscriptionHistoryResponse getSubscriptionHistories(String customerCode) {
        validateCustomerCode(customerCode);

        logger.info("Searching subscription histories for customer: {}", customerCode);

        try {
            List<SubscriptionHistoryDetail> histories =
                subscriptionHistoryMapper.findSubscriptionHistories(customerCode);

            return createResponse(histories);
        } catch (DataAccessException e) {
            logger.error("Failed to retrieve subscription histories", e);
            throw new BusinessException("定期購入履歴の取得に失敗しました。");
        }
    }

    private void validateCustomerCode(String customerCode) {
        if (customerCode == null || customerCode.trim().isEmpty()) {
            throw new ApiValidationException("顧客番号は必須です。");
        } else if (!isValidCustomerCode(customerCode)) {
            throw new ApiValidationException("顧客番号は12桁以下で入力してください。");
        }
    }

    private boolean isValidCustomerCode(String customerCode) {
        return customerCode.length() <= 12;
    }

    private SubscriptionHistoryResponse createResponse(List<SubscriptionHistoryDetail> histories) {
        SubscriptionHistoryResponse response = new SubscriptionHistoryResponse();
        response.setCode("200");
        response.setMessage("OK");
        response.setRegular_sale_order_detail(histories);
        return response;
    }
}
