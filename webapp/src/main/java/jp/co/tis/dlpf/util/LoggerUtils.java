package jp.co.tis.dlpf.util;

import java.util.UUID;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;

public class LoggerUtils {

    public static Logger getLogger() {
        return LogManager.getLogger(getCallerClassName());
    }

    public static void setLogContext(String transactionId) {
        ThreadContext.put("requestId", UUID.randomUUID().toString());
        ThreadContext.put("transactionid", transactionId);
        ThreadContext.put("threadid", String.valueOf(Thread.currentThread().getId()));
        ThreadContext.put("fncid", getFunctionId());
    }

    public static void clearLogContext() {
        ThreadContext.clearAll();
    }

    private static String getCallerClassName() {
        StackTraceElement[] stElements = Thread.currentThread().getStackTrace();
        for (int i = 1; i < stElements.length; i++) {
            StackTraceElement ste = stElements[i];
            if (!ste.getClassName().equals(LoggerUtils.class.getName()) &&
                ste.getClassName().indexOf("java.lang.Thread") != 0) {
                return ste.getClassName();
            }
        }
        return LoggerUtils.class.getName();
    }

    private static String getFunctionId() {
        // You can implement your own function ID generation logic here
        return "FNC" + System.currentTimeMillis();
    }
}
