<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.tis.dlpf.mapper.PurchaseOrderDetailMapper">

    <select id="findDetailsFromOMS" resultType="jp.co.tis.dlpf.dto.PurchaseOrderDetail">
        SELECT
            oh.order_no as order_number,
            oh.order_datetime as order_datetime,
            oh.ext_order_status as order_status,
            oh.marketing_channel as order_method,
            sh.delivery_slip_no as delivery_slip_number,
            sh.shipping_method as delivery_company,
            sh.arrival_date as scheduled_delivery_date,
            od.regular_contract_no as subscription_contract_number,
            oh.last_name,
            oh.first_name,
            oh.address3 as address1,
            oh.address4 as address2,
            oh.address2 as city,
            oh.postal_code,
            oh.prefecture_code as prefecture,
            oh.address1 as country,
            oh.phone_number,
            oh.ext_payment_method_type as payment_method,
            oh.credit_card_no as card_number,
            oh.credit_card_pay_count as payment_installments,
            sh.delivery_appointed_date as preferred_delivery_date,
            sh.delivery_appointed_time_start as preferred_delivery_time_start,
            sh.delivery_appointed_time_end as preferred_delivery_time_end,
            oh.subtotal_before_campaign as order_subtotal_amount,
            oh.bill_price as order_total_amount,
            sh.shipping_charge as shipping_fee,
            oh.coupon_code,
            oh.coupon_name,
            oh.grant_point_total as earned_points,
            oh.reduction_point_total as used_points,
            oh.neo_customer_no as customer_number,
            CAST(oh.last_name || ' ' || oh.first_name AS VARCHAR) as customer_name,
            CAST(oh.last_name_kana || ' ' || oh.first_name_kana AS VARCHAR) as customer_name_kana,
            CAST(TO_CHAR(oh.order_recieve_datetime, 'YYYY/MM/DD') AS VARCHAR) as order_receive_datetime,
            oh.order_no as order_no_2,
            CAST(och.campaign_instructions_name || ' ¥' || CAST(ocua.use_amount AS VARCHAR) AS VARCHAR) as campaign_detail,
            CASE CAST(oh.ext_order_status AS VARCHAR)
                WHEN '02' THEN '受注確定'
                WHEN '03' THEN '受注キャンセル'
                WHEN '10' THEN '同梱物設定中'
                WHEN '14' THEN '発送保留'
                WHEN '15' THEN '発送指示完了（倉庫）'
                WHEN '16' THEN '発送指示後強制停止'
                WHEN '20' THEN '発送完了'
                ELSE CAST(oh.ext_order_status AS VARCHAR)
            END as ext_order_status,
            CASE CAST(oh.marketing_channel AS VARCHAR)
                WHEN '01' THEN '電話'
                WHEN '02' THEN 'Webサイト（荷寄せあり）'
                WHEN '03' THEN 'ハガキ'
                WHEN '04' THEN 'ご案内コール'
                WHEN '05' THEN '相談室電話'
                WHEN '06' THEN 'その他'
                WHEN '07' THEN 'FAX'
                WHEN '08' THEN '封書'
                WHEN '09' THEN '社員販売'
                WHEN '13' THEN 'スマホサイト'
                WHEN '14' THEN 'イベント'
                WHEN '94' THEN 'Webキャンペーン'
                WHEN '95' THEN '楽天'
                WHEN '97' THEN 'Ａｍａｚｏｎ'
                WHEN '21' THEN 'Webサイト（荷寄せなし）'
                WHEN '30' THEN '直営店'
                ELSE CAST(oh.marketing_channel AS VARCHAR)
            END as marketing_channel,
            od.regular_contract_no,
            CAST(sh.address_last_name || ' ' || sh.address_first_name AS VARCHAR) as delivery_recipient_name,
            sh.address1 || sh.address2 || sh.address3 || sh.address4 as delivery_recipient_address,
            sh.phone_number as delivery_recipient_tel,
            CASE CAST(oh.ext_payment_method_type AS VARCHAR)
                WHEN '10' THEN '請求なし'
                WHEN '24' THEN 'クレジット'
                WHEN '27' THEN '代引'
                WHEN '28' THEN 'PayPay'
                WHEN '41' THEN 'd払い'
                WHEN '42' THEN 'au Pay'
                WHEN '43' THEN '楽天Pay'
                WHEN '44' THEN 'NP後払い'
                ELSE CAST(oh.ext_payment_method_type AS VARCHAR)
            END as ext_payment_method_type,
            oh.bill_price,
            COALESCE(sh.shipping_charge, 0) + COALESCE(sh.shipping_charge_tax, 0) as shipping_charge,
            oh.total_before_campaign,
            od.campaign_instructions_name as campaign_detail,
            CAST(TO_CHAR(oh.order_datetime, 'YYYY/MM/DD HH24:MI:SS') AS VARCHAR) as order_datetime_2,
            oh.order_user,
            CAST(TO_CHAR(oh.order_update_datetime, 'YYYY/MM/DD HH24:MI:SS') AS VARCHAR) as order_update_datetime,
            oh.change_user,
            CAST(TO_CHAR(sh.shipping_date, 'YYYY/MM/DD') AS VARCHAR) as shipping_date,
            CAST(TO_CHAR(sh.arrival_date, 'YYYY/MM/DD') AS VARCHAR) as arrival_date,
            CAST(
                CASE
                    WHEN sh.delivery_appointed_date IS NOT NULL
                    AND sh.delivery_appointed_time_start IS NOT NULL
                    AND sh.delivery_appointed_time_end IS NOT NULL
                    THEN TO_CHAR(sh.delivery_appointed_date, 'YYYY/MM/DD') || ' ' ||
                         sh.delivery_appointed_time_start || ' - ' ||
                         sh.delivery_appointed_time_end
                    ELSE NULL
                END AS VARCHAR
            ) as delivery_appointed_datetime,
            sh.delivery_memo,
            CASE CAST(oh.ext_order_status AS VARCHAR)
                WHEN '02' THEN '受注確定'
                WHEN '03' THEN '受注キャンセル'
                WHEN '10' THEN '同梱物設定中'
                WHEN '14' THEN '発送保留'
                WHEN '15' THEN '発送指示完了（倉庫）'
                WHEN '16' THEN '発送指示後強制停止'
                WHEN '20' THEN '発送完了'
                ELSE CAST(oh.ext_order_status AS VARCHAR)
            END as shipping_status,
            sh.delivery_slip_no,
            CASE CAST(sh.shipping_method AS VARCHAR)
                WHEN '01' THEN 'ゆうパック'
                WHEN '02' THEN 'ゆうメール'
                WHEN '03' THEN 'ヤマト宅急便'
                WHEN '04' THEN 'ヤマトメール便'
                WHEN '07' THEN '送り状不要'
                WHEN '11' THEN 'ヤマトコンパクト便'
                WHEN '12' THEN 'ヤマトネコポス便'
                ELSE CAST(sh.shipping_method AS VARCHAR)
            END as shipping_method
        FROM
            order_header oh
            LEFT JOIN shipping_header sh ON oh.order_no = sh.order_no
            LEFT JOIN order_detail od ON oh.order_no = od.order_no
            LEFT JOIN order_campaign_history och ON oh.order_no = och.order_no
            LEFT JOIN order_campaign_use_amount ocua ON oh.order_no = ocua.order_no
        WHERE
            oh.order_no = #{orderNo}
            <if test="customerCode != null">
            AND oh.ncs_customer_no = #{customerCode}
            </if>
    </select>

    <select id="findDetailsFromPOS" resultType="jp.co.tis.dlpf.dto.PurchaseOrderDetail">
        SELECT
            sah.chit_num as order_number,
            CAST(sah.business_date AS VARCHAR) as order_datetime,
            CASE CAST(sah.check_kind AS VARCHAR)
                WHEN '0' THEN '売上'
                WHEN '1' THEN '返品'
                ELSE CAST(sah.check_kind AS VARCHAR)
            END as order_status,
            sah.shop_cd as order_method,
            sah.total_amount as order_subtotal_amount,
            sap.pay_amount as order_total_amount,
            sah.customer_cd as customer_number,
            CAST(TO_CHAR(sah.business_date, 'YYYY/MM/DD') AS VARCHAR) as order_recieve_datetime,
            CAST(sah.shop_cd || sah.register_num || sah.business_date || sah.chit_num AS VARCHAR) as order_no_2,
            CASE CAST(sah.check_kind AS VARCHAR)
                WHEN '0' THEN '売上'
                WHEN '1' THEN '返品'
                ELSE CAST(sah.check_kind AS VARCHAR)
            END as ext_order_status,
            sm.shop_name_full as marketing_channel,
            CASE CAST(sap.pay_kind AS VARCHAR)
                WHEN '0' THEN '現金'
                WHEN '1' THEN 'ｸﾚｼﾞｯﾄ'
                WHEN '2' THEN '商品券'
                WHEN '3' THEN '売掛金'
                WHEN '4' THEN '電子ﾏﾈｰ'
                WHEN '5' THEN 'ポイント支払'
                WHEN '6' THEN 'ﾃﾞﾋﾞｯﾄ'
                WHEN '7' THEN 'Tポイント'
                WHEN '8' THEN 'Rポイント'
                WHEN '9' THEN 'QR支払'
                ELSE CAST(sap.pay_kind AS VARCHAR)
            END as ext_payment_method_type,
            sap.pay_amount as bill_price,
            sah.total_amount as total_before_campaign,
            CAST(sah.string_reserve1 || ' ¥' || CAST(sad.master_unit_price - sad.amount AS VARCHAR) AS VARCHAR) as campaign_detail,
            CAST(sah.system_datetime AS VARCHAR) as order_datetime_2,
            CAST(sah.sales_man_cd || ' / ' || sah.sales_man_name AS VARCHAR) as order_user
        FROM
            sales_info_alignment_header sah
            LEFT JOIN sales_info_alignment_payment sap
                ON sah.shop_cd = sap.shop_cd
                AND sah.register_num = sap.register_num
                AND sah.business_date = sap.business_date
                AND sah.receipt_num = sap.receipt_num
            LEFT JOIN sales_info_alignment_detail sad
                ON sah.shop_cd = sad.shop_cd
                AND sah.register_num = sad.register_num
                AND sah.business_date = sad.business_date
                AND sah.receipt_num = sad.receipt_num
            LEFT JOIN shop_mst sm ON sah.shop_cd = sm.shop_cd
        WHERE
            sah.chit_num = #{orderNo}
            <if test="customerCode != null">
            AND sah.customer_cd = #{customerCode}
            </if>
    </select>

    <!-- Product Info List Mapping -->
    <select id="findProductInfoFromOMS" resultType="jp.co.tis.dlpf.dto.ProductInfo">
        SELECT
            od.commodity_code as product_number,
            od.commodity_name as product_name,
            od.purchasing_amount as quantity,
            od.unit_price,
            od.commodity_tax_rate as product_tax_rate
        FROM
            order_detail od
        WHERE
            od.order_no = #{orderNo}
    </select>

    <select id="findProductInfoFromPOS" resultType="jp.co.tis.dlpf.dto.ProductInfo">
        SELECT
            sad.item_cd as product_number,
            sad.item_name as product_name,
            sad.quantity,
            sad.unit_price,
            sad.tax_rate as product_tax_rate
        FROM
            sales_info_alignment_header sah
            INNER JOIN sales_info_alignment_detail sad
                ON sah.shop_cd = sad.shop_cd
                AND sah.register_num = sad.register_num
                AND sah.business_date = sad.business_date
                AND sah.receipt_num = sad.receipt_num
        WHERE
            sah.chit_num = #{orderNo}
            AND sah.check_kind IN ('0', '1')
    </select>

    <!-- Campaign Info Mapping -->
    <select id="findCampaignInfoFromOMS" resultType="jp.co.tis.dlpf.dto.CampaignInfo">
        SELECT
            oc.campaign_instructions_name as campaign_name,
            oc.campaign_instructions_code as usage_code,
            ocua.tax_rate,
            ocua.use_amount as usage_amount
        FROM
            order_campaign oc
            INNER JOIN order_campaign_use_amount ocua
                ON oc.order_no = ocua.order_no
                AND oc.campaign_instructions_code = ocua.use_code
        WHERE
            oc.order_no = #{orderNo}
    </select>

    <!-- Product Detail Mapping -->
    <select id="findProductDetailFromOMS" resultType="jp.co.tis.dlpf.dto.ProductDetail">
        SELECT
            od.commodity_code,
            od.commodity_name,
            od.purchasing_amount,
            od.unit_price,
            FLOOR(od.unit_price + od.commodity_tax) as commodity_tax,
            od.retail_price,
            (od.retail_price + od.retail_tax) as retail_tax,
            (od.retail_price + od.retail_tax) * od.purchasing_amount as total_with_tax
        FROM
            order_detail od
        WHERE
            od.order_no = #{orderNo}
        ORDER BY
            od.commodity_code ASC
    </select>

    <select id="findProductDetailFromPOS" resultType="jp.co.tis.dlpf.dto.ProductDetail">
        SELECT
            sad.string_reserve4 as commodity_code,
            sad.item_name as commodity_name,
            sad.quantity as purchasing_amount,
            sad.unit_price,
            FLOOR(sad.unit_price * (sad.tax_rate + 100) / 100) as commodity_tax,
            (sad.amount -
             sad.line_minus_amount -
             sad.bmset_minus_amount -
             sad.point_minus_amount -
             sad.coupon_minus_amount -
             sad.sub_total_minus_amount) as retail_price,
            FLOOR((sad.amount -
                   sad.line_minus_amount -
                   sad.bmset_minus_amount -
                   sad.point_minus_amount -
                   sad.coupon_minus_amount -
                   sad.sub_total_minus_amount) *
                  (sad.tax_rate + 100) / 100
            ) as retail_tax,
            FLOOR((sad.amount -
                   sad.line_minus_amount -
                   sad.bmset_minus_amount -
                   sad.point_minus_amount -
                   sad.coupon_minus_amount -
                   sad.sub_total_minus_amount) *
                  (sad.tax_rate + 100) / 100
            ) * sad.quantity as total_with_tax
        FROM
            sales_info_alignment_header sah
            INNER JOIN sales_info_alignment_detail sad
                ON sah.shop_cd = sad.shop_cd
                AND sah.register_num = sad.register_num
                AND sah.business_date = sad.business_date
                AND sah.receipt_num = sad.receipt_num
        WHERE
            sah.chit_num = #{orderNo}
            AND sah.check_kind IN ('0', '1')
        ORDER BY
            sad.string_reserve4 ASC
    </select>
</mapper>
