<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.tis.dlpf.mapper.PurchaseOrderMapper">
    <!-- System A Query -->
    <select id="findHeadersFromOMS" resultType="jp.co.tis.dlpf.dto.PurchaseOrderHeader">
        SELECT DISTINCT
            main.*
        FROM (
            SELECT
                oh.order_no,
                oh.order_datetime as order_date,
                CASE oh.ext_order_status
                    WHEN '02' THEN '受注確定'
                    WHEN '03' THEN '受注キャンセル'
                    WHEN '10' THEN '同梱物設定中'
                    WHEN '14' THEN '発送保留'
                    WHEN '15' THEN '発送指示完了（倉庫）'
                    WHEN '16' THEN '発送指示後強制停止'
                    WHEN '20' THEN '発送完了'
                    ELSE oh.ext_order_status
                END as order_status,
                FIRST_VALUE(od.commodity_code) OVER (PARTITION BY oh.order_no ORDER BY od.commodity_code) as product_id,
                FIRST_VALUE(od.commodity_name) OVER (PARTITION BY oh.order_no ORDER BY od.commodity_code) as product_name,
                (SELECT COUNT(DISTINCT commodity_code)
                 FROM order_detail od2
                 WHERE od2.order_no = oh.order_no) as product_count,
                oh.neo_customer_no as customer_no,
                TO_CHAR(oh.order_datetime, 'YYYY/MM/DD') as order_date_2,
                oh.order_no as order_no_2,
                CASE oh.marketing_channel
                    WHEN '01' THEN '電話'
                    WHEN '02' THEN 'Webサイト（荷寄せあり）'
                    WHEN '03' THEN 'ハガキ'
                    WHEN '04' THEN 'ご案内コール'
                    WHEN '05' THEN '相談室電話'
                    WHEN '06' THEN 'その他'
                    WHEN '07' THEN 'FAX'
                    WHEN '08' THEN '封書'
                    WHEN '09' THEN '社員販売'
                    WHEN '13' THEN 'スマホサイト'
                    WHEN '14' THEN 'イベント'
                    WHEN '94' THEN 'Webキャンペーン'
                    WHEN '95' THEN '楽天'
                    WHEN '97' THEN 'Ａｍａｚｏｎ'
                    WHEN '21' THEN 'Webサイト（荷寄せなし）'
                    WHEN '30' THEN '直営店'
                    ELSE oh.marketing_channel
                END as sales_channel,
                CASE
                    WHEN od.regular_contract_no != '' THEN '○'
                    ELSE 'ー'
                END as regular_delivery,
                CASE oh.ext_order_status
                    WHEN '02' THEN '受注確定'
                    WHEN '03' THEN '受注キャンセル'
                    WHEN '10' THEN '同梱物設定中'
                    WHEN '14' THEN '発送保留'
                    WHEN '15' THEN '発送指示完了（倉庫）'
                    WHEN '16' THEN '発送指示後強制停止'
                    WHEN '20' THEN '発送完了'
                    ELSE oh.ext_order_status
                END as shipping_status,
                oh.bill_price as invoice_amount,
                CASE oh.ext_payment_method_type
                    WHEN '10' THEN '請求なし'
                    WHEN '24' THEN 'クレジット'
                    WHEN '27' THEN '代引'
                    WHEN '28' THEN 'PayPay'
                    WHEN '41' THEN 'd払い'
                    WHEN '42' THEN 'au Pay'
                    WHEN '43' THEN '楽天Pay'
                    WHEN '44' THEN 'NP後払い'
                    ELSE oh.ext_payment_method_type
                END as payment_method,
                sh.delivery_slip_no as prefecture_code,
                TO_CHAR(sh.arrival_date, 'YYYY/MM/DD') as scheduled_delivery_date
            FROM
                order_header oh
                LEFT JOIN order_detail od ON oh.order_no = od.order_no
                LEFT JOIN shipping_header sh ON oh.order_no = sh.order_no
            WHERE
                oh.order_no IS NOT NULL
                AND oh.neo_customer_no = #{customerCode}
                <if test="searchFrom != null">
                    AND oh.order_datetime >= #{searchFrom}
                </if>
                <if test="searchTo != null">
                    AND oh.order_datetime &lt;= #{searchTo}
                </if>
        ) main
        WHERE main.order_no IS NOT NULL
        ORDER BY
            order_no DESC
    </select>

    <!-- System B Query -->
    <select id="findHeadersFromPOS" resultType="jp.co.tis.dlpf.dto.PurchaseOrderHeader">
        SELECT DISTINCT
            main.*
        FROM (
            SELECT
                sah.chit_num as order_no,
                sah.business_date as order_date,
                CASE CAST(sah.check_kind AS VARCHAR)
                    WHEN '0' THEN '売上'
                    WHEN '1' THEN '返品'
                    ELSE CAST(sah.check_kind AS VARCHAR)
                END as order_status,
                CASE
                    WHEN sah.check_kind IN ('0', '1') THEN sad.item_cd
                    ELSE null
                END as product_id,
                CASE
                    WHEN sah.check_kind IN ('0', '1') THEN sad.item_name
                    ELSE null
                END as product_name,
                null as product_count,
                sah.customer_cd as customer_no,
                TO_CHAR(sah.business_date, 'YYYY/MM/DD') as order_date_2,
                CONCAT(sah.shop_cd, sah.register_num, sah.business_date, sah.chit_num) as order_no_2,
                sm.shop_name_full as sales_channel,
                null as regular_delivery,
                null as shipping_status,
                sap.pay_amount as invoice_amount,
                CASE CAST(sap.pay_kind AS VARCHAR)
                    WHEN '0' THEN '現金'
                    WHEN '1' THEN 'ｸﾚｼﾞｯﾄ'
                    WHEN '2' THEN '商品券'
                    WHEN '3' THEN '売掛金'
                    WHEN '4' THEN '電子ﾏﾈｰ'
                    WHEN '5' THEN 'ポイント支払'
                    WHEN '6' THEN 'ﾃﾞﾋﾞｯﾄ'
                    WHEN '7' THEN 'Tポイント'
                    WHEN '8' THEN 'Rポイント'
                    WHEN '9' THEN 'QR支払'
                    ELSE CAST(sap.pay_kind AS VARCHAR)
                END as payment_method,
                null as prefecture_code,
                null as scheduled_delivery_date
            FROM
                sales_info_alignment_header sah
                LEFT JOIN sales_info_alignment_detail sad
                    ON sah.shop_cd = sad.shop_cd
                    AND sah.register_num = sad.register_num
                    AND sah.business_date = sad.business_date
                    AND sah.receipt_num = sad.receipt_num
                LEFT JOIN sales_info_alignment_payment sap
                    ON sah.shop_cd = sap.shop_cd
                    AND sah.register_num = sap.register_num
                    AND sah.business_date = sap.business_date
                    AND sah.receipt_num = sap.receipt_num
                LEFT JOIN shop_mst sm
                    ON sah.shop_cd = sm.shop_cd
            WHERE
                sah.chit_num IS NOT NULL
                AND sah.customer_cd = #{customerCode}
                <if test="searchFrom != null">
                    AND sah.business_date >= #{searchFrom}
                </if>
                <if test="searchTo != null">
                    AND sah.business_date &lt;= #{searchTo}
                </if>
        ) main
        WHERE main.order_no IS NOT NULL
        ORDER BY
            order_no DESC
    </select>
</mapper>
