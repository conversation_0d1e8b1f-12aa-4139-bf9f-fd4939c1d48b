<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jp.co.tis.dlpf.mapper.SubscriptionHistoryMapper">
    <select id="findSubscriptionHistories" resultType="jp.co.tis.dlpf.dto.SubscriptionHistoryDetail">
        SELECT
            rsch.neo_customer_no as account_number,
            rsch.regular_contract_no as subscription_number,
            CASE CAST(rsch.regular_sale_cont_status AS VARCHAR)
                WHEN '1' THEN '継続中'
                WHEN '2' THEN '休止'
                WHEN '9' THEN '解約'
                ELSE CAST(rsch.regular_sale_cont_status AS VARCHAR)
            END as subscription_status,
            TO_CHAR(rscd.next_shipping_date, 'YYYY/MM/DD') as next_shipment_date,
            CASE CAST(rsch.marketing_channel AS VARCHAR)
                WHEN '01' THEN '電話'
                WHEN '02' THEN 'Webサイト（荷寄せあり）'
                WHEN '03' THEN 'ハガキ'
                WHEN '04' THEN 'ご案内コール'
                WHEN '05' THEN '相談室電話'
                WHEN '06' THEN 'その他'
                WHEN '07' THEN 'FAX'
                WHEN '08' THEN '封書'
                WHEN '09' THEN '社員販売'
                WHEN '13' THEN 'スマホサイト'
                WHEN '14' THEN 'イベント'
                WHEN '94' THEN 'Webキャンペーン'
                WHEN '95' THEN '楽天'
                WHEN '97' THEN 'Amazon'
                WHEN '21' THEN 'Webサイト（荷寄せなし）'
                WHEN '30' THEN '直営店'
                ELSE CAST(rsch.marketing_channel AS VARCHAR)
            END as sales_channel,
            rscd.commodity_code as product_code,
            rscd.commodity_name as product_name,
            rscd.contract_amount as quantity,
            rsc.retail_price as product_price,
            (rscd.contract_amount * rsc.retail_price) as total_amount,
            CASE CAST(rsch.ext_payment_method_type AS VARCHAR)
                WHEN '10' THEN '請求なし'
                WHEN '24' THEN 'クレジット'
                WHEN '27' THEN '代引'
                WHEN '28' THEN 'PayPay'
                WHEN '41' THEN 'd払い'
                WHEN '42' THEN 'au Pay'
                WHEN '43' THEN '楽天Pay'
                WHEN '44' THEN 'NP後払い'
                ELSE CAST(rsch.ext_payment_method_type AS VARCHAR)
            END as payment_method,
            rscd.regular_kind as cycle,
            CONCAT(
                ca.address_last_name, ' ', ca.address_first_name
            ) as shipping_name,
            CONCAT(
                ca.address1, ca.address2, ca.address3, ca.address4
            ) as shipping_address,
            ca.phone_number as shipping_tel,
            TO_CHAR(rsch.regular_sale_cont_datetime, 'YYYY/MM/DD HH:MI') as create_at,
            ua1.user_name as create_by,
            TO_CHAR(rsch.regular_update_datetime, 'YYYY/MM/DD HH:MI') as update_at,
            ua2.user_name as update_by,
            rsch.delivery_memo as delivery_request
        FROM
            regular_sale_cont_header rsch
            LEFT JOIN regular_sale_cont_detail rscd ON rsch.regular_contract_no = rscd.regular_contract_no
            LEFT JOIN regular_sale_base rsb ON rscd.commodity_code = rsb.commodity_code
            LEFT JOIN regular_sale_composition rsc ON rsb.regular_sale_code = rsc.regular_sale_code
            LEFT JOIN customer_address ca ON rsch.customer_code = ca.customer_code
            LEFT JOIN user_account ua1 ON rsch.order_user_code = ua1.user_code
            LEFT JOIN user_account ua2 ON rsch.change_user_code = ua2.user_code
        WHERE
            rsch.neo_customer_no = #{customerCode}
        ORDER BY
            rsch.regular_contract_no DESC
    </select>
</mapper>
