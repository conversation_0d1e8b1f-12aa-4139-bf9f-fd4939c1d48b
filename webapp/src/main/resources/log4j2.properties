status = warn

# Console appender configuration
appender.console.type = Console
appender.console.name = ConsoleAppender
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d{yyyy/MM/dd HH:mm:ss,SSS} [%p] %C %X{requestId} %X{transactionid} %X{threadid} %X{fncid} %m%n

# Root logger
rootLogger.level = info
rootLogger.appenderRef.console.ref = ConsoleAppender

# Application logger
logger.app.name = jp.co.tis.dlpf
logger.app.level = debug
logger.app.additivity = false
logger.app.appenderRef.console.ref = ConsoleAppender
