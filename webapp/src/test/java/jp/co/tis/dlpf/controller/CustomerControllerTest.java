package jp.co.tis.dlpf.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import jp.co.tis.dlpf.config.TestConfig;
import jp.co.tis.dlpf.dto.CustomerRequest;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.service.CustomerService;

@WebMvcTest(CustomerController.class)
@Import(TestConfig.class)
class CustomerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CustomerService customerService;

    @Test
    @WithMockUser
    void createCustomer_Success() throws Exception {
        String requestJson = """
                {
                    "customerNo": "910010",
                    "customerId": "001v00320SRm1nAAD",
                    "lastName": "変更管理",
                    "firstName": "花子",
                    "email": "<EMAIL>",
                    "birthday": "1989-09-09",
                    "gender": 2
                }
                """;

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.message").value("OK"));
    }

    @Test
    @WithMockUser
    void createCustomer_Error() throws Exception {
        doThrow(new BusinessException("顧客情報の更新に失敗しました。"))
                .when(customerService)
                .createCustomer(any(CustomerRequest.class));

        String requestJson = """
                {
                    "customerNo": "910010",
                    "customerId": "001v00320SRm1nAAD",
                    "lastName": "変更管理",
                    "firstName": "花子",
                    "email": "<EMAIL>",
                    "birthday": "1989-09-09",
                    "gender": 2
                }
                """;

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content(requestJson))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("顧客情報の更新に失敗しました。"));
    }

    @Test
    @WithMockUser
    void createCustomer_ValidationError() throws Exception {
        String requestJson = """
                {
                    "customer_no": "910010",
                    "customer_id": "001v00320SRm1nAAD",
                    "last_name": "変更管理",
                    "first_name": "花子",
                    "email": "invalid-email",
                    "birthday": "1989-09-09",
                    "gender": 2
                }
                """;

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").exists());
    }

    @Test
    @WithMockUser
    void createCustomer_NonUtf8Encoding() throws Exception {
        String requestJson = """
                {
                    "customerNo": "910010",
                    "customerId": "001v00320SRm1nAAD",
                    "lastName": "変更管理",
                    "firstName": "花子",
                    "email": "<EMAIL>",
                    "birthday": "1989-09-09",
                    "gender": 2
                }
                """;

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json; charset=ISO-8859-1")
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.message").value("OK"));
    }

    @Test
    @WithMockUser
    void createCustomer_NullContentType() throws Exception {
        String requestJson = """
                {
                    "customerNo": "910010",
                    "customerId": "001v00320SRm1nAAD",
                    "lastName": "変更管理",
                    "firstName": "花子",
                    "email": "<EMAIL>",
                    "birthday": "1989-09-09",
                    "gender": 2
                }
                """;

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.message").value("OK"));
    }

    @Test
    @WithMockUser
    void createCustomer_MalformedJson() throws Exception {
        String malformedJson = "{invalid json}";

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content(malformedJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("リクエストの形式が不正です。"));
    }

    @Test
    @WithMockUser
    void createCustomer_EmptyRequestBody() throws Exception {
        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content(""))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("リクエストボディが空です。"));
    }

    @Test
    @WithMockUser
    void createCustomer_InvalidGender() throws Exception {
        String requestJson = """
                {
                    "customerNo": "910010",
                    "customerId": "001v00320SRm1nAAD",
                    "lastName": "変更管理",
                    "firstName": "花子",
                    "email": "<EMAIL>",
                    "birthday": "1989-09-09",
                    "gender": 5
                }
                """;

        mockMvc.perform(post("/api/customers")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content(requestJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("性別は1または2を指定してください。"));
    }
}
