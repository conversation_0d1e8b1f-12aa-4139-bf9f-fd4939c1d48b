package jp.co.tis.dlpf.controller;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.servlet.MockMvc;

import jp.co.tis.dlpf.config.TestConfig;
import jp.co.tis.dlpf.service.HealthCheckService;

@WebMvcTest(HealthCheckController.class)
@Import(TestConfig.class)
class HealthCheckControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HealthCheckService healthCheckService;

    @Test
    void healthCheck_Success() throws Exception {
        // Arrange
        when(healthCheckService.isDatabaseHealthy()).thenReturn(true);

        // Act & Assert
        mockMvc.perform(get("/api/healthcheck"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.message").value("Service is healthy"));
    }

    @Test
    void healthCheck_DatabaseFailure() throws Exception {
        // Arrange
        when(healthCheckService.isDatabaseHealthy()).thenReturn(false);

        // Act & Assert
        mockMvc.perform(get("/api/healthcheck"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.status").value("DOWN"))
                .andExpect(jsonPath("$.message").value("Database check failed"));
    }

    @Test
    void healthCheck_ServiceException() throws Exception {
        // Arrange
        when(healthCheckService.isDatabaseHealthy()).thenThrow(new RuntimeException("Unexpected service error"));

        // Act & Assert
        mockMvc.perform(get("/api/healthcheck"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.status").value("DOWN"))
                .andExpect(jsonPath("$.message").value("Service is unhealthy: Unexpected service error"));
    }
}