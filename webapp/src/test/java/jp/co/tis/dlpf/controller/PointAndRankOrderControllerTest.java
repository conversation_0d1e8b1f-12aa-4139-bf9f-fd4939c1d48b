package jp.co.tis.dlpf.controller;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import jp.co.tis.dlpf.config.SecurityConfig;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.service.PointAndRankOrderService;

@WebMvcTest(PointAndRankOrderController.class)
@Import(SecurityConfig.class)
class PointAndRankOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PointAndRankOrderService pointAndRankOrderService;

    // Case 1: 正常系 - 登録成功
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenValidRequest_thenReturns200() throws Exception {
        when(pointAndRankOrderService.registerPointAndRankOrder(anyString())).thenReturn(1);

        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content("{\"order_no\":\"1234567890123456\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.message").value("OK"));
    }

    // Case 2: 正常系 - 境界値テスト (16文字の注文番号)
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenBoundaryValueOrderNo_thenReturns200() throws Exception {
        when(pointAndRankOrderService.registerPointAndRankOrder(anyString())).thenReturn(1);

        // 16文字の注文番号 (最大長)
        String exactLength16OrderNo = "1234567890123456";

        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content("{\"order_no\":\"" + exactLength16OrderNo + "\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200"))
                .andExpect(jsonPath("$.message").value("OK"));
    }

    // Case 6: 異常系 - 内部サーバーエラー
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenDatabaseError_thenReturns500() throws Exception {
        doThrow(new BusinessException("内部サーバーエラーが発生しました。"))
                .when(pointAndRankOrderService).registerPointAndRankOrder(anyString());

        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content("{\"order_no\":\"1234567890123456\"}"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value("500"))
                .andExpect(jsonPath("$.message").value("内部サーバーエラーが発生しました。"));
    }

    // Case 3: 異常系 - 必須パラメータなし
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenMissingRequiredParam_thenReturns400() throws Exception {
        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content("{}"))
                .andExpect(status().isBadRequest());
    }

    // Case 5: 異常系 - 不正なエンコーディング
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenInvalidEncoding_thenReturns400() throws Exception {
        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType("application/json; charset=shift-jis")
                .content("{\"order_no\":\"1234567890123456\"}"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("400"))
                .andExpect(jsonPath("$.message").value("文字エンコーディングが不正です。UTF-8を指定してください。"));
    }

    // 認証エラー (ドキュメントにない追加テスト)
    @Test
    void whenUnauthorized_thenReturns401() throws Exception {
        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"order_no\":\"1234567890123456\"}"))
                .andExpect(status().isUnauthorized());
    }

    // Case 4: 異常系 - 無効なパラメータ (注文番号が長すぎる)
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenOrderNoTooLong_thenReturns400() throws Exception {
        mockMvc.perform(post("/api/point-and-rank-orders")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
                .content("{\"order_no\":\"12345678901234567\"}"))
                .andExpect(status().isBadRequest());
    }
}