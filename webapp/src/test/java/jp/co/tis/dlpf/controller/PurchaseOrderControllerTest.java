package jp.co.tis.dlpf.controller;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import jp.co.tis.dlpf.config.SecurityConfig;
import jp.co.tis.dlpf.dto.PurchaseOrderHeader;
import jp.co.tis.dlpf.dto.PurchaseOrderResponse;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.service.PurchaseOrderService;

@WebMvcTest(PurchaseOrderController.class)
@Import(SecurityConfig.class)
class PurchaseOrderControllerTest {

        @Autowired
        private MockMvc mockMvc;

        @MockBean
        private PurchaseOrderService purchaseOrderService;

        @Test
        @WithMockUser(username = "user", password = "password")
        void whenValidRequest_thenReturns200() throws Exception {
                PurchaseOrderResponse mockResponse = createSuccessResponse(new ArrayList<>());
                when(purchaseOrderService.getPurchaseOrders(any(), any(), any())).thenReturn(mockResponse);

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isOk())
                                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                                .andExpect(jsonPath("$.code").value("1"));
        }

        @Test
        @WithMockUser(username = "user", password = "password")
        void whenDatabaseError_thenReturns500() throws Exception {
                when(purchaseOrderService.getPurchaseOrders(any(), any(), any()))
                                .thenThrow(new BusinessException("購入履歴の取得に失敗しました。"));

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isInternalServerError())
                                .andExpect(jsonPath("$.code").value("500"))
                                .andExpect(jsonPath("$.message").value("購入履歴の取得に失敗しました。"));
        }

        @Test
        @WithMockUser(username = "user", password = "password")
        void whenValidRequest_thenReturnsCombinedOrders() throws Exception {
                List<PurchaseOrderHeader> orders = new ArrayList<>();
                PurchaseOrderHeader order = new PurchaseOrderHeader();
                order.setOrder_no("TEST001");
                order.setProduct_name("Test Product");
                orders.add(order);

                PurchaseOrderResponse mockResponse = createSuccessResponse(orders);
                when(purchaseOrderService.getPurchaseOrders(any(), any(), any())).thenReturn(mockResponse);

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .param("search_from", "2024-01-01")
                                .param("search_to", "2024-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.order_list", hasSize(1)))
                                .andExpect(jsonPath("$.order_list[0].order_no").value("TEST001"));
        }

        private PurchaseOrderResponse createSuccessResponse(List<PurchaseOrderHeader> orderList) {
                PurchaseOrderResponse response = new PurchaseOrderResponse();
                response.setCode("1");
                response.setErr_message("");
                response.setErr_code("");
                response.setOrder_list(orderList);
                return response;
        }

        // Case 2: 正常系 - 検索結果0件
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenNoResults_thenReturns200WithEmptyList() throws Exception {
                when(purchaseOrderService.getPurchaseOrders(any(), any(), any()))
                                .thenReturn(createSuccessResponse(new ArrayList<>()));

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.order_list").isEmpty());
        }

        // Case 3: 異常系 - 必須パラメータなし
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenMissingRequiredParam_thenReturns400() throws Exception {
                mockMvc.perform(get("/api/purchase-orders/headers")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").value("customer_codeは必須です。"));
        }

        // Case 4: 異常系 - 無効なパラメータ
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenInvalidParam_thenReturns400() throws Exception {
                when(purchaseOrderService.getPurchaseOrders(any(), any(), any()))
                                .thenThrow(new BusinessException("入力パラメータが不正です。"));

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "invalid123!@#")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").value("顧客番号は10桁の数字で入力してください。"));
        }

        // Case 5: 異常系 - 不正なエンコーディング
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenInvalidEncoding_thenReturns400() throws Exception {
                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .contentType("application/json; charset=shift-jis"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").value("文字エンコーディングが不正です。UTF-8を指定してください。"));
        }

        // Case 6: 異常系 - SQL実行エラー
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenSQLError_thenReturns500() throws Exception {
                when(purchaseOrderService.getPurchaseOrders(any(), any(), any()))
                                .thenThrow(new BusinessException("内部サーバーエラーが発生しました。") {
                                });

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isInternalServerError())
                                .andExpect(jsonPath("$.message").value("内部サーバーエラーが発生しました。"));
        }

        // Case 7: 異常系 - 無効な日付形式
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenInvalidDateFormat_thenReturns400() throws Exception {
                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .param("search_from", "invalid-date")
                                .param("search_to", "2024-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").value("search_fromで入力バリデーションエラーが発生しました。"));
        }

        // Case 8: 異常系 - 認証エラー
        @Test
        void whenUnauthorized_thenReturns401() throws Exception {
                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .contentType(MediaType.APPLICATION_JSON))
                                .andExpect(status().isUnauthorized());
        }

        // Case 9: 異常系 - customer_noが空の場合
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenEmptyCustomerNo_thenReturns400() throws Exception {
                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "")
                                .param("search_from", "2024-01-01")
                                .param("search_to", "2024-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").value("顧客番号は必須です。"));
        }

        // Case 10: 異常系 - from_dateがto_dateより後の場合
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenFromDateAfterToDate_thenReturns400() throws Exception {
                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", "1234567890")
                                .param("search_from", "2024-12-31")
                                .param("search_to", "2024-01-01")
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").value("検索開始日時は検索終了日時より前の日付を指定してください。"));
        }

        // Case 11: 異常系 - SQLインジェクション攻撃の試行
        @Test
        @WithMockUser(username = "user", password = "password")
        void whenSQLInjectionAttempt_thenReturns400() throws Exception {
                String maliciousInput = "1234567890' OR '1'='1";

                mockMvc.perform(get("/api/purchase-orders/headers")
                                .param("customer_code", maliciousInput)
                                .contentType(MediaType.APPLICATION_JSON)
                                .characterEncoding("UTF-8"))
                                .andExpect(status().isBadRequest())
                                .andExpect(jsonPath("$.code").value("400"))
                                .andExpect(jsonPath("$.message").exists());
        }

}
