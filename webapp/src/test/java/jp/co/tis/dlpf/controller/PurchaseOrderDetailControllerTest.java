package jp.co.tis.dlpf.controller;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import jp.co.tis.dlpf.dto.PurchaseOrderDetailResponse;
import jp.co.tis.dlpf.exception.GlobalExceptionHandler;
import jp.co.tis.dlpf.service.PurchaseOrderDetailService;

@ExtendWith(MockitoExtension.class)
public class PurchaseOrderDetailControllerTest {

    private MockMvc mockMvc;

    @Mock
    private PurchaseOrderDetailService purchaseOrderDetailService;

    @InjectMocks
    private PurchaseOrderDetailController controller;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders
            .standaloneSetup(controller)
            .setControllerAdvice(new GlobalExceptionHandler())
            .build();
    }

    @Test
    public void getPurchaseOrderDetails_Success() throws Exception {
        String orderNo = "ORDER123";
        String customerCode = "CUST456";
        PurchaseOrderDetailResponse mockResponse = new PurchaseOrderDetailResponse();
        mockResponse.setCode("1");

        when(purchaseOrderDetailService.getPurchaseOrderDetails(orderNo, customerCode))
            .thenReturn(mockResponse);

        mockMvc.perform(get("/api/purchase-orders/details")
                .param("order_no", orderNo)
                .param("customer_code", customerCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("1"));

        verify(purchaseOrderDetailService).getPurchaseOrderDetails(orderNo, customerCode);
    }

    @Test
    public void getPurchaseOrderDetails_Error() throws Exception {
        String orderNo = "INVALID";
        String customerCode = "INVALID";

        when(purchaseOrderDetailService.getPurchaseOrderDetails(orderNo, customerCode))
            .thenThrow(new RuntimeException("Error"));

        mockMvc.perform(get("/api/purchase-orders/details")
                .param("order_no", orderNo)
                .param("customer_code", customerCode))
                .andExpect(status().isInternalServerError());
    }

    @Test
    public void getDetails_InvalidEncoding() throws Exception {
        mockMvc.perform(get("/api/purchase-orders/details")
                .param("order_no", "ORDER123")
                .header("Content-Type", "application/json; charset=shift-jis"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("文字エンコーディングが不正です。UTF-8を指定してください。"));
    }

    @Test
    public void getDetails_MissingOrderNo() throws Exception {
        mockMvc.perform(get("/api/purchase-orders/details")
                .param("order_no", "")
                .header("Content-Type", "application/json; charset=utf-8"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("注文番号は必須です。"));
    }

    @Test
    public void getDetails_NullOrderNo() throws Exception {
        mockMvc.perform(get("/api/purchase-orders/details")
                .header("Content-Type", "application/json; charset=utf-8"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("order_noは必須です。"));
    }

    @Test
    public void getDetails_NoContentType() throws Exception {
        String orderNo = "ORDER123";
        String customerCode = "CUST456";
        PurchaseOrderDetailResponse mockResponse = new PurchaseOrderDetailResponse();
        mockResponse.setCode("1");

        when(purchaseOrderDetailService.getPurchaseOrderDetails(orderNo, customerCode))
            .thenReturn(mockResponse);

        mockMvc.perform(get("/api/purchase-orders/details")
                .param("order_no", orderNo)
                .param("customer_code", customerCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("1"));
    }
}
