package jp.co.tis.dlpf.controller;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import jp.co.tis.dlpf.config.SecurityConfig;
import jp.co.tis.dlpf.dto.SubscriptionHistoryResponse;
import jp.co.tis.dlpf.exception.ApiValidationException;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.service.SubscriptionHistoryService;

@WebMvcTest(SubscriptionHistoryController.class)
@Import(SecurityConfig.class)
class SubscriptionHistoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SubscriptionHistoryService subscriptionHistoryService;

    // Case 1: 正常系 - 正常なリクエスト
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenValidRequest_thenReturns200() throws Exception {
        SubscriptionHistoryResponse mockResponse = new SubscriptionHistoryResponse();
        mockResponse.setCode("200");
        mockResponse.setMessage("OK");
        mockResponse.setRegular_sale_order_detail(new ArrayList<>());

        when(subscriptionHistoryService.getSubscriptionHistories("1234567890"))
            .thenReturn(mockResponse);

        mockMvc.perform(get("/api/subscription-histories")
                .param("customer_code", "1234567890")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.message").value("OK"));
    }

    // Case 2: 正常系 - 検索結果0件
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenNoResults_thenReturns200WithEmptyList() throws Exception {
        SubscriptionHistoryResponse mockResponse = new SubscriptionHistoryResponse();
        mockResponse.setCode("200");
        mockResponse.setMessage("OK");
        mockResponse.setRegular_sale_order_detail(new ArrayList<>());

        when(subscriptionHistoryService.getSubscriptionHistories("1234567890"))
            .thenReturn(mockResponse);

        mockMvc.perform(get("/api/subscription-histories")
                .param("customer_code", "1234567890")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.regular_sale_order_detail").isEmpty());
    }

    // Case 3: 異常系 - 必須パラメータなし
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenMissingRequiredParam_thenReturns400() throws Exception {
        mockMvc.perform(get("/api/subscription-histories")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8"))
            .andExpect(status().isBadRequest());
    }

    // Case 4: 異常系 - 無効なパラメータ
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenInvalidParam_thenReturns400() throws Exception {
        when(subscriptionHistoryService.getSubscriptionHistories("invalid123"))
            .thenThrow(new ApiValidationException("顧客番号は10桁の数字で入力してください。"));

        mockMvc.perform(get("/api/subscription-histories")
                .param("customer_code", "invalid123")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("顧客番号は10桁の数字で入力してください。"));
    }

    // Case 5: 異常系 - 不正なエンコーディング
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenInvalidEncoding_thenReturns400() throws Exception {
        mockMvc.perform(get("/api/subscription-histories")
                .param("customer_code", "1234567890")
                .contentType("application/json; charset=shift-jis"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("文字エンコーディングが不正です。UTF-8を指定してください。"));
    }

    // Case 6: 異常系 - DBエラー
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenDatabaseError_thenReturns500() throws Exception {
        when(subscriptionHistoryService.getSubscriptionHistories("1234567890"))
            .thenThrow(new BusinessException("定期購入履歴の取得に失敗しました。"));

        mockMvc.perform(get("/api/subscription-histories")
                .param("customer_code", "1234567890")
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8"))
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value("定期購入履歴の取得に失敗しました。"));
    }

    // Case 7: 異常系 - SQLインジェクション攻撃の試行
    @Test
    @WithMockUser(username = "user", password = "password")
    void whenSQLInjectionAttempt_thenReturns400() throws Exception {
        String maliciousInput = "1234567890' OR '1'='1";

        when(subscriptionHistoryService.getSubscriptionHistories(maliciousInput))
            .thenThrow(new ApiValidationException("顧客番号は10桁の数字で入力してください。"));

        mockMvc.perform(get("/api/subscription-histories")
                .param("customer_code", maliciousInput)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("顧客番号は10桁の数字で入力してください。"));
    }
}
