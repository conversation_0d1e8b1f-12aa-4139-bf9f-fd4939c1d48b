package jp.co.tis.dlpf.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import jp.co.tis.dlpf.dto.CustomerAddress;
import jp.co.tis.dlpf.dto.CustomerCreditCard;
import jp.co.tis.dlpf.dto.CustomerRequest;
import jp.co.tis.dlpf.dto.ErrorResponse;
import jp.co.tis.dlpf.exception.BusinessException;

@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {

        private static final String CUSTOMER_API_URL = "/front/api/ext/customer/info/renewal";

        @Mock
        private RestTemplate restTemplate;

        @InjectMocks
        private CustomerService customerService;

        @Captor
        private ArgumentCaptor<HttpEntity<CustomerRequest>> requestCaptor;

        @Test
        void createCustomer_Success() {
                // Prepare test data
                CustomerRequest request = createTestCustomerRequest();

                // Mock successful response
                ErrorResponse successResponse = ErrorResponse.builder()
                                .code("200")
                                .message("OK")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(successResponse, HttpStatus.OK));

                // Execute and verify
                assertDoesNotThrow(() -> customerService.createCustomer(request));

                // Verify the request
                verify(restTemplate).postForEntity(
                                eq(CUSTOMER_API_URL),
                                requestCaptor.capture(),
                                eq(ErrorResponse.class));

                // Verify request content
                CustomerRequest capturedRequest = requestCaptor.getValue().getBody();
                assertNotNull(capturedRequest, "Request body should not be null");
                assertEquals(request.getCustomerId(), capturedRequest.getCustomerId());
                assertEquals(request.getEmail(), capturedRequest.getEmail());
        }

        @Test
        void createCustomer_Success_WithTestData() {
                // Test case 3: Success with test data
                CustomerRequest request = createTestCustomerRequest();
                request.setCustomerNo("TEST001"); // Test data marker

                ErrorResponse successResponse = ErrorResponse.builder()
                                .code("200")
                                .message("OK")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(successResponse, HttpStatus.OK));

                assertDoesNotThrow(() -> customerService.createCustomer(request));
        }

        @Test
        void createCustomer_MissingRequiredParameter() {
                // Test case 4: Missing required parameter
                CustomerRequest request = createTestCustomerRequest();
                request.setCustomerNo(null); // Required parameter missing

                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message("必須パラメータが存在しない")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("必須パラメータが存在しない", exception.getMessage());
        }

        @Test
        void createCustomer_InvalidParameter() {
                // Test case 5: Invalid parameter value
                CustomerRequest request = createTestCustomerRequest();
                request.setEmail("invalid-email"); // Invalid email format

                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message("リクエストパラメータに誤りがある")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("リクエストパラメータに誤りがある", exception.getMessage());
        }

        @Test
        void createCustomer_OmsError() {
                // Test case 8: OMS error response
                CustomerRequest request = createTestCustomerRequest();

                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message("OMSのレスポンスエラー")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("OMSのレスポンスエラー", exception.getMessage());
        }

        @Test
        void createCustomer_ServerError() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock server error
                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("500")
                                .message("内部サーバーエラー")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR));

                // Execute and verify exception
                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("内部サーバーエラー", exception.getMessage());
        }

        @Test
        void createCustomer_HttpClientError() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock HTTP client error
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR));

                // Execute and verify exception
                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("顧客情報の更新に失敗しました。", exception.getMessage());
        }

        @Test
        void createCustomer_NullResponseBody() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock response with null body
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class))).thenReturn(new ResponseEntity<>(null, HttpStatus.OK));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("予期せぬエラーが発生しました。", exception.getMessage());
        }

        @Test
        void createCustomer_MultipleAddresses() {
                CustomerRequest request = createTestCustomerRequest();

                // Create a new mutable list for addresses
                List<CustomerAddress> addresses = new ArrayList<>(request.getAddresses());

                // Add second address
                CustomerAddress secondAddress = new CustomerAddress();
                secondAddress.setAddress_id("2");
                secondAddress.setFirst_name("太郎");
                secondAddress.setLast_name("変更管理");
                secondAddress.setAddress1("大阪府");
                secondAddress.setPostal_code("5300001");
                secondAddress.setPhone("0601234567");
                addresses.add(secondAddress);

                request.setAddresses(addresses);

                ErrorResponse successResponse = ErrorResponse.builder()
                                .code("200")
                                .message("OK")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(successResponse, HttpStatus.OK));

                assertDoesNotThrow(() -> customerService.createCustomer(request));
        }

        @Test
        void createCustomer_NoAddressesAndCreditCards() {
                CustomerRequest request = createTestCustomerRequest();
                request.setAddresses(null);
                request.setCreditcard(null);

                ErrorResponse successResponse = ErrorResponse.builder()
                                .code("200")
                                .message("OK")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(successResponse, HttpStatus.OK));

                assertDoesNotThrow(() -> customerService.createCustomer(request));
        }

        @Test
        void createCustomer_EmptyEmail() {
                CustomerRequest request = createTestCustomerRequest();
                request.setEmail("");

                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message("メールアドレスは必須です")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("メールアドレスは必須です", exception.getMessage());
        }

        @Test
        void createCustomer_NonSuccessCodeWithSuccessfulHttpStatus() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock response with non-200 code but successful HTTP status
                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("201")
                                .message("処理は完了しましたが、警告があります")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.OK));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("処理は完了しましたが、警告があります", exception.getMessage());
        }

        @Test
        void createCustomer_NonSuccessCodeWithNullMessage() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock response with non-200 code and null message
                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message(null)
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.OK));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("顧客情報の更新に失敗しました。", exception.getMessage());
        }

        @Test
        void createCustomer_NonSuccessCodeWithEmptyMessage() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock response with non-200 code and empty message
                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message("")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.OK));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("顧客情報の更新に失敗しました。", exception.getMessage());
        }

        @Test
        void createCustomer_SuccessHttpStatusWithErrorMessage() {
                CustomerRequest request = createTestCustomerRequest();

                // Mock response with success HTTP status but error message
                ErrorResponse errorResponse = ErrorResponse.builder()
                                .code("400")
                                .message("データの検証エラーがあります")
                                .build();
                when(restTemplate.postForEntity(
                                eq(CUSTOMER_API_URL),
                                any(HttpEntity.class),
                                eq(ErrorResponse.class)))
                                .thenReturn(new ResponseEntity<>(errorResponse, HttpStatus.OK));

                BusinessException exception = assertThrows(BusinessException.class,
                                () -> customerService.createCustomer(request));
                assertEquals("データの検証エラーがあります", exception.getMessage());
        }

        private CustomerRequest createTestCustomerRequest() {
                CustomerRequest request = new CustomerRequest();
                request.setCustomerNo("910010");
                request.setCustomerId("001v00320SRm1nAAD");
                request.setLastName("変更管理");
                request.setFirstName("花子");
                request.setEmail("<EMAIL>");
                request.setBirthday(LocalDate.parse("1989-09-09"));
                request.setGender(2);

                // Add address
                CustomerAddress address = new CustomerAddress();
                address.setAddress_id("1");
                address.setFirst_name("花子");
                address.setLast_name("変更管理");
                address.setAddress1("東京都");
                address.setPostal_code("1010048");
                address.setPhone("0101234567");
                request.setAddresses(Arrays.asList(address));

                // Add credit card
                CustomerCreditCard creditCard = new CustomerCreditCard();
                creditCard.setCreditcard_id("123456");
                creditCard.setCreditcard_detail_id("101");
                creditCard.setActive_card_flag(true);
                request.setCreditcard(Arrays.asList(creditCard));

                return request;
        }
}
