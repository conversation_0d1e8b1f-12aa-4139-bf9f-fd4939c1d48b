package jp.co.tis.dlpf.service;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import jp.co.tis.dlpf.mapper.HealthCheckMapper;

@ExtendWith(MockitoExtension.class)
class HealthCheckServiceTest {

    @Mock
    private HealthCheckMapper healthCheckMapper;

    @InjectMocks
    private HealthCheckService healthCheckService;

    @Test
    void whenDatabaseIsHealthy_thenReturnsTrue() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenReturn(1);

        // Act
        boolean result = healthCheckService.isDatabaseHealthy();

        // Assert
        assertTrue(result, "Database health check should return true when database is healthy");
    }

    @Test
    void whenDatabaseReturnsNull_thenReturnsFalse() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenReturn(null);

        // Act
        boolean result = healthCheckService.isDatabaseHealthy();

        // Assert
        assertFalse(result, "Database health check should return false when database returns null");
    }

    @Test
    void whenDatabaseReturnsUnexpectedValue_thenReturnsFalse() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenReturn(0);

        // Act
        boolean result = healthCheckService.isDatabaseHealthy();

        // Assert
        assertFalse(result, "Database health check should return false when database returns unexpected value");
    }

    @Test
    void whenDatabaseThrowsException_thenReturnsFalse() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenThrow(new RuntimeException("Database connection error"));

        // Act
        boolean result = healthCheckService.isDatabaseHealthy();

        // Assert
        assertFalse(result, "Database health check should return false when database throws exception");
    }
}