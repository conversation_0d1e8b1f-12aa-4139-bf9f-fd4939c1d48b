package jp.co.tis.dlpf.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataIntegrityViolationException;

import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.PointAndRankOrderMapper;

@ExtendWith(MockitoExtension.class)
class PointAndRankOrderServiceTest {

    @Mock
    private PointAndRankOrderMapper pointAndRankOrderMapper;

    @InjectMocks
    private PointAndRankOrderService pointAndRankOrderService;

    @Test
    void whenRegisterPointAndRankOrder_thenSuccess() {
        // Arrange
        String orderNo = "1234567890123456";
        when(pointAndRankOrderMapper.insertPointAndRankOrder(any())).thenReturn(1);

        // Act
        int result = pointAndRankOrderService.registerPointAndRankOrder(orderNo);

        // Assert
        assertEquals(1, result);
    }

    @Test
    void whenRegisterPointAndRankOrder_thenThrowsBusinessException() {
        // Arrange
        String orderNo = "1234567890123456";
        when(pointAndRankOrderMapper.insertPointAndRankOrder(any()))
                .thenThrow(new DataIntegrityViolationException("Database error"));

        // Act & Assert
        assertThrows(BusinessException.class, () -> {
            pointAndRankOrderService.registerPointAndRankOrder(orderNo);
        });
    }
}