package jp.co.tis.dlpf.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;

import jp.co.tis.dlpf.dto.PurchaseOrderDetail;
import jp.co.tis.dlpf.dto.PurchaseOrderDetailResponse;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.PurchaseOrderDetailMapper;

@ExtendWith(MockitoExtension.class)
public class PurchaseOrderDetailServiceTest {

    @Mock
    private PurchaseOrderDetailMapper mapper;

    @InjectMocks
    private PurchaseOrderDetailService service;

    @Test
    public void getPurchaseOrderDetails_Success() {
        String orderNo = "ORDER123";
        String customerCode = "CUST456";
        List<PurchaseOrderDetail> mockOmsResults = new ArrayList<>();
        List<PurchaseOrderDetail> mockPosResults = new ArrayList<>();

        when(mapper.findDetailsFromOMS(orderNo, customerCode)).thenReturn(mockOmsResults);
        when(mapper.findDetailsFromPOS(orderNo, customerCode)).thenReturn(mockPosResults);

        PurchaseOrderDetailResponse response = service.getPurchaseOrderDetails(orderNo, customerCode);

        assertNotNull(response);
        assertEquals("1", response.getCode());
        assertTrue(response.getErr_message().isEmpty());
        verify(mapper).findDetailsFromOMS(orderNo, customerCode);
        verify(mapper).findDetailsFromPOS(orderNo, customerCode);
    }

    @Test
    public void getPurchaseOrderDetails_DataAccessError() {
        String orderNo = "ORDER123";
        String customerCode = "CUST456";

        when(mapper.findDetailsFromOMS(orderNo, customerCode))
            .thenThrow(new DataAccessException("DB Error") {});

        assertThrows(BusinessException.class, () ->
            service.getPurchaseOrderDetails(orderNo, customerCode));
    }

        // Add these tests to PurchaseOrderDetailServiceTest
        @Test
        public void getPurchaseOrderDetails_DBConnectionError() {
            String orderNo = "ORDER123";
            String customerCode = "CUST456";

            when(mapper.findDetailsFromOMS(orderNo, customerCode))
                .thenThrow(new DataAccessException("Connection refused") {});

            BusinessException exception = assertThrows(BusinessException.class, () ->
                service.getPurchaseOrderDetails(orderNo, customerCode));

            assertEquals("購入履歴明細の取得に失敗しました。", exception.getMessage());
        }

        @Test
        public void getPurchaseOrderDetails_SQLError() {
            String orderNo = "ORDER123";
            String customerCode = "CUST456";

            when(mapper.findDetailsFromOMS(orderNo, customerCode))
                .thenThrow(new DataAccessException("SQL syntax error") {});

            BusinessException exception = assertThrows(BusinessException.class, () ->
                service.getPurchaseOrderDetails(orderNo, customerCode));

            assertEquals("購入履歴明細の取得に失敗しました。", exception.getMessage());
        }

        @Test
        public void getPurchaseOrderDetails_SQLInjectionAttempt() {
            String orderNo = "ORDER123'; DROP TABLE users; --";
            String customerCode = "CUST456'; DELETE FROM orders; --";

            when(mapper.findDetailsFromOMS(orderNo, customerCode))
                .thenReturn(new ArrayList<>());
            when(mapper.findDetailsFromPOS(orderNo, customerCode))
                .thenReturn(new ArrayList<>());

            PurchaseOrderDetailResponse response = service.getPurchaseOrderDetails(orderNo, customerCode);

            assertNotNull(response);
            assertEquals("1", response.getCode());
            assertTrue(response.getDetail_list().isEmpty());
        }
}
