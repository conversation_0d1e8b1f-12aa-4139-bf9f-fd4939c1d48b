package jp.co.tis.dlpf.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;

import jp.co.tis.dlpf.dto.SubscriptionHistoryDetail;
import jp.co.tis.dlpf.dto.SubscriptionHistoryResponse;
import jp.co.tis.dlpf.exception.ApiValidationException;
import jp.co.tis.dlpf.exception.BusinessException;
import jp.co.tis.dlpf.mapper.SubscriptionHistoryMapper;

@ExtendWith(MockitoExtension.class)
class SubscriptionHistoryServiceTest {

    @Mock
    private SubscriptionHistoryMapper subscriptionHistoryMapper;

    @InjectMocks
    private SubscriptionHistoryService subscriptionHistoryService;

    // Case 1: 正常系 - 正常なリクエスト
    @Test
    void whenValidRequest_thenReturnsSuccessResponse() {
        List<SubscriptionHistoryDetail> mockHistories = new ArrayList<>();
        when(subscriptionHistoryMapper.findSubscriptionHistories("1234567890"))
                .thenReturn(mockHistories);

        SubscriptionHistoryResponse response = subscriptionHistoryService.getSubscriptionHistories("1234567890");

        assertNotNull(response);
        assertEquals("200", response.getCode());
        assertEquals("OK", response.getMessage());
    }

    // Case 2: 正常系 - 検索結果0件
    @Test
    void whenNoResults_thenReturnsEmptyList() {
        when(subscriptionHistoryMapper.findSubscriptionHistories("1234567890"))
                .thenReturn(new ArrayList<>());

        SubscriptionHistoryResponse response = subscriptionHistoryService.getSubscriptionHistories("1234567890");

        assertNotNull(response);
        assertNotNull(response.getRegular_sale_order_detail());
        assertEquals(0, response.getRegular_sale_order_detail().size());
    }

    // Case 3: 異常系 - 必須パラメータなし
    @Test
    void whenNullCustomerCode_thenThrowsException() {
        assertThrows(ApiValidationException.class, () -> subscriptionHistoryService.getSubscriptionHistories(null));
    }

    // Case 4: 異常系 - 無効なパラメータ
    @Test
    void whenInvalidCustomerCode_thenThrowsException() {
        // Create a customer code that's longer than 12 characters
        String invalidCode = "1234567890123";
        assertThrows(ApiValidationException.class,
                () -> subscriptionHistoryService.getSubscriptionHistories(invalidCode));
    }

    // Case 6: 異常系 - DBエラー
    @Test
    void whenDatabaseError_thenThrowsException() {
        when(subscriptionHistoryMapper.findSubscriptionHistories(anyString()))
                .thenThrow(new DataAccessException("DB Error") {
                });

        assertThrows(BusinessException.class, () -> subscriptionHistoryService.getSubscriptionHistories("1234567890"));
    }

    // Case 7: 異常系 - SQLインジェクション攻撃の試行
    @Test
    void whenSQLInjectionAttempt_thenThrowsException() {
        String maliciousInput = "1234567890' OR '1'='1";

        assertThrows(ApiValidationException.class,
                () -> subscriptionHistoryService.getSubscriptionHistories(maliciousInput));
    }
}
