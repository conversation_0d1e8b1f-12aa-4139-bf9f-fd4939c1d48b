package jp.co.tis.dlpf.webapp;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import jp.co.tis.dlpf.dto.HealthCheckResponse;
import jp.co.tis.dlpf.mapper.HealthCheckMapper;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class HealthCheckIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private HealthCheckMapper healthCheckMapper;

    @Test
    void healthCheck_Success() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenReturn(1);

        // Act
        ResponseEntity<HealthCheckResponse> response = restTemplate.getForEntity(
                "/api/healthcheck", HealthCheckResponse.class);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("UP", response.getBody().getStatus());
        assertEquals("Service is healthy", response.getBody().getMessage());
    }

    @Test
    void healthCheck_DatabaseFailure() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenReturn(null);

        // Act
        ResponseEntity<HealthCheckResponse> response = restTemplate.getForEntity(
                "/api/healthcheck", HealthCheckResponse.class);

        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("DOWN", response.getBody().getStatus());
        assertEquals("Database check failed", response.getBody().getMessage());
    }

    @Test
    void healthCheck_ServiceException() {
        // Arrange
        when(healthCheckMapper.checkDatabaseConnection()).thenThrow(new RuntimeException("Database error"));

        // Act
        ResponseEntity<HealthCheckResponse> response = restTemplate.getForEntity(
                "/api/healthcheck", HealthCheckResponse.class);

        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("DOWN", response.getBody().getStatus());
        assertEquals("Database check failed", response.getBody().getMessage());
    }
}